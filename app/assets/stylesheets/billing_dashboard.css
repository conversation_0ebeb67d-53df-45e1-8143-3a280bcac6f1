/* Billing Dashboard Styles */

.billing-dashboard {
  padding: var(--space-32);
}

/* Subscription Overview */
.subscription-overview {
  display: grid;
  gap: var(--space-24);
}

.plan-details {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.plan-header {
  display: flex;
  align-items: center;
  gap: var(--space-16);
}

.plan-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.plan-icon svg {
  width: 24px;
  height: 24px;
}

.plan-info h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0 0 var(--space-4) 0;
}

.plan-status {
  margin: 0;
}

.plan-pricing {
  text-align: right;
}

.price-display {
  display: flex;
  align-items: baseline;
  gap: var(--space-4);
}

.price-display .currency {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
}

.price-display .price {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
}

.price-display .period {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
}

/* Plan Features */
.plan-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-16);
}

.feature-card {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-20);
  display: flex;
  align-items: flex-start;
  gap: var(--space-16);
}

.feature-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.feature-icon svg {
  width: 20px;
  height: 20px;
  color: var(--color-primary);
}

.feature-details h4 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  margin: 0 0 var(--space-4) 0;
}

.feature-details p {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
}

/* Usage Cards */
.usage-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-16);
}

.usage-card {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-20);
}

.usage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-12);
}

.usage-header h4 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  margin: 0;
}

.usage-percentage {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-primary);
}

.progress-bar {
  height: 8px;
  background: rgba(var(--color-primary-rgb), 0.1);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-bottom: var(--space-12);
}

.progress-fill {
  height: 100%;
  background: var(--color-primary);
  border-radius: var(--radius-full);
  transition: width 0.3s ease;
}

.usage-details {
  display: flex;
  justify-content: space-between;
  font-size: var(--font-size-sm);
}

/* Payment Method */
.payment-method-section {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
}

.payment-card {
  display: flex;
  align-items: center;
  gap: var(--space-16);
  padding: var(--space-16);
  background: var(--color-background);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-md);
}

.payment-card.active {
  border-color: var(--color-primary);
}

.card-icon {
  width: 48px;
  height: 48px;
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-primary);
}

.card-icon svg {
  width: 24px;
  height: 24px;
}

.card-details {
  flex: 1;
}

.card-number {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--space-4);
}

.card-meta {
  display: flex;
  align-items: center;
  gap: var(--space-12);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* Billing History */
.billing-history {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
}

/* Pricing Grid */
.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-20);
}

.pricing-card {
  position: relative;
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  text-align: center;
  transition: all 0.3s ease;
}

.pricing-card:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
}

.pricing-card.current {
  border-color: var(--color-primary);
  border-width: 2px;
}

.pricing-card.featured {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-lg);
}

.featured-label {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--color-primary);
  color: white;
  padding: var(--space-4) var(--space-12);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.plan-badge {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--space-16);
}

.plan-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: var(--space-2);
  margin-bottom: var(--space-24);
}

.plan-price .currency {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
}

.plan-price .price {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
}

.plan-price .period {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.plan-features {
  list-style: none;
  padding: 0;
  margin: 0 0 var(--space-24) 0;
  text-align: left;
}

.plan-features li {
  display: flex;
  align-items: center;
  gap: var(--space-8);
  margin-bottom: var(--space-12);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.check-icon {
  width: 16px;
  height: 16px;
  color: var(--color-success);
  flex-shrink: 0;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: var(--space-48) var(--space-24);
}

.empty-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--space-24);
  background: var(--color-background);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-secondary);
}

.empty-icon svg {
  width: 32px;
  height: 32px;
}

.empty-state h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0 0 var(--space-8) 0;
}

.empty-state p {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0 0 var(--space-24) 0;
}

/* Dark Theme */
[data-color-scheme="dark"] .billing-dashboard {
  --color-background: #0a0a0a;
  --color-surface: #1a1a1a;
  --color-text: #f5f5f5;
  --color-text-secondary: #a8a8a8;
  --color-card-border: rgba(255, 255, 255, 0.1);
}

[data-color-scheme="dark"] .plan-icon,
[data-color-scheme="dark"] .feature-icon {
  background: rgba(var(--color-primary-rgb), 0.2);
}

[data-color-scheme="dark"] .progress-bar {
  background: rgba(var(--color-primary-rgb), 0.2);
}

[data-color-scheme="dark"] .payment-card {
  background: #0a0a0a;
}

[data-color-scheme="dark"] .card-icon {
  background: #1a1a1a;
}

[data-color-scheme="dark"] .empty-icon {
  background: #1a1a1a;
}

/* Responsive */
@media (max-width: 768px) {
  .billing-dashboard {
    padding: var(--space-16);
  }

  .plan-details {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-16);
  }

  .plan-pricing {
    text-align: left;
  }

  .plan-features {
    grid-template-columns: 1fr;
  }

  .usage-cards {
    grid-template-columns: 1fr;
  }

  .pricing-grid {
    grid-template-columns: 1fr;
  }
}