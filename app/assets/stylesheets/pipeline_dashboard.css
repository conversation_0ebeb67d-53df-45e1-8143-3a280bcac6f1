/* Pipeline Dashboard Styles */

.pipeline-dashboard {
  padding: var(--space-32);
  background-color: var(--color-background);
  min-height: 100vh;
}

/* Performance Overview */
.performance-overview {
  margin-bottom: var(--space-32);
}

.performance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-24);
}

.performance-card {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  position: relative;
  overflow: hidden;
  transition: all var(--duration-normal) var(--ease-standard);
}

.performance-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.performance-card.active {
  border-color: var(--color-primary);
  background: linear-gradient(135deg, var(--color-surface) 0%, rgba(var(--color-primary-rgb), 0.05) 100%);
}

.performance-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, rgba(var(--color-primary-rgb), 0.1), rgba(var(--color-primary-rgb), 0.2));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-16);
  color: var(--color-primary);
}

.performance-icon.running {
  background: linear-gradient(135deg, rgba(var(--color-success-rgb), 0.1), rgba(var(--color-success-rgb), 0.2));
  color: var(--color-success);
  animation: pulse-glow 2s infinite;
}

.performance-icon.success {
  background: linear-gradient(135deg, rgba(var(--color-success-rgb), 0.1), rgba(var(--color-success-rgb), 0.2));
  color: var(--color-success);
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(var(--color-success-rgb), 0.4);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(var(--color-success-rgb), 0);
  }
}

.performance-content {
  flex: 1;
}

.performance-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin: 0 0 var(--space-8) 0;
}

.performance-value {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin: 0 0 var(--space-8) 0;
  line-height: 1;
}

.performance-trend {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.performance-trend.positive {
  color: var(--color-success);
}

.performance-trend.warning {
  color: var(--color-warning);
}

.trend-text {
  font-weight: var(--font-weight-medium);
}

.live-indicator {
  position: absolute;
  top: var(--space-16);
  right: var(--space-16);
  display: flex;
  align-items: center;
  gap: var(--space-6);
  padding: var(--space-4) var(--space-12);
  background: rgba(var(--color-success-rgb), 0.1);
  color: var(--color-success);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  letter-spacing: 0.05em;
}

.live-dot {
  width: 6px;
  height: 6px;
  background: var(--color-success);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.performance-chart {
  position: absolute;
  bottom: var(--space-16);
  right: var(--space-16);
  opacity: 0.3;
}

/* Active Pipelines Monitor */
.active-pipelines-monitor {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-32);
  overflow: hidden;
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-24);
  border-bottom: 1px solid var(--color-card-border);
}

.monitor-header h2 {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
}

.monitor-controls {
  display: flex;
  gap: var(--space-12);
}

.monitor-controls .btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-6);
}

.pipeline-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--space-20);
  padding: var(--space-24);
}

.pipeline-card {
  background: var(--color-background);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-20);
  transition: all var(--duration-normal) var(--ease-standard);
}

.pipeline-card:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
}

.pipeline-card.running {
  border-color: var(--color-primary);
  background: linear-gradient(135deg, var(--color-background) 0%, rgba(var(--color-primary-rgb), 0.05) 100%);
}

.pipeline-card.completed {
  border-color: var(--color-success);
  background: linear-gradient(135deg, var(--color-background) 0%, rgba(var(--color-success-rgb), 0.05) 100%);
}

.pipeline-card.failed {
  border-color: var(--color-error);
  background: linear-gradient(135deg, var(--color-background) 0%, rgba(var(--color-error-rgb), 0.05) 100%);
}

.pipeline-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-16);
}

.pipeline-info h3 {
  margin: 0 0 var(--space-4) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
}

.pipeline-info p {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.pipeline-status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-6);
  padding: var(--space-4) var(--space-12);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  letter-spacing: 0.05em;
}

.pipeline-status-badge.running {
  background: rgba(var(--color-primary-rgb), 0.1);
  color: var(--color-primary);
}

.pipeline-status-badge.completed {
  background: rgba(var(--color-success-rgb), 0.1);
  color: var(--color-success);
}

.pipeline-status-badge.failed {
  background: rgba(var(--color-error-rgb), 0.1);
  color: var(--color-error);
}

.status-icon {
  width: 6px;
  height: 6px;
  background: currentColor;
  border-radius: 50%;
}

.pipeline-status-badge.running .status-icon {
  animation: pulse 2s infinite;
}

/* Pipeline Progress */
.pipeline-progress {
  margin-bottom: var(--space-16);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-8);
}

.progress-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
}

.progress-percentage {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--color-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), var(--color-primary-hover));
  border-radius: var(--radius-full);
  transition: width 0.3s ease;
  position: relative;
}

.progress-glow {
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: progress-shimmer 2s infinite;
}

@keyframes progress-shimmer {
  0% {
    transform: translateX(-20px);
  }
  100% {
    transform: translateX(20px);
  }
}

.progress-stats {
  display: flex;
  gap: var(--space-16);
  margin-top: var(--space-12);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.stat-item svg {
  width: 16px;
  height: 16px;
  opacity: 0.6;
}

/* Pipeline Actions */
.pipeline-actions {
  display: flex;
  gap: var(--space-8);
}

.action-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-secondary);
  border: none;
  border-radius: var(--radius-md);
  color: var(--color-text);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
}

.action-btn:hover {
  background: var(--color-secondary-hover);
  transform: translateY(-1px);
}

.action-btn.warning {
  background: rgba(var(--color-warning-rgb), 0.1);
  color: var(--color-warning);
}

.action-btn.warning:hover {
  background: rgba(var(--color-warning-rgb), 0.2);
}

.action-btn.success {
  background: rgba(var(--color-success-rgb), 0.1);
  color: var(--color-success);
}

.action-btn.success:hover {
  background: rgba(var(--color-success-rgb), 0.2);
}

.action-btn.error {
  background: rgba(var(--color-error-rgb), 0.1);
  color: var(--color-error);
}

.action-btn.error:hover {
  background: rgba(var(--color-error-rgb), 0.2);
}

/* Empty State */
.empty-state {
  padding: var(--space-32) var(--space-24);
  text-align: center;
  color: var(--color-text-secondary);
}

.empty-state svg {
  margin: 0 auto var(--space-24);
  opacity: 0.3;
}

.empty-state h3 {
  margin: 0 0 var(--space-8) 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
}

.empty-state p {
  margin: 0 0 var(--space-24) 0;
  font-size: var(--font-size-base);
}

/* Pipeline History */
.pipeline-history {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-24);
  border-bottom: 1px solid var(--color-card-border);
}

.history-header h2 {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
}

.history-filters {
  display: flex;
  gap: var(--space-12);
}

.filter-select {
  padding: var(--space-8) var(--space-16);
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  color: var(--color-text);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
}

.filter-select:hover {
  border-color: var(--color-primary);
}

.filter-select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: var(--focus-ring);
}

/* History Table */
.history-table {
  overflow-x: auto;
}

.history-table table {
  width: 100%;
  border-collapse: collapse;
}

.history-table th {
  text-align: left;
  padding: var(--space-16) var(--space-24);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  background: var(--color-background);
  border-bottom: 1px solid var(--color-border);
}

.history-table td {
  padding: var(--space-16) var(--space-24);
  border-bottom: 1px solid var(--color-border);
}

.history-row {
  transition: background var(--duration-fast) var(--ease-standard);
}

.history-row:hover {
  background: var(--color-background);
}

.pipeline-name {
  display: flex;
  align-items: center;
  gap: var(--space-8);
  font-weight: var(--font-weight-medium);
}

.pipeline-name svg {
  color: var(--color-primary);
}

.in-progress {
  color: var(--color-primary);
  font-style: italic;
}

.record-count {
  font-variant-numeric: tabular-nums;
}

.record-percentage {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

.status-pill {
  display: inline-flex;
  align-items: center;
  padding: var(--space-4) var(--space-12);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.status-pill.completed {
  background: rgba(var(--color-success-rgb), 0.1);
  color: var(--color-success);
}

.status-pill.failed {
  background: rgba(var(--color-error-rgb), 0.1);
  color: var(--color-error);
}

.status-pill.running {
  background: rgba(var(--color-primary-rgb), 0.1);
  color: var(--color-primary);
}

.action-buttons {
  display: flex;
  gap: var(--space-8);
}

.icon-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
}

.icon-btn:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
  background: rgba(var(--color-primary-rgb), 0.05);
}