/* Premium Navigation Styles */

/* Glassmorphism Base */
.nav-glassmorphic {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Premium Scrollbar */
#desktop-sidebar::-webkit-scrollbar {
  width: 6px;
}

#desktop-sidebar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

#desktop-sidebar::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #6366f1, #8b5cf6);
  border-radius: 3px;
}

#desktop-sidebar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #4f46e5, #7c3aed);
}

/* Navigation Item Animations */
.nav-item-premium {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-item-premium::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(to right, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
  border-radius: 0.75rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.nav-item-premium:hover::before {
  opacity: 1;
}

/* Submenu Animations */
.submenu-premium {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.submenu-premium.open {
  max-height: 500px;
}

/* Activity Feed Animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.activity-item {
  animation: slideIn 0.3s ease-out;
}

/* Status Indicators */
@keyframes statusPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

.status-indicator {
  animation: statusPulse 2s infinite;
}

/* Mobile Menu Animations */
.mobile-menu-enter {
  transform: translateX(-100%);
}

.mobile-menu-enter-active {
  transform: translateX(0);
  transition: transform 0.3s ease-out;
}

.mobile-menu-exit {
  transform: translateX(0);
}

.mobile-menu-exit-active {
  transform: translateX(-100%);
  transition: transform 0.3s ease-in;
}

/* Premium Hover Effects */
.premium-hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.premium-hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Gradient Text Animation */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-text-animated {
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

/* Icon Rotation Effects */
.icon-rotate {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.icon-rotate:hover {
  transform: rotate(360deg);
}

/* Glow Effects */
.glow-effect {
  position: relative;
}

.glow-effect::after {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(45deg, #6366f1, #8b5cf6, #ec4899, #6366f1);
  background-size: 400% 400%;
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
  animation: gradientShift 3s ease infinite;
  filter: blur(10px);
  z-index: -1;
}

.glow-effect:hover::after {
  opacity: 0.7;
}

/* Shimmer Loading Effect */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.shimmer-loading {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Premium Focus States */
.premium-focus:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1), 
              0 0 0 1px rgba(99, 102, 241, 0.3);
}

/* Smooth Height Transitions */
.smooth-height {
  transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Premium Badge Animations */
@keyframes badgePulse {
  0% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
  }
}

.badge-pulse {
  animation: badgePulse 2s infinite;
}

/* Navigation Dividers */
.nav-divider {
  position: relative;
  margin: 1rem 0;
}

.nav-divider::before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, 
    transparent, 
    rgba(99, 102, 241, 0.2) 20%, 
    rgba(139, 92, 246, 0.2) 80%, 
    transparent
  );
}

/* Premium Tooltips */
.premium-tooltip {
  position: relative;
}

.premium-tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-8px);
  padding: 0.5rem 1rem;
  background: rgba(17, 24, 39, 0.95);
  backdrop-filter: blur(10px);
  color: white;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 0.5rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 50;
}

.premium-tooltip:hover::after {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0);
}