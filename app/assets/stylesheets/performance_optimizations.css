/* Performance Optimizations for Premium Landing Page */

/* Critical CSS - Above the fold optimizations */
.critical-content {
  contain: layout style paint;
}

/* GPU acceleration for animations */
.premium-animate-float,
.premium-animate-pulse,
.premium-btn,
.premium-card {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Optimize backdrop-filter performance */
.premium-card-glass {
  contain: layout style paint;
  isolation: isolate;
}

/* Optimize gradient performance */
.premium-gradient-text {
  contain: layout style paint;
}

/* Lazy loading optimizations */
.lazy-load {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.lazy-load.loaded {
  opacity: 1;
}

/* Image optimization */
img {
  content-visibility: auto;
  contain-intrinsic-size: 300px 200px;
}

/* Reduce layout thrashing */
.premium-grid {
  contain: layout;
}

/* Optimize scroll performance */
.premium-section {
  contain: layout style paint;
}

/* Font loading optimizations */
@font-face {
  font-family: 'Inter';
  font-display: swap;
  src: url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
}

/* Reduce paint complexity */
.premium-card::before,
.premium-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  pointer-events: none;
  will-change: opacity;
}

/* Optimize animations for 60fps */
@keyframes premium-float-optimized {
  0%, 100% { 
    transform: translate3d(0, 0, 0); 
  }
  50% { 
    transform: translate3d(0, -10px, 0); 
  }
}

@keyframes premium-pulse-optimized {
  0%, 100% { 
    opacity: 1; 
    transform: scale3d(1, 1, 1); 
  }
  50% { 
    opacity: 0.7; 
    transform: scale3d(1.05, 1.05, 1); 
  }
}

/* Replace existing animations with optimized versions */
.premium-animate-float {
  animation: premium-float-optimized 3s ease-in-out infinite;
}

.premium-animate-pulse {
  animation: premium-pulse-optimized 2s ease-in-out infinite;
}

/* Accessibility optimizations */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .premium-card-glass {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid #000;
  }
  
  .premium-btn-primary {
    background: #000;
    color: #fff;
    border: 2px solid #fff;
  }
  
  .premium-btn-secondary {
    background: #fff;
    color: #000;
    border: 2px solid #000;
  }
}

/* Focus management for accessibility */
.premium-btn:focus,
.premium-card:focus {
  outline: 3px solid #4f46e5;
  outline-offset: 2px;
}

/* Skip link for screen readers */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 9999;
}

.skip-link:focus {
  top: 6px;
}

/* Optimize for print */
@media print {
  .premium-animate-float,
  .premium-animate-pulse {
    animation: none;
  }
  
  .premium-card-glass {
    background: #fff;
    border: 1px solid #000;
  }
  
  .premium-gradient-text {
    color: #000 !important;
    background: none !important;
    -webkit-text-fill-color: #000 !important;
  }
}

/* Content visibility optimizations */
.premium-section:not(.in-viewport) {
  content-visibility: auto;
  contain-intrinsic-size: 0 500px;
}

/* Intersection observer optimizations */
.fade-in-observer {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in-observer.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Memory usage optimizations */
.premium-card-large {
  contain: layout style paint size;
}

/* Optimize for mobile performance */
@media (max-width: 768px) {
  .premium-card-glass {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
  
  .premium-animate-float,
  .premium-animate-pulse {
    animation-duration: 4s;
  }
}

/* Critical resource hints */
.preload-font {
  font-display: swap;
}

/* Optimize SVG performance */
svg {
  contain: layout style paint;
}

/* Reduce composite layers */
.premium-btn:not(:hover):not(:focus) {
  will-change: auto;
}

.premium-card:not(:hover):not(:focus) {
  will-change: auto;
}
