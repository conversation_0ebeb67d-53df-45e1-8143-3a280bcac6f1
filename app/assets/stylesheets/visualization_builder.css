/* Visualization Builder Styles */
.visualization-builder {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.visualization-builder__header {
  background: rgba(255, 255, 255, 0.95);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
}

.visualization-builder__controls {
  background: rgba(255, 255, 255, 0.98);
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.visualization-builder__chart-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.visualization-builder__chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
  border-radius: 12px 12px 0 0;
}

.visualization-builder__data-table {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  max-height: 400px;
  overflow-y: auto;
}

.visualization-builder__data-table::-webkit-scrollbar {
  width: 6px;
}

.visualization-builder__data-table::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.visualization-builder__data-table::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.visualization-builder__data-table::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.visualization-builder__insight-card {
  transition: all 0.3s ease;
  border-radius: 12px;
  border: 1px solid transparent;
}

.visualization-builder__insight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 255, 255, 0.2);
}

.visualization-builder__control-group {
  position: relative;
}

.visualization-builder__control-group label {
  display: block;
  font-weight: 600;
  font-size: 0.875rem;
  color: #374151;
  margin-bottom: 6px;
  transition: color 0.2s ease;
}

.visualization-builder__control-group:focus-within label {
  color: #6366f1;
}

.visualization-builder__select,
.visualization-builder__input {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  background: white;
}

.visualization-builder__select:focus,
.visualization-builder__input:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  transform: translateY(-1px);
}

.visualization-builder__button {
  display: inline-flex;
  align-items: center;
  padding: 10px 16px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  text-decoration: none;
  border: none;
  cursor: pointer;
}

.visualization-builder__button--primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
}

.visualization-builder__button--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
}

.visualization-builder__button--secondary {
  background: white;
  color: #6b7280;
  border: 2px solid #e5e7eb;
}

.visualization-builder__button--secondary:hover {
  background: #f9fafb;
  border-color: #d1d5db;
  transform: translateY(-1px);
}

.visualization-builder__loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  flex-direction: column;
  color: #6b7280;
}

.visualization-builder__loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #6366f1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.visualization-builder__notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 16px 20px;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  transform: translateX(400px);
  transition: transform 0.3s ease;
  z-index: 1000;
}

.visualization-builder__notification--success {
  background: linear-gradient(135deg, #10b981, #059669);
}

.visualization-builder__notification--error {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.visualization-builder__notification--show {
  transform: translateX(0);
}

/* Chart container enhancements */
.visualization-builder__chart-wrapper {
  position: relative;
  height: 400px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
}

.visualization-builder__chart-wrapper canvas {
  border-radius: 8px;
}

/* Table styling improvements */
.visualization-builder__table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.visualization-builder__table th {
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  color: #374151;
  font-weight: 600;
  font-size: 0.875rem;
  padding: 12px 16px;
  text-align: left;
  border-bottom: 2px solid #e5e7eb;
}

.visualization-builder__table th:first-child {
  border-top-left-radius: 8px;
}

.visualization-builder__table th:last-child {
  border-top-right-radius: 8px;
}

.visualization-builder__table td {
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  color: #6b7280;
  font-size: 0.875rem;
}

.visualization-builder__table tr:hover td {
  background: #f9fafb;
}

.visualization-builder__table tr:last-child td {
  border-bottom: none;
}

/* Responsive design */
@media (max-width: 1024px) {
  .visualization-builder__controls {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .visualization-builder__controls {
    grid-template-columns: 1fr;
  }
  
  .visualization-builder__chart-container {
    margin-bottom: 20px;
  }
  
  .visualization-builder__button {
    width: 100%;
    justify-content: center;
    margin-bottom: 8px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .visualization-builder__controls {
    background: rgba(31, 41, 55, 0.98);
  }
  
  .visualization-builder__control-group label {
    color: #d1d5db;
  }
  
  .visualization-builder__select,
  .visualization-builder__input {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }
  
  .visualization-builder__chart-container {
    background: #1f2937;
  }
  
  .visualization-builder__data-table {
    background: #1f2937;
  }
  
  .visualization-builder__table th {
    background: linear-gradient(135deg, #374151, #4b5563);
    color: #f9fafb;
    border-bottom-color: #6b7280;
  }
  
  .visualization-builder__table td {
    color: #d1d5db;
    border-bottom-color: #4b5563;
  }
  
  .visualization-builder__table tr:hover td {
    background: #374151;
  }
}