/* Data Source Wizard Styles */

.data-source-wizard {
  max-width: 900px;
  margin: 0 auto;
}

.wizard-container {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

/* Progress Section */
.wizard-progress {
  background: var(--color-background);
  padding: var(--space-32);
  border-bottom: 1px solid var(--color-border);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-24);
  gap: var(--space-24);
}

.progress-info h2 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin: 0 0 var(--space-8) 0;
}

.progress-info p {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin: 0;
}

.progress-stats {
  text-align: right;
  flex-shrink: 0;
}

.step-indicator {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-4);
}

.progress-percent {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
}

/* Progress Bar */
.progress-bar-container {
  margin-bottom: var(--space-32);
}

.progress-bar {
  height: 8px;
  background: var(--color-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
  transition: width var(--duration-normal) var(--ease-standard);
}

/* Step Indicators */
.step-indicators {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-8);
  position: relative;
  z-index: 2;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  background: var(--color-surface);
  border: 2px solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-secondary);
  position: relative;
  transition: all var(--duration-normal) var(--ease-standard);
}

.step-item.active .step-number {
  background: var(--color-primary);
  border-color: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.step-item.completed .step-number {
  background: var(--color-success);
  border-color: var(--color-success);
  color: white;
}

.step-item.completed .step-number span {
  display: none;
}

.step-item.completed .step-check {
  display: block;
}

.step-check {
  display: none;
  position: absolute;
  color: white;
}

.step-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  transition: color var(--duration-fast) var(--ease-standard);
}

.step-item.active .step-label,
.step-item.completed .step-label {
  color: var(--color-text);
}

.step-connector {
  width: 100px;
  height: 2px;
  background: var(--color-border);
  position: relative;
  z-index: 1;
}

.step-connector::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 0%;
  background: var(--color-success);
  transition: width var(--duration-normal) var(--ease-standard);
}

.step-item.completed + .step-connector::after {
  width: 100%;
}

/* Form Container */
.wizard-form-container {
  padding: var(--space-32);
  position: relative;
}

.wizard-form {
  position: relative;
}

/* Auto-save Status */
.autosave-status {
  position: absolute;
  top: calc(var(--space-32) * -1 + var(--space-8));
  right: 0;
  display: flex;
  align-items: center;
  gap: var(--space-16);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-8);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
  background: var(--color-success);
  animation: pulse 2s infinite;
}

.autosave-status.saving .status-dot {
  background: var(--color-warning);
}

.autosave-status.error .status-dot {
  background: var(--color-error);
  animation: none;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Wizard Steps */
.wizard-step {
  display: none;
  animation: fadeIn var(--duration-normal) var(--ease-standard);
}

.wizard-step.active {
  display: block;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.step-content {
  max-width: 800px;
  margin: 0 auto;
}

.step-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0 0 var(--space-12) 0;
}

.step-subtitle {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin: 0 0 var(--space-32) 0;
}

/* Platform Selection Grid */
.platforms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--space-16);
  margin-bottom: var(--space-32);
}

.platform-card {
  background: var(--color-background);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  text-align: center;
  cursor: pointer;
  position: relative;
  transition: all var(--duration-fast) var(--ease-standard);
}

.platform-card:hover:not(.disabled) {
  border-color: var(--color-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.platform-card.selected {
  background: rgba(var(--color-primary-rgb), 0.05);
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
}

.platform-card.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.platform-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--space-16);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
}

.platform-icon.shopify {
  background: rgba(var(--color-primary-rgb), 0.1);
}

.platform-icon.stripe {
  background: rgba(147, 51, 234, 0.1);
}

.platform-icon.quickbooks {
  background: rgba(59, 130, 246, 0.1);
}

.platform-icon.google_analytics {
  background: rgba(251, 146, 60, 0.1);
}

.platform-icon.mailchimp {
  background: rgba(250, 204, 21, 0.1);
}

.platform-card h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0 0 var(--space-8) 0;
}

.platform-card p {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
}

.platform-radio {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.coming-soon-badge {
  position: absolute;
  top: var(--space-16);
  right: var(--space-16);
  padding: var(--space-4) var(--space-12);
  background: var(--color-secondary);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
}

/* Coming Soon Section */
.coming-soon-section {
  margin-top: var(--space-48);
  padding-top: var(--space-32);
  border-top: 1px solid var(--color-border);
}

.coming-soon-section h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-secondary);
  margin: 0 0 var(--space-24) 0;
}

.platforms-grid.coming-soon {
  opacity: 0.6;
}

/* Configuration Form */
.configuration-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-32);
}

.form-section {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
}

.form-section h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0 0 var(--space-20) 0;
}

.form-group {
  margin-bottom: var(--space-24);
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  margin-bottom: var(--space-8);
}

.form-input,
.form-select {
  width: 100%;
  padding: var(--space-12) var(--space-16);
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  color: var(--color-text);
  transition: all var(--duration-fast) var(--ease-standard);
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: var(--focus-ring);
}

textarea.form-input {
  resize: vertical;
  min-height: 100px;
}

.form-hint {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-top: var(--space-6);
}

/* Toggle Switch */
.toggle-label {
  display: flex;
  align-items: center;
  gap: var(--space-12);
  cursor: pointer;
}

.toggle-input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.toggle-switch {
  width: 48px;
  height: 24px;
  background: var(--color-border);
  border-radius: var(--radius-full);
  position: relative;
  transition: background var(--duration-fast) var(--ease-standard);
}

.toggle-switch::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: var(--radius-full);
  transition: transform var(--duration-fast) var(--ease-standard);
}

.toggle-input:checked + .toggle-switch {
  background: var(--color-primary);
}

.toggle-input:checked + .toggle-switch::after {
  transform: translateX(24px);
}

.toggle-text {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

/* Preview Section */
.preview-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-32);
}

.connection-test {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-32);
  text-align: center;
}

.test-status {
  margin-bottom: var(--space-24);
}

.status-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--space-16);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-full);
}

.status-icon.loading {
  background: rgba(var(--color-info-rgb), 0.1);
  color: var(--color-info);
}

.status-icon.success {
  background: rgba(var(--color-success-rgb), 0.1);
  color: var(--color-success);
}

.status-icon.error {
  background: rgba(var(--color-error-rgb), 0.1);
  color: var(--color-error);
}

.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.status-message h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0 0 var(--space-8) 0;
}

.status-message p {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin: 0;
}

/* Data Preview */
.data-preview {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
}

.data-preview h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0 0 var(--space-16) 0;
}

.preview-table-container {
  overflow-x: auto;
}

.preview-placeholder {
  text-align: center;
  color: var(--color-text-secondary);
  padding: var(--space-32);
}

/* Data Mapping */
.data-mapping {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
}

.data-mapping h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0 0 var(--space-8) 0;
}

/* Review Section */
.review-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-24);
}

.summary-card,
.next-steps-card {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
}

.summary-card h4,
.next-steps-card h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0 0 var(--space-20) 0;
}

.summary-items {
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-12) 0;
  border-bottom: 1px solid var(--color-border);
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.summary-value {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

/* Timeline */
.steps-timeline {
  display: flex;
  flex-direction: column;
  gap: var(--space-20);
}

.timeline-item {
  display: flex;
  gap: var(--space-16);
}

.timeline-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  background: rgba(var(--color-primary-rgb), 0.1);
  color: var(--color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.timeline-content h5 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0 0 var(--space-4) 0;
}

.timeline-content p {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
}

/* Navigation */
.wizard-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--space-32);
  border-top: 1px solid var(--color-border);
  margin-top: var(--space-32);
}

.nav-center {
  flex: 0 0 auto;
}

/* Help Section */
.help-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-24);
  margin-top: var(--space-48);
}

.help-card {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  display: flex;
  gap: var(--space-20);
  transition: all var(--duration-normal) var(--ease-standard);
}

.help-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.help-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  background: rgba(var(--color-primary-rgb), 0.1);
  color: var(--color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.help-content h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0 0 var(--space-8) 0;
}

.help-content p {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0 0 var(--space-12) 0;
}

.help-link {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

.help-link:hover {
  color: var(--color-primary-hover);
  text-decoration: underline;
}

.help-actions {
  display: flex;
  gap: var(--space-16);
}

/* Error Panel */
.error-panel {
  background: rgba(var(--color-error-rgb), 0.05);
  border: 1px solid rgba(var(--color-error-rgb), 0.2);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  margin-top: var(--space-32);
  display: flex;
  gap: var(--space-16);
}

.error-icon {
  color: var(--color-error);
  flex-shrink: 0;
}

.error-content h3 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-error);
  margin: 0 0 var(--space-12) 0;
}

.error-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.error-list li {
  color: var(--color-text);
  font-size: var(--font-size-sm);
  padding: var(--space-4) 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .wizard-container {
    border-radius: 0;
    border-left: 0;
    border-right: 0;
  }
  
  .wizard-progress,
  .wizard-form-container {
    padding: var(--space-24);
  }
  
  .progress-header {
    flex-direction: column;
    text-align: center;
  }
  
  .progress-stats {
    text-align: center;
  }
  
  .step-indicators {
    flex-wrap: wrap;
    gap: var(--space-16);
  }
  
  .step-connector {
    display: none;
  }
  
  .platforms-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
  
  .review-section {
    grid-template-columns: 1fr;
  }
  
  .help-section {
    grid-template-columns: 1fr;
  }
  
  .wizard-navigation {
    flex-wrap: wrap;
    gap: var(--space-16);
  }
  
  .wizard-navigation button {
    flex: 1;
    min-width: 120px;
  }
  
  .nav-center {
    order: -1;
    width: 100%;
    text-align: center;
  }
}

@media (max-width: 640px) {
  .platforms-grid {
    grid-template-columns: 1fr;
  }
  
  .autosave-status {
    position: static;
    margin-bottom: var(--space-16);
    justify-content: center;
  }
  
  .status-time {
    display: none;
  }
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-8);
  padding: var(--space-12) var(--space-24);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
}

.btn--primary {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
  border-color: var(--color-primary);
}

.btn--primary:hover {
  background: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
}

.btn--primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn--outline {
  background: transparent;
  color: var(--color-text);
  border-color: var(--color-border);
}

.btn--outline:hover {
  background: var(--color-surface);
  border-color: var(--color-primary);
}

.btn--outline:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn--ghost {
  background: transparent;
  color: var(--color-text-secondary);
  border-color: transparent;
}

.btn--ghost:hover {
  color: var(--color-text);
  background: var(--color-secondary);
}