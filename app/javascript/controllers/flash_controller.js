import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["progressBar"]
  connect() {
    // Calculate display duration based on message length for better readability
    const messageText = this.element.textContent || ''
    const baseTime = 12000 // 12 seconds base time for comfortable reading
    const extraTime = Math.min(messageText.length * 80, 8000) // Up to 8 extra seconds for longer messages
    this.displayDuration = baseTime + extraTime
    this.startTime = Date.now()
    this.isPaused = false

    // Start progress bar animation if target exists
    if (this.hasProgressBarTarget) {
      this.animateProgressBar()
    }

    // Set up hover pause/resume functionality
    this.setupHoverHandlers()

    // Set up keyboard accessibility
    this.setupKeyboardHandlers()

    // Auto-dismiss after calculated duration
    this.startDismissTimer()
  }
  
  disconnect() {
    if (this.timeout) {
      clearTimeout(this.timeout)
    }
  }
  
  dismiss() {
    this.element.classList.add('dismissing')
  }
  
  remove() {
    this.element.remove()
  }

  startDismissTimer() {
    this.timeout = setTimeout(() => {
      this.dismiss()
    }, this.displayDuration)
  }

  setupHoverHandlers() {
    this.element.addEventListener('mouseenter', () => this.pauseTimer())
    this.element.addEventListener('mouseleave', () => this.resumeTimer())
  }

  setupKeyboardHandlers() {
    this.element.addEventListener('keydown', (event) => {
      if (event.key === 'Escape') {
        event.preventDefault()
        this.dismiss()
      }
    })

    // Focus the flash message for screen readers
    setTimeout(() => {
      this.element.focus()
    }, 100)
  }

  pauseTimer() {
    if (this.isPaused || !this.timeout) return

    this.isPaused = true
    this.remainingTime = this.displayDuration - (Date.now() - this.startTime)
    clearTimeout(this.timeout)

    // Pause progress bar animation
    if (this.hasProgressBarTarget) {
      const computedStyle = window.getComputedStyle(this.progressBarTarget)
      const currentTransform = computedStyle.transform
      this.progressBarTarget.style.transition = 'none'
      this.progressBarTarget.style.transform = currentTransform
    }
  }

  resumeTimer() {
    if (!this.isPaused) return

    this.isPaused = false
    this.startTime = Date.now()

    // Resume progress bar animation
    if (this.hasProgressBarTarget && this.remainingTime > 0) {
      this.progressBarTarget.style.transition = `transform ${this.remainingTime}ms linear`
      requestAnimationFrame(() => {
        this.progressBarTarget.style.transform = 'scaleX(0)'
      })
    }

    // Restart timer with remaining time
    this.timeout = setTimeout(() => {
      this.dismiss()
    }, this.remainingTime)
  }

  animateProgressBar() {
    if (!this.hasProgressBarTarget) return

    // Set initial state
    this.progressBarTarget.style.transform = 'scaleX(1)'
    this.progressBarTarget.style.transition = `transform ${this.displayDuration}ms linear`

    // Start animation
    requestAnimationFrame(() => {
      this.progressBarTarget.style.transform = 'scaleX(0)'
    })
  }
}