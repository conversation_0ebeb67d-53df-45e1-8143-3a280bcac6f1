<% content_for :page_title, "Analytics Dashboard" %>

<div class="analytics-dashboard">
  <!-- Ultra Premium Header -->
  <div class="dashboard-header">
    <div class="header-content">
      <div class="header-main">
        <div class="header-title-group">
          <div class="header-icon-wrapper">
            <div class="header-icon-gradient">
              <svg class="header-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
          </div>
          <div class="header-text">
            <h1 class="header-title">Analytics Dashboard</h1>
            <p class="header-subtitle">Instant insights from your fully managed, no-code data warehouse</p>
          </div>
        </div>
        
        <!-- Premium Controls -->
        <div class="header-controls">
          <div class="live-indicator">
            <div class="live-dot"></div>
            <span class="live-text">Live</span>
          </div>
          <%= form_with url: analytics_path, method: :get, local: true, class: "date-range-form" do |form| %>
            <%= form.select :date_range, 
                options_for_select([
                  ['Last 7 days', '7_days'],
                  ['Last 30 days', '30_days'],
                  ['Last 90 days', '90_days'],
                  ['Last year', '1_year']
                ], @date_range),
                {},
                { 
                  class: "date-range-select",
                  onchange: "this.form.submit();"
                } %>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Ultra Premium Key Metrics -->
  <div class="dashboard-content">
    <div class="metrics-grid">
      <!-- Total Data Sources -->
      <div class="metric-card">
        <div class="metric-card-inner">
          <div class="metric-icon-wrapper">
            <div class="metric-icon-gradient gradient-primary">
              <svg class="metric-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125" />
              </svg>
            </div>
          </div>
          <div class="metric-content">
            <h3 class="metric-label">Total Sources</h3>
            <p class="metric-value"><%= number_with_delimiter(@total_data_sources) %></p>
            <div class="metric-footer">
              <span class="metric-status success"><%= @active_data_sources %> active</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Success Rate -->
      <div class="metric-card">
        <div class="metric-card-inner">
          <div class="metric-icon-wrapper">
            <div class="metric-icon-gradient gradient-success">
              <svg class="metric-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <div class="metric-content">
            <h3 class="metric-label">Success Rate</h3>
            <p class="metric-value"><%= @success_rate %>%</p>
            <div class="metric-progress">
              <div class="progress-bar">
                <div class="progress-fill success" style="width: <%= @success_rate %>%"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Total Jobs -->
      <div class="metric-card">
        <div class="metric-card-inner">
          <div class="metric-icon-wrapper">
            <div class="metric-icon-gradient gradient-purple">
              <svg class="metric-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
          </div>
          <div class="metric-content">
            <h3 class="metric-label">Sync Jobs</h3>
            <p class="metric-value"><%= number_with_delimiter(@total_jobs) %></p>
            <div class="metric-footer split">
              <span class="metric-status success"><%= @successful_jobs %> successful</span>
              <span class="metric-status error"><%= @failed_jobs %> failed</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Records Processed -->
      <div class="metric-card">
        <div class="metric-card-inner">
          <div class="metric-icon-wrapper">
            <div class="metric-icon-gradient gradient-orange">
              <svg class="metric-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
          </div>
          <div class="metric-content">
            <h3 class="metric-label">Total Records</h3>
            <p class="metric-value"><%= number_with_delimiter(@total_records) %></p>
            <div class="metric-footer">
              <span class="metric-status"><%= number_with_delimiter(@processed_records) %> processed</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Processing Rate -->
      <div class="metric-card">
        <div class="metric-card-inner">
          <div class="metric-icon-wrapper">
            <div class="metric-icon-gradient gradient-teal">
              <svg class="metric-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <div class="metric-content">
            <h3 class="metric-label">Processing Rate</h3>
            <p class="metric-value"><%= @processing_rate %>%</p>
            <div class="metric-progress">
              <div class="progress-bar">
                <div class="progress-fill teal" style="width: <%= @processing_rate %>%"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Ultra Premium Charts Section -->
    <div class="charts-section">
      <div class="charts-grid">
        <!-- Data Sources by Type -->
        <div class="chart-card">
          <div class="chart-header">
            <div class="chart-header-content">
              <div class="chart-icon-wrapper">
                <div class="chart-icon-gradient gradient-primary">
                  <svg class="chart-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" />
                    <path stroke-linecap="round" stroke-linejoin="round" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z" />
                  </svg>
                </div>
              </div>
              <h3 class="chart-title">Data Sources by Type</h3>
            </div>
          </div>
          <div class="p-6">
            <div class="space-y-3">
              <% @data_sources_by_type.each_with_index do |(type, count), index| %>
                <% colors = ['bg-blue-600', 'bg-green-600', 'bg-purple-600', 'bg-orange-600', 'bg-pink-600'] %>
                <% color = colors[index % colors.length] %>
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors duration-200">
                  <div class="flex items-center gap-3">
                    <div class="w-4 h-4 <%= color %> rounded-full"></div>
                    <span class="font-medium text-gray-900"><%= type.humanize %></span>
                  </div>
                  <span class="text-lg font-semibold text-gray-900"><%= count %></span>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>

      <!-- Top Performing Data Sources -->
      <div class="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex items-center space-x-3">
            <div class="p-2 bg-green-600 rounded-lg shadow-sm">
              <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900">Top Performing Sources</h3>
          </div>
        </div>
        <div class="p-6">
          <% if @top_data_sources.any? %>
            <div class="space-y-3">
              <% @top_data_sources.each_with_index do |(name, records), index| %>
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors duration-200">
                  <div class="flex items-center gap-3">
                    <div class="p-2 bg-green-600 rounded-lg shadow-sm">
                      <span class="text-white font-medium text-sm">#<%= index + 1 %></span>
                    </div>
                    <span class="font-medium text-gray-900"><%= name %></span>
                  </div>
                  <span class="text-lg font-semibold text-gray-900"><%= number_with_delimiter(records) %></span>
                </div>
              <% end %>
            </div>
          <% else %>
            <div class="text-center py-12">
              <div class="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <p class="text-gray-500">No data available for the selected period</p>
            </div>
          <% end %>
        </div>
        </div>
      </div>
    </div>

    <!-- Activity Timeline & Performance Trends -->
    <div class="activity-section">
      <div class="section-container">
        <div class="activity-grid">
        <!-- Daily Activity Chart -->
        <div class="activity-chart-card">
          <div class="chart-header">
            <div class="chart-header-content">
              <div class="chart-icon-wrapper">
                <div class="chart-icon-gradient gradient-primary">
                  <svg class="chart-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
              </div>
              <h3 class="chart-title">Daily Activity Trend</h3>
            </div>
          </div>
          <div class="chart-body">
            <% if @daily_activity.any? %>
              <div class="activity-bars">
                <% max_activity = @daily_activity.values.max %>
                <% @daily_activity.each do |date, count| %>
                  <div class="activity-bar-item">
                    <span class="activity-date"><%= date.strftime('%m/%d') %></span>
                    <div class="activity-bar-wrapper">
                      <div class="activity-bar-background"></div>
                      <div class="activity-bar-fill" 
                           style="width: <%= max_activity > 0 ? (count.to_f / max_activity * 100).round(1) : 0 %>%"></div>
                    </div>
                    <span class="activity-count"><%= count %></span>
                  </div>
                <% end %>
              </div>
            <% else %>
              <div class="empty-state">
                <div class="empty-state-icon">
                  <svg class="empty-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <p class="empty-text">No activity data for the selected period</p>
              </div>
            <% end %>
          </div>
        </div>

        <!-- System Health -->
        <div class="system-health-card">
          <div class="chart-header">
            <div class="chart-header-content">
              <div class="chart-icon-wrapper">
                <div class="chart-icon-gradient gradient-success">
                  <svg class="chart-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                </div>
              </div>
              <h3 class="chart-title">System Health</h3>
            </div>
          </div>
          <div class="health-metrics">
            <!-- Active Sources -->
            <div class="health-metric-item active">
              <div class="health-metric-header">
                <span class="health-metric-label">Active Sources</span>
                <span class="health-metric-value"><%= @active_data_sources %></span>
              </div>
              <div class="health-progress">
                <div class="health-progress-bar">
                  <div class="health-progress-fill active" style="width: <%= @total_data_sources > 0 ? (@active_data_sources.to_f / @total_data_sources * 100).round(1) : 0 %>%"></div>
                </div>
              </div>
            </div>
            
            <!-- Pending -->
            <div class="health-metric-item pending">
              <div class="health-metric-header">
                <span class="health-metric-label">Pending</span>
                <span class="health-metric-value"><%= [@total_data_sources - @active_data_sources - @failed_jobs, 0].max %></span>
              </div>
              <div class="health-progress">
                <div class="health-progress-bar">
                  <div class="health-progress-fill pending" style="width: <%= @total_data_sources > 0 ? ([@total_data_sources - @active_data_sources - @failed_jobs, 0].max.to_f / @total_data_sources * 100).round(1) : 0 %>%"></div>
                </div>
              </div>
            </div>
            
            <!-- Issues -->
            <div class="health-metric-item issues">
              <div class="health-metric-header">
                <span class="health-metric-label">Issues</span>
                <span class="health-metric-value"><%= @failed_jobs %></span>
              </div>
              <div class="health-progress">
                <div class="health-progress-bar">
                  <div class="health-progress-fill issues" style="width: <%= @total_data_sources > 0 ? (@failed_jobs.to_f / @total_data_sources * 100).round(1) : 0 %>%"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      </div>
    </div>

    <!-- Recent Errors & Alerts -->
    <% if @recent_errors.any? %>
      <div class="alerts-section">
        <div class="section-container">
          <div class="alerts-card error">
          <div class="alerts-header">
            <div class="alerts-header-content">
              <div class="alerts-icon-wrapper">
                <div class="alerts-icon-gradient gradient-error">
                  <svg class="alerts-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
              </div>
              <h3 class="alerts-title">Recent Issues & Alerts</h3>
            </div>
            <span class="alerts-badge error">
              <%= @recent_errors.count %> Issues
            </span>
          </div>
          <div class="p-6">
            <div class="space-y-3">
              <% @recent_errors.each do |job| %>
                <div class="group flex items-center justify-between p-4 bg-gradient-to-r  rounded-xl border border-red-200/50 hover:shadow-md transition-colors duration-200">
                  <div class="flex items-center gap-3">
                    <div class="p-2 bg-red-500 -600 rounded-lg shadow-lg">
                      <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="3">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </div>
                    <div>
                      <p class="font-semibold text-gray-900"><%= job.data_source.name %></p>
                      <p class="text-sm text-gray-600"><%= job.error_message.presence || 'Connection failed' %></p>
                    </div>
                  </div>
                  <div class="text-right">
                    <span class="text-sm text-gray-500"><%= time_ago_in_words(job.created_at) %> ago</span>
                    <div class="mt-1">
                      <button class="text-xs text-red-600 hover:text-red-800 font-semibold transition-colors">Retry</button>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>
        </div>
      </div>
    <% else %>
      <div class="alerts-section">
        <div class="section-container">
          <div class="alerts-card success">
          <div class="all-systems-operational">
            <div class="operational-icon-wrapper">
              <div class="operational-icon-gradient">
                <svg class="operational-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <h3 class="operational-title">All Systems Operational</h3>
            <p class="operational-text">No errors or issues detected in the selected time period.</p>
          </div>
        </div>
        </div>
      </div>
    <% end %>

    <!-- Advanced E-commerce Analytics -->
    <% if @ecommerce_insights.present? && @ecommerce_insights.any? %>
      <div class="business-intelligence-section">
        <div class="section-container">
          <div class="section-header">
            <h2 class="section-title">Advanced Business Intelligence</h2>
            <p class="section-subtitle">Deep insights from your e-commerce data</p>
          </div>

          <!-- Revenue & Growth Metrics -->
          <% if @ecommerce_insights[:total_revenue].present? %>
            <div class="bi-metrics-grid">
              <!-- Total Revenue -->
              <div class="bi-metric-card">
                <div class="bi-metric-inner">
                  <div class="bi-metric-icon-wrapper">
                    <div class="bi-metric-icon-gradient gradient-success">
                      <span class="bi-metric-emoji">💰</span>
                    </div>
                  </div>
                  <div class="bi-metric-content">
                    <h4 class="bi-metric-label">Total Revenue</h4>
                    <p class="bi-metric-value">$<%= number_with_delimiter(@ecommerce_insights[:total_revenue][:total_revenue], precision: 2) %></p>
                    <div class="bi-metric-detail">
                      AOV: $<%= @ecommerce_insights[:total_revenue][:average_order_value] %>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Customer Growth -->
              <% if @ecommerce_insights[:growth_metrics].present? %>
                <div class="bi-metric-card">
                  <div class="bi-metric-inner">
                    <div class="bi-metric-icon-wrapper">
                      <div class="bi-metric-icon-gradient gradient-primary">
                        <span class="bi-metric-emoji">📈</span>
                      </div>
                    </div>
                    <div class="bi-metric-content">
                      <h4 class="bi-metric-label">Customer Growth</h4>
                      <p class="bi-metric-value">
                        <% growth = @ecommerce_insights[:growth_metrics][:customer_growth_rate] %>
                        <%= growth > 0 ? '+' : '' %><%= growth %>%
                      </p>
                      <div class="bi-metric-detail">
                        Revenue: <%= @ecommerce_insights[:growth_metrics][:revenue_growth_rate] > 0 ? '+' : '' %><%= @ecommerce_insights[:growth_metrics][:revenue_growth_rate] %>%
                      </div>
                    </div>
                  </div>
                </div>
              <% end %>

              <!-- Market Expansion Score -->
              <% if @ecommerce_insights[:growth_opportunities].present? %>
                <div class="bi-metric-card">
                  <div class="bi-metric-inner">
                    <div class="bi-metric-icon-wrapper">
                      <div class="bi-metric-icon-gradient gradient-purple">
                        <span class="bi-metric-emoji">🌍</span>
                      </div>
                    </div>
                    <div class="bi-metric-content">
                      <h4 class="bi-metric-label">Expansion Score</h4>
                      <p class="bi-metric-value"><%= @ecommerce_insights[:growth_opportunities][:market_expansion_score] %></p>
                      <div class="bi-metric-detail">
                        Markets: <%= @ecommerce_insights[:growth_opportunities][:geographic_opportunities][:top_markets].count %>
                      </div>
                    </div>
                  </div>
                </div>
              <% end %>

              <!-- Risk Assessment -->
              <% if @ecommerce_insights[:risk_indicators].present? %>
                <div class="bi-metric-card">
                  <div class="bi-metric-inner">
                    <div class="bi-metric-icon-wrapper">
                      <div class="bi-metric-icon-gradient gradient-orange">
                        <span class="bi-metric-emoji">⚠️</span>
                      </div>
                    </div>
                    <div class="bi-metric-content">
                      <h4 class="bi-metric-label">Risk Level</h4>
                      <p class="bi-metric-value capitalize"><%= @ecommerce_insights[:risk_indicators][:risk_score] %></p>
                      <div class="bi-metric-detail">
                        Churn: <%= @ecommerce_insights[:risk_indicators][:customer_churn_risk] %>
                      </div>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          <% end %>

          <!-- Detailed Analytics Sections -->
          <div class="bi-detailed-grid">
            <!-- Top Customer Segments -->
            <% if @ecommerce_insights[:top_performing_segments].present? %>
            <div class="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
              <!-- removed gradient background --><div class="hidden from-indigo-500/5 to-purple-500/5"></div>
              <div class="relative">
                <div class="px-6 py-4 border-b border-gray-200 ">
                  <div class="flex items-center space-x-3">
                    <div class="p-2 bg-indigo-500 to-purple-600 rounded-lg shadow-lg">
                      <span class="text-lg">👥</span>
                    </div>
                    <h3 class="text-lg font-bold text-gray-900">Customer Segments</h3>
                  </div>
                </div>
                <div class="p-6 space-y-3">
                  <% @ecommerce_insights[:top_performing_segments].each_with_index do |(segment, data), index| %>
                    <div class="group flex items-center justify-between p-4 bg-gradient-to-r  rounded-xl border border-indigo-200/50 hover:shadow-md transition-colors duration-200">
                      <div class="flex items-center gap-3">
                        <div class="p-2 bg-indigo-500 to-purple-600 rounded-lg shadow-lg">
                          <span class="text-white font-bold text-sm">#<%= index + 1 %></span>
                        </div>
                        <div>
                          <span class="font-semibold text-gray-900"><%= segment %></span>
                          <div class="text-sm text-gray-600">
                            <%= data[:order_count] %> orders • <%= data[:unique_customers] %> customers
                          </div>
                        </div>
                      </div>
                      <div class="text-right">
                        <span class="text-lg font-bold text-gray-900">$<%= number_with_delimiter(data[:total_revenue], precision: 0) %></span>
                        <div class="text-xs text-gray-500">$<%= data[:average_order_value] %> AOV</div>
                      </div>
                    </div>
                  <% end %>
                </div>
              </div>
            </div>
          <% end %>

          <!-- Growth Opportunities -->
          <% if @ecommerce_insights[:growth_opportunities].present? %>
            <div class="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
              <!-- removed gradient background --><div class="hidden from-green-500/5 to-emerald-500/5"></div>
              <div class="relative">
                <div class="px-6 py-4 border-b border-gray-200 ">
                  <div class="flex items-center space-x-3">
                    <div class="p-2 bg-green-500 -600 rounded-lg shadow-lg">
                      <span class="text-lg">🚀</span>
                    </div>
                    <h3 class="text-lg font-bold text-gray-900">Growth Opportunities</h3>
                  </div>
                </div>
                <div class="p-6">
                  <!-- Geographic Opportunities -->
                  <% if @ecommerce_insights[:growth_opportunities][:geographic_opportunities].present? %>
                    <div class="mb-6">
                      <h4 class="font-semibold text-gray-800 mb-3 flex items-center gap-2">
                        🌍 Top Markets
                      </h4>
                      <div class="space-y-2">
                        <% @ecommerce_insights[:growth_opportunities][:geographic_opportunities][:top_markets].each do |country, orders| %>
                          <div class="flex items-center justify-between p-3 bg-gradient-to-r  rounded-xl border border-green-200/50">
                            <span class="font-medium text-gray-900"><%= country %></span>
                            <span class="text-green-600 font-semibold text-sm"><%= orders %> orders</span>
                          </div>
                        <% end %>
                      </div>
                    </div>
                  <% end %>

                  <!-- Seasonal Insights -->
                  <% if @ecommerce_insights[:growth_opportunities][:seasonal_insights].present? %>
                    <div class="mb-6">
                      <h4 class="font-semibold text-gray-800 mb-3 flex items-center gap-2">
                        📅 Peak Months
                      </h4>
                      <div class="flex flex-wrap gap-2">
                        <% @ecommerce_insights[:growth_opportunities][:seasonal_insights][:peak_months].each do |month| %>
                          <span class="px-3 py-1 bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 rounded-lg text-xs font-semibold border border-green-200/50">
                            <%= Date::MONTHNAMES[month] %>
                          </span>
                        <% end %>
                      </div>
                      <div class="mt-3 text-sm text-gray-600">
                        Seasonality Score: <span class="font-semibold"><%= @ecommerce_insights[:growth_opportunities][:seasonal_insights][:seasonality_score] %>%</span>
                      </div>
                    </div>
                  <% end %>

                  <!-- Cross-sell Opportunities -->
                  <% if @ecommerce_insights[:growth_opportunities][:cross_sell_opportunities].present? %>
                    <div>
                      <h4 class="font-semibold text-gray-800 mb-3 flex items-center gap-2">
                        🔗 Cross-sell Opportunities
                      </h4>
                      <div class="p-3 bg-gradient-to-r  rounded-xl border border-green-200/50">
                        <span class="text-sm text-gray-600">
                          <span class="font-semibold text-green-700"><%= @ecommerce_insights[:growth_opportunities][:cross_sell_opportunities].count %></span> product combinations identified
                        </span>
                      </div>
                    </div>
                  <% end %>
                </div>
              </div>
            </div>
          <% end %>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Predictive Analytics CTA -->
    <div class="cta-section-wrapper">
      <div class="section-container">
        <div class="cta-card predictions">
          <div class="cta-background"></div>
          <div class="cta-content">
            <div class="cta-main">
              <div class="cta-icon-wrapper">
                <span class="cta-icon">🔮</span>
              </div>
              <h3 class="cta-title">Want Advanced Predictions?</h3>
              <p class="cta-description">
                Explore our AI-powered Predictive Analytics Engine for advanced forecasting, trend analysis, and scenario planning.
              </p>
              <div class="cta-features-grid">
                <div class="cta-feature">
                  <span class="cta-feature-icon">📈</span>
                  <span class="cta-feature-text">Demand Forecasting</span>
                </div>
                <div class="cta-feature">
                  <span class="cta-feature-icon">🎯</span>
                  <span class="cta-feature-text">Behavior Analysis</span>
                </div>
                <div class="cta-feature">
                  <span class="cta-feature-icon">💰</span>
                  <span class="cta-feature-text">Revenue Predictions</span>
                </div>
                <div class="cta-feature">
                  <span class="cta-feature-icon">🎲</span>
                  <span class="cta-feature-text">Scenario Planning</span>
                </div>
              </div>
              <%= link_to ai_predictions_path, class: "cta-button" do %>
                <span class="cta-button-icon">🚀</span>
                <span class="cta-button-text">Launch Predictive Analytics</span>
              <% end %>
            </div>
            <div class="cta-visual">
              <svg class="cta-graph" viewBox="0 0 200 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                <defs>
                  <linearGradient id="ctaGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:rgba(255,255,255,0.3)"/>
                    <stop offset="100%" style="stop-color:rgba(255,255,255,0.1)"/>
                  </linearGradient>
                </defs>
                <path d="M10 80 Q 50 20 90 40 T 170 30" stroke="url(#ctaGradient)" stroke-width="2" stroke-dasharray="5,5"/>
                <path d="M10 80 Q 50 60 90 50 T 170 45" stroke="rgba(255,255,255,0.8)" stroke-width="3"/>
                <circle cx="170" cy="45" r="4" fill="rgba(255,255,255,0.9)"/>
                <path d="M20 70 L 30 60 L 40 65 L 50 55 L 60 50 L 70 52 L 80 48 L 90 45" stroke="rgba(255,255,255,0.5)" stroke-width="1"/>
                <circle cx="90" cy="45" r="3" fill="rgba(255,255,255,0.7)"/>
                <circle cx="50" cy="55" r="2" fill="rgba(255,255,255,0.6)"/>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</div>