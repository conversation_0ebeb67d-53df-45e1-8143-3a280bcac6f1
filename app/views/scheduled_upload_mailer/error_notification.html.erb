<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scheduled Upload Error</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        line-height: 1.6;
        color: #374151;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
      }
      .header {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        color: white;
        padding: 30px;
        border-radius: 8px 8px 0 0;
        text-align: center;
      }
      .content {
        background: #ffffff;
        padding: 30px;
        border: 1px solid #e5e7eb;
        border-top: none;
      }
      .error-badge {
        display: inline-block;
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 14px;
        color: white;
        background-color: #ef4444;
        margin: 10px 0;
      }
      .info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin: 20px 0;
      }
      .info-item {
        background: #f9fafb;
        padding: 15px;
        border-radius: 6px;
        border-left: 4px solid #ef4444;
      }
      .info-label {
        font-weight: 600;
        color: #374151;
        font-size: 14px;
        margin-bottom: 5px;
      }
      .info-value {
        color: #6b7280;
        font-size: 16px;
      }
      .error-section {
        margin: 25px 0;
        padding: 20px;
        background: #fef2f2;
        border: 1px solid #fecaca;
        border-radius: 6px;
      }
      .error-message {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        background: #ffffff;
        padding: 15px;
        border-radius: 4px;
        border: 1px solid #fecaca;
        color: #dc2626;
        font-size: 14px;
        white-space: pre-wrap;
        word-break: break-word;
      }
      .footer {
        background: #f9fafb;
        padding: 20px;
        border-radius: 0 0 8px 8px;
        text-align: center;
        font-size: 14px;
        color: #6b7280;
        border: 1px solid #e5e7eb;
        border-top: none;
      }
      .button {
        display: inline-block;
        padding: 12px 24px;
        background: #ef4444;
        color: white;
        text-decoration: none;
        border-radius: 6px;
        font-weight: 600;
        margin: 15px 0;
      }
      .troubleshooting {
        background: #f0f9ff;
        border: 1px solid #bae6fd;
        border-radius: 6px;
        padding: 20px;
        margin: 25px 0;
      }
      .troubleshooting h3 {
        color: #0369a1;
        margin-top: 0;
      }
      .troubleshooting ul {
        margin: 10px 0;
        padding-left: 20px;
      }
      .troubleshooting li {
        margin: 8px 0;
        color: #374151;
      }
      @media (max-width: 600px) {
        .info-grid {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </head>
  <body>
    <div class="header">
      <h1>🚨 Scheduled Upload Error</h1>
      <p>Data Refinery Platform</p>
    </div>

    <div class="content">
      <h2><%= @scheduled_upload.name %></h2>
      
      <div class="error-badge">
        ⚠️ Error Occurred
      </div>

      <div class="info-grid">
        <div class="info-item">
          <div class="info-label">Data Source</div>
          <div class="info-value"><%= @data_source.name %></div>
        </div>
        
        <div class="info-item">
          <div class="info-label">Error Time</div>
          <div class="info-value"><%= Time.current.strftime('%Y-%m-%d %H:%M:%S') %></div>
        </div>
        
        <div class="info-item">
          <div class="info-label">Schedule</div>
          <div class="info-value"><%= @scheduled_upload.frequency.humanize %></div>
        </div>
        
        <div class="info-item">
          <div class="info-label">Next Attempt</div>
          <div class="info-value">
            <% if @scheduled_upload.active? %>
              <%= @scheduled_upload.next_run_at.strftime('%Y-%m-%d %H:%M:%S') %>
            <% else %>
              Disabled
            <% end %>
          </div>
        </div>
      </div>

      <div class="error-section">
        <h3>🔍 Error Details</h3>
        <div class="error-message"><%= @error_message %></div>
      </div>

      <div class="troubleshooting">
        <h3>🛠️ Troubleshooting Steps</h3>
        <ul>
          <li><strong>Check Data Source:</strong> Verify that the data source is accessible and contains files to process.</li>
          <li><strong>Review Permissions:</strong> Ensure the system has proper access permissions to the data source.</li>
          <li><strong>Validate Configuration:</strong> Check that the scheduled upload configuration is correct.</li>
          <li><strong>Monitor Resources:</strong> Verify that system resources are available for processing.</li>
          <li><strong>Check Network:</strong> Ensure network connectivity to external data sources.</li>
        </ul>
        
        <p><strong>If the issue persists:</strong></p>
        <ul>
          <li>Review the full error logs in the system dashboard</li>
          <li>Contact your system administrator</li>
          <li>Consider temporarily disabling the scheduled upload if necessary</li>
        </ul>
      </div>

      <div style="text-align: center; margin: 30px 0;">
        <a href="<%= Rails.application.routes.url_helpers.data_source_url(@data_source, host: Rails.application.config.action_mailer.default_url_options[:host]) %>" class="button">
          View Data Source
        </a>
      </div>
    </div>

    <div class="footer">
      <p>This is an automated error notification from Data Refinery Platform.</p>
      <p>Please address this issue to ensure your scheduled uploads continue working properly.</p>
    </div>
  </body>
</html>