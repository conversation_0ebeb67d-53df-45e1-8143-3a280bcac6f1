<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scheduled Upload Summary</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        line-height: 1.6;
        color: #374151;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
      }
      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 8px 8px 0 0;
        text-align: center;
      }
      .content {
        background: #ffffff;
        padding: 30px;
        border: 1px solid #e5e7eb;
        border-top: none;
      }
      .status-badge {
        display: inline-block;
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 14px;
        color: white;
        margin: 10px 0;
      }
      .info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin: 20px 0;
      }
      .info-item {
        background: #f9fafb;
        padding: 15px;
        border-radius: 6px;
        border-left: 4px solid #667eea;
      }
      .info-label {
        font-weight: 600;
        color: #374151;
        font-size: 14px;
        margin-bottom: 5px;
      }
      .info-value {
        color: #6b7280;
        font-size: 16px;
      }
      .files-section {
        margin: 25px 0;
        padding: 20px;
        background: #f8fafc;
        border-radius: 6px;
      }
      .file-item {
        padding: 10px;
        border-bottom: 1px solid #e5e7eb;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .file-item:last-child {
        border-bottom: none;
      }
      .error-section {
        margin: 25px 0;
        padding: 20px;
        background: #fef2f2;
        border: 1px solid #fecaca;
        border-radius: 6px;
      }
      .error-item {
        padding: 8px 0;
        color: #dc2626;
        font-size: 14px;
      }
      .footer {
        background: #f9fafb;
        padding: 20px;
        border-radius: 0 0 8px 8px;
        text-align: center;
        font-size: 14px;
        color: #6b7280;
        border: 1px solid #e5e7eb;
        border-top: none;
      }
      .button {
        display: inline-block;
        padding: 12px 24px;
        background: #667eea;
        color: white;
        text-decoration: none;
        border-radius: 6px;
        font-weight: 600;
        margin: 15px 0;
      }
      @media (max-width: 600px) {
        .info-grid {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </head>
  <body>
    <div class="header">
      <h1>📊 Scheduled Upload Summary</h1>
      <p>Data Refinery Platform</p>
    </div>

    <div class="content">
      <h2><%= @scheduled_upload.name %></h2>
      
      <div class="status-badge" style="background-color: <%= @status_color %>">
        <%= @status_text %>
      </div>

      <div class="info-grid">
        <div class="info-item">
          <div class="info-label">Data Source</div>
          <div class="info-value"><%= @data_source.name %></div>
        </div>
        
        <div class="info-item">
          <div class="info-label">Execution Time</div>
          <div class="info-value"><%= @log_entry.started_at.strftime('%Y-%m-%d %H:%M:%S') %></div>
        </div>
        
        <div class="info-item">
          <div class="info-label">Duration</div>
          <div class="info-value"><%= @log_entry.duration_in_words %></div>
        </div>
        
        <div class="info-item">
          <div class="info-label">Files Processed</div>
          <div class="info-value"><%= @log_entry.files_processed || 0 %></div>
        </div>
      </div>

      <% if @log_entry.files_failed && @log_entry.files_failed > 0 %>
        <div class="info-item" style="border-left-color: #ef4444; background: #fef2f2;">
          <div class="info-label">Files Failed</div>
          <div class="info-value" style="color: #dc2626;"><%= @log_entry.files_failed %></div>
        </div>
      <% end %>

      <% if @log_entry.details && @log_entry.details['processed_files'] && @log_entry.details['processed_files'].any? %>
        <div class="files-section">
          <h3>📁 Processed Files</h3>
          <% @log_entry.details['processed_files'].each do |file| %>
            <div class="file-item">
              <span><%= file['file_name'] %></span>
              <span style="color: #6b7280; font-size: 14px;"><%= number_to_human_size(file['file_size']) if file['file_size'] %></span>
            </div>
          <% end %>
        </div>
      <% end %>

      <% if @log_entry.details && @log_entry.details['errors'] && @log_entry.details['errors'].any? %>
        <div class="error-section">
          <h3>⚠️ Errors</h3>
          <% @log_entry.details['errors'].each do |error| %>
            <div class="error-item"><%= error %></div>
          <% end %>
        </div>
      <% end %>

      <% if @log_entry.details && @log_entry.details['message'] %>
        <div style="margin: 20px 0; padding: 15px; background: #f0f9ff; border-radius: 6px; border-left: 4px solid #0ea5e9;">
          <strong>Message:</strong> <%= @log_entry.details['message'] %>
        </div>
      <% end %>

      <div style="text-align: center; margin: 30px 0;">
        <a href="<%= Rails.application.routes.url_helpers.data_source_url(@data_source, host: Rails.application.config.action_mailer.default_url_options[:host]) %>" class="button">
          View Data Source
        </a>
      </div>
    </div>

    <div class="footer">
      <p>This is an automated notification from Data Refinery Platform.</p>
      <p>If you no longer wish to receive these notifications, please update your scheduled upload settings.</p>
    </div>
  </body>
</html>