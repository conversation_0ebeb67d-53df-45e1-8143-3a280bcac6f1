<!-- Enhanced Upcube-inspired Top Navigation -->
<div class="sticky top-0 z-40 bg-white border-b border-gray-200" data-controller="navbar">
  <div class="px-4 sm:px-6 lg:px-8">
    <div class="flex h-16 items-center justify-between">
      <!-- Mobile menu button -->
      <button type="button" class="lg:hidden -m-2.5 p-2.5 text-gray-700" data-action="click->mobile-menu#toggle">
        <span class="sr-only">Open sidebar</span>
        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
        </svg>
      </button>

      <!-- Enhanced Search bar -->
      <div class="flex-1 flex justify-center lg:justify-start lg:ml-6">
        <div class="max-w-lg w-full lg:max-w-md">
          <div class="relative" data-controller="search">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
              </svg>
            </div>
            <input class="block w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors" 
                   placeholder="Search data sources, users, analytics..." 
                   type="search"
                   data-action="input->search#performSearch focus->search#showResults blur->search#hideResults"
                   data-search-target="input">
            
            <!-- Search Results Dropdown -->
            <div class="hidden absolute top-full left-0 right-0 mt-1 bg-white rounded-lg border border-gray-200 shadow-lg max-h-96 overflow-y-auto z-50" data-search-target="results">
              <div class="p-2">
                <div class="text-xs font-medium text-gray-500 uppercase tracking-wider px-3 py-2">Quick Actions</div>
                <a href="/data_sources/new" class="flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg">
                  <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-indigo-100">
                    <svg class="h-4 w-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4" />
                    </svg>
                  </div>
                  <span>Add Data Source</span>
                </a>
                <a href="/users/new" class="flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg">
                  <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-green-100">
                    <svg class="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M18 7.5v3m0 0v3m0-3h3m-3 0h-3m-1.5-6.75H12v2.25m0 0h3.75m-3.75 0h-.375a1.125 1.125 0 00-1.125 1.125v9.75c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V10.125a1.125 1.125 0 00-1.125-1.125H18.75" />
                    </svg>
                  </div>
                  <span>Invite User</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right section -->
      <div class="flex items-center gap-x-2 lg:gap-x-4">
        
        <!-- Quick Actions -->
        <div class="hidden lg:flex items-center gap-2">
          <button class="inline-flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  data-action="click->navbar#showShortcuts">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
              <path stroke-linecap="round" stroke-linejoin="round" d="M13.19 8.688a4.5 4.5 0 011.242 7.244l-4.5 4.5a4.5 4.5 0 01-6.364-6.364l1.757-1.757m13.35-.622l1.757-1.757a4.5 4.5 0 00-6.364-6.364l-4.5 4.5a4.5 4.5 0 001.242 7.244" />
            </svg>
            Quick Actions
          </button>
        </div>

        <!-- Enhanced Notifications -->
        <div class="relative" data-controller="notifications">
          <button type="button" 
                  class="relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 rounded-lg transition-colors"
                  data-action="click->notifications#toggle"
                  data-notifications-target="button">
            <span class="sr-only">View notifications</span>
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0" />
            </svg>
            <!-- Notification Badge -->
            <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white" data-notifications-target="badge"></span>
          </button>

          <!-- Comprehensive Notifications Dropdown -->
          <div class="hidden absolute right-0 z-50 mt-2 w-96 bg-white rounded-lg border border-gray-200 shadow-lg" data-notifications-target="dropdown">
            <!-- Header -->
            <div class="px-4 py-3 border-b border-gray-200">
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">Notifications</h3>
                <div class="flex items-center gap-2">
                  <button class="text-sm text-indigo-600 hover:text-indigo-800 font-medium"
                          data-action="click->notifications#markAllRead">
                    Mark all read
                  </button>
                  <button class="p-1 text-gray-400 hover:text-gray-600 rounded"
                          data-action="click->notifications#showSettings">
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M10.343 3.94c.09-.542.56-.94 1.11-.94h1.093c.55 0 1.02.398 1.11.94l.149.894c.07.424.384.764.78.93.398.164.855.142 1.205-.108l.737-.527a1.125 1.125 0 011.45.12l.773.774c.39.389.44 1.002.12 1.45l-.527.737c-.25.35-.272.806-.107 1.204.165.397.505.71.93.78l.893.15c.543.09.94.56.94 1.109v1.094c0 .55-.397 1.02-.94 1.11l-.893.149c-.425.07-.765.383-.93.78-.165.398-.143.854.107 1.204l.527.738c.32.447.269 1.06-.12 1.45l-.774.773a1.125 1.125 0 01-1.449.12l-.738-.527c-.35-.25-.806-.272-1.203-.107-.397.165-.71.505-.781.929l-.149.894c-.09.542-.56.94-1.11.94h-1.094c-.55 0-1.019-.398-1.11-.94l-.148-.894c-.071-.424-.384-.764-.781-.93-.398-.164-.854-.142-1.204.108l-.738.527c-.447.32-1.06.269-1.45-.12l-.773-.774a1.125 1.125 0 01-.12-1.45l.527-.737c.25-.35.273-.806.108-1.204-.165-.397-.505-.71-.93-.78l-.894-.15c-.542-.09-.94-.56-.94-1.109v-1.094c0-.55.398-1.02.94-1.11l.894-.149c.424-.07.765-.383.93-.78.165-.398.143-.854-.107-1.204l-.527-.738a1.125 1.125 0 01.12-1.45l.773-.773a1.125 1.125 0 011.45-.12l.737.527c.35.25.807.272 1.204.107.397-.165.71-.505.78-.929l.15-.894z" />
                      <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>

            <!-- Notification Tabs -->
            <div class="border-b border-gray-200">
              <nav class="flex -mb-px">
                <button class="py-2 px-4 border-b-2 border-indigo-500 text-sm font-medium text-indigo-600" data-action="click->notifications#showTab" data-tab="all">
                  All (3)
                </button>
                <button class="py-2 px-4 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700" data-action="click->notifications#showTab" data-tab="unread">
                  Unread (2)
                </button>
                <button class="py-2 px-4 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700" data-action="click->notifications#showTab" data-tab="mentions">
                  Mentions
                </button>
              </nav>
            </div>

            <!-- Notification List -->
            <div class="max-h-96 overflow-y-auto" data-notifications-target="list">
              <!-- Data Quality Alert -->
              <div class="px-4 py-3 hover:bg-gray-50 border-b border-gray-100 transition-colors">
                <div class="flex items-start gap-3">
                  <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-red-100 mt-1">
                    <svg class="h-4 w-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                    </svg>
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between">
                      <p class="text-sm font-medium text-gray-900">Data Quality Alert</p>
                      <span class="text-xs text-gray-500">2 min ago</span>
                    </div>
                    <p class="text-sm text-gray-600 mt-1">Shopify data source has 15% missing records in the last sync</p>
                    <div class="flex items-center gap-2 mt-2">
                      <button class="text-xs text-indigo-600 hover:text-indigo-800 font-medium">View Details</button>
                      <button class="text-xs text-gray-500 hover:text-gray-700">Dismiss</button>
                    </div>
                  </div>
                  <div class="h-2 w-2 bg-indigo-500 rounded-full mt-2"></div>
                </div>
              </div>

              <!-- New User Joined -->
              <div class="px-4 py-3 hover:bg-gray-50 border-b border-gray-100 transition-colors">
                <div class="flex items-start gap-3">
                  <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-green-100 mt-1">
                    <svg class="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M18 7.5v3m0 0v3m0-3h3m-3 0h-3m-1.5-6.75H12v2.25m0 0h3.75m-3.75 0h-.375a1.125 1.125 0 00-1.125 1.125v9.75c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V10.125a1.125 1.125 0 00-1.125-1.125H18.75" />
                    </svg>
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between">
                      <p class="text-sm font-medium text-gray-900">New team member</p>
                      <span class="text-xs text-gray-500">1 hour ago</span>
                    </div>
                    <p class="text-sm text-gray-600 mt-1">Sarah Chen joined your organization as a Data Analyst</p>
                    <div class="flex items-center gap-2 mt-2">
                      <button class="text-xs text-indigo-600 hover:text-indigo-800 font-medium">Welcome User</button>
                      <button class="text-xs text-gray-500 hover:text-gray-700">Dismiss</button>
                    </div>
                  </div>
                  <div class="h-2 w-2 bg-indigo-500 rounded-full mt-2"></div>
                </div>
              </div>

              <!-- Sync Completed -->
              <div class="px-4 py-3 hover:bg-gray-50 transition-colors">
                <div class="flex items-start gap-3">
                  <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100 mt-1">
                    <svg class="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 01-1.043 3.296 3.745 3.745 0 01-3.296 1.043A3.745 3.745 0 0112 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 01-3.296-1.043 3.745 3.745 0 01-1.043-3.296A3.745 3.745 0 013 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 011.043-3.296 3.746 3.746 0 013.296-1.043A3.746 3.746 0 0112 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 013.296 1.043 3.746 3.746 0 011.043 3.296A3.745 3.745 0 0121 12z" />
                    </svg>
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between">
                      <p class="text-sm font-medium text-gray-900">Sync completed</p>
                      <span class="text-xs text-gray-500">3 hours ago</span>
                    </div>
                    <p class="text-sm text-gray-600 mt-1">QuickBooks sync completed successfully. 1,234 records processed</p>
                    <div class="flex items-center gap-2 mt-2">
                      <button class="text-xs text-indigo-600 hover:text-indigo-800 font-medium">View Report</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Footer -->
            <div class="px-4 py-3 border-t border-gray-200 bg-gray-50">
              <a href="/notifications" class="block text-center text-sm font-medium text-indigo-600 hover:text-indigo-800">
                View all notifications
              </a>
            </div>
          </div>
        </div>

        <!-- Enhanced Profile dropdown -->
        <div class="relative" data-controller="user-menu">
          <button type="button" 
                  class="flex items-center gap-x-2 text-sm leading-6 text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 rounded-lg p-1" 
                  data-action="click->user-menu#toggle"
                  data-user-menu-target="button">
            <img class="h-8 w-8 rounded-full bg-gray-50 ring-2 ring-white" 
                 src="https://ui-avatars.com/api/?name=<%= current_user.first_name %>+<%= current_user.last_name %>&background=6366f1&color=ffffff" 
                 alt="<%= current_user.first_name %>">
            <span class="hidden lg:flex lg:items-center">
              <span class="ml-1 text-sm font-medium leading-6 text-gray-900"><%= current_user.first_name %></span>
              <svg class="ml-1 h-4 w-4 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
              </svg>
            </span>
          </button>

          <!-- Comprehensive User Dropdown -->
          <div class="hidden absolute right-0 z-50 mt-2 w-80 bg-white rounded-lg border border-gray-200 shadow-lg" data-user-menu-target="dropdown">
            <!-- User Info Header -->
            <div class="px-4 py-3 border-b border-gray-200">
              <div class="flex items-center gap-3">
                <img class="h-12 w-12 rounded-full bg-gray-50" 
                     src="https://ui-avatars.com/api/?name=<%= current_user.first_name %>+<%= current_user.last_name %>&background=6366f1&color=ffffff" 
                     alt="<%= current_user.first_name %>">
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-gray-900 truncate"><%= current_user.full_name %></p>
                  <p class="text-sm text-gray-500 truncate"><%= current_user.email %></p>
                  <div class="flex items-center gap-2 mt-1">
                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-indigo-100 text-indigo-800">
                      <%= current_user.role.humanize %>
                    </span>
                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                      <div class="h-1.5 w-1.5 bg-green-500 rounded-full mr-1"></div>
                      Online
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Menu Items -->
            <div class="py-2">
              <!-- Profile Section -->
              <div class="px-2">
                <div class="text-xs font-medium text-gray-500 uppercase tracking-wider px-3 py-2">Account</div>
                <%= link_to edit_user_registration_path, class: "flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg mx-1" do %>
                  <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  <span>Profile Settings</span>
                <% end %>
                
                <a href="/account/preferences" class="flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg mx-1">
                  <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M10.343 3.94c.09-.542.56-.94 1.11-.94h1.093c.55 0 1.02.398 1.11.94l.149.894c.07.424.384.764.78.93.398.164.855.142 1.205-.108l.737-.527a1.125 1.125 0 011.45.12l.773.774c.39.389.44 1.002.12 1.45l-.527.737c-.25.35-.272.806-.107 1.204.165.397.505.71.93.78l.893.15c.543.09.94.56.94 1.109v1.094c0 .55-.397 1.02-.94 1.11l-.893.149c-.425.07-.765.383-.93.78-.165.398-.143.854.107 1.204l.527.738c.32.447.269 1.06-.12 1.45l-.774.773a1.125 1.125 0 01-1.449.12l-.738-.527c-.35-.25-.806-.272-1.203-.107-.397.165-.71.505-.781.929l-.149.894c-.09.542-.56.94-1.11.94h-1.094c-.55 0-1.019-.398-1.11-.94l-.148-.894c-.071-.424-.384-.764-.781-.93-.398-.164-.854-.142-1.204.108l-.738.527c-.447.32-1.06.269-1.45-.12l-.773-.774a1.125 1.125 0 01-.12-1.45l.527-.737c.25-.35.273-.806.108-1.204-.165-.397-.505-.71-.93-.78l-.894-.15c-.542-.09-.94-.56-.94-1.109v-1.094c0-.55.398-1.02.94-1.11l.894-.149c.424-.07.765-.383.93-.78.165-.398.143-.854-.107-1.204l-.527-.738a1.125 1.125 0 01.12-1.45l.773-.773a1.125 1.125 0 011.45-.12l.737.527c.35.25.807.272 1.204.107.397-.165.71-.505.78-.929l.15-.894z" />
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  <span>Preferences</span>
                </a>

                <a href="/account/security" class="flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg mx-1">
                  <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
                  </svg>
                  <span>Security</span>
                </a>
              </div>

              <div class="border-t border-gray-100 my-2"></div>

              <!-- Quick Actions -->
              <div class="px-2">
                <div class="text-xs font-medium text-gray-500 uppercase tracking-wider px-3 py-2">Quick Actions</div>
                <a href="/dashboard" class="flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg mx-1">
                  <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
                  </svg>
                  <span>Dashboard</span>
                </a>

                <a href="/analytics" class="flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg mx-1">
                  <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z" />
                  </svg>
                  <span>Analytics</span>
                </a>

                <% if policy(User).index? %>
                  <a href="/users" class="flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg mx-1">
                    <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M18 18.72a9.094 9.094 0 003.741-.479 3 3 0 00-4.682-2.72m.94 3.198l.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0112 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 016 18.719m12 0a5.971 5.971 0 00-.941-3.197m0 0A5.995 5.995 0 0012 12.75a5.995 5.995 0 00-5.058 2.772m0 0a3 3 0 00-4.681 2.72 8.986 8.986 0 003.74.477m.94-3.197a5.971 5.971 0 00-.94 3.197M15 6.75a3 3 0 11-6 0 3 3 0 016 0zm6 3a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0zm-13.5 0a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z" />
                    </svg>
                    <span>Team Management</span>
                  </a>
                <% end %>
              </div>

              <div class="border-t border-gray-100 my-2"></div>

              <!-- Support & Help -->
              <div class="px-2">
                <div class="text-xs font-medium text-gray-500 uppercase tracking-wider px-3 py-2">Support</div>
                <a href="/help" class="flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg mx-1">
                  <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9 5.25h.008v.008H12v-.008z" />
                  </svg>
                  <span>Help Center</span>
                </a>

                <a href="/support" class="flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg mx-1">
                  <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
                  </svg>
                  <span>Contact Support</span>
                </a>

                <button class="flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg mx-1 w-full text-left"
                        data-action="click->user-menu#showKeyboardShortcuts">
                  <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                  </svg>
                  <span>Keyboard Shortcuts</span>
                </button>
              </div>

              <div class="border-t border-gray-100 my-2"></div>

              <!-- Sign Out -->
              <%= link_to destroy_user_session_path, method: :delete, class: "flex items-center gap-3 px-5 py-2 text-sm text-red-700 hover:bg-red-50 mx-1 rounded-lg" do %>
                <svg class="h-4 w-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15M12 9l-3 3m0 0l3 3m-3-3h12.75" />
                </svg>
                <span>Sign out</span>
              <% end %>
            </div>

            <!-- Footer -->
            <div class="px-4 py-3 border-t border-gray-200 bg-gray-50">
              <div class="flex items-center justify-between text-xs text-gray-500">
                <span>DataReflow v2.1.0</span>
                <span>Last login: <%= current_user.current_sign_in_at&.strftime("%b %d, %Y") || "Today" %></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

