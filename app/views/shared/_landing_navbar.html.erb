<!-- Premium Professional Navigation -->
<nav class="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm border-b border-gray-200 transition-all duration-300" data-controller="landing-navbar">
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <div class="flex h-16 items-center justify-between">
      <!-- Logo -->
      <%= link_to root_path, class: "flex items-center" do %>
        <div class="flex h-9 w-9 items-center justify-center rounded-lg bg-gradient-to-br from-indigo-600 to-indigo-700 shadow-sm">
          <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
          </svg>
        </div>
        <span class="ml-3 text-xl font-semibold text-gray-900">DataReflow</span>
      <% end %>

      <!-- Desktop Navigation -->
      <div class="hidden md:flex md:items-center md:space-x-8">
        <a href="#features" class="text-sm font-medium text-gray-700 hover:text-indigo-600 transition-colors duration-200">
          Features
        </a>
        <a href="#integrations" class="text-sm font-medium text-gray-700 hover:text-indigo-600 transition-colors duration-200">
          Integrations
        </a>
        <a href="#testimonials" class="text-sm font-medium text-gray-700 hover:text-indigo-600 transition-colors duration-200">
          Customers
        </a>
        <a href="#pricing" class="text-sm font-medium text-gray-700 hover:text-indigo-600 transition-colors duration-200">
          Pricing
        </a>
        <a href="/docs" class="text-sm font-medium text-gray-700 hover:text-indigo-600 transition-colors duration-200">
          Documentation
        </a>
      </div>

      <!-- Right Side Actions -->
      <div class="flex items-center space-x-4">
        <% if user_signed_in? %>
          <%= link_to dashboard_path, class: "hidden md:inline-flex text-sm font-medium text-gray-700 hover:text-indigo-600 transition-colors duration-200" do %>
            Dashboard
          <% end %>
          
          <div class="hidden md:block h-5 w-px bg-gray-300"></div>
          
          <%= link_to destroy_user_session_path, method: :delete, 
              class: "hidden md:inline-flex items-center rounded-lg bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200 transition-all duration-200" do %>
            Sign out
          <% end %>
        <% else %>
          <%= link_to new_user_session_path, 
              class: "hidden md:inline-flex text-sm font-medium text-gray-700 hover:text-indigo-600 transition-colors duration-200" do %>
            Sign in
          <% end %>
          
          <%= link_to new_user_registration_path, 
              class: "hidden md:inline-flex items-center rounded-lg bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700 transition-all duration-200" do %>
            Start free trial
          <% end %>
        <% end %>

        <!-- Mobile menu button -->
        <button type="button" 
                class="md:hidden inline-flex items-center justify-center rounded-lg p-2 text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-all duration-200"
                data-action="click->landing-navbar#toggleMobile">
          <span class="sr-only">Open main menu</span>
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" data-landing-navbar-target="hamburgerIcon">
            <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
          </svg>
          <svg class="hidden h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" data-landing-navbar-target="closeIcon">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- Mobile menu panel -->
  <div class="md:hidden hidden" data-landing-navbar-target="mobileMenu">
    <div class="space-y-1 px-2 pb-3 pt-2">
      <a href="#features" class="block rounded-lg px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-all duration-200" data-action="click->landing-navbar#closeMobile">
        Features
      </a>
      <a href="#integrations" class="block rounded-lg px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-all duration-200" data-action="click->landing-navbar#closeMobile">
        Integrations
      </a>
      <a href="#testimonials" class="block rounded-lg px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-all duration-200" data-action="click->landing-navbar#closeMobile">
        Customers
      </a>
      <a href="#pricing" class="block rounded-lg px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-all duration-200" data-action="click->landing-navbar#closeMobile">
        Pricing
      </a>
      <a href="/docs" class="block rounded-lg px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-all duration-200" data-action="click->landing-navbar#closeMobile">
        Documentation
      </a>
    </div>
    
    <div class="border-t border-gray-200 px-2 py-3">
      <% if user_signed_in? %>
        <%= link_to dashboard_path, 
            class: "block rounded-lg px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-all duration-200",
            data: { action: "click->landing-navbar#closeMobile" } do %>
          Dashboard
        <% end %>
        
        <%= link_to destroy_user_session_path, method: :delete, 
            class: "mt-1 block rounded-lg bg-gray-100 px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-200 transition-all duration-200" do %>
          Sign out
        <% end %>
      <% else %>
        <%= link_to new_user_session_path, 
            class: "block rounded-lg px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-all duration-200" do %>
          Sign in
        <% end %>
        
        <%= link_to new_user_registration_path, 
            class: "mt-1 block rounded-lg bg-indigo-600 px-3 py-2 text-base font-medium text-white hover:bg-indigo-700 transition-all duration-200" do %>
          Start free trial
        <% end %>
      <% end %>
    </div>
  </div>
</nav>

<!-- Add navbar spacer -->
<div class="h-16"></div>