<!-- Top Navigation Bar -->
<div class="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
  <!-- Mobile menu button -->
  <button type="button" class="-m-2.5 p-2.5 text-gray-700 lg:hidden" data-action="click->mobile-menu#toggle">
    <span class="sr-only">Open sidebar</span>
    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
    </svg>
  </button>

  <!-- Separator -->
  <div class="h-6 w-px bg-gray-200 lg:hidden" aria-hidden="true"></div>

  <div class="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
    <!-- Page Title and Breadcrumb -->
    <div class="flex flex-1 items-center">
      <h1 class="text-lg font-semibold leading-6 text-gray-900">
        <%= content_for(:page_title) || "Dashboard" %>
      </h1>
      <% if content_for?(:breadcrumb) %>
        <nav class="hidden sm:flex ml-4" aria-label="Breadcrumb">
          <ol role="list" class="flex items-center space-x-2">
            <li>
              <div class="flex items-center">
                <svg class="h-4 w-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
                <span class="ml-2 text-sm font-medium text-gray-500"><%= content_for(:breadcrumb) %></span>
              </div>
            </li>
          </ol>
        </nav>
      <% end %>
    </div>

    <div class="flex items-center gap-x-4 lg:gap-x-6">
      <!-- Search -->
      <div class="relative flex-1 max-w-xs">
        <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
          <svg class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd" />
          </svg>
        </div>
        <input type="search" 
               placeholder="Search..." 
               class="block w-full rounded-md border-0 py-1.5 pl-10 pr-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6">
      </div>

      <!-- Notifications -->
      <button type="button" class="-m-2.5 p-2.5 text-gray-400 hover:text-gray-500" data-controller="dropdown" data-action="click->dropdown#toggle">
        <span class="sr-only">View notifications</span>
        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0" />
        </svg>
        <!-- Notification badge -->
        <span class="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-red-100 transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full">3</span>
      </button>

      <!-- Separator -->
      <div class="hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200" aria-hidden="true"></div>

      <!-- Profile dropdown -->
      <div class="relative" data-controller="dropdown">
        <button type="button" 
                class="-m-1.5 flex items-center p-1.5 hover:bg-gray-50 rounded-lg" 
                data-action="click->dropdown#toggle">
          <span class="sr-only">Open user menu</span>
          <div class="h-8 w-8 rounded-full bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
            <span class="text-sm font-medium text-white"><%= current_user.initials %></span>
          </div>
          <span class="hidden lg:flex lg:items-center">
            <span class="ml-4 text-sm font-semibold leading-6 text-gray-900"><%= current_user.full_name %></span>
            <svg class="ml-2 h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
            </svg>
          </span>
        </button>

        <!-- Dropdown menu -->
        <div class="absolute right-0 z-10 mt-2.5 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 hidden" 
             data-dropdown-target="menu">
          <%= link_to "#", class: "block px-3 py-1 text-sm leading-6 text-gray-900 hover:bg-gray-50" do %>
            Your profile
          <% end %>
          <%= link_to organization_path, class: "block px-3 py-1 text-sm leading-6 text-gray-900 hover:bg-gray-50" do %>
            Settings
          <% end %>
          <%= link_to destroy_user_session_path, method: :delete, 
              class: "block px-3 py-1 text-sm leading-6 text-gray-900 hover:bg-gray-50" do %>
            Sign out
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>