<!-- Enhanced Mobile Menu -->
<div class="relative z-50 lg:hidden hidden" data-mobile-menu-target="overlay">
  <div class="fixed inset-0 bg-gray-900/80 backdrop-blur-sm"></div>
  
  <div class="fixed inset-0 flex">
    <div class="relative mr-16 flex w-full max-w-xs flex-1">
      <div class="absolute left-full top-0 flex w-16 justify-center pt-5">
        <button type="button" class="-m-2.5 p-2.5" data-action="click->mobile-menu#close">
          <span class="sr-only">Close sidebar</span>
          <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Mobile sidebar content -->
      <div class="flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 pb-4">
        <!-- Logo -->
        <div class="flex h-16 shrink-0 items-center border-b border-gray-100">
          <%= link_to root_path, class: "flex items-center" do %>
            <div class="flex items-center">
              <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600">
                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
              </div>
              <span class="ml-3 text-xl font-bold text-gray-900">DataReflow</span>
            </div>
          <% end %>
        </div>

        <!-- User Info Section -->
        <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
          <div class="flex items-center space-x-3">
            <div class="h-10 w-10 rounded-full bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
              <span class="text-sm font-medium text-white"><%= current_user.email.first.upcase %></span>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 truncate"><%= current_user.full_name || current_user.email.split('@').first.humanize %></p>
              <p class="text-xs text-gray-500 truncate"><%= current_user.organization.name %></p>
              <span class="inline-flex items-center rounded-full bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10 mt-1">
                <%= current_user.organization.plan.humanize %>
              </span>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="space-y-2">
          <%= link_to new_data_source_path, 
              class: "flex items-center justify-center gap-x-2 rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 transition-colors duration-200" do %>
            <svg class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
              <path d="M10.75 4.75a.75.75 0 00-1.5 0v4.5h-4.5a.75.75 0 000 1.5h4.5v4.5a.75.75 0 001.5 0v-4.5h4.5a.75.75 0 000-1.5h-4.5v-4.5z" />
            </svg>
            Add Data Source
          <% end %>
          
          <button type="button" class="flex items-center justify-center gap-x-2 w-full rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 transition-colors duration-200">
            <svg class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.5 2A1.5 1.5 0 003 3.5v13A1.5 1.5 0 004.5 18h11a1.5 1.5 0 001.5-1.5V7.621a1.5 1.5 0 00-.44-1.06l-4.12-4.122A1.5 1.5 0 0011.378 2H4.5zm2.25 8.5a.75.75 0 000 1.5h6.5a.75.75 0 000-1.5h-6.5zm0 3a.75.75 0 000 1.5h6.5a.75.75 0 000-1.5h-6.5z" clip-rule="evenodd" />
            </svg>
            Export Report
          </button>
        </div>

        <!-- Search -->
        <div class="relative">
          <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <svg class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd" />
            </svg>
          </div>
          <input type="search" 
                 placeholder="Search data sources, reports..." 
                 class="block w-full rounded-lg border-0 py-2 pl-10 pr-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6">
        </div>

        <!-- Notifications Section -->
        <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
          <div class="flex items-center justify-between mb-3">
            <h3 class="text-sm font-medium text-gray-900">Recent Notifications</h3>
            <span class="inline-flex items-center rounded-full bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-inset ring-red-600/10">3 new</span>
          </div>
          <div class="space-y-3">
            <div class="flex items-start space-x-3">
              <div class="flex-shrink-0">
                <div class="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center">
                  <svg class="h-3 w-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                  </svg>
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-xs font-medium text-gray-900">Data sync completed</p>
                <p class="text-xs text-gray-500">Shopify integration finished</p>
              </div>
            </div>
            <div class="flex items-start space-x-3">
              <div class="flex-shrink-0">
                <div class="h-6 w-6 rounded-full bg-yellow-100 flex items-center justify-center">
                  <svg class="h-3 w-3 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                  </svg>
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-xs font-medium text-gray-900">API rate limit warning</p>
                <p class="text-xs text-gray-500">QuickBooks API approaching limit</p>
              </div>
            </div>
          </div>
          <div class="mt-3 pt-3 border-t border-gray-200">
            <a href="#" class="text-xs text-blue-600 hover:text-blue-500 font-medium">View all notifications</a>
          </div>
        </div>

        <!-- Mobile Navigation -->
        <nav class="flex flex-1 flex-col">
          <ul role="list" class="flex flex-1 flex-col gap-y-7">
            <li>
              <ul role="list" class="-mx-2 space-y-1">
                <!-- Dashboard -->
                <li>
                  <%= link_to dashboard_path, 
                      class: "group flex gap-x-3 rounded-md p-3 text-sm leading-6 font-medium #{current_page?(dashboard_path) ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:text-blue-700 hover:bg-gray-50'} transition-colors duration-200" do %>
                    <svg class="h-5 w-5 shrink-0 #{current_page?(dashboard_path) ? 'text-blue-700' : 'text-gray-400 group-hover:text-blue-700'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2H3a2 2 0 00-2 2z"></path>
                    </svg>
                    Dashboard
                  <% end %>
                </li>

                <!-- Data Sources -->
                <li>
                  <%= link_to data_sources_path, 
                      class: "group flex gap-x-3 rounded-md p-3 text-sm leading-6 font-medium #{current_page?(data_sources_path) ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:text-blue-700 hover:bg-gray-50'} transition-colors duration-200" do %>
                    <svg class="h-5 w-5 shrink-0 #{current_page?(data_sources_path) ? 'text-blue-700' : 'text-gray-400 group-hover:text-blue-700'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                    </svg>
                    Data Sources
                  <% end %>
                </li>

                <!-- Analytics -->
                <li>
                  <%= link_to "#", 
                      class: "group flex gap-x-3 rounded-md p-3 text-sm leading-6 font-medium text-gray-700 hover:text-blue-700 hover:bg-gray-50 transition-colors duration-200" do %>
                    <svg class="h-5 w-5 shrink-0 text-gray-400 group-hover:text-blue-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Analytics
                  <% end %>
                </li>

                <!-- Reports -->
                <li>
                  <%= link_to "#", 
                      class: "group flex gap-x-3 rounded-md p-3 text-sm leading-6 font-medium text-gray-700 hover:text-blue-700 hover:bg-gray-50 transition-colors duration-200" do %>
                    <svg class="h-5 w-5 shrink-0 text-gray-400 group-hover:text-blue-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Reports
                  <% end %>
                </li>

                <% if current_user.can_manage_users? %>
                  <!-- Team -->
                  <li>
                    <%= link_to users_path, 
                        class: "group flex gap-x-3 rounded-md p-3 text-sm leading-6 font-medium #{current_page?(users_path) ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:text-blue-700 hover:bg-gray-50'} transition-colors duration-200" do %>
                      <svg class="h-5 w-5 shrink-0 #{current_page?(users_path) ? 'text-blue-700' : 'text-gray-400 group-hover:text-blue-700'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                      </svg>
                      Team
                    <% end %>
                  </li>
                <% end %>

                <% if current_user.can_manage_organization? %>
                  <!-- Settings -->
                  <li>
                    <%= link_to organization_path, 
                        class: "group flex gap-x-3 rounded-md p-3 text-sm leading-6 font-medium #{current_page?(organization_path) ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:text-blue-700 hover:bg-gray-50'} transition-colors duration-200" do %>
                      <svg class="h-5 w-5 shrink-0 #{current_page?(organization_path) ? 'text-blue-700' : 'text-gray-400 group-hover:text-blue-700'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      </svg>
                      Settings
                    <% end %>
                  </li>
                <% end %>
              </ul>
            </li>

            <!-- Account Actions -->
            <li class="mt-auto space-y-1">
              <div class="border-t border-gray-200 pt-4">
                <%= link_to edit_user_registration_path, class: "group flex gap-x-3 rounded-md p-3 text-sm leading-6 font-medium text-gray-700 hover:text-blue-700 hover:bg-gray-50 transition-colors duration-200" do %>
                  <svg class="h-5 w-5 shrink-0 text-gray-400 group-hover:text-blue-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                  </svg>
                  Your Profile
                <% end %>
                
                <% if current_user.can_manage_organization? %>
                  <%= link_to billing_organization_path, class: "group flex gap-x-3 rounded-md p-3 text-sm leading-6 font-medium text-gray-700 hover:text-blue-700 hover:bg-gray-50 transition-colors duration-200" do %>
                    <svg class="h-5 w-5 shrink-0 text-gray-400 group-hover:text-blue-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                    </svg>
                    Billing & Usage
                  <% end %>
                <% end %>
                
                <a href="#" class="group flex gap-x-3 rounded-md p-3 text-sm leading-6 font-medium text-gray-700 hover:text-blue-700 hover:bg-gray-50 transition-colors duration-200">
                  <svg class="h-5 w-5 shrink-0 text-gray-400 group-hover:text-blue-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  Help & Support
                </a>
                
                <%= link_to destroy_user_session_path, data: { turbo_method: :delete }, class: "group flex gap-x-3 rounded-md p-3 text-sm leading-6 font-medium text-red-700 hover:text-red-800 hover:bg-red-50 transition-colors duration-200" do %>
                  <svg class="h-5 w-5 shrink-0 text-red-400 group-hover:text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                  </svg>
                  Sign out
                <% end %>
              </div>
            </li>
          </ul>
        </nav>
      </div>
    </div>
  </div>
</div>