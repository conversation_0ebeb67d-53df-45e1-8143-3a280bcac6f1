<!-- Premium Professional Sidebar Navigation -->
<div class="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col">
  <!-- Sidebar component -->
  <div class="flex grow flex-col gap-y-5 overflow-y-auto bg-gray-900 px-6 pb-4">
    <div class="flex h-16 shrink-0 items-center">
      <%= link_to root_path, class: "flex items-center gap-x-2" do %>
        <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-indigo-600 shadow-sm">
          <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
          </svg>
        </div>
        <span class="text-xl font-semibold text-white">DataReflow</span>
      <% end %>
    </div>
    
    <nav class="flex flex-1 flex-col">
      <ul role="list" class="flex flex-1 flex-col gap-y-7">
        <li>
          <ul role="list" class="-mx-2 space-y-1">
            <% 
              nav_items = [
                { name: 'Dashboard', href: dashboard_path, icon: 'M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25', controller: 'dashboard' },
                { name: 'Data Sources', href: data_sources_path, icon: 'M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125', controller: 'data_sources' },
                { name: 'Analytics', href: analytics_path, icon: 'M3.75 3v11.25A2.25 2.25 0 006 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0118 16.5h-2.25m-7.5 0h7.5m-7.5 0l-1 3m8.5-3l1 3m0 0l.5 1.5m-.5-1.5h-9.5m0 0l-.5 1.5m.75-9l3-3 2.148 2.148A12.061 12.061 0 0116.5 7.605', controller: 'analytics' },
                { name: 'Pipelines', href: pipeline_dashboard_index_path, icon: 'M9.348 14.651a3.75 3.75 0 010-5.303m5.304 0a3.75 3.75 0 010 5.303m-7.425 2.122a6.75 6.75 0 010-9.546m9.546 0a6.75 6.75 0 010 9.546M5.106 18.894c-3.808-3.808-3.808-9.98 0-13.789m13.788 0c3.808 3.808 3.808 9.981 0 13.79M12 12h.008v.007H12V12zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z', controller: 'pipeline_dashboard' },
                { name: 'Reports', href: '#', icon: 'M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z', controller: 'reports' },
                { name: 'Team', href: '#', icon: 'M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z', controller: 'team' },
                { name: 'Settings', href: organization_path, icon: 'M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.*************.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z', controller: 'organizations' }
              ]
              
              current_controller = controller_name
            %>
            
            <% nav_items.each do |item| %>
              <li>
                <%= link_to item[:href], 
                    class: "group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold #{current_controller == item[:controller] ? 'bg-gray-800 text-white' : 'text-gray-400 hover:text-white hover:bg-gray-800'} transition-all duration-200" do %>
                  <svg class="h-6 w-6 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="<%= item[:icon] %>" />
                  </svg>
                  <%= item[:name] %>
                  <% if item[:name] == 'Data Sources' && @data_sources&.any? %>
                    <span class="ml-auto w-9 min-w-max whitespace-nowrap rounded-full bg-gray-700 px-2.5 py-0.5 text-center text-xs font-medium leading-5 text-gray-200 ring-1 ring-inset ring-gray-600">
                      <%= @data_sources.count %>
                    </span>
                  <% end %>
                <% end %>
              </li>
            <% end %>
          </ul>
        </li>
        
        <!-- AI Features Section -->
        <li>
          <div class="text-xs font-semibold leading-6 text-gray-400">AI Features</div>
          <ul role="list" class="-mx-2 mt-2 space-y-1">
            <% 
              ai_items = [
                { name: 'Natural Language Query', href: '#', icon: 'M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12c0 4.556-4.03 8.25-9 8.25a9.764 9.764 0 01-2.555-.337A5.972 5.972 0 015.41 20.97a5.969 5.969 0 01-.474-.065 4.48 4.48 0 00.978-2.025c.09-.457-.133-.901-.467-1.226C3.93 16.178 3 14.189 3 12c0-4.556 4.03-8.25 9-8.25s9 3.694 9 8.25z' },
                { name: 'BI Agent', href: '#', icon: 'M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z' },
                { name: 'Interactive Presentations', href: '#', icon: 'M3.75 3v11.25A2.25 2.25 0 006 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0118 16.5h-2.25m-7.5 0h7.5m-7.5 0l-1 3m8.5-3l1 3m0 0l.5 1.5m-.5-1.5h-9.5m0 0l-.5 1.5M9 11.25v1.5M12 9v3.75m3-6v6' }
              ]
            %>
            
            <% ai_items.each do |item| %>
              <li>
                <%= link_to item[:href], 
                    class: "group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold text-gray-400 hover:text-white hover:bg-gray-800 transition-all duration-200" do %>
                  <svg class="h-6 w-6 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="<%= item[:icon] %>" />
                  </svg>
                  <%= item[:name] %>
                  <span class="ml-auto rounded-md bg-indigo-600/10 px-2 py-1 text-xs font-medium text-indigo-400 ring-1 ring-inset ring-indigo-600/20">
                    Beta
                  </span>
                <% end %>
              </li>
            <% end %>
          </ul>
        </li>
        
        <!-- Bottom section -->
        <li class="mt-auto">
          <div class="mb-4">
            <!-- System Status -->
            <div class="rounded-lg bg-gray-800 p-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-xs font-medium text-gray-400">System Status</span>
                <div class="flex items-center gap-x-1.5">
                  <div class="flex-none rounded-full bg-emerald-500/20 p-1">
                    <div class="h-1.5 w-1.5 rounded-full bg-emerald-500"></div>
                  </div>
                  <span class="text-xs text-gray-400">Operational</span>
                </div>
              </div>
              <div class="space-y-2">
                <div>
                  <div class="flex justify-between text-xs">
                    <span class="text-gray-500">API Uptime</span>
                    <span class="text-gray-400">99.99%</span>
                  </div>
                </div>
                <div>
                  <div class="flex justify-between text-xs">
                    <span class="text-gray-500">Response Time</span>
                    <span class="text-gray-400">42ms</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- User Profile -->
          <div class="flex items-center gap-x-4 px-2 py-3 text-sm font-semibold leading-6 text-gray-400">
            <% if current_user&.avatar&.attached? %>
              <%= image_tag current_user.avatar, class: "h-8 w-8 rounded-full bg-gray-800" %>
            <% else %>
              <div class="h-8 w-8 rounded-full bg-gray-800 flex items-center justify-center">
                <span class="text-xs text-gray-300">
                  <%= current_user&.first_name&.first || 'U' %>
                </span>
              </div>
            <% end %>
            <span class="flex-1 text-xs">
              <span class="block text-gray-200"><%= current_user&.full_name || 'User' %></span>
              <span class="block text-gray-500"><%= current_user&.email %></span>
            </span>
            <%= link_to destroy_user_session_path, method: :delete,
                class: "text-gray-500 hover:text-gray-300 transition-colors duration-200",
                title: "Sign out" do %>
              <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15M12 9l-3 3m0 0l3 3m-3-3h12.75" />
              </svg>
            <% end %>
          </div>
        </li>
      </ul>
    </nav>
  </div>
</div>

<!-- Mobile navigation -->
<div class="sticky top-0 z-40 flex items-center gap-x-6 bg-gray-900 px-4 py-4 shadow-sm sm:px-6 lg:hidden">
  <button type="button" class="-m-2.5 p-2.5 text-gray-400 lg:hidden" data-controller="mobile-sidebar" data-action="click->mobile-sidebar#toggle">
    <span class="sr-only">Open sidebar</span>
    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
    </svg>
  </button>
  <div class="flex-1 text-sm font-semibold leading-6 text-white">Dashboard</div>
  <a href="#">
    <span class="sr-only">Your profile</span>
    <% if current_user&.avatar&.attached? %>
      <%= image_tag current_user.avatar, class: "h-8 w-8 rounded-full bg-gray-800" %>
    <% else %>
      <div class="h-8 w-8 rounded-full bg-gray-800 flex items-center justify-center">
        <span class="text-xs text-gray-300">
          <%= current_user&.first_name&.first || 'U' %>
        </span>
      </div>
    <% end %>
  </a>
</div>