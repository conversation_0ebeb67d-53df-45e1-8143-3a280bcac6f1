<!-- DataFlow Pro Flash Messages -->
<% if flash.any? %>
  <div class="flash-messages">
    <% flash.each do |type, message_data| %>
      <% 
        # Handle both simple string messages and complex hash messages
        if message_data.is_a?(Hash)
          message = message_data['message'] || message_data[:message]
          title = message_data['title'] || message_data[:title]
        else
          message = message_data
          title = nil
        end
        
        # Set type based on flash type
        alert_type = case type.to_s
        when 'notice', 'success'
          'success'
        when 'alert', 'error'
          'error'
        when 'warning'
          'warning'
        when 'info'
          'info'
        else
          'info'
        end
        
        # Icon mapping
        icon = case alert_type
        when 'success'
          '✅'
        when 'error'
          '❌'
        when 'warning'
          '⚠️'
        else
          'ℹ️'
        end
      %>
      
      <div class="alert alert--<%= alert_type %>" data-controller="flash" data-action="animationend->flash#remove">
        <span class="alert__icon"><%= icon %></span>
        <div class="alert__content">
          <% if title.present? %>
            <strong><%= title %></strong><br>
          <% end %>
          <%= message %>
        </div>
        <button type="button" class="alert__close" data-action="click->flash#dismiss" aria-label="Dismiss">
          &times;
        </button>
      </div>
    <% end %>
  </div>
<% end %>

<style>
  .flash-messages {
    position: fixed;
    top: var(--space-24);
    right: var(--space-24);
    z-index: 1000;
    max-width: 400px;
    width: 100%;
  }
  
  .alert {
    animation: slideIn 0.3s ease-out;
    margin-bottom: var(--space-12);
  }
  
  .alert.dismissing {
    animation: slideOut 0.3s ease-in forwards;
  }
  
  @keyframes slideIn {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  @keyframes slideOut {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }
  
  @media (max-width: 768px) {
    .flash-messages {
      left: var(--space-16);
      right: var(--space-16);
      top: var(--space-16);
    }
  }
</style>