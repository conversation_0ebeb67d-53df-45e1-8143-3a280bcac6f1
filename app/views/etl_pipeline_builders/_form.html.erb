<!-- Pipeline Configuration Steps -->
<div class="px-4 sm:px-6 lg:px-8 py-8">
  <!-- Step Navigation -->
  <nav aria-label="Progress">
    <ol class="flex items-center justify-center space-x-5">
      <li data-pipeline-builder-target="step1Nav" class="relative">
        <div class="flex items-center">
          <span class="h-10 w-10 bg-indigo-600 text-white rounded-full flex items-center justify-center text-sm font-medium">1</span>
          <span class="ml-4 text-sm font-medium text-gray-900">Basic Info</span>
        </div>
      </li>
      <li data-pipeline-builder-target="step2Nav" class="relative">
        <div class="absolute left-0 top-5 -ml-px mt-0.5 h-0.5 w-full bg-gray-300"></div>
        <div class="group relative flex items-center">
          <span class="h-10 w-10 bg-gray-300 text-gray-500 rounded-full flex items-center justify-center text-sm font-medium">2</span>
          <span class="ml-4 text-sm font-medium text-gray-500">Source</span>
        </div>
      </li>
      <li data-pipeline-builder-target="step3Nav" class="relative">
        <div class="absolute left-0 top-5 -ml-px mt-0.5 h-0.5 w-full bg-gray-300"></div>
        <div class="group relative flex items-center">
          <span class="h-10 w-10 bg-gray-300 text-gray-500 rounded-full flex items-center justify-center text-sm font-medium">3</span>
          <span class="ml-4 text-sm font-medium text-gray-500">Transformations</span>
        </div>
      </li>
      <li data-pipeline-builder-target="step4Nav" class="relative">
        <div class="absolute left-0 top-5 -ml-px mt-0.5 h-0.5 w-full bg-gray-300"></div>
        <div class="group relative flex items-center">
          <span class="h-10 w-10 bg-gray-300 text-gray-500 rounded-full flex items-center justify-center text-sm font-medium">4</span>
          <span class="ml-4 text-sm font-medium text-gray-500">Destination</span>
        </div>
      </li>
      <li data-pipeline-builder-target="step5Nav" class="relative">
        <div class="absolute left-0 top-5 -ml-px mt-0.5 h-0.5 w-full bg-gray-300"></div>
        <div class="group relative flex items-center">
          <span class="h-10 w-10 bg-gray-300 text-gray-500 rounded-full flex items-center justify-center text-sm font-medium">5</span>
          <span class="ml-4 text-sm font-medium text-gray-500">Schedule</span>
        </div>
      </li>
    </ol>
  </nav>

  <!-- Form steps content -->
  <div>
    <!-- Copy all step content from new.html.erb starting from Step 1 through Step 5 -->
    <!-- This is the same content, just extracted to a partial -->
  </div>

  <!-- Navigation Buttons -->
  <div class="mt-8 flex justify-between">
    <button type="button" data-action="click->pipeline-builder#previousStep" data-pipeline-builder-target="prevButton" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
      <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
      </svg>
      Previous
    </button>

    <div class="flex space-x-3">
      <button type="button" data-action="click->pipeline-builder#testPipeline" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" d="M9.75 3.104v5.714a2.25 2.25 0 01-.659 1.591L5 14.5M9.75 3.104c-.251.023-.501.05-.75.082m.75-.082a24.301 24.301 0 014.5 0m0 0v5.714c0 .597.237 1.17.659 1.591L19.8 15.3M14.25 3.104c.251.023.501.05.75.082M19.8 15.3l-1.57.393A9.065 9.065 0 0112 15a9.065 9.065 0 00-6.23-.693L5 14.5m14.8.8l1.402 1.402c1.232 1.232.65 3.318-1.067 3.611l-3.98.793a2.125 2.125 0 01-1.113-.825L12 15M8.25 12h4.5" />
        </svg>
        Test Pipeline
      </button>
      
      <button type="button" data-action="click->pipeline-builder#nextStep" data-pipeline-builder-target="nextButton" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
        Next
        <svg class="-mr-1 ml-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
        </svg>
      </button>

      <%= form.submit pipeline.persisted? ? "Update Pipeline" : "Create Pipeline", data: { pipeline_builder_target: "submitButton" }, class: "hidden inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
    </div>
  </div>
</div>

<!-- Transformation Rule Modal -->
<div data-pipeline-builder-target="transformationModal" class="hidden fixed z-10 inset-0 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
  <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
    <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
    <div class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
      <div>
        <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
          Add Transformation Rule
        </h3>
        <div class="mt-4" data-pipeline-builder-target="transformationForm">
          <!-- Dynamic form content will be loaded here -->
        </div>
      </div>
      <div class="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
        <button type="button" data-action="click->pipeline-builder#saveTransformation" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:col-start-2 sm:text-sm">
          Add Rule
        </button>
        <button type="button" data-action="click->pipeline-builder#closeTransformationModal" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:col-start-1 sm:text-sm">
          Cancel
        </button>
      </div>
    </div>
  </div>
</div>