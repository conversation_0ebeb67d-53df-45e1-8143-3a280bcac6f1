<div class="min-h-screen bg-gray-50">
  <!-- <PERSON> Header -->
  <div class="bg-white shadow-sm border-b border-gray-200">
    <div class="px-4 sm:px-6 lg:px-8">
      <div class="py-6">
        <div class="md:flex md:items-center md:justify-between">
          <div class="flex-1 min-w-0">
            <div class="flex items-center">
              <%= link_to etl_pipeline_builders_path, class: "mr-4 text-gray-400 hover:text-gray-500" do %>
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18" />
                </svg>
              <% end %>
              <div>
                <h1 class="text-3xl font-bold text-gray-900"><%= @pipeline.name %></h1>
                <div class="mt-1 flex items-center">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                    <%= case @pipeline.pipeline_type
                        when 'etl' then 'bg-green-100 text-green-800'
                        when 'elt' then 'bg-blue-100 text-blue-800'
                        when 'streaming' then 'bg-purple-100 text-purple-800'
                        else 'bg-gray-100 text-gray-800'
                        end %>">
                    <%= @pipeline.pipeline_type.upcase %>
                  </span>
                  <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                    <%= case @pipeline.status
                        when 'active' then 'bg-green-100 text-green-800'
                        when 'paused' then 'bg-yellow-100 text-yellow-800'
                        when 'draft' then 'bg-gray-100 text-gray-800'
                        when 'archived' then 'bg-red-100 text-red-800'
                        end %>">
                    <%= @pipeline.status.capitalize %>
                  </span>
                  <% if @pipeline.scheduled? %>
                    <span class="ml-2 inline-flex items-center text-sm text-gray-500">
                      <svg class="mr-1.5 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Next run: <%= @next_scheduled_run&.strftime("%b %d, %I:%M %p") || "N/A" %>
                    </span>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
          <div class="mt-4 flex md:mt-0 md:ml-4 space-x-3">
            <% if @pipeline.active? %>
              <%= button_to "Run Now", execute_etl_pipeline_builder_path(@pipeline), method: :post, class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500" %>
              <%= button_to "Pause", etl_pipeline_builder_path(@pipeline), method: :patch, params: { pipeline_configuration: { status: 'paused' } }, class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
            <% elsif @pipeline.paused? %>
              <%= button_to "Resume", etl_pipeline_builder_path(@pipeline), method: :patch, params: { pipeline_configuration: { status: 'active' } }, class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500" %>
            <% end %>
            <%= link_to "Edit", edit_etl_pipeline_builder_path(@pipeline), class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
            <div class="relative inline-block text-left">
              <button type="button" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" aria-expanded="false" aria-haspopup="true">
                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.75a.75.75 0 110-********* 0 010 1.5zM12 12.75a.75.75 0 110-********* 0 010 1.5zM12 18.75a.75.75 0 110-********* 0 010 1.5z" />
                </svg>
                More
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Pipeline Description -->
  <% if @pipeline.description.present? %>
    <div class="px-4 sm:px-6 lg:px-8 pt-6">
      <div class="bg-white shadow rounded-lg p-6">
        <p class="text-gray-700"><%= @pipeline.description %></p>
      </div>
    </div>
  <% end %>

  <!-- Metrics Dashboard -->
  <div class="px-4 sm:px-6 lg:px-8 py-6">
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Success Rate</dt>
                <dd class="flex items-baseline">
                  <div class="text-2xl font-semibold text-gray-900">
                    <%= @metrics[:success_rate] %>%
                  </div>
                  <p class="ml-2 flex items-baseline text-sm text-gray-600">
                    <span class="text-xs">last 30 days</span>
                  </p>
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Total Runs</dt>
                <dd class="flex items-baseline">
                  <div class="text-2xl font-semibold text-gray-900">
                    <%= @metrics[:total_runs] %>
                  </div>
                  <p class="ml-2 flex items-baseline text-sm text-green-600">
                    <span class="text-green-600"><%= @metrics[:successful_runs] %></span>
                    <span class="text-gray-400 mx-1">/</span>
                    <span class="text-red-600"><%= @metrics[:failed_runs] %></span>
                  </p>
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Avg Duration</dt>
                <dd class="flex items-baseline">
                  <div class="text-2xl font-semibold text-gray-900">
                    <%= (@metrics[:average_duration] || 0).round(1) %>s
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Avg Progress</dt>
                <dd class="flex items-baseline">
                  <div class="text-2xl font-semibold text-gray-900">
                    <%= (@metrics[:average_rows_processed] || 0).round %>%
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Pipeline Configuration Details -->
  <div class="px-4 sm:px-6 lg:px-8 pb-6">
    <div class="bg-white shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-900">Pipeline Configuration</h2>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-3">
          <!-- Source -->
          <div>
            <h3 class="text-sm font-medium text-gray-900 mb-3">Source</h3>
            <div class="bg-gray-50 rounded-lg p-4">
              <div class="flex items-center mb-2">
                <svg class="h-5 w-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125" />
                </svg>
                <span class="text-sm font-medium text-gray-700"><%= @pipeline.source_config['type']&.capitalize %></span>
              </div>
              <% if @pipeline.source_config['database_type'] %>
                <p class="text-sm text-gray-500">Database: <%= @pipeline.source_config['database_type'] %></p>
              <% end %>
              <% if @pipeline.source_config['table'] %>
                <p class="text-sm text-gray-500">Table: <%= @pipeline.source_config['table'] %></p>
              <% end %>
            </div>
          </div>

          <!-- Transformations -->
          <div>
            <h3 class="text-sm font-medium text-gray-900 mb-3">Transformations</h3>
            <div class="bg-gray-50 rounded-lg p-4">
              <% if @pipeline.transformation_rules.present? %>
                <div class="space-y-2">
                  <% @pipeline.transformation_rules.first(3).each do |rule| %>
                    <div class="flex items-center">
                      <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-indigo-100 text-indigo-800">
                        <%= rule['type'] %>
                      </span>
                      <span class="ml-2 text-sm text-gray-500 truncate"><%= rule['name'] %></span>
                    </div>
                  <% end %>
                  <% if @pipeline.transformation_rules.size > 3 %>
                    <p class="text-sm text-gray-500">+<%= @pipeline.transformation_rules.size - 3 %> more</p>
                  <% end %>
                </div>
              <% else %>
                <p class="text-sm text-gray-500">No transformations</p>
              <% end %>
            </div>
          </div>

          <!-- Destination -->
          <div>
            <h3 class="text-sm font-medium text-gray-900 mb-3">Destination</h3>
            <div class="bg-gray-50 rounded-lg p-4">
              <div class="flex items-center mb-2">
                <svg class="h-5 w-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008zm0 3h.008v.008h-.008v-.008zm0 3h.008v.008h-.008v-.008z" />
                </svg>
                <span class="text-sm font-medium text-gray-700"><%= @pipeline.destination_config['type']&.capitalize %></span>
              </div>
              <% if @pipeline.destination_config['warehouse_type'] %>
                <p class="text-sm text-gray-500">Warehouse: <%= @pipeline.destination_config['warehouse_type'] %></p>
              <% end %>
              <% if @pipeline.destination_config['table_name'] %>
                <p class="text-sm text-gray-500">Table: <%= @pipeline.destination_config['table_name'] %></p>
              <% end %>
            </div>
          </div>
        </div>

        <!-- Schedule & Error Handling -->
        <div class="mt-6 grid grid-cols-1 gap-6 sm:grid-cols-2">
          <div>
            <h3 class="text-sm font-medium text-gray-900 mb-3">Schedule</h3>
            <div class="bg-gray-50 rounded-lg p-4">
              <% if @pipeline.scheduled? %>
                <p class="text-sm font-medium text-gray-700"><%= @pipeline.schedule_config['type'].capitalize %></p>
                <% if @pipeline.schedule_config['cron_expression'] %>
                  <p class="text-sm text-gray-500 font-mono"><%= @pipeline.schedule_config['cron_expression'] %></p>
                <% end %>
              <% else %>
                <p class="text-sm text-gray-500">Manual execution only</p>
              <% end %>
            </div>
          </div>

          <div>
            <h3 class="text-sm font-medium text-gray-900 mb-3">Error Handling</h3>
            <div class="bg-gray-50 rounded-lg p-4">
              <p class="text-sm font-medium text-gray-700"><%= (@pipeline.error_handling_strategy || 'circuit_breaker').humanize %></p>
              <% if @pipeline.retry_policy %>
                <p class="text-sm text-gray-500">Max retries: <%= @pipeline.retry_policy['max_retries'] || 3 %></p>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Execution History -->
  <div class="px-4 sm:px-6 lg:px-8 pb-8">
    <div class="bg-white shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-medium text-gray-900">Execution History</h2>
          <% if @executions.any? %>
            <span class="text-sm text-gray-500">Last 10 executions</span>
          <% end %>
        </div>
      </div>

      <% if @executions.any? %>
        <div class="overflow-hidden">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Execution ID
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Started
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Duration
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Progress
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Triggered By
                </th>
                <th scope="col" class="relative px-6 py-3">
                  <span class="sr-only">View</span>
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <% @executions.each do |execution| %>
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    #<%= execution.id %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <%= execution.started_at.strftime("%b %d, %I:%M %p") %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <% if execution.duration_seconds %>
                      <%= distance_of_time_in_words(execution.duration_seconds) %>
                    <% else %>
                      -
                    <% end %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                      <%= case execution.status
                          when 'successful' then 'bg-green-100 text-green-800'
                          when 'failed' then 'bg-red-100 text-red-800'
                          when 'running' then 'bg-blue-100 text-blue-800'
                          when 'queued' then 'bg-gray-100 text-gray-800'
                          else 'bg-gray-100 text-gray-800'
                          end %>">
                      <%= execution.status %>
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <%= execution.progress ? "#{execution.progress}%" : "-" %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <%= execution.user&.email || "System" %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <%= link_to "View Details", "#", class: "text-indigo-600 hover:text-indigo-900", 
                        data: { execution_id: execution.id }, 
                        title: "Execution ##{execution.id}" %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      <% else %>
        <div class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No executions yet</h3>
          <p class="mt-1 text-sm text-gray-500">Get started by running this pipeline.</p>
          <% if @pipeline.active? %>
            <div class="mt-6">
              <%= button_to "Run Pipeline", execute_etl_pipeline_builder_path(@pipeline), method: :post, class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>
  </div>
</div>