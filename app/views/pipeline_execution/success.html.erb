<% content_for :page_title, "Pipeline Executed Successfully!" %>

<div class="min-h-screen bg-gradient-to-br from-slate-50 via-green-50 to-emerald-50 flex items-center justify-center px-4"
     data-controller="confetti page-transition"
     data-confetti-trigger-value="auto">
  
  <div class="relative max-w-3xl w-full">
    <!-- Success Card -->
    <div class="relative bg-white/90 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 p-12">
      <div class="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5 rounded-3xl"></div>
      
      <div class="relative text-center">
        <!-- Success Icon -->
        <div class="mb-8 flex justify-center">
          <div class="relative">
            <div class="w-32 h-32 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center shadow-2xl animate-bounce">
              <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="absolute inset-0 w-32 h-32 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full animate-ping opacity-30"></div>
          </div>
        </div>
        
        <!-- Success Message -->
        <h1 class="text-4xl font-bold bg-gradient-to-r from-gray-900 via-green-900 to-emerald-900 bg-clip-text text-transparent mb-4">
          Pipeline Executed Successfully!
        </h1>
        
        <p class="text-xl text-slate-600 font-medium mb-8">
          <%= @pipeline_execution.pipeline.name %> has completed processing
        </p>
        
        <!-- Execution Stats -->
        <div class="grid grid-cols-3 gap-4 mb-8 max-w-2xl mx-auto">
          <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-4">
            <div class="text-2xl font-bold text-blue-600 mb-1">
              <%= number_with_delimiter(@pipeline_execution.records_processed || 0) %>
            </div>
            <div class="text-sm text-slate-600">Records Processed</div>
          </div>
          
          <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-4">
            <div class="text-2xl font-bold text-green-600 mb-1">
              <%= @pipeline_execution.duration || '< 1 min' %>
            </div>
            <div class="text-sm text-slate-600">Processing Time</div>
          </div>
          
          <div class="bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-4">
            <div class="text-2xl font-bold text-purple-600 mb-1">
              <%= @pipeline_execution.transformations_applied || 0 %>
            </div>
            <div class="text-sm text-slate-600">Transformations</div>
          </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <%= link_to pipeline_execution_path(@pipeline_execution), 
              class: "group inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300",
              data: {
                controller: "magnetic-button ripple-effect",
                action: "mousemove->magnetic-button#mouseMove mouseleave->magnetic-button#mouseLeave click->ripple-effect#click"
              } do %>
            <svg class="w-5 h-5 mr-2 group-hover:rotate-12 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            View Results
          <% end %>
          
          <%= link_to pipeline_dashboard_path, 
              class: "group inline-flex items-center justify-center px-8 py-4 border border-slate-300/50 rounded-xl shadow-md text-slate-700 bg-white/70 backdrop-blur-sm hover:bg-white/90 hover:shadow-lg transition-all duration-300",
              data: {
                controller: "magnetic-button ripple-effect",
                action: "mousemove->magnetic-button#mouseMove mouseleave->magnetic-button#mouseLeave click->ripple-effect#click"
              } do %>
            <svg class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
            </svg>
            Back to Dashboard
          <% end %>
        </div>
        
        <!-- Celebrate Again Button -->
        <div class="mt-8">
          <button class="text-sm text-slate-500 hover:text-slate-700 transition-colors"
                  data-action="click->confetti#trigger">
            🎉 Celebrate Again
          </button>
        </div>
      </div>
    </div>
    
    <!-- Floating particles animation -->
    <div class="absolute -top-10 -left-10 w-32 h-32 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full blur-3xl opacity-20 animate-pulse"></div>
    <div class="absolute -bottom-10 -right-10 w-40 h-40 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-full blur-3xl opacity-20 animate-pulse" style="animation-delay: 1s"></div>
    <div class="absolute top-1/2 -left-20 w-24 h-24 bg-gradient-to-br from-purple-400 to-pink-500 rounded-full blur-3xl opacity-20 animate-pulse" style="animation-delay: 2s"></div>
  </div>
</div>