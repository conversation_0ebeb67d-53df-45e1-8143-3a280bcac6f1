<% content_for :title, "New Scheduled Upload - #{@data_source.name}" %>

<div class="bg-white shadow-sm border-b border-gray-200">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="py-6">
      <nav class="flex" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-4">
          <li>
            <%= link_to data_sources_path, class: "text-gray-500 hover:text-gray-700" do %>
              Data Sources
            <% end %>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="flex-shrink-0 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
              <%= link_to @data_source.name, @data_source, class: "ml-4 text-gray-500 hover:text-gray-700" %>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="flex-shrink-0 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
              <%= link_to "Scheduled Uploads", data_source_scheduled_uploads_path(@data_source), class: "ml-4 text-gray-500 hover:text-gray-700" %>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="flex-shrink-0 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
              <span class="ml-4 text-gray-900 font-medium">New</span>
            </div>
          </li>
        </ol>
      </nav>
      <h1 class="mt-2 text-2xl font-bold text-gray-900">Create Scheduled Upload</h1>
      <p class="mt-1 text-sm text-gray-500">Set up automated file uploads from your data source</p>
    </div>
  </div>
</div>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Help Section -->
  <div class="bg-blue-50 border border-blue-200 rounded-md p-4 mb-8">
    <div class="flex">
      <div class="flex-shrink-0">
        <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
        </svg>
      </div>
      <div class="ml-3">
        <h3 class="text-sm font-medium text-blue-800">About Scheduled Uploads</h3>
        <div class="mt-2 text-sm text-blue-700">
          <p>Scheduled uploads automatically check your data source for new files and process them according to your specified schedule. This is perfect for:</p>
          <ul class="list-disc list-inside mt-2 space-y-1">
            <li>Regular data imports from external systems</li>
            <li>Processing files that are dropped into a folder on a schedule</li>
            <li>Automated data pipeline workflows</li>
            <li>Keeping your data up-to-date without manual intervention</li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <%= render 'form', scheduled_upload: @scheduled_upload %>
</div>