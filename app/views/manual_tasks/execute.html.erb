<div class="max-w-3xl mx-auto">
  <div class="mb-6">
    <%= link_to manual_task_path(@task), class: "text-blue-600 hover:text-blue-800 flex items-center gap-1" do %>
      ← Back to Task Details
    <% end %>
  </div>

  <div class="bg-white rounded-lg shadow">
    <div class="p-6 border-b">
      <h1 class="text-2xl font-bold text-gray-900">Execute Manual Task</h1>
      <p class="mt-2 text-gray-600">Review the task details and provide any required information before execution.</p>
    </div>

    <div class="p-6 space-y-6">
      <!-- Task summary -->
      <div class="bg-gray-50 rounded-lg p-4">
        <h3 class="font-semibold mb-2"><%= @task.name %></h3>
        <p class="text-gray-600 text-sm mb-3"><%= @task.description %></p>
        <div class="flex items-center gap-4 text-sm">
          <span class="flex items-center gap-1">
            <strong>Type:</strong> <%= @task.task_type.humanize %>
          </span>
          <span class="flex items-center gap-1">
            <strong>Priority:</strong> <%= @task.priority %>
          </span>
          <span class="flex items-center gap-1">
            <strong>Pipeline:</strong> <%= @task.pipeline_execution.pipeline_name %>
          </span>
        </div>
      </div>

      <!-- Task configuration display -->
      <% if @task.configuration.present? && @task.configuration.any? %>
        <div>
          <h3 class="font-semibold mb-2">Task Configuration</h3>
          <div class="bg-gray-50 rounded-lg p-4">
            <pre class="text-sm overflow-x-auto"><%= JSON.pretty_generate(@task.configuration) %></pre>
          </div>
        </div>
      <% end %>

      <!-- Execution form -->
      <%= form_with url: execute_manual_task_path(@task), method: :post, local: true do |form| %>
        <div class="space-y-4">
          <!-- Execution notes -->
          <div>
            <%= form.label :notes, "Execution Notes", class: "block text-sm font-medium text-gray-700 mb-1" %>
            <%= form.text_area :notes, 
              rows: 4,
              class: "block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500",
              placeholder: "Add any notes about this execution (optional)..." %>
            <p class="mt-1 text-sm text-gray-500">
              These notes will be recorded with the execution history.
            </p>
          </div>

          <!-- Task-specific configuration based on task type -->
          <% case @task.task_type %>
          <% when 'extraction' %>
            <div class="bg-blue-50 rounded-lg p-4">
              <h4 class="font-semibold text-blue-900 mb-2">Extraction Task</h4>
              <p class="text-sm text-blue-700">
                This task will extract data from the configured data source. 
                The extraction will use the current connection settings and respect any rate limits.
              </p>
            </div>
          
          <% when 'transformation' %>
            <div class="bg-purple-50 rounded-lg p-4">
              <h4 class="font-semibold text-purple-900 mb-2">Transformation Task</h4>
              <p class="text-sm text-purple-700">
                This task will transform the raw data according to the configured rules. 
                Make sure the extraction has completed successfully before proceeding.
              </p>
            </div>

          <% when 'validation' %>
            <div class="bg-green-50 rounded-lg p-4">
              <h4 class="font-semibold text-green-900 mb-2">Validation Task</h4>
              <p class="text-sm text-green-700">
                This task will validate the data quality according to the configured rules. 
                Any validation failures will be recorded but won't stop the pipeline.
              </p>
            </div>

          <% when 'notification' %>
            <div class="bg-yellow-50 rounded-lg p-4">
              <h4 class="font-semibold text-yellow-900 mb-2">Notification Task</h4>
              <div class="space-y-3 mt-3">
                <div>
                  <%= form.label "configuration[recipients]", "Additional Recipients", 
                    class: "block text-sm font-medium text-gray-700" %>
                  <%= form.text_field "configuration[recipients]", 
                    class: "mt-1 block w-full rounded-md border-gray-300",
                    placeholder: "<EMAIL>, <EMAIL>" %>
                  <p class="mt-1 text-xs text-gray-500">
                    Comma-separated email addresses (optional)
                  </p>
                </div>
              </div>
            </div>

          <% when 'approval' %>
            <div class="bg-orange-50 rounded-lg p-4">
              <h4 class="font-semibold text-orange-900 mb-2">Approval Task</h4>
              <p class="text-sm text-orange-700">
                This is an approval checkpoint. By executing this task, you are providing 
                the required approval for the pipeline to continue.
              </p>
            </div>
          <% end %>

          <!-- Warnings -->
          <% if @task.retry_count > 0 %>
            <div class="bg-red-50 rounded-lg p-4">
              <h4 class="font-semibold text-red-900 mb-1">Previous Execution Failed</h4>
              <p class="text-sm text-red-700">
                This task has failed <%= pluralize(@task.retry_count, 'time') %>. 
                <% if @task.error_message.present? %>
                  Last error: <%= @task.error_message %>
                <% end %>
              </p>
            </div>
          <% end %>

          <!-- Confirmation -->
          <div class="bg-gray-50 rounded-lg p-4">
            <label class="flex items-start">
              <%= form.check_box :confirm, class: "mt-1 mr-2" %>
              <span class="text-sm text-gray-700">
                I understand this action will execute the task immediately and may affect the connected systems.
              </span>
            </label>
          </div>
        </div>

        <div class="mt-6 flex items-center justify-between">
          <div>
            <%= link_to "Cancel", manual_task_path(@task), class: "btn btn-ghost" %>
          </div>
          <div class="flex items-center gap-2">
            <%= form.submit "Execute Task", 
              class: "btn btn-primary",
              data: { 
                disable_with: "Executing...",
                turbo_confirm: "Are you sure you want to execute this task?"
              } %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>