<div class="max-w-3xl mx-auto">
  <div class="mb-6">
    <%= link_to manual_task_path(@task), class: "text-blue-600 hover:text-blue-800 flex items-center gap-1" do %>
      ← Back to Task Details
    <% end %>
  </div>

  <div class="bg-white rounded-lg shadow">
    <div class="p-6 border-b">
      <h1 class="text-2xl font-bold text-gray-900">Approve Task</h1>
      <p class="mt-2 text-gray-600">Review and approve this task to allow the pipeline to continue.</p>
    </div>

    <div class="p-6 space-y-6">
      <!-- Task summary -->
      <div class="bg-blue-50 rounded-lg p-4">
        <h3 class="font-semibold mb-2"><%= @task.name %></h3>
        <p class="text-gray-600 text-sm mb-3"><%= @task.description %></p>
        <div class="flex items-center gap-4 text-sm">
          <span class="flex items-center gap-1">
            <strong>Type:</strong> <%= @task.task_type.humanize %>
          </span>
          <span class="flex items-center gap-1">
            <strong>Priority:</strong> <%= @task.priority %>
          </span>
          <span class="flex items-center gap-1">
            <strong>Pipeline:</strong> <%= @task.pipeline_execution.pipeline_name %>
          </span>
        </div>
      </div>

      <!-- Approval requirements -->
      <div>
        <h3 class="font-semibold mb-3">Approval Requirements</h3>
        <div class="bg-gray-50 rounded-lg p-4">
          <ul class="space-y-2">
            <li class="flex items-start">
              <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              <span class="text-sm">All dependent tasks have completed successfully</span>
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              <span class="text-sm">Data validation checks have passed</span>
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              <span class="text-sm">No critical errors in the pipeline execution</span>
            </li>
          </ul>
        </div>
      </div>

      <!-- Task metadata -->
      <% if @task.metadata.present? && @task.metadata['approval_context'].present? %>
        <div>
          <h3 class="font-semibold mb-3">Additional Context</h3>
          <div class="bg-gray-50 rounded-lg p-4">
            <pre class="text-sm whitespace-pre-wrap"><%= @task.metadata['approval_context'] %></pre>
          </div>
        </div>
      <% end %>

      <!-- Recent pipeline activity -->
      <div>
        <h3 class="font-semibold mb-3">Recent Pipeline Activity</h3>
        <div class="space-y-2">
          <% @task.pipeline_execution.tasks.completed.recent.limit(5).each do |recent_task| %>
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
              <div>
                <span class="font-medium text-sm"><%= recent_task.name %></span>
                <span class="text-xs text-gray-500 ml-2">
                  Completed <%= time_ago_in_words(recent_task.completed_at) %> ago
                </span>
              </div>
              <span class="bg-green-100 text-green-800 px-2 py-1 text-xs rounded-full">Completed</span>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Approval form -->
      <%= form_with url: approve_manual_task_path(@task), method: :post, local: true do |form| %>
        <div class="space-y-4">
          <!-- Approval notes -->
          <div>
            <%= form.label :approval_notes, "Approval Notes", class: "block text-sm font-medium text-gray-700 mb-1" %>
            <%= form.text_area :approval_notes, 
              rows: 3,
              class: "block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500",
              placeholder: "Add any notes about this approval (optional)..." %>
            <p class="mt-1 text-sm text-gray-500">
              These notes will be recorded in the task history.
            </p>
          </div>

          <!-- Warning -->
          <div class="bg-amber-50 rounded-lg p-4">
            <div class="flex">
              <svg class="w-5 h-5 text-amber-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-amber-800">Important</h3>
                <p class="mt-1 text-sm text-amber-700">
                  By approving this task, you authorize the pipeline to continue with the next steps. 
                  This action cannot be undone.
                </p>
              </div>
            </div>
          </div>

          <!-- Confirmation -->
          <div class="bg-gray-50 rounded-lg p-4">
            <label class="flex items-start">
              <%= form.check_box :confirm, class: "mt-1 mr-2" %>
              <span class="text-sm text-gray-700">
                I have reviewed the task details and approve this action to proceed.
              </span>
            </label>
          </div>
        </div>

        <div class="mt-6 flex items-center justify-between">
          <div>
            <%= link_to "Cancel", manual_task_path(@task), class: "btn btn-ghost" %>
          </div>
          <div class="flex items-center gap-2">
            <%= link_to "Reject Instead", reject_manual_task_path(@task), 
              class: "btn btn-secondary" %>
            <%= form.submit "Approve Task", 
              class: "btn btn-primary",
              data: { 
                disable_with: "Approving...",
                turbo_confirm: "Are you sure you want to approve this task?"
              } %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>
