<% content_for :title, "Sign In - DataReflow" %>

<div class="min-h-screen flex items-center justify-center relative bg-gradient-to-br from-slate-50 via-white to-indigo-50">
  <!-- Subtle Background Pattern -->
  <div class="absolute inset-0">
    <div class="absolute inset-0 bg-[linear-gradient(to_right,#80808012_1px,transparent_1px),linear-gradient(to_bottom,#80808012_1px,transparent_1px)] bg-[size:24px_24px]"></div>
  </div>
  
  <!-- Professional Accent Elements -->
  <div class="absolute top-0 right-0 w-[40rem] h-[40rem] bg-gradient-to-br from-indigo-100/20 to-purple-100/20 rounded-full blur-3xl"></div>
  <div class="absolute bottom-0 left-0 w-[40rem] h-[40rem] bg-gradient-to-tr from-blue-100/20 to-indigo-100/20 rounded-full blur-3xl"></div>

  <!-- Main Container -->
  <div class="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
    <div class="flex flex-col lg:flex-row items-center justify-center gap-12 lg:gap-20">
      
      <!-- Left Side - Branding -->
      <div class="w-full lg:w-1/2 max-w-xl text-center lg:text-left">
        <!-- Logo -->
        <div class="flex items-center justify-center lg:justify-start mb-8">
          <div class="h-16 w-16 bg-gradient-to-br from-indigo-600 to-indigo-700 rounded-2xl flex items-center justify-center shadow-lg">
            <svg class="h-9 w-9 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
              <path stroke-linecap="round" stroke-linejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
          <div class="ml-4">
            <h1 class="text-3xl font-bold text-gray-900">DataReflow</h1>
            <p class="text-sm text-gray-600">Enterprise Data Platform</p>
          </div>
        </div>

        <!-- Welcome Message -->
        <div class="mb-10">
          <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
            Welcome back to your
            <span class="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600">
              data hub
            </span>
          </h2>
          <p class="text-xl text-gray-600 leading-relaxed">
            Transform your business with real-time insights and AI-powered analytics.
          </p>
        </div>

        <!-- Features -->
        <div class="space-y-4 mb-8">
          <div class="flex items-center text-gray-700">
            <div class="h-10 w-10 bg-green-100 rounded-lg flex items-center justify-center mr-4">
              <svg class="h-5 w-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
              </svg>
            </div>
            <div>
              <div class="font-semibold">Real-time Analytics</div>
              <div class="text-sm text-gray-600">Live dashboards updated every minute</div>
            </div>
          </div>

          <div class="flex items-center text-gray-700">
            <div class="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
              <svg class="h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
            </div>
            <div>
              <div class="font-semibold">AI-Powered Insights</div>
              <div class="text-sm text-gray-600">Smart recommendations from your data</div>
            </div>
          </div>

          <div class="flex items-center text-gray-700">
            <div class="h-10 w-10 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
              <svg class="h-5 w-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
              </svg>
            </div>
            <div>
              <div class="font-semibold">Enterprise Security</div>
              <div class="text-sm text-gray-600">SOC2 compliant with 256-bit encryption</div>
            </div>
          </div>
        </div>

        <!-- Stats -->
        <div class="grid grid-cols-3 gap-4 p-6 bg-white/50 backdrop-blur-sm rounded-2xl border border-gray-200/50">
          <div class="text-center">
            <div class="text-2xl font-bold text-gray-900">99.9%</div>
            <div class="text-xs text-gray-600">Uptime SLA</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-gray-900">15+</div>
            <div class="text-xs text-gray-600">Integrations</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-gray-900">24/7</div>
            <div class="text-xs text-gray-600">Support</div>
          </div>
        </div>
      </div>

      <!-- Right Side - Sign In Form -->
      <div class="w-full lg:w-1/2 max-w-md">
        <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl border border-gray-200/50 p-8 lg:p-10">
          <!-- Form Header -->
          <div class="text-center mb-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-2">Sign in to your account</h3>
            <p class="text-gray-600">
              Or
              <%= link_to "start your free trial", new_user_registration_path, 
                  class: "font-medium text-indigo-600 hover:text-indigo-700" %>
            </p>
          </div>

          <!-- Form -->
          <%= form_for(resource, as: resource_name, url: session_path(resource_name), local: true, html: { class: "space-y-6" }) do |f| %>
            <%= render "devise/shared/error_messages", resource: resource %>
            
            <!-- Email Field -->
            <div>
              <%= f.label :email, "Email address", class: "block text-sm font-medium text-gray-700 mb-2" %>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                  </svg>
                </div>
                <%= f.email_field :email, 
                    autofocus: true, 
                    autocomplete: "email",
                    class: "block w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors",
                    placeholder: "<EMAIL>" %>
              </div>
            </div>

            <!-- Password Field -->
            <div>
              <%= f.label :password, class: "block text-sm font-medium text-gray-700 mb-2" %>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                  </svg>
                </div>
                <%= f.password_field :password, 
                    autocomplete: "current-password",
                    class: "block w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors",
                    placeholder: "Enter your password" %>
              </div>
            </div>

            <!-- Remember Me & Forgot Password -->
            <div class="flex items-center justify-between">
              <% if devise_mapping.rememberable? %>
                <div class="flex items-center">
                  <%= f.check_box :remember_me, class: "h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500" %>
                  <%= f.label :remember_me, class: "ml-2 text-sm text-gray-700" %>
                </div>
              <% end %>
              
              <%= link_to "Forgot password?", new_password_path(resource_name), 
                  class: "text-sm font-medium text-indigo-600 hover:text-indigo-700" %>
            </div>

            <!-- Submit Button -->
            <div>
              <%= f.submit "Sign in", 
                  class: "w-full py-3 px-4 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white font-semibold rounded-xl shadow-lg hover:from-indigo-700 hover:to-indigo-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 transform hover:-translate-y-0.5" %>
            </div>
          <% end %>

          <!-- Divider -->
          <div class="relative my-8">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-300"></div>
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-4 bg-white text-gray-500">Or continue with</span>
            </div>
          </div>

          <!-- Social Login -->
          <div class="grid grid-cols-2 gap-4">
            <button class="flex items-center justify-center px-4 py-3 bg-white border border-gray-300 rounded-xl shadow-sm hover:bg-gray-50 transition-colors">
              <svg class="h-5 w-5" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              <span class="ml-2 text-gray-700">Google</span>
            </button>
            
            <button class="flex items-center justify-center px-4 py-3 bg-white border border-gray-300 rounded-xl shadow-sm hover:bg-gray-50 transition-colors">
              <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
              </svg>
              <span class="ml-2 text-gray-700">GitHub</span>
            </button>
          </div>
        </div>

        <!-- Security Badge -->
        <div class="mt-6 text-center">
          <div class="inline-flex items-center text-sm text-gray-500">
            <svg class="h-4 w-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
            </svg>
            Secured by 256-bit SSL encryption
          </div>
        </div>
      </div>
    </div>
  </div>
</div>