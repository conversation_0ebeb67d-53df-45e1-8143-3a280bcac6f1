<% content_for :title, "Reset Your Password - DataReflow" %>

<div class="min-h-screen flex items-center justify-center relative bg-gradient-to-br from-slate-50 via-white to-indigo-50">
  <!-- Subtle Background Pattern -->
  <div class="absolute inset-0">
    <div class="absolute inset-0 bg-[linear-gradient(to_right,#80808012_1px,transparent_1px),linear-gradient(to_bottom,#80808012_1px,transparent_1px)] bg-[size:24px_24px]"></div>
  </div>
  
  <!-- Professional Accent Elements -->
  <div class="absolute top-0 right-0 w-[30rem] h-[30rem] bg-gradient-to-br from-green-100/20 to-blue-100/20 rounded-full blur-3xl"></div>
  <div class="absolute bottom-0 left-0 w-[30rem] h-[30rem] bg-gradient-to-tr from-indigo-100/20 to-green-100/20 rounded-full blur-3xl"></div>

  <!-- Main Container -->
  <div class="w-full max-w-lg mx-auto px-4 sm:px-6 lg:px-8 py-12 relative z-10">
    <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl border border-gray-200/50 p-8 lg:p-10">
      <!-- Success Icon -->
      <div class="w-20 h-20 bg-gradient-to-br from-green-100 to-emerald-100 rounded-full flex items-center justify-center mx-auto mb-6">
        <svg class="h-10 w-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
          <path stroke-linecap="round" stroke-linejoin="round" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
        </svg>
      </div>

      <!-- Header -->
      <div class="text-center mb-8">
        <h2 class="text-3xl font-bold text-gray-900 mb-2">Create new password</h2>
        <p class="text-gray-600">
          Your new password must be different from previous used passwords.
        </p>
      </div>

      <!-- Form -->
      <%= form_for(resource, as: resource_name, url: password_path(resource_name), html: { method: :put, class: "space-y-6", data: { controller: "password-form" } }) do |f| %>
        <%= render "devise/shared/error_messages", resource: resource %>
        <%= f.hidden_field :reset_password_token %>

        <!-- New Password Field -->
        <div>
          <%= f.label :password, "New password", class: "block text-sm font-medium text-gray-700 mb-2" do %>
            New password
            <span class="text-xs text-gray-500 ml-1">(<%= @minimum_password_length || 6 %> characters minimum)</span>
          <% end %>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
              </svg>
            </div>
            <%= f.password_field :password, 
                autofocus: true, 
                autocomplete: "new-password",
                class: "block w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors",
                placeholder: "Enter your new password",
                data: { password_form_target: "passwordInput" } %>
          </div>
          <!-- Password strength indicator -->
          <div class="mt-2">
            <div class="h-1 w-full bg-gray-200 rounded-full overflow-hidden">
              <div class="h-full bg-gradient-to-r from-red-500 to-green-500 transition-all duration-300" style="width: 0%" data-password-form-target="strengthBar"></div>
            </div>
            <p class="text-xs text-gray-500 mt-1">Use a mix of letters, numbers, and symbols</p>
          </div>
        </div>

        <!-- Confirm Password Field -->
        <div>
          <%= f.label :password_confirmation, "Confirm new password", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <%= f.password_field :password_confirmation, 
                autocomplete: "new-password",
                class: "block w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors",
                placeholder: "Confirm your new password",
                data: { password_form_target: "confirmationInput" } %>
          </div>
        </div>

        <!-- Password Requirements -->
        <div class="bg-blue-50/50 rounded-xl p-4">
          <h4 class="text-sm font-medium text-gray-900 mb-2">Password must contain:</h4>
          <ul class="space-y-1 text-sm text-gray-600">
            <li class="flex items-center password-requirement" data-requirement="length">
              <svg class="h-4 w-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              At least <%= @minimum_password_length || 6 %> characters
            </li>
            <li class="flex items-center password-requirement" data-requirement="uppercase">
              <svg class="h-4 w-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              One uppercase letter
            </li>
            <li class="flex items-center password-requirement" data-requirement="number-special">
              <svg class="h-4 w-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              One number or special character
            </li>
          </ul>
        </div>

        <!-- Submit Button -->
        <div>
          <%= f.submit "Reset password", 
              class: "w-full py-3 px-4 bg-gradient-to-r from-green-600 to-green-700 text-white font-semibold rounded-xl shadow-lg hover:from-green-700 hover:to-green-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 transform hover:-translate-y-0.5" %>
        </div>
      <% end %>

      <!-- Back to Sign In -->
      <div class="mt-8 text-center">
        <p class="text-sm text-gray-600">
          <%= link_to "Back to sign in", new_user_session_path, 
              class: "font-medium text-indigo-600 hover:text-indigo-700 relative group" %>
        </p>
      </div>
    </div>

    <!-- Security Note -->
    <div class="mt-6 text-center">
      <div class="inline-flex items-center text-sm text-gray-500">
        <svg class="h-4 w-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
        </svg>
        Your password will be updated immediately
      </div>
    </div>
  </div>
</div>
