<% content_for :title, "Reset Password - DataReflow" %>

<div class="min-h-screen flex items-center justify-center relative bg-gradient-to-br from-slate-50 via-white to-indigo-50">
  <!-- Subtle Background Pattern -->
  <div class="absolute inset-0">
    <div class="absolute inset-0 bg-[linear-gradient(to_right,#80808012_1px,transparent_1px),linear-gradient(to_bottom,#80808012_1px,transparent_1px)] bg-[size:24px_24px]"></div>
  </div>
  
  <!-- Professional Accent Elements -->
  <div class="absolute top-0 left-0 w-[30rem] h-[30rem] bg-gradient-to-br from-blue-100/20 to-indigo-100/20 rounded-full blur-3xl"></div>
  <div class="absolute bottom-0 right-0 w-[30rem] h-[30rem] bg-gradient-to-tr from-purple-100/20 to-blue-100/20 rounded-full blur-3xl"></div>

  <!-- Main Container -->
  <div class="w-full max-w-lg mx-auto px-4 sm:px-6 lg:px-8 py-12 relative z-10">
    <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl border border-gray-200/50 p-8 lg:p-10">
      <!-- Icon -->
      <div class="w-20 h-20 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full flex items-center justify-center mx-auto mb-6">
        <svg class="h-10 w-10 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
          <path stroke-linecap="round" stroke-linejoin="round" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
        </svg>
      </div>

      <!-- Header -->
      <div class="text-center mb-8">
        <h2 class="text-3xl font-bold text-gray-900 mb-2">Forgot your password?</h2>
        <p class="text-gray-600">
          No worries! We'll send you reset instructions.
        </p>
      </div>

      <!-- Form -->
      <%= form_for(resource, as: resource_name, url: password_path(resource_name), html: { method: :post, class: "space-y-6" }) do |f| %>
        <%= render "devise/shared/error_messages", resource: resource %>

        <!-- Email Field -->
        <div>
          <%= f.label :email, "Email address", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
              </svg>
            </div>
            <%= f.email_field :email, 
                autofocus: true, 
                autocomplete: "email",
                class: "block w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors",
                placeholder: "Enter your email address" %>
          </div>
          <p class="mt-2 text-sm text-gray-500">
            We'll send a password reset link to this email address.
          </p>
        </div>

        <!-- Submit Button -->
        <div>
          <%= f.submit "Send reset instructions", 
              class: "w-full py-3 px-4 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white font-semibold rounded-xl shadow-lg hover:from-indigo-700 hover:to-indigo-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 transform hover:-translate-y-0.5" %>
        </div>
      <% end %>

      <!-- Back to Sign In -->
      <div class="mt-8 text-center">
        <p class="text-sm text-gray-600">
          Remember your password?
          <%= link_to "Back to sign in", new_user_session_path, 
              class: "font-medium text-indigo-600 hover:text-indigo-700 relative group" %>
        </p>
      </div>

      <!-- Help Section -->
      <div class="mt-8 pt-8 border-t border-gray-200">
        <div class="bg-blue-50/50 rounded-xl p-4">
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-blue-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-blue-900">Need help?</h3>
              <p class="text-sm text-blue-700 mt-1">
                If you're having trouble accessing your account, 
                <%= link_to "contact support", "#", class: "font-medium underline hover:no-underline" %>
                for assistance.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Security Note -->
    <div class="mt-6 text-center">
      <div class="inline-flex items-center text-sm text-gray-500">
        <svg class="h-4 w-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
        </svg>
        Password reset links expire after 24 hours
      </div>
    </div>
  </div>
</div>
