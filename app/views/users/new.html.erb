<% content_for :title, "Invite User" %>

<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
  <!-- Header -->
  <div class="bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600 shadow-2xl">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="flex items-center gap-4">
        <%= link_to users_path, class: "h-10 w-10 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm hover:bg-white/30 transition-all duration-200" do %>
          <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
        <% end %>
        <div>
          <h1 class="text-3xl font-bold text-white">Invite Team Member</h1>
          <p class="text-indigo-100 mt-1">Add a new user to your organization</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Content -->
  <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="bg-gradient-to-br from-white to-gray-50/50 rounded-3xl shadow-xl border border-gray-100/50 p-8">
      <%= form_with model: @user, local: true, class: "space-y-6" do |form| %>
        <% if @user.errors.any? %>
          <div class="bg-red-50 border border-red-200 rounded-2xl p-4">
            <div class="flex items-center gap-3 mb-3">
              <div class="h-8 w-8 bg-red-500 rounded-xl flex items-center justify-center">
                <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h4 class="text-red-800 font-semibold">Please fix the following errors:</h4>
            </div>
            <ul class="list-disc list-inside text-red-700 space-y-1">
              <% @user.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        <% end %>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <%= form.label :first_name, class: "block text-sm font-semibold text-gray-700 mb-2" %>
            <%= form.text_field :first_name, 
                class: "w-full px-4 py-3 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200",
                placeholder: "Enter first name" %>
          </div>

          <div>
            <%= form.label :last_name, class: "block text-sm font-semibold text-gray-700 mb-2" %>
            <%= form.text_field :last_name, 
                class: "w-full px-4 py-3 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200",
                placeholder: "Enter last name" %>
          </div>
        </div>

        <div>
          <%= form.label :email, class: "block text-sm font-semibold text-gray-700 mb-2" %>
          <%= form.email_field :email, 
              class: "w-full px-4 py-3 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200",
              placeholder: "Enter email address" %>
        </div>

        <div>
          <%= form.label :role, class: "block text-sm font-semibold text-gray-700 mb-2" %>
          <%= form.select :role, 
              options_for_select([
                ['Member', 'member'],
                ['Manager', 'manager'],
                ['Admin', 'admin']
              ], @user.role),
              { prompt: 'Select a role' },
              { class: "w-full px-4 py-3 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200" } %>
          <p class="mt-2 text-sm text-gray-500">
            Choose the appropriate role for this user's permissions level.
          </p>
        </div>

        <div class="bg-blue-50 border border-blue-200 rounded-2xl p-4">
          <div class="flex items-start gap-3">
            <div class="h-6 w-6 bg-blue-500 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="h-3 w-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h4 class="text-blue-800 font-semibold text-sm mb-1">Invitation Process</h4>
              <p class="text-blue-700 text-sm">
                An invitation email will be sent to the user with instructions to set up their account and join your organization.
              </p>
            </div>
          </div>
        </div>

        <div class="flex items-center gap-4 pt-4">
          <%= form.submit "Send Invitation", 
              class: "flex-1 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-2xl transition-all duration-200 transform hover:scale-105 shadow-lg" %>
          <%= link_to "Cancel", users_path, 
              class: "flex-1 bg-white hover:bg-gray-50 text-gray-700 font-semibold py-3 px-6 rounded-2xl border-2 border-gray-300 hover:border-gray-400 transition-all duration-200 text-center" %>
        </div>
      <% end %>
    </div>
  </div>
</div>