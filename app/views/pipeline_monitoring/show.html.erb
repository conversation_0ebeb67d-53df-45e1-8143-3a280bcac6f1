<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
  <!-- Enhanced <PERSON><PERSON> with Glassmorphism -->
  <div class="relative bg-white/80 backdrop-blur-xl border-b border-white/20 shadow-xl">
    <div class="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-indigo-600/10"></div>
    <div class="relative px-4 sm:px-6 lg:px-8 py-8">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <%= link_to pipeline_monitoring_index_path, class: "group p-2 text-slate-500 hover:text-slate-700 bg-white/70 hover:bg-white/90 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-0.5" do %>
            <svg class="h-5 w-5 group-hover:-translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
          <% end %>
          <div class="space-y-2">
            <div class="flex items-center space-x-3">
              <div class="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg">
                <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 via-blue-900 to-indigo-900 bg-clip-text text-transparent">
                <%= @execution.pipeline_name %>
              </h1>
            </div>
            <div class="flex items-center text-sm text-slate-600 space-x-4">
              <div class="flex items-center space-x-1">
                <svg class="h-4 w-4 text-slate-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
                </svg>
                <span>ID: <%= @execution.execution_id %></span>
              </div>
              <div class="flex items-center space-x-1">
                <svg class="h-4 w-4 text-slate-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Started <%= time_ago_in_words(@execution.started_at) %> ago</span>
              </div>
              <% if @execution.user %>
                <div class="flex items-center space-x-1">
                  <svg class="h-4 w-4 text-slate-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  <span>by <%= @execution.user.full_name %></span>
                </div>
              <% end %>
            </div>
          </div>
        </div>
        
        <div class="flex items-center space-x-4">
          <!-- Premium Status Badge -->
          <% case @execution.status %>
          <% when 'running' %>
            <div class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border border-blue-200/50 shadow-lg">
              <span class="mr-2 relative flex h-3 w-3">
                <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-blue-400 opacity-75"></span>
                <span class="relative inline-flex rounded-full h-3 w-3 bg-blue-500"></span>
              </span>
              Running
            </div>
          <% when 'completed' %>
            <div class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border border-green-200/50 shadow-lg">
              <svg class="mr-2 h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              Completed
            </div>
          <% when 'failed' %>
            <div class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-gradient-to-r from-red-100 to-rose-100 text-red-800 border border-red-200/50 shadow-lg">
              <svg class="mr-2 h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
              Failed
            </div>
          <% else %>
            <div class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-gradient-to-r from-slate-100 to-gray-100 text-slate-800 border border-slate-200/50 shadow-lg">
              <%= @execution.status.humanize %>
            </div>
          <% end %>
          
          <!-- Stop Button for Running Executions -->
          <% if @execution.status == 'running' %>
            <button type="button" class="group inline-flex items-center px-4 py-2 border border-red-300/50 shadow-lg text-sm font-semibold rounded-xl text-red-700 bg-white/70 hover:bg-red-50/80 backdrop-blur-sm transition-all duration-300 hover:shadow-xl hover:-translate-y-0.5">
              <svg class="h-4 w-4 mr-2 group-hover:scale-110 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" />
              </svg>
              Stop Execution
            </button>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Premium Progress Overview -->
  <div class="px-4 sm:px-6 lg:px-8 py-8">
    <div class="relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
      <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-purple-500/5"></div>
      <div class="relative">
        <div class="px-6 py-5 border-b border-slate-200/50 bg-gradient-to-r from-indigo-50/50 to-purple-50/50">
          <div class="flex items-center space-x-3">
            <div class="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg shadow-lg">
              <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-bold text-slate-900">Execution Progress</h3>
              <p class="text-sm text-slate-600">Real-time pipeline execution status</p>
            </div>
          </div>
        </div>
        <div class="p-8">
          <!-- Overall Progress -->
          <div class="mb-8">
            <div class="flex items-center justify-between mb-4">
              <span class="text-sm font-semibold text-slate-700 uppercase tracking-wider">Overall Progress</span>
              <span class="text-2xl font-bold text-slate-900"><%= @execution.progress.to_i %>%</span>
            </div>
            <div class="relative">
              <div class="overflow-hidden h-4 text-xs flex rounded-full bg-slate-200 shadow-inner">
                <div style="width: <%= @execution.progress %>%" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gradient-to-r from-indigo-500 to-purple-600 transition-all duration-1000 rounded-full"></div>
              </div>
            </div>
          </div>

          <!-- Stage Progress -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <% ['extraction', 'transformation', 'loading'].each_with_index do |stage, index| %>
              <% is_current = @execution.current_stage == stage %>
              <% is_completed = index < ['extraction', 'transformation', 'loading'].index(@execution.current_stage || 'extraction') %>
              
              <div class="group relative">
                <div class="<%= is_completed ? 'bg-gradient-to-br from-green-100 to-emerald-100 border-green-300' : is_current ? 'bg-gradient-to-br from-blue-100 to-indigo-100 border-blue-300' : 'bg-gradient-to-br from-slate-100 to-gray-100 border-slate-300' %> rounded-2xl border-2 p-6 transition-all duration-500 hover:shadow-lg hover:-translate-y-1">
                  <div class="text-center">
                    <% if is_completed %>
                      <div class="mx-auto w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center shadow-lg mb-4">
                        <svg class="h-6 w-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                      </div>
                    <% elsif is_current %>
                      <div class="mx-auto w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg mb-4">
                        <span class="relative flex h-6 w-6">
                          <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-white opacity-75"></span>
                          <span class="relative inline-flex rounded-full h-6 w-6 bg-white"></span>
                        </span>
                      </div>
                    <% else %>
                      <div class="mx-auto w-12 h-12 bg-gradient-to-br from-slate-300 to-gray-400 rounded-full flex items-center justify-center shadow-lg mb-4">
                        <div class="w-6 h-6 bg-white rounded-full"></div>
                      </div>
                    <% end %>
                    
                    <h4 class="text-lg font-bold <%= is_completed ? 'text-green-900' : is_current ? 'text-blue-900' : 'text-slate-700' %> mb-2">
                      <%= stage.capitalize %>
                    </h4>
                    
                    <% if is_current %>
                      <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-blue-200/50 text-blue-800">
                        <div class="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2 animate-pulse"></div>
                        In Progress
                      </div>
                    <% elsif is_completed %>
                      <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-green-200/50 text-green-800">
                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                        Completed
                      </div>
                    <% else %>
                      <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-slate-200/50 text-slate-600">
                        Pending
                      </div>
                    <% end %>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Premium Details Grid -->
  <div class="px-4 sm:px-6 lg:px-8 pb-8">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Premium Tasks Section -->
      <div class="relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-green-500/5"></div>
        <div class="relative">
          <div class="px-6 py-5 border-b border-slate-200/50 bg-gradient-to-r from-emerald-50/50 to-green-50/50">
            <div class="flex items-center space-x-3">
              <div class="p-2 bg-gradient-to-br from-emerald-500 to-green-600 rounded-lg shadow-lg">
                <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-bold text-slate-900">Tasks</h3>
                <p class="text-sm text-slate-600">Individual task execution status</p>
              </div>
            </div>
          </div>
          <div class="max-h-96 overflow-y-auto">
            <div class="divide-y divide-slate-200/50">
              <% @tasks.each do |task| %>
                <div class="px-6 py-4 hover:bg-gradient-to-r hover:from-slate-50/30 hover:to-white/30 transition-all duration-300">
                  <div class="flex items-center justify-between">
                    <div class="flex-1 space-y-1">
                      <p class="text-sm font-semibold text-slate-900"><%= task.name %></p>
                      <p class="text-xs text-slate-500 flex items-center space-x-1">
                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
                        </svg>
                        <span><%= task.task_type.humanize %></span>
                      </p>
                    </div>
                    <div class="flex items-center space-x-2">
                      <% case task.status %>
                      <% when 'completed' %>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border border-green-200/50">
                          <svg class="mr-1 h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                          </svg>
                          Completed
                        </span>
                      <% when 'running' %>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border border-blue-200/50">
                          <svg class="mr-1 h-3 w-3 animate-spin" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Running
                        </span>
                      <% when 'failed' %>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-red-100 to-rose-100 text-red-800 border border-red-200/50">
                          <svg class="mr-1 h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                          </svg>
                          Failed
                        </span>
                      <% else %>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-slate-100 to-gray-100 text-slate-700 border border-slate-200/50">
                          <%= task.status.humanize %>
                        </span>
                      <% end %>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>

      <!-- Premium Metrics Section -->
      <div class="relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-indigo-500/5"></div>
        <div class="relative">
          <div class="px-6 py-5 border-b border-slate-200/50 bg-gradient-to-r from-purple-50/50 to-indigo-50/50">
            <div class="flex items-center space-x-3">
              <div class="p-2 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg shadow-lg">
                <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-bold text-slate-900">Performance Metrics</h3>
                <p class="text-sm text-slate-600">Execution performance indicators</p>
              </div>
            </div>
          </div>
          <div class="p-6">
            <div class="grid grid-cols-1 gap-6">
              <!-- Duration Metric -->
              <div class="bg-gradient-to-r from-slate-50/50 to-white/50 rounded-xl p-4 border border-slate-200/50">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <div class="p-2 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg">
                      <svg class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <dt class="text-sm font-semibold text-slate-700">Duration</dt>
                      <dd class="text-lg font-bold text-slate-900">
                        <% if @execution.completed_at %>
                          <%= distance_of_time_in_words(@execution.started_at, @execution.completed_at) %>
                        <% else %>
                          <%= distance_of_time_in_words(@execution.started_at, Time.current) %> <span class="text-sm text-slate-500">(ongoing)</span>
                        <% end %>
                      </dd>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Records Processed Metric -->
              <div class="bg-gradient-to-r from-emerald-50/50 to-green-50/50 rounded-xl p-4 border border-emerald-200/50">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <div class="p-2 bg-gradient-to-br from-emerald-500 to-green-600 rounded-lg">
                      <svg class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                      </svg>
                    </div>
                    <div>
                      <dt class="text-sm font-semibold text-slate-700">Records Processed</dt>
                      <dd class="text-lg font-bold text-slate-900">
                        <%= number_with_delimiter(@execution.result_summary&.dig('rows_loaded') || 0) %>
                      </dd>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Processing Rate Metric -->
              <div class="bg-gradient-to-r from-purple-50/50 to-indigo-50/50 rounded-xl p-4 border border-purple-200/50">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <div class="p-2 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg">
                      <svg class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    </div>
                    <div>
                      <dt class="text-sm font-semibold text-slate-700">Processing Rate</dt>
                      <dd class="text-lg font-bold text-slate-900">
                        <% if @performance_metrics[:records_per_second] %>
                          <%= number_with_delimiter(@performance_metrics[:records_per_second].round(2)) %> <span class="text-sm text-slate-500">rec/s</span>
                        <% else %>
                          <span class="text-slate-400">N/A</span>
                        <% end %>
                      </dd>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Execution Mode Metric -->
              <div class="bg-gradient-to-r from-amber-50/50 to-yellow-50/50 rounded-xl p-4 border border-amber-200/50">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <div class="p-2 bg-gradient-to-br from-amber-500 to-yellow-600 rounded-lg">
                      <svg class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    </div>
                    <div>
                      <dt class="text-sm font-semibold text-slate-700">Execution Mode</dt>
                      <dd class="text-lg font-bold text-slate-900">
                        <%= @execution.execution_mode.humanize %>
                      </dd>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Premium Logs Section -->
  <div class="px-4 sm:px-6 lg:px-8 pb-8">
    <div class="relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
      <div class="absolute inset-0 bg-gradient-to-br from-slate-500/5 to-gray-500/5"></div>
      <div class="relative">
        <div class="px-6 py-5 border-b border-slate-200/50 bg-gradient-to-r from-slate-50/50 to-gray-50/50">
          <div class="flex items-center space-x-3">
            <div class="p-2 bg-gradient-to-br from-slate-600 to-gray-700 rounded-lg shadow-lg">
              <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-bold text-slate-900">Execution Logs</h3>
              <p class="text-sm text-slate-600">Real-time execution log stream</p>
            </div>
          </div>
        </div>
        <div class="p-6 max-h-96 overflow-y-auto bg-slate-900/95 backdrop-blur-sm">
          <div class="space-y-2 font-mono text-sm">
            <% @execution_logs.each do |log| %>
              <div class="flex items-start space-x-3 py-1">
                <span class="text-slate-400 shrink-0 text-xs">
                  <%= log[:timestamp].strftime('%H:%M:%S.%L') %>
                </span>
                <span class="<%= log[:level] == 'error' ? 'text-red-400' : 'text-slate-100' %> flex-1">
                  <span class="text-slate-500">[<%= log[:level].upcase %>]</span> <%= log[:message] %>
                </span>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Live Update Indicator -->
  <% if @execution.status == 'running' %>
    <div class="fixed bottom-6 right-6 z-50">
      <div class="flex items-center space-x-2 px-4 py-2 bg-white/90 backdrop-blur-xl rounded-full shadow-xl border border-white/20">
        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
        <span class="text-xs font-semibold text-slate-700">Live Updates</span>
      </div>
    </div>
  <% end %>
</div>

<!-- Auto-refresh for running executions -->
<% if @execution.status == 'running' %>
  <%= turbo_stream_from "pipeline_execution_#{@execution.id}" %>
<% end %>

<!-- Enhanced JavaScript for Premium UX -->
<script>
  // Add smooth scroll behavior
  document.documentElement.style.scrollBehavior = 'smooth';

  // Add intersection observer for card animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.style.opacity = '1';
        entry.target.style.transform = 'translateY(0)';
      }
    });
  }, observerOptions);

  // Observe all cards for animation
  document.querySelectorAll('.relative.bg-white\\/80').forEach(card => {
    card.style.opacity = '0';
    card.style.transform = 'translateY(20px)';
    card.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';
    observer.observe(card);
  });
</script>