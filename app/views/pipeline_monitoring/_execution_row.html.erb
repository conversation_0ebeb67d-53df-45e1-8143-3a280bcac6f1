<div class="group px-6 py-4 hover:bg-gradient-to-r hover:from-blue-50/30 hover:to-indigo-50/30 transition-all duration-300 border-b border-slate-200/30 last:border-b-0">
  <div class="flex items-center justify-between">
    <div class="flex-1 min-w-0 space-y-2">
      <div class="flex items-center space-x-3">
        <p class="text-sm font-semibold text-slate-900 truncate group-hover:text-slate-700">
          <%= execution.pipeline_name %>
        </p>
        <% if execution.status == 'running' %>
          <div class="flex items-center space-x-1">
            <span class="relative flex h-3 w-3">
              <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-blue-400 opacity-75"></span>
              <span class="relative inline-flex rounded-full h-3 w-3 bg-blue-500 shadow-lg"></span>
            </span>
            <span class="text-xs font-medium text-blue-600">Live</span>
          </div>
        <% end %>
      </div>
      
      <div class="flex items-center text-xs text-slate-500 space-x-3">
        <div class="flex items-center space-x-1">
          <svg class="h-3 w-3 text-slate-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>Started <%= time_ago_in_words(execution.started_at) %> ago</span>
        </div>
        <% if execution.user %>
          <div class="flex items-center space-x-1">
            <svg class="h-3 w-3 text-slate-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            <span>by <%= execution.user.first_name %></span>
          </div>
        <% end %>
      </div>
    </div>
    
    <div class="flex items-center space-x-4 ml-4">
      <!-- Enhanced Progress Bar -->
      <div class="flex items-center space-x-3">
        <div class="w-24">
          <div class="relative">
            <div class="overflow-hidden h-2 text-xs flex rounded-full bg-slate-200 shadow-inner">
              <div style="width: <%= execution.progress %>%" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gradient-to-r from-blue-500 to-indigo-600 transition-all duration-1000 rounded-full"></div>
            </div>
          </div>
        </div>
        <span class="text-xs font-semibold text-slate-600 min-w-[2.5rem] text-right"><%= execution.progress.to_i %>%</span>
      </div>
      
      <!-- Premium Stage Indicator -->
      <span class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border border-blue-200/50 shadow-sm">
        <div class="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2 animate-pulse"></div>
        <%= execution.current_stage&.humanize || 'Initializing' %>
      </span>
      
      <!-- Premium View Details Button -->
      <%= link_to pipeline_monitoring_path(execution), class: "group/link flex items-center justify-center w-8 h-8 text-indigo-600 hover:text-indigo-800 bg-indigo-50/50 hover:bg-indigo-100/80 rounded-lg transition-all duration-300 hover:shadow-md hover:scale-110", data: { turbo_frame: "_top" } do %>
        <svg class="h-4 w-4 group-hover/link:translate-x-0.5 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
        </svg>
      <% end %>
    </div>
  </div>
</div>