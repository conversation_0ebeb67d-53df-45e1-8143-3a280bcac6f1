<div class="min-h-screen bg-gray-50">
  <!-- Professional Header -->
  <div class="bg-white border-b border-gray-200 shadow-sm">
    <div class="px-4 sm:px-6 lg:px-8 py-6">
      <div class="flex items-center justify-between">
        <div>
          <div class="flex items-center space-x-3">
            <div class="p-2 bg-indigo-600 rounded-lg shadow-sm">
              <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h1 class="text-2xl font-semibold text-gray-900">
              Pipeline Monitoring
            </h1>
          </div>
          <p class="mt-1 text-sm text-gray-600">Real-time monitoring and analytics for your data pipelines</p>
        </div>
        <div class="flex items-center space-x-3">
          <div class="flex items-center space-x-2 px-3 py-1.5 bg-green-50 rounded-full border border-green-200">
            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
            <span class="text-green-700 text-sm font-medium">Live</span>
          </div>
          <%= link_to system_health_pipeline_monitoring_index_path, class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 shadow-sm transition-colors duration-200" do %>
            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            System Health
          <% end %>
          <%= link_to alerts_pipeline_monitoring_index_path, class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 shadow-sm transition-colors duration-200" do %>
            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
            </svg>
            Alerts
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Premium Alert Banner -->
  <% if @alert_conditions.any? %>
    <div class="relative bg-gradient-to-r from-amber-50/90 to-yellow-50/90 backdrop-blur-xl border-b border-amber-200/50 shadow-lg">
      <div class="absolute inset-0 bg-gradient-to-r from-amber-400/5 to-yellow-400/5"></div>
      <div class="relative px-4 sm:px-6 lg:px-8 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <div class="p-2 bg-gradient-to-br from-amber-500 to-yellow-600 rounded-lg shadow-lg animate-pulse">
              <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </div>
            <div>
              <span class="text-sm font-bold text-amber-900">
                <%= pluralize(@alert_conditions.count, 'Alert Condition') %> Detected
              </span>
              <p class="text-xs text-amber-700 mt-0.5">Immediate attention required</p>
            </div>
          </div>
          <button type="button" class="group p-2 text-amber-400 hover:text-amber-600 bg-white/50 hover:bg-white/80 rounded-lg transition-all duration-300">
            <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Premium Key Metrics -->
  <div class="px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
      <!-- Health Score -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-<%= @pipeline_health[:health_score] >= 80 ? 'green' : @pipeline_health[:health_score] >= 60 ? 'yellow' : 'red' %>-500/5 to-<%= @pipeline_health[:health_score] >= 80 ? 'emerald' : @pipeline_health[:health_score] >= 60 ? 'amber' : 'rose' %>-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-center space-x-4 mb-4">
            <div class="p-3 bg-gradient-to-br from-<%= @pipeline_health[:health_score] >= 80 ? 'green' : @pipeline_health[:health_score] >= 60 ? 'yellow' : 'red' %>-500 to-<%= @pipeline_health[:health_score] >= 80 ? 'emerald' : @pipeline_health[:health_score] >= 60 ? 'amber' : 'rose' %>-600 rounded-xl shadow-lg group-hover:shadow-<%= @pipeline_health[:health_score] >= 80 ? 'green' : @pipeline_health[:health_score] >= 60 ? 'yellow' : 'red' %>-500/25 transition-all duration-300">
              <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="flex-1">
              <h3 class="text-sm font-semibold text-slate-600 uppercase tracking-wider">Health Score</h3>
              <div class="text-3xl font-bold text-slate-900 mt-1"><%= @pipeline_health[:health_score] %>%</div>
            </div>
          </div>
          <div class="w-full bg-slate-200 rounded-full h-2">
            <div class="bg-gradient-to-r from-<%= @pipeline_health[:health_score] >= 80 ? 'green' : @pipeline_health[:health_score] >= 60 ? 'yellow' : 'red' %>-500 to-<%= @pipeline_health[:health_score] >= 80 ? 'emerald' : @pipeline_health[:health_score] >= 60 ? 'amber' : 'rose' %>-600 h-2 rounded-full transition-all duration-1000" style="width: <%= @pipeline_health[:health_score] %>%"></div>
          </div>
          <div class="mt-2 text-xs text-slate-500">
            <% health_status = @pipeline_health[:health_score] >= 80 ? "Excellent" : @pipeline_health[:health_score] >= 60 ? "Good" : "Needs Attention" %>
            Status: <span class="font-semibold text-<%= @pipeline_health[:health_score] >= 80 ? 'green' : @pipeline_health[:health_score] >= 60 ? 'yellow' : 'red' %>-600"><%= health_status %></span>
          </div>
        </div>
      </div>

      <!-- Active Pipelines -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-center space-x-4 mb-4">
            <div class="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
              <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <div class="flex-1">
              <h3 class="text-sm font-semibold text-slate-600 uppercase tracking-wider">Active Pipelines</h3>
              <div class="text-3xl font-bold text-slate-900 mt-1"><%= @pipeline_health[:active_pipelines] %></div>
            </div>
          </div>
          <div class="flex items-center space-x-2 text-xs text-slate-500">
            <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <span>Currently running</span>
          </div>
        </div>
      </div>

      <!-- Success Rate -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-green-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-center space-x-4 mb-4">
            <div class="p-3 bg-gradient-to-br from-emerald-500 to-green-600 rounded-xl shadow-lg group-hover:shadow-emerald-500/25 transition-all duration-300">
              <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <div class="flex-1">
              <h3 class="text-sm font-semibold text-slate-600 uppercase tracking-wider">Success Rate</h3>
              <div class="text-3xl font-bold text-slate-900 mt-1"><%= @pipeline_stats[:success_rate] %>%</div>
            </div>
          </div>
          <div class="w-full bg-slate-200 rounded-full h-2">
            <div class="bg-gradient-to-r from-emerald-500 to-green-600 h-2 rounded-full transition-all duration-1000" style="width: <%= @pipeline_stats[:success_rate] %>%"></div>
          </div>
          <div class="mt-2 text-xs text-slate-500">
            <span>Last 30 days</span>
          </div>
        </div>
      </div>

      <!-- Queue Depth -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-indigo-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-center space-x-4 mb-4">
            <div class="p-3 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-purple-500/25 transition-all duration-300">
              <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            </div>
            <div class="flex-1">
              <h3 class="text-sm font-semibold text-slate-600 uppercase tracking-wider">Queue Depth</h3>
              <div class="text-3xl font-bold text-slate-900 mt-1"><%= @pipeline_health[:queue_depth] %></div>
            </div>
          </div>
          <div class="flex items-center space-x-2 text-xs text-slate-500">
            <% queue_status = @pipeline_health[:queue_depth] > 100 ? "High" : @pipeline_health[:queue_depth] > 50 ? "Medium" : "Normal" %>
            <div class="w-2 h-2 bg-<%= queue_status == 'High' ? 'red' : queue_status == 'Medium' ? 'yellow' : 'green' %>-500 rounded-full"></div>
            <span>Queue status: <span class="font-semibold text-<%= queue_status == 'High' ? 'red' : queue_status == 'Medium' ? 'yellow' : 'green' %>-600"><%= queue_status %></span></span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Premium Activity Timeline -->
  <div class="px-4 sm:px-6 lg:px-8 py-8">
    <div class="relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
      <div class="absolute inset-0 bg-gradient-to-br from-slate-500/5 to-gray-500/5"></div>
      <div class="relative">
        <div class="px-6 py-5 border-b border-slate-200/50 bg-gradient-to-r from-slate-50/50 to-gray-50/50">
          <div class="flex items-center space-x-3">
            <div class="p-2 bg-gradient-to-br from-slate-600 to-gray-700 rounded-lg shadow-lg">
              <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-bold text-slate-900">Pipeline Activity</h3>
              <p class="text-sm text-slate-600">Real-time view of pipeline executions</p>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2">
          <!-- Active Executions -->
          <div class="border-r border-slate-200/50">
            <div class="px-6 py-4 bg-gradient-to-r from-blue-50/50 to-indigo-50/50 border-b border-slate-200/50">
              <div class="flex items-center space-x-2">
                <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <h4 class="text-sm font-bold text-slate-900">Active Executions</h4>
              </div>
            </div>
            <div class="max-h-96 overflow-y-auto">
              <% if @active_executions.any? %>
                <div class="divide-y divide-slate-200/50">
                  <% @active_executions.each do |execution| %>
                    <%= turbo_frame_tag "execution_#{execution.id}" do %>
                      <%= render 'execution_row', execution: execution %>
                    <% end %>
                  <% end %>
                </div>
              <% else %>
                <div class="text-center py-12">
                  <div class="mx-auto w-16 h-16 bg-gradient-to-br from-slate-100 to-slate-200 rounded-full flex items-center justify-center mb-4">
                    <svg class="h-8 w-8 text-slate-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <p class="text-slate-500 font-medium">No active executions</p>
                  <p class="text-xs text-slate-400 mt-1">All pipelines are idle</p>
                </div>
              <% end %>
            </div>
          </div>

          <!-- Recent Completions -->
          <div>
            <div class="px-6 py-4 bg-gradient-to-r from-emerald-50/50 to-green-50/50 border-b border-slate-200/50">
              <div class="flex items-center space-x-2">
                <div class="w-2 h-2 bg-emerald-500 rounded-full"></div>
                <h4 class="text-sm font-bold text-slate-900">Recent Completions</h4>
              </div>
            </div>
            <div class="max-h-96 overflow-y-auto">
              <% if @recent_completions.any? %>
                <div class="divide-y divide-slate-200/50">
                  <% @recent_completions.each do |execution| %>
                    <div class="group px-6 py-4 hover:bg-gradient-to-r hover:from-slate-50/50 hover:to-white/50 transition-all duration-300">
                      <div class="flex items-center justify-between">
                        <div class="flex-1 min-w-0">
                          <p class="text-sm font-semibold text-slate-900 truncate group-hover:text-slate-700">
                            <%= execution.pipeline_name %>
                          </p>
                          <p class="text-xs text-slate-500 mt-1">
                            <%= execution.completed_at ? time_ago_in_words(execution.completed_at) : 'N/A' %> ago
                          </p>
                        </div>
                        <div class="flex items-center space-x-3">
                          <% if execution.status == 'completed' %>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border border-green-200/50">
                              ✓ Success
                            </span>
                          <% else %>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-red-100 to-rose-100 text-red-800 border border-red-200/50">
                              ✗ Failed
                            </span>
                          <% end %>
                          <%= link_to pipeline_monitoring_path(execution), class: "group/link p-2 text-indigo-600 hover:text-indigo-800 bg-indigo-50/50 hover:bg-indigo-100/80 rounded-lg transition-all duration-300 hover:shadow-md" do %>
                            <svg class="h-4 w-4 group-hover/link:translate-x-0.5 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                            </svg>
                          <% end %>
                        </div>
                      </div>
                    </div>
                  <% end %>
                </div>
              <% else %>
                <div class="text-center py-12">
                  <div class="mx-auto w-16 h-16 bg-gradient-to-br from-slate-100 to-slate-200 rounded-full flex items-center justify-center mb-4">
                    <svg class="h-8 w-8 text-slate-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                  </div>
                  <p class="text-slate-500 font-medium">No recent completions</p>
                  <p class="text-xs text-slate-400 mt-1">No pipelines completed in the last 24 hours</p>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Premium Hourly Activity Chart -->
  <div class="px-4 sm:px-6 lg:px-8 py-8">
    <div class="relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
      <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-purple-500/5"></div>
      <div class="relative">
        <div class="px-6 py-5 border-b border-slate-200/50 bg-gradient-to-r from-indigo-50/50 to-purple-50/50">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg shadow-lg">
                <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-bold text-slate-900">24-Hour Activity</h3>
                <p class="text-sm text-slate-600">Pipeline execution trends and patterns</p>
              </div>
            </div>
            <div class="flex items-center space-x-2 px-3 py-1 bg-slate-100/80 rounded-full">
              <div class="w-2 h-2 bg-indigo-500 rounded-full"></div>
              <span class="text-xs font-semibold text-slate-600">24 Hours</span>
            </div>
          </div>
        </div>
        <div class="p-8">
          <div class="h-80" data-controller="pipeline-activity-chart" data-pipeline-activity-chart-data-value="<%= @hourly_metrics.to_json %>">
            <canvas data-pipeline-activity-chart-target="canvas" class="rounded-xl"></canvas>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Live Update Indicator -->
  <div class="fixed bottom-6 right-6 z-50">
    <div class="flex items-center space-x-2 px-4 py-2 bg-white/90 backdrop-blur-xl rounded-full shadow-xl border border-white/20">
      <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
      <span class="text-xs font-semibold text-slate-700">Auto-refresh</span>
    </div>
  </div>
</div>

<!-- Auto-refresh for active executions -->
<%= turbo_stream_from "pipeline_executions_#{current_organization.id}" %>

<!-- Enhanced JavaScript for Premium UX -->
<script>
  // Add smooth scroll behavior
  document.documentElement.style.scrollBehavior = 'smooth';

  // Add intersection observer for card animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.style.opacity = '1';
        entry.target.style.transform = 'translateY(0)';
      }
    });
  }, observerOptions);

  // Observe all cards for animation
  document.querySelectorAll('.group').forEach(card => {
    card.style.opacity = '0';
    card.style.transform = 'translateY(20px)';
    card.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';
    observer.observe(card);
  });

  // Auto-refresh indicator animation
  const indicator = document.querySelector('.fixed.bottom-6');
  if (indicator) {
    setInterval(() => {
      indicator.classList.add('scale-110');
      setTimeout(() => {
        indicator.classList.remove('scale-110');
      }, 200);
    }, 5000);
  }
</script>