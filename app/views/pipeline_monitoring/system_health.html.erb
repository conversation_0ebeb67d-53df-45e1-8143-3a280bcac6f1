<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
  <!-- Enhan<PERSON> <PERSON><PERSON> with Glassmorphism -->
  <div class="relative bg-white/80 backdrop-blur-xl border-b border-white/20 shadow-xl">
    <div class="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-indigo-600/10"></div>
    <div class="relative px-4 sm:px-6 lg:px-8 py-8">
      <div class="flex items-center justify-between">
        <div class="space-y-2">
          <div class="flex items-center space-x-3">
            <div class="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg">
              <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 via-blue-900 to-indigo-900 bg-clip-text text-transparent">
              System Health
            </h1>
          </div>
          <p class="text-slate-600 font-medium">Real-time system monitoring and performance analytics</p>
        </div>
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2 px-4 py-2 bg-green-100/80 backdrop-blur-sm rounded-full border border-green-200/50">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span class="text-green-700 text-sm font-semibold">Live</span>
          </div>
          <%= link_to pipeline_monitoring_index_path, class: "group inline-flex items-center px-6 py-3 border border-slate-300/50 rounded-xl shadow-lg text-sm font-semibold text-slate-700 bg-white/70 backdrop-blur-sm hover:bg-white/90 hover:shadow-xl transition-all duration-300 hover:-translate-y-0.5" do %>
            <svg class="h-4 w-4 mr-2 group-hover:-translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to Monitoring
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Premium System Overview Cards -->
  <div class="px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
      <!-- CPU Usage Card -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-3 mb-4">
                <div class="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
                  <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                  </svg>
                </div>
                <div>
                  <h3 class="text-sm font-semibold text-slate-600 uppercase tracking-wider">CPU Usage</h3>
                  <div class="text-3xl font-bold text-slate-900 mt-1"><%= @resource_usage[:cpu_usage] %>%</div>
                </div>
              </div>
              <div class="w-full bg-slate-200 rounded-full h-2">
                <div class="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-1000" style="width: <%= @resource_usage[:cpu_usage] %>%"></div>
              </div>
              <div class="mt-2 text-xs text-slate-500">
                <% cpu_status = @resource_usage[:cpu_usage] > 80 ? "High" : @resource_usage[:cpu_usage] > 60 ? "Medium" : "Normal" %>
                Status: <span class="font-semibold <%= cpu_status == 'High' ? 'text-red-600' : cpu_status == 'Medium' ? 'text-yellow-600' : 'text-green-600' %>"><%= cpu_status %></span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Memory Usage Card -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-green-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-3 mb-4">
                <div class="p-3 bg-gradient-to-br from-emerald-500 to-green-600 rounded-xl shadow-lg group-hover:shadow-emerald-500/25 transition-all duration-300">
                  <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
                  </svg>
                </div>
                <div>
                  <h3 class="text-sm font-semibold text-slate-600 uppercase tracking-wider">Memory</h3>
                  <div class="text-3xl font-bold text-slate-900 mt-1"><%= @resource_usage[:memory_usage] %>%</div>
                </div>
              </div>
              <div class="w-full bg-slate-200 rounded-full h-2">
                <div class="bg-gradient-to-r from-emerald-500 to-green-600 h-2 rounded-full transition-all duration-1000" style="width: <%= @resource_usage[:memory_usage] %>%"></div>
              </div>
              <div class="mt-2 text-xs text-slate-500">
                <% memory_status = @resource_usage[:memory_usage] > 85 ? "High" : @resource_usage[:memory_usage] > 70 ? "Medium" : "Normal" %>
                Status: <span class="font-semibold <%= memory_status == 'High' ? 'text-red-600' : memory_status == 'Medium' ? 'text-yellow-600' : 'text-green-600' %>"><%= memory_status %></span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Disk Usage Card -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-indigo-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-3 mb-4">
                <div class="p-3 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-purple-500/25 transition-all duration-300">
                  <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                </div>
                <div>
                  <h3 class="text-sm font-semibold text-slate-600 uppercase tracking-wider">Storage</h3>
                  <div class="text-3xl font-bold text-slate-900 mt-1"><%= @resource_usage[:disk_usage] %>%</div>
                </div>
              </div>
              <div class="w-full bg-slate-200 rounded-full h-2">
                <div class="bg-gradient-to-r from-purple-500 to-indigo-600 h-2 rounded-full transition-all duration-1000" style="width: <%= @resource_usage[:disk_usage] %>%"></div>
              </div>
              <div class="mt-2 text-xs text-slate-500">
                <% disk_status = @resource_usage[:disk_usage] > 90 ? "Critical" : @resource_usage[:disk_usage] > 75 ? "High" : "Normal" %>
                Status: <span class="font-semibold <%= disk_status == 'Critical' ? 'text-red-600' : disk_status == 'High' ? 'text-yellow-600' : 'text-green-600' %>"><%= disk_status %></span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Network I/O Card -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-3 mb-4">
                <div class="p-3 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl shadow-lg group-hover:shadow-orange-500/25 transition-all duration-300">
                  <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <div>
                  <h3 class="text-sm font-semibold text-slate-600 uppercase tracking-wider">Network</h3>
                  <div class="text-3xl font-bold text-slate-900 mt-1"><%= @resource_usage[:network_io] %>%</div>
                </div>
              </div>
              <div class="w-full bg-slate-200 rounded-full h-2">
                <div class="bg-gradient-to-r from-orange-500 to-red-600 h-2 rounded-full transition-all duration-1000" style="width: <%= @resource_usage[:network_io] %>%"></div>
              </div>
              <div class="mt-2 text-xs text-slate-500">
                <% network_status = @resource_usage[:network_io] > 80 ? "High" : @resource_usage[:network_io] > 50 ? "Medium" : "Normal" %>
                Status: <span class="font-semibold <%= network_status == 'High' ? 'text-red-600' : network_status == 'Medium' ? 'text-yellow-600' : 'text-green-600' %>"><%= network_status %></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Premium Queue and Worker Status -->
  <div class="px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
      <!-- Enhanced Queue Metrics -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-500/5"></div>
        <div class="relative">
          <div class="px-6 py-5 border-b border-slate-200/50 bg-gradient-to-r from-blue-50/50 to-indigo-50/50">
            <div class="flex items-center space-x-3">
              <div class="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg shadow-lg">
                <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-bold text-slate-900">Queue Status</h3>
                <p class="text-sm text-slate-600">Background job queue metrics</p>
              </div>
            </div>
          </div>
          <div class="p-6">
            <% if @queue_metrics.any? %>
              <div class="space-y-4">
                <% @queue_metrics.each_with_index do |(queue_name, count), index| %>
                  <div class="flex items-center justify-between p-4 bg-gradient-to-r from-slate-50/50 to-white/50 rounded-xl border border-slate-200/50 hover:shadow-md transition-all duration-300">
                    <div class="flex items-center space-x-3">
                      <div class="w-3 h-3 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full shadow-lg"></div>
                      <span class="font-semibold text-slate-900 capitalize"><%= queue_name %></span>
                    </div>
                    <div class="flex items-center space-x-2">
                      <span class="text-2xl font-bold text-slate-900"><%= count %></span>
                      <span class="text-sm text-slate-500 font-medium">jobs</span>
                    </div>
                  </div>
                <% end %>
              </div>
            <% else %>
              <div class="text-center py-12">
                <div class="mx-auto w-16 h-16 bg-gradient-to-br from-slate-100 to-slate-200 rounded-full flex items-center justify-center mb-4">
                  <svg class="h-8 w-8 text-slate-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                  </svg>
                </div>
                <p class="text-slate-500 font-medium">No queue data available</p>
                <p class="text-xs text-slate-400 mt-1">Queue system may not be active</p>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Enhanced Worker Status -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-green-500/5"></div>
        <div class="relative">
          <div class="px-6 py-5 border-b border-slate-200/50 bg-gradient-to-r from-emerald-50/50 to-green-50/50">
            <div class="flex items-center space-x-3">
              <div class="p-2 bg-gradient-to-br from-emerald-500 to-green-600 rounded-lg shadow-lg">
                <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-bold text-slate-900">Worker Status</h3>
                <p class="text-sm text-slate-600">Background worker information</p>
              </div>
            </div>
          </div>
          <div class="p-6">
            <div class="space-y-6">
              <!-- Active Workers -->
              <div class="flex items-center justify-between p-4 bg-gradient-to-r from-emerald-50/50 to-green-50/50 rounded-xl border border-emerald-200/50">
                <div class="flex items-center space-x-3">
                  <div class="p-2 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-lg shadow-sm">
                    <svg class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <span class="font-semibold text-slate-900">Active Workers</span>
                </div>
                <div class="text-2xl font-bold text-emerald-600"><%= @worker_status[:workers] %></div>
              </div>

              <!-- Active Jobs -->
              <div class="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50/50 to-indigo-50/50 rounded-xl border border-blue-200/50">
                <div class="flex items-center space-x-3">
                  <div class="p-2 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg shadow-sm">
                    <svg class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                  </div>
                  <span class="font-semibold text-slate-900">Active Jobs</span>
                </div>
                <div class="text-2xl font-bold text-blue-600"><%= @worker_status[:active_jobs] %></div>
              </div>

              <!-- Failed Jobs -->
              <div class="flex items-center justify-between p-4 bg-gradient-to-r from-red-50/50 to-pink-50/50 rounded-xl border border-red-200/50">
                <div class="flex items-center space-x-3">
                  <div class="p-2 bg-gradient-to-br from-red-500 to-red-600 rounded-lg shadow-sm">
                    <svg class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.864-.833-2.634 0L4.168 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                  <span class="font-semibold text-slate-900">Failed Jobs</span>
                </div>
                <div class="text-2xl font-bold text-red-600"><%= @worker_status[:failed_jobs] %></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Premium Error Trends Chart -->
  <div class="px-4 sm:px-6 lg:px-8 py-8">
    <div class="relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
      <div class="absolute inset-0 bg-gradient-to-br from-slate-500/5 to-gray-500/5"></div>
      <div class="relative">
        <div class="px-6 py-5 border-b border-slate-200/50 bg-gradient-to-r from-slate-50/50 to-gray-50/50">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="p-2 bg-gradient-to-br from-slate-600 to-gray-700 rounded-lg shadow-lg">
                <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-bold text-slate-900">Error Trends</h3>
                <p class="text-sm text-slate-600">7-day error analysis and patterns</p>
              </div>
            </div>
            <div class="flex items-center space-x-2 px-3 py-1 bg-slate-100/80 rounded-full">
              <div class="w-2 h-2 bg-red-500 rounded-full"></div>
              <span class="text-xs font-semibold text-slate-600">7 Days</span>
            </div>
          </div>
        </div>
        <div class="p-8">
          <% if @error_trends.any? %>
            <div class="h-80" data-controller="error-trends-chart" data-error-trends-chart-data-value="<%= @error_trends.to_json %>">
              <canvas data-error-trends-chart-target="canvas" class="rounded-xl"></canvas>
            </div>
          <% else %>
            <div class="text-center py-16">
              <div class="mx-auto w-20 h-20 bg-gradient-to-br from-green-100 to-emerald-200 rounded-full flex items-center justify-center mb-6">
                <svg class="h-10 w-10 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h4 class="text-lg font-bold text-slate-900 mb-2">All Systems Healthy</h4>
              <p class="text-slate-500 font-medium">No errors detected in the past 7 days</p>
              <p class="text-xs text-slate-400 mt-2">Your system is running smoothly</p>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Live Update Indicator -->
  <div class="fixed bottom-6 right-6 z-50">
    <div class="flex items-center space-x-2 px-4 py-2 bg-white/90 backdrop-blur-xl rounded-full shadow-xl border border-white/20">
      <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
      <span class="text-xs font-semibold text-slate-700">Auto-refresh: 30s</span>
    </div>
  </div>
</div>

<!-- Enhanced Auto-refresh with smooth transitions -->
<script>
  let refreshTimer;
  let countdownTimer;
  let secondsLeft = 30;

  function updateCountdown() {
    const indicator = document.querySelector('.fixed.bottom-6 span');
    if (indicator) {
      indicator.textContent = `Auto-refresh: ${secondsLeft}s`;
    }
    secondsLeft--;
    
    if (secondsLeft < 0) {
      // Add smooth fade transition before refresh
      document.body.style.transition = 'opacity 0.3s ease-in-out';
      document.body.style.opacity = '0.9';
      
      setTimeout(() => {
        location.reload();
      }, 300);
    }
  }

  function startRefreshCycle() {
    secondsLeft = 30;
    countdownTimer = setInterval(updateCountdown, 1000);
  }

  // Start the refresh cycle
  startRefreshCycle();

  // Add smooth scroll behavior
  document.documentElement.style.scrollBehavior = 'smooth';

  // Add intersection observer for card animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.style.opacity = '1';
        entry.target.style.transform = 'translateY(0)';
      }
    });
  }, observerOptions);

  // Observe all cards for animation
  document.querySelectorAll('.group').forEach(card => {
    card.style.opacity = '0';
    card.style.transform = 'translateY(20px)';
    card.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';
    observer.observe(card);
  });
</script>