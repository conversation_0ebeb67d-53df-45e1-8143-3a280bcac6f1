<% content_for :page_title, "Pipeline Alerts - DataReflow" %>

<!-- Premium Pipeline Alerts Dashboard -->
<div class="min-h-screen bg-gray-50" role="main" aria-label="Pipeline Alerts Dashboard">

  <!-- Page Header -->
  <div class="bg-white shadow-sm border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Pipeline Alerts</h1>
          <p class="mt-2 text-gray-600">Monitor and manage pipeline alerts and system notifications</p>
        </div>
        <%= link_to pipeline_monitoring_index_path,
            class: "inline-flex items-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700 transition-colors duration-200" do %>
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
          Pipeline Dashboard
        <% end %>
      </div>
    </div>
  </div>

  <!-- Alerts Content -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <% if @alerts.any? %>
      <div class="space-y-6">
        <% @alerts.each do |alert| %>
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
               data-controller="alert-card"
               data-alert-card-alert-id-value="<%= alert.id %>">

            <div class="flex items-start justify-between">
              <div class="flex items-start space-x-4 flex-1">
                <!-- Severity Icon -->
                <div class="flex-shrink-0">
                  <% severity_colors = {
                       'critical' => 'bg-red-100 text-red-600',
                       'high' => 'bg-orange-100 text-orange-600',
                       'medium' => 'bg-yellow-100 text-yellow-600',
                       'low' => 'bg-blue-100 text-blue-600'
                     } %>
                  <div class="w-10 h-10 rounded-lg flex items-center justify-center <%= severity_colors[alert.severity] || 'bg-gray-100 text-gray-600' %>">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                      <% case alert.severity %>
                      <% when 'critical' %>
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                      <% when 'high' %>
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      <% when 'medium' %>
                        <path stroke-linecap="round" stroke-linejoin="round" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      <% when 'low' %>
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      <% else %>
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
                      <% end %>
                    </svg>
                  </div>
                </div>

                <!-- Alert Content -->
                <div class="flex-1 min-w-0">
                  <div class="flex items-center space-x-3 mb-2">
                    <h3 class="text-lg font-semibold text-gray-900 truncate"><%= alert.title %></h3>

                    <!-- Severity Badge -->
                    <% severity_badge_colors = {
                         'critical' => 'bg-red-100 text-red-800 border-red-200',
                         'high' => 'bg-orange-100 text-orange-800 border-orange-200',
                         'medium' => 'bg-yellow-100 text-yellow-800 border-yellow-200',
                         'low' => 'bg-blue-100 text-blue-800 border-blue-200'
                       } %>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border <%= severity_badge_colors[alert.severity] || 'bg-gray-100 text-gray-800 border-gray-200' %>">
                      <%= alert.severity.capitalize %>
                    </span>

                    <!-- Status Badge -->
                    <% status_badge_colors = {
                         'active' => 'bg-red-100 text-red-800 border-red-200',
                         'acknowledged' => 'bg-yellow-100 text-yellow-800 border-yellow-200',
                         'resolved' => 'bg-green-100 text-green-800 border-green-200',
                         'dismissed' => 'bg-gray-100 text-gray-800 border-gray-200'
                       } %>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border <%= status_badge_colors[alert.status] || 'bg-gray-100 text-gray-800 border-gray-200' %>">
                      <%= alert.status.capitalize %>
                    </span>
                  </div>

                  <p class="text-gray-600 mb-3"><%= alert.message %></p>

                  <div class="flex items-center space-x-4 text-sm text-gray-500">
                    <div class="flex items-center space-x-1">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      <span><%= time_ago_in_words(alert.created_at) %> ago</span>
                    </div>

                    <% if alert.data_source %>
                      <div class="flex items-center space-x-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                        </svg>
                        <span><%= alert.data_source.name %></span>
                      </div>
                    <% end %>

                    <% if alert.user %>
                      <div class="flex items-center space-x-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        <span><%= alert.user.full_name %></span>
                      </div>
                    <% end %>
                  </div>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="flex items-center space-x-2 ml-4">
                <% unless alert.resolved? %>
                  <% unless alert.acknowledged? %>
                    <button class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                            data-action="click->alert-card#acknowledge"
                            title="Acknowledge Alert">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                    </button>
                  <% end %>

                  <button class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                          data-action="click->alert-card#resolve"
                          title="Resolve Alert">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </button>
                <% end %>

                <button class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-gray-600 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                        data-action="click->alert-card#dismiss"
                        title="Dismiss Alert">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        <% end %>
      </div>

      <!-- Pagination -->
      <% if @alerts.respond_to?(:total_pages) && @alerts.total_pages > 1 %>
        <div class="mt-8 flex justify-center">
          <%= paginate @alerts %>
        </div>
      <% end %>

    <% else %>
      <!-- Empty State -->
      <div class="text-center py-12">
        <div class="mx-auto w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mb-6">
          <svg class="h-12 w-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No Pipeline Alerts</h3>
        <p class="text-gray-600 mb-6">Great news! There are currently no pipeline alerts requiring your attention.</p>
        <%= link_to pipeline_monitoring_index_path,
            class: "inline-flex items-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700 transition-colors duration-200" do %>
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
          View Pipeline Dashboard
        <% end %>
      </div>
    <% end %>
  </div>
</div>
