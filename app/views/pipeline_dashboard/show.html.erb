<div class="max-w-7xl mx-auto">
  <!-- Pipeline Header -->
  <div class="mb-6 flex items-center justify-between">
    <div>
      <nav class="flex items-center text-sm text-gray-500" aria-label="Breadcrumb">
        <%= link_to "Pipeline Dashboard", pipeline_dashboard_index_path, class: "hover:text-gray-700" %>
        <svg class="mx-2 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
        </svg>
        <span class="text-gray-900"><%= @pipeline_execution.pipeline_name %></span>
      </nav>
      <h1 class="mt-2 text-2xl font-bold text-gray-900"><%= @pipeline_execution.pipeline_name %></h1>
      <p class="mt-1 text-sm text-gray-600">
        Execution ID: <%= @pipeline_execution.execution_id %>
      </p>
    </div>
    
    <div class="flex items-center gap-3">
      <% if @pipeline_execution.status == 'running' %>
        <button class="btn btn-secondary" data-turbo-method="post" data-turbo-action="/pipelines/<%= @pipeline_execution.id %>/pause">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          Pause Pipeline
        </button>
      <% elsif @pipeline_execution.status == 'failed' %>
        <button class="btn btn-primary" data-turbo-method="post" data-turbo-action="/pipelines/<%= @pipeline_execution.id %>/retry">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          Retry Pipeline
        </button>
      <% end %>
    </div>
  </div>

  <!-- Pipeline Overview -->
  <div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
    <!-- Status Card -->
    <div class="bg-white rounded-lg shadow p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm text-gray-600">Status</p>
          <p class="mt-1">
            <span class="<%= @pipeline_execution.status == 'completed' ? 'bg-green-100 text-green-800' : @pipeline_execution.status == 'failed' ? 'bg-red-100 text-red-800' : @pipeline_execution.status == 'running' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800' %> px-3 py-1 text-sm rounded-full font-medium">
              <%= @pipeline_execution.status.humanize %>
            </span>
          </p>
        </div>
        <div class="flex items-center justify-center w-10 h-10 <%= @pipeline_execution.status == 'completed' ? 'bg-green-100' : @pipeline_execution.status == 'failed' ? 'bg-red-100' : @pipeline_execution.status == 'running' ? 'bg-blue-100' : 'bg-gray-100' %> rounded-lg">
          <% if @pipeline_execution.status == 'completed' %>
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          <% elsif @pipeline_execution.status == 'failed' %>
            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          <% elsif @pipeline_execution.status == 'running' %>
            <svg class="w-6 h-6 text-blue-600 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
          <% else %>
            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Progress Card -->
    <div class="bg-white rounded-lg shadow p-6">
      <div>
        <p class="text-sm text-gray-600">Progress</p>
        <div class="mt-2">
          <div class="flex items-center justify-between mb-1">
            <span class="text-2xl font-bold text-gray-900"><%= @progress_details[:percentage] %>%</span>
            <span class="text-xs text-gray-500">
              <%= @progress_details[:completed_weight] %> / <%= @progress_details[:total_weight] %> weighted
            </span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="bg-indigo-600 h-2 rounded-full transition-all duration-300" style="width: <%= @progress_details[:percentage] %>%"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Duration Card -->
    <div class="bg-white rounded-lg shadow p-6">
      <div>
        <p class="text-sm text-gray-600">Duration</p>
        <p class="mt-1 text-2xl font-bold text-gray-900">
          <%= @pipeline_execution.duration_formatted || "In Progress" %>
        </p>
        <p class="text-xs text-gray-500 mt-1">
          Started <%= time_ago_in_words(@pipeline_execution.started_at) %> ago
        </p>
      </div>
    </div>

    <!-- Tasks Summary Card -->
    <div class="bg-white rounded-lg shadow p-6">
      <div>
        <p class="text-sm text-gray-600">Tasks</p>
        <div class="mt-2 grid grid-cols-2 gap-2 text-sm">
          <div class="flex items-center justify-between">
            <span class="text-gray-600">Completed:</span>
            <span class="font-medium text-green-600"><%= @task_statistics[:completed] %></span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-gray-600">In Progress:</span>
            <span class="font-medium text-blue-600"><%= @task_statistics[:in_progress] %></span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-gray-600">Failed:</span>
            <span class="font-medium text-red-600"><%= @task_statistics[:failed] %></span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-gray-600">Pending:</span>
            <span class="font-medium text-gray-600"><%= @task_statistics[:pending] %></span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Tasks List -->
    <div class="lg:col-span-2">
      <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b">
          <h3 class="text-lg font-semibold text-gray-900">Pipeline Tasks</h3>
        </div>
        
        <div class="divide-y divide-gray-200">
          <% @tasks.each do |task| %>
            <%= turbo_frame_tag task do %>
              <div class="p-6 hover:bg-gray-50 transition-colors">
                <div class="flex items-start justify-between">
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center gap-3">
                      <% if task.completed? %>
                        <svg class="w-5 h-5 text-green-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                      <% elsif task.failed? %>
                        <svg class="w-5 h-5 text-red-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                      <% elsif task.in_progress? %>
                        <svg class="w-5 h-5 text-blue-500 flex-shrink-0 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                      <% else %>
                        <svg class="w-5 h-5 text-gray-400 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm0-2a6 6 0 100-12 6 6 0 000 12z" clip-rule="evenodd" />
                        </svg>
                      <% end %>
                      
                      <div class="min-w-0 flex-1">
                        <h4 class="text-sm font-medium text-gray-900 truncate"><%= task.name %></h4>
                        <% if task.description.present? %>
                          <p class="text-xs text-gray-600 truncate"><%= task.description %></p>
                        <% end %>
                      </div>
                    </div>
                    
                    <div class="mt-2 flex items-center gap-4 text-xs">
                      <span class="<%= task.execution_mode_badge_class %> px-2 py-0.5 rounded-full">
                        <%= task.execution_mode.humanize %>
                      </span>
                      <span class="<%= task.status_badge_class %> px-2 py-0.5 rounded-full">
                        <%= task.status.humanize %>
                      </span>
                      <% if task.on_critical_path %>
                        <span class="bg-purple-100 text-purple-800 px-2 py-0.5 rounded-full">
                          Critical Path
                        </span>
                      <% end %>
                      <% if task.duration_seconds %>
                        <span class="text-gray-600">
                          Duration: <%= task.duration_seconds %>s
                        </span>
                      <% end %>
                    </div>
                    
                    <% if task.error_message.present? %>
                      <div class="mt-2 p-2 bg-red-50 rounded text-xs text-red-700">
                        <%= task.error_message %>
                      </div>
                    <% end %>
                  </div>
                  
                  <div class="ml-4 flex-shrink-0">
                    <% if task.execution_mode == 'manual' && task.status == 'ready' %>
                      <%= link_to manual_task_path(task), class: "text-indigo-600 hover:text-indigo-800" do %>
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                        </svg>
                      <% end %>
                    <% end %>
                  </div>
                </div>
              </div>
            <% end %>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Timeline -->
    <div class="lg:col-span-1">
      <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b">
          <h3 class="text-lg font-semibold text-gray-900">Execution Timeline</h3>
        </div>
        
        <div class="p-6">
          <div class="flow-root">
            <ul role="list" class="-mb-8">
              <% @timeline_events.each_with_index do |event, index| %>
                <li>
                  <div class="relative pb-8">
                    <% if index != @timeline_events.length - 1 %>
                      <span class="absolute left-4 top-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                    <% end %>
                    <div class="relative flex space-x-3">
                      <div>
                        <span class="h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white 
                          <%= event[:color] == 'green' ? 'bg-green-500' : event[:color] == 'red' ? 'bg-red-500' : event[:color] == 'blue' ? 'bg-blue-500' : 'bg-gray-400' %>">
                          <% case event[:icon] %>
                          <% when 'play' %>
                            <svg class="h-4 w-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                            </svg>
                          <% when 'check' %>
                            <svg class="h-4 w-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                          <% when 'x' %>
                            <svg class="h-4 w-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                          <% when 'flag' %>
                            <svg class="h-4 w-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                              <path fill-rule="evenodd" d="M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z" clip-rule="evenodd" />
                            </svg>
                          <% else %>
                            <svg class="h-4 w-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            </svg>
                          <% end %>
                        </span>
                      </div>
                      <div class="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                        <div>
                          <p class="text-sm font-medium text-gray-900"><%= event[:title] %></p>
                          <p class="text-xs text-gray-500"><%= event[:description] %></p>
                        </div>
                        <div class="whitespace-nowrap text-right text-xs text-gray-500">
                          <time datetime="<%= event[:time] %>"><%= event[:time].strftime("%I:%M %p") %></time>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>

      <!-- Recent Executions -->
      <div class="bg-white rounded-lg shadow mt-6">
        <div class="p-6 border-b">
          <h3 class="text-lg font-semibold text-gray-900">Recent Task Executions</h3>
        </div>
        
        <div class="p-6">
          <% if @recent_executions.any? %>
            <div class="space-y-3">
              <% @recent_executions.each do |execution| %>
                <div class="text-sm">
                  <div class="flex items-center justify-between">
                    <span class="font-medium text-gray-900 truncate"><%= execution.task.name %></span>
                    <span class="<%= execution.status_badge_class %> px-2 py-0.5 text-xs rounded-full">
                      <%= execution.status.humanize %>
                    </span>
                  </div>
                  <div class="flex items-center justify-between mt-1 text-xs text-gray-500">
                    <span><%= execution.executed_by&.name || "System" %></span>
                    <span><%= time_ago_in_words(execution.started_at) %> ago</span>
                  </div>
                </div>
              <% end %>
            </div>
          <% else %>
            <p class="text-sm text-gray-500 text-center">No executions yet.</p>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Subscribe to real-time updates for this pipeline -->
<%= turbo_stream_from @pipeline_execution %>