<!-- Premium Enhanced Pipeline Dashboard Show -->
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <!-- Enhanced Header with Parallax Background -->
  <div class="relative overflow-hidden bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 text-white">
    <!-- Animated background pattern -->
    <div class="absolute inset-0 opacity-20">
      <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%239C92AC" fill-opacity="0.4"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E'); background-size: 60px 60px;"></div>
    </div>
    
    <div class="relative px-4 sm:px-6 lg:px-8 py-8">
      <!-- Breadcrumb -->
      <nav class="flex mb-4" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-2 text-sm">
          <li>
            <%= link_to pipeline_dashboard_index_path, class: "text-blue-300 hover:text-white transition-colors" do %>
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
              </svg>
            <% end %>
          </li>
          <li class="flex items-center">
            <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
            <span class="ml-2 text-gray-300">Pipeline Dashboard</span>
          </li>
          <li class="flex items-center">
            <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
            <span class="ml-2 text-white font-medium"><%= @pipeline_execution.pipeline_name %></span>
          </li>
        </ol>
      </nav>
      
      <!-- Pipeline Header -->
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-3xl font-bold tracking-tight flex items-center">
            <div class="w-12 h-12 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center mr-4">
              <svg class="w-7 h-7 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
              </svg>
            </div>
            <%= @pipeline_execution.pipeline_name %>
          </h1>
          <p class="mt-2 text-blue-200">
            Started <%= distance_of_time_in_words_to_now(@pipeline_execution.started_at) %> ago
            <% if @pipeline_execution.data_source %>
              • <%= @pipeline_execution.data_source.name %>
            <% end %>
          </p>
        </div>
        
        <!-- Action buttons -->
        <div class="mt-4 sm:mt-0 flex space-x-3">
          <% if @pipeline_execution.can_pause? %>
            <%= button_to pause_pipeline_execution_path(@pipeline_execution), 
              method: :post, 
              class: "px-4 py-2 bg-white/10 backdrop-blur-sm border border-white/20 text-white rounded-lg hover:bg-white/20 transition-all duration-200 flex items-center" do %>
              <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Pause Pipeline
            <% end %>
          <% elsif @pipeline_execution.can_retry? %>
            <%= button_to retry_pipeline_execution_path(@pipeline_execution), 
              method: :post, 
              class: "px-4 py-2 bg-white/10 backdrop-blur-sm border border-white/20 text-white rounded-lg hover:bg-white/20 transition-all duration-200 flex items-center" do %>
              <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Retry Pipeline
            <% end %>
          <% end %>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Main Content -->
  <div class="px-4 sm:px-6 lg:px-8 py-8 -mt-8 relative z-10">
    <!-- Overview Cards with 3D Effect -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Status Card -->
      <div class="group relative">
        <div class="absolute inset-0 bg-gradient-to-r <%= @pipeline_execution.status == 'completed' ? 'from-green-600 to-green-700' : @pipeline_execution.status == 'failed' ? 'from-red-600 to-red-700' : 'from-blue-600 to-blue-700' %> rounded-2xl transform rotate-1 group-hover:rotate-2 transition-transform duration-300"></div>
        <div class="relative bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6 transform transition-transform duration-300 group-hover:-translate-y-0.5">
          <div class="flex items-center justify-between mb-4">
            <div class="p-3 <%= @pipeline_execution.status == 'completed' ? 'bg-green-100 dark:bg-green-900/30' : @pipeline_execution.status == 'failed' ? 'bg-red-100 dark:bg-red-900/30' : 'bg-blue-100 dark:bg-blue-900/30' %> rounded-xl">
              <svg class="w-6 h-6 <%= @pipeline_execution.status == 'completed' ? 'text-green-600 dark:text-green-400' : @pipeline_execution.status == 'failed' ? 'text-red-600 dark:text-red-400' : 'text-blue-600 dark:text-blue-400' %>" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <% if @pipeline_execution.status == 'completed' %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                <% elsif @pipeline_execution.status == 'failed' %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                <% else %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                <% end %>
              </svg>
            </div>
            <% if @pipeline_execution.status == 'running' %>
              <div class="flex space-x-1">
                <div class="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style="animation-delay: 0ms;"></div>
                <div class="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style="animation-delay: 150ms;"></div>
                <div class="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style="animation-delay: 300ms;"></div>
              </div>
            <% end %>
          </div>
          <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</p>
          <p class="text-2xl font-bold text-gray-900 dark:text-gray-100 capitalize"><%= @pipeline_execution.status %></p>
        </div>
      </div>
      
      <!-- Progress Card -->
      <div class="group relative">
        <div class="absolute inset-0 bg-gradient-to-r from-purple-600 to-purple-700 rounded-2xl transform -rotate-1 group-hover:-rotate-2 transition-transform duration-300"></div>
        <div class="relative bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6 transform transition-transform duration-300 group-hover:-translate-y-0.5">
          <div class="mb-4">
            <div class="relative w-20 h-20 mx-auto">
              <svg class="w-20 h-20 transform -rotate-90">
                <circle cx="40" cy="40" r="36" fill="none" stroke="currentColor" stroke-width="8" class="text-gray-200 dark:text-gray-700" />
                <circle cx="40" cy="40" r="36" fill="none" stroke="url(#progress-gradient)" stroke-width="8" 
                        stroke-dasharray="<%= 226 %>" 
                        stroke-dashoffset="<%= 226 - (226 * @pipeline_execution.progress_percentage / 100.0) %>"
                        class="transition-all duration-1000" />
                <defs>
                  <linearGradient id="progress-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:#9333ea;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#a855f7;stop-opacity:1" />
                  </linearGradient>
                </defs>
              </svg>
              <div class="absolute inset-0 flex items-center justify-center">
                <span class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  <%= @pipeline_execution.progress_percentage %>%
                </span>
              </div>
            </div>
          </div>
          <p class="text-sm font-medium text-gray-500 dark:text-gray-400 text-center">Progress</p>
        </div>
      </div>
      
      <!-- Duration Card -->
      <div class="group relative">
        <div class="absolute inset-0 bg-gradient-to-r from-amber-600 to-amber-700 rounded-2xl transform rotate-1 group-hover:rotate-2 transition-transform duration-300"></div>
        <div class="relative bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6 transform transition-transform duration-300 group-hover:-translate-y-0.5">
          <div class="flex items-center justify-between mb-4">
            <div class="p-3 bg-amber-100 dark:bg-amber-900/30 rounded-xl">
              <svg class="w-6 h-6 text-amber-600 dark:text-amber-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Duration</p>
          <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">
            <% if @pipeline_execution.duration_seconds %>
              <%= distance_of_time_in_words(@pipeline_execution.duration_seconds) %>
            <% else %>
              <span data-duration-timer="<%= @pipeline_execution.started_at.to_i %>">--:--</span>
            <% end %>
          </p>
          <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Avg: <%= distance_of_time_in_words(@average_duration || 0) %>
          </p>
        </div>
      </div>
      
      <!-- Tasks Card -->
      <div class="group relative">
        <div class="absolute inset-0 bg-gradient-to-r from-green-600 to-green-700 rounded-2xl transform -rotate-1 group-hover:-rotate-2 transition-transform duration-300"></div>
        <div class="relative bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6 transform transition-transform duration-300 group-hover:-translate-y-0.5">
          <div class="flex items-center justify-between mb-4">
            <div class="p-3 bg-green-100 dark:bg-green-900/30 rounded-xl">
              <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
              </svg>
            </div>
          </div>
          <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Tasks</p>
          <div class="flex items-baseline space-x-1">
            <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">
              <%= @pipeline_execution.completed_tasks %>/<%= @pipeline_execution.total_tasks %>
            </p>
            <% if @pipeline_execution.failed_tasks > 0 %>
              <span class="text-sm font-medium text-red-600 dark:text-red-400">
                (<%= @pipeline_execution.failed_tasks %> failed)
              </span>
            <% end %>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Tasks and Timeline -->
      <div class="lg:col-span-2 space-y-8">
        <!-- Enhanced Pipeline Flow Visualization -->
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm p-8">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">Pipeline Flow</h3>
          
          <div class="relative overflow-x-auto pb-4">
            <div class="flex items-center space-x-4 min-w-max">
              <% @tasks.each_with_index do |task, index| %>
                <div class="relative group" data-task-node="<%= task.id %>">
                  <!-- Connection line -->
                  <% if index < @tasks.count - 1 %>
                    <div class="absolute top-1/2 left-full w-16 h-0.5 -translate-y-1/2 z-0">
                      <div class="h-full bg-gray-300 dark:bg-gray-600"></div>
                      <div class="h-full bg-gradient-to-r from-green-500 to-green-600 transition-all duration-1000 absolute top-0 left-0"
                           style="width: <%= task.completed? ? '100%' : '0%' %>"></div>
                    </div>
                  <% end %>
                  
                  <!-- Task node -->
                  <div class="relative w-32 h-32 bg-white dark:bg-gray-900 rounded-2xl shadow-lg border-2 <%= task.completed? ? 'border-green-500' : task.failed? ? 'border-red-500' : task.in_progress? ? 'border-blue-500 animate-pulse' : 'border-gray-300 dark:border-gray-600' %> p-4 flex flex-col items-center justify-center cursor-pointer hover:shadow-2xl transition-all duration-300 hover:-translate-y-1">
                    <!-- Progress ring -->
                    <svg class="absolute inset-0 w-full h-full">
                      <circle cx="64" cy="64" r="62" fill="none" stroke="currentColor" stroke-width="2" class="text-gray-200 dark:text-gray-700" />
                      <circle cx="64" cy="64" r="62" fill="none" stroke="currentColor" stroke-width="2" 
                              class="<%= task.completed? ? 'text-green-500' : task.failed? ? 'text-red-500' : task.in_progress? ? 'text-blue-500' : 'text-gray-300' %>"
                              stroke-dasharray="390" 
                              stroke-dashoffset="<%= 390 - (390 * (task.progress || 0) / 100.0) %>"
                              transform="rotate(-90 64 64)"
                              style="transition: stroke-dashoffset 1s ease-in-out;" />
                    </svg>
                    
                    <!-- Content -->
                    <div class="relative text-center z-10">
                      <div class="w-10 h-10 mx-auto mb-2 <%= task.completed? ? 'bg-green-100 dark:bg-green-900/30' : task.failed? ? 'bg-red-100 dark:bg-red-900/30' : task.in_progress? ? 'bg-blue-100 dark:bg-blue-900/30' : 'bg-gray-100 dark:bg-gray-800' %> rounded-xl flex items-center justify-center">
                        <svg class="w-5 h-5 <%= task.completed? ? 'text-green-600 dark:text-green-400' : task.failed? ? 'text-red-600 dark:text-red-400' : task.in_progress? ? 'text-blue-600 dark:text-blue-400' : 'text-gray-400' %>" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <% if task.task_type == 'extraction' %>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                          <% elsif task.task_type == 'transformation' %>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                          <% else %>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          <% end %>
                        </svg>
                      </div>
                      <p class="text-xs font-medium text-gray-900 dark:text-gray-100 line-clamp-2"><%= task.name %></p>
                      <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        <%= task.status.humanize %>
                      </p>
                    </div>
                    
                    <!-- Hover tooltip -->
                    <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-20">
                      <div class="bg-gray-900 dark:bg-gray-700 text-white text-xs rounded-lg py-2 px-3 whitespace-nowrap">
                        <p class="font-medium"><%= task.name %></p>
                        <p class="text-gray-300">
                          <%= task.duration_seconds ? "Duration: #{distance_of_time_in_words(task.duration_seconds)}" : "Not started" %>
                        </p>
                        <% if task.error_message %>
                          <p class="text-red-300 mt-1"><%= task.error_message.truncate(50) %></p>
                        <% end %>
                        <div class="absolute top-full left-1/2 transform -translate-x-1/2 -mt-1">
                          <div class="border-4 border-transparent border-t-gray-900 dark:border-t-gray-700"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>
        
        <!-- Tasks List with Timeline -->
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm">
          <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Tasks Timeline</h3>
          </div>
          
          <div class="p-6">
            <div class="relative">
              <!-- Timeline line -->
              <div class="absolute left-8 top-0 bottom-0 w-0.5 bg-gray-200 dark:bg-gray-700"></div>
              
              <% @tasks.each_with_index do |task, index| %>
                <div class="relative flex items-start mb-8 last:mb-0 group" data-aos="fade-up" data-aos-delay="<%= index * 50 %>">
                  <!-- Timeline dot -->
                  <div class="absolute left-8 w-4 h-4 transform -translate-x-1/2">
                    <div class="w-4 h-4 rounded-full <%= task.completed? ? 'bg-green-500' : task.failed? ? 'bg-red-500' : task.in_progress? ? 'bg-blue-500' : 'bg-gray-300 dark:bg-gray-600' %> ring-4 ring-white dark:ring-gray-800 group-hover:scale-125 transition-transform"></div>
                    <% if task.in_progress? %>
                      <div class="absolute inset-0 w-4 h-4 rounded-full bg-blue-500 animate-ping"></div>
                    <% end %>
                  </div>
                  
                  <!-- Task content -->
                  <div class="ml-12 flex-1">
                    <div class="bg-gray-50 dark:bg-gray-900 rounded-xl p-4 group-hover:shadow-lg transition-shadow">
                      <div class="flex items-start justify-between">
                        <div class="flex-1">
                          <h4 class="font-medium text-gray-900 dark:text-gray-100"><%= task.name %></h4>
                          <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                            <%= task.task_type.humanize %> • <%= task.execution_mode.humanize %>
                          </p>
                          
                          <!-- Task metadata -->
                          <div class="flex flex-wrap gap-2 mt-3">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= task.status_badge_class %>">
                              <%= task.status.humanize %>
                            </span>
                            <% if task.assignee %>
                              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                  <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                                </svg>
                                <%= task.assignee.name %>
                              </span>
                            <% end %>
                            <% if task.duration_seconds %>
                              <span class="inline-flex items-center text-xs text-gray-500 dark:text-gray-400">
                                <svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <%= distance_of_time_in_words(task.duration_seconds) %>
                              </span>
                            <% end %>
                          </div>
                          
                          <% if task.error_message %>
                            <div class="mt-3 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                              <p class="text-sm text-red-600 dark:text-red-400">
                                <strong>Error:</strong> <%= task.error_message %>
                              </p>
                            </div>
                          <% end %>
                        </div>
                        
                        <!-- Task actions -->
                        <div class="ml-4 flex-shrink-0">
                          <%= link_to task_path(task), class: "text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" do %>
                            <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                          <% end %>
                        </div>
                      </div>
                      
                      <!-- Timestamps -->
                      <div class="mt-3 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                        <% if task.started_at %>
                          <span>Started: <%= task.started_at.strftime('%H:%M:%S') %></span>
                        <% end %>
                        <% if task.completed_at %>
                          <span>Completed: <%= task.completed_at.strftime('%H:%M:%S') %></span>
                        <% end %>
                      </div>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Sidebar -->
      <div class="lg:col-span-1 space-y-6">
        <!-- Pipeline Info -->
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Pipeline Information</h3>
          
          <dl class="space-y-4">
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Execution ID</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 font-mono"><%= @pipeline_execution.id %></dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Execution Mode</dt>
              <dd class="mt-1">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                  <%= @pipeline_execution.execution_mode %>
                </span>
              </dd>
            </div>
            
            <% if @pipeline_execution.user %>
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Started By</dt>
                <dd class="mt-1 flex items-center text-sm text-gray-900 dark:text-gray-100">
                  <div class="w-6 h-6 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white text-xs font-medium mr-2">
                    <%= @pipeline_execution.user.initials %>
                  </div>
                  <%= @pipeline_execution.user.name %>
                </dd>
              </div>
            <% end %>
            
            <% if @pipeline_execution.data_source %>
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Data Source</dt>
                <dd class="mt-1">
                  <%= link_to @pipeline_execution.data_source.name, 
                    data_source_path(@pipeline_execution.data_source), 
                    class: "text-blue-600 dark:text-blue-400 hover:underline text-sm" %>
                </dd>
              </div>
            <% end %>
          </dl>
        </div>
        
        <!-- Live Metrics -->
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Live Metrics</h3>
          
          <div class="space-y-4">
            <!-- Records processed -->
            <div>
              <div class="flex justify-between text-sm mb-1">
                <span class="text-gray-500 dark:text-gray-400">Records Processed</span>
                <span class="font-medium text-gray-900 dark:text-gray-100" data-metric="records-processed">
                  <%= number_with_delimiter(@pipeline_execution.metadata&.dig('records_processed') || 0) %>
                </span>
              </div>
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div class="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-500"
                     style="width: <%= [(@pipeline_execution.metadata&.dig('records_processed').to_f / (@pipeline_execution.metadata&.dig('total_records') || 1) * 100), 100].min %>%"></div>
              </div>
            </div>
            
            <!-- Processing rate -->
            <div>
              <p class="text-sm text-gray-500 dark:text-gray-400">Processing Rate</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-gray-100" data-metric="processing-rate">
                <%= @pipeline_execution.metadata&.dig('records_per_second') || 0 %>
                <span class="text-sm font-normal text-gray-500 dark:text-gray-400">records/sec</span>
              </p>
            </div>
            
            <!-- Memory usage -->
            <div>
              <p class="text-sm text-gray-500 dark:text-gray-400">Memory Usage</p>
              <div class="flex items-center mt-1">
                <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2">
                  <div class="bg-gradient-to-r from-green-500 to-green-600 h-2 rounded-full"
                       style="width: <%= @pipeline_execution.metadata&.dig('memory_usage_percent') || 0 %>%"></div>
                </div>
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
                  <%= @pipeline_execution.metadata&.dig('memory_usage_percent') || 0 %>%
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Recent Executions -->
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm">
          <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Recent Executions</h3>
          </div>
          
          <div class="divide-y divide-gray-200 dark:divide-gray-700">
            <% @recent_executions.each do |execution| %>
              <div class="p-4 hover:bg-gray-50 dark:hover:bg-gray-900/50 transition-colors">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <div class="w-2 h-2 rounded-full <%= execution.status == 'completed' ? 'bg-green-500' : execution.status == 'failed' ? 'bg-red-500' : 'bg-gray-400' %>"></div>
                    <div>
                      <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                        <%= execution.pipeline_name %>
                      </p>
                      <p class="text-xs text-gray-500 dark:text-gray-400">
                        <%= distance_of_time_in_words_to_now(execution.started_at) %> ago
                      </p>
                    </div>
                  </div>
                  <%= link_to pipeline_dashboard_path(execution), class: "text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" do %>
                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Premium JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Live duration timer
  const durationTimer = document.querySelector('[data-duration-timer]');
  if (durationTimer) {
    const startTime = parseInt(durationTimer.dataset.durationTimer);
    setInterval(() => {
      const elapsed = Math.floor(Date.now() / 1000) - startTime;
      const hours = Math.floor(elapsed / 3600);
      const minutes = Math.floor((elapsed % 3600) / 60);
      const seconds = elapsed % 60;
      durationTimer.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }, 1000);
  }
  
  // Subscribe to pipeline updates
  if (window.subscribeToPipeline) {
    const subscription = window.subscribeToPipeline(<%= @pipeline_execution.id %>, {
      onTaskStatusUpdate: (data) => {
        // Update task node
        const node = document.querySelector(`[data-task-node="${data.task_id}"]`);
        if (node) {
          // Update node appearance based on status
          // This would be more complex in production
        }
      },
      onPipelineStatusUpdate: (data) => {
        // Update pipeline metrics
        if (data.progress_percentage !== undefined) {
          // Update progress circles and bars
        }
      }
    });
  }
  
  // Initialize AOS for scroll animations
  if (typeof AOS !== 'undefined') {
    AOS.init({
      duration: 600,
      easing: 'ease-out-cubic',
      once: true,
      offset: 50
    });
  }
});
</script>