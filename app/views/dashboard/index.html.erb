<% content_for :page_title, "Dashboard" %>
<% content_for :page_subtitle, "Real-time insights and AI-powered analytics for your business" %>

<!-- DataFlow Pro Dashboard -->
<div class="content-section active">
  <!-- Metrics Grid -->
  <div class="metrics-grid">
    <div class="metric-card">
      <div class="metric-icon">📊</div>
      <div class="metric-content">
        <h3>Data Sources</h3>
        <p class="metric-value"><%= @stats[:total_data_sources] %></p>
        <p class="metric-change positive">
          <%= @stats[:connected_sources] %> connected
        </p>
      </div>
    </div>

    <div class="metric-card">
      <div class="metric-icon">📈</div>
      <div class="metric-content">
        <h3>Records Processed</h3>
        <p class="metric-value"><%= number_with_delimiter(@stats[:total_records]) %></p>
        <p class="metric-change positive">↑ 8.2% vs last month</p>
      </div>
    </div>

    <div class="metric-card">
      <div class="metric-icon">✅</div>
      <div class="metric-content">
        <h3>Pipeline Health</h3>
        <p class="metric-value">98%</p>
        <p class="metric-change positive">Excellent</p>
      </div>
    </div>

    <div class="metric-card">
      <div class="metric-icon">🕒</div>
      <div class="metric-content">
        <h3>Last Sync</h3>
        <p class="metric-value">
          <%= @stats[:last_sync] ? time_ago_in_words(@stats[:last_sync]) + " ago" : "Never" %>
        </p>
        <p class="metric-change <%= @stats[:last_sync] ? 'positive' : '' %>">
          <%= @stats[:last_sync] ? 'Active' : 'Inactive' %>
        </p>
      </div>
    </div>
  </div>

  <!-- Charts Section -->
  <div class="charts-section">
    <div class="chart-container">
      <div class="chart-header">
        <h3>Revenue Trends</h3>
        <div class="chart-controls">
          <button class="btn btn--outline" style="padding: var(--space-6) var(--space-12); font-size: var(--font-size-sm);">Daily</button>
          <button class="btn btn--outline" style="padding: var(--space-6) var(--space-12); font-size: var(--font-size-sm);">Weekly</button>
          <button class="btn btn--primary" style="padding: var(--space-6) var(--space-12); font-size: var(--font-size-sm);">Monthly</button>
        </div>
      </div>
      <div class="chart-wrapper">
        <canvas id="revenueChart"></canvas>
      </div>
    </div>

    <div class="chart-container">
      <div class="chart-header">
        <h3>Customer Segments</h3>
        <div class="chart-controls">
          <button class="btn btn--outline" style="padding: var(--space-6) var(--space-12); font-size: var(--font-size-sm);">Export</button>
        </div>
      </div>
      <div class="chart-wrapper">
        <canvas id="segmentChart"></canvas>
      </div>
    </div>
  </div>

  <!-- AI Insights -->
  <h2 style="font-size: var(--font-size-xl); font-weight: var(--font-weight-semibold); margin: var(--space-32) 0 var(--space-20) 0;">AI-Powered Insights</h2>
  
  <div class="insights-grid">
    <div class="insight-card critical">
      <div class="insight-header">
        <span class="insight-type">🚨 Critical Alert</span>
        <span class="confidence-score">95% confidence</span>
      </div>
      <p>Revenue from your Shopify store has declined by 23% in the past week. This appears to be correlated with a 45% drop in email campaign engagement.</p>
      <button class="btn btn--primary">Investigate</button>
    </div>

    <div class="insight-card high">
      <div class="insight-header">
        <span class="insight-type">💡 Opportunity</span>
        <span class="confidence-score">88% confidence</span>
      </div>
      <p>Customers who purchase Product A are 3.2x more likely to buy Product B within 30 days. Consider creating a bundle offer.</p>
      <button class="btn btn--secondary">View Details</button>
    </div>

    <div class="insight-card medium">
      <div class="insight-header">
        <span class="insight-type">📊 Trend Analysis</span>
        <span class="confidence-score">92% confidence</span>
      </div>
      <p>Weekend sales have increased by 15% over the past month. Peak hours are Saturday 2-4 PM EST.</p>
      <button class="btn btn--secondary">Learn More</button>
    </div>
  </div>

  <!-- Data Sources Section -->
  <h2 style="font-size: var(--font-size-xl); font-weight: var(--font-weight-semibold); margin: var(--space-32) 0 var(--space-20) 0;">Connected Data Sources</h2>
  
  <div class="chart-container">
    <% if @data_sources.any? %>
      <div style="display: flex; flex-direction: column; gap: var(--space-16);">
        <% @data_sources.first(5).each do |data_source| %>
          <div style="display: flex; align-items: center; padding: var(--space-16); border: 1px solid var(--color-border); border-radius: var(--radius-md); transition: all var(--duration-fast) var(--ease-standard);">
            <div style="width: 40px; height: 40px; border-radius: var(--radius-md); display: flex; align-items: center; justify-content: center; font-weight: var(--font-weight-semibold); margin-right: var(--space-16); <%= case data_source.source_type
              when 'shopify' then 'background-color: rgba(33, 128, 141, 0.1); color: var(--color-primary);'
              when 'stripe' then 'background-color: rgba(147, 51, 234, 0.1); color: rgb(147, 51, 234);'
              when 'google_analytics' then 'background-color: rgba(251, 146, 60, 0.1); color: rgb(251, 146, 60);'
              when 'quickbooks' then 'background-color: rgba(59, 130, 246, 0.1); color: rgb(59, 130, 246);'
              when 'mailchimp' then 'background-color: rgba(250, 204, 21, 0.1); color: rgb(250, 204, 21);'
              else 'background-color: var(--color-secondary); color: var(--color-text-secondary);'
              end %>">
              <%= data_source.source_type.first(2).upcase %>
            </div>
            <div style="flex: 1;">
              <p style="font-weight: var(--font-weight-medium); margin-bottom: var(--space-4);"><%= data_source.name %></p>
              <p style="font-size: var(--font-size-sm); color: var(--color-text-secondary);"><%= data_source.source_type.humanize %></p>
            </div>
            <div style="display: flex; align-items: center; gap: var(--space-12);">
              <span style="padding: var(--space-4) var(--space-12); border-radius: var(--radius-full); font-size: var(--font-size-xs); font-weight: var(--font-weight-medium); <%= case data_source.status
                when 'connected' then 'background-color: rgba(33, 128, 141, 0.1); color: var(--color-success);'
                when 'syncing' then 'background-color: rgba(59, 130, 246, 0.1); color: rgb(59, 130, 246);'
                when 'error' then 'background-color: rgba(192, 21, 47, 0.1); color: var(--color-error);'
                else 'background-color: var(--color-secondary); color: var(--color-text-secondary);'
                end %>">
                <%= data_source.status.humanize %>
              </span>
              <%= link_to data_source_path(data_source), style: "color: var(--color-text-secondary); text-decoration: none;" do %>
                <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                </svg>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
      
      <div style="text-align: center; margin-top: var(--space-24);">
        <%= link_to "View All Data Sources →", data_sources_path, class: "btn btn--secondary" %>
      </div>
    <% else %>
      <div style="text-align: center; padding: var(--space-32);">
        <div style="width: 64px; height: 64px; margin: 0 auto var(--space-16); background-color: var(--color-secondary); border-radius: var(--radius-full); display: flex; align-items: center; justify-content: center;">
          <svg width="32" height="32" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" style="color: var(--color-text-secondary);">
            <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125" />
          </svg>
        </div>
        <h3 style="font-size: var(--font-size-lg); font-weight: var(--font-weight-medium); margin-bottom: var(--space-8);">No data sources connected</h3>
        <p style="color: var(--color-text-secondary); margin-bottom: var(--space-24);">Get started by connecting your first data source to unlock powerful insights.</p>
        <%= link_to "Add Data Source", new_data_source_path, class: "btn btn--primary" %>
      </div>
    <% end %>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
  // Initialize charts when page loads
  document.addEventListener('DOMContentLoaded', function() {
    // Revenue Chart
    const revenueCtx = document.getElementById('revenueChart');
    if (revenueCtx) {
      new Chart(revenueCtx, {
        type: 'line',
        data: {
          labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
          datasets: [{
            label: 'Revenue',
            data: [30000, 35000, 32000, 38000, 42000, 45000],
            borderColor: getComputedStyle(document.documentElement).getPropertyValue('--color-primary'),
            backgroundColor: getComputedStyle(document.documentElement).getPropertyValue('--color-primary') + '20',
            tension: 0.4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                callback: function(value) {
                  return '$' + (value / 1000) + 'k';
                }
              }
            }
          }
        }
      });
    }

    // Segment Chart
    const segmentCtx = document.getElementById('segmentChart');
    if (segmentCtx) {
      new Chart(segmentCtx, {
        type: 'doughnut',
        data: {
          labels: ['New', 'Returning', 'VIP', 'At Risk'],
          datasets: [{
            data: [30, 45, 15, 10],
            backgroundColor: [
              getComputedStyle(document.documentElement).getPropertyValue('--color-primary'),
              getComputedStyle(document.documentElement).getPropertyValue('--color-success'),
              getComputedStyle(document.documentElement).getPropertyValue('--color-warning'),
              getComputedStyle(document.documentElement).getPropertyValue('--color-error')
            ]
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom'
            }
          }
        }
      });
    }
  });
</script>