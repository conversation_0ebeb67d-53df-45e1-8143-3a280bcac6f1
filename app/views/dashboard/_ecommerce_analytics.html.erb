<% if @ecommerce_stats.present? && @ecommerce_stats.any? %>
<!-- E-commerce Analytics Section -->
<div class="mb-8">
  <div class="flex items-center gap-3 mb-6">
    <div class="h-10 w-10 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg">
      <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
      </svg>
    </div>
    <h2 class="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">E-commerce Analytics</h2>
    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-emerald-100 text-emerald-800">Last 30 days</span>
  </div>

  <!-- E-commerce Key Metrics -->
  <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 xl:grid-cols-4 mb-8">
    <!-- Total Revenue -->
    <div class="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-emerald-50 to-emerald-100 p-6 shadow-lg border border-emerald-200/50 hover:shadow-xl hover:scale-105 transition-all duration-300">
      <div class="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-emerald-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      <div class="relative">
        <div class="flex items-center justify-between mb-4">
          <div class="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-emerald-500 to-emerald-600 shadow-lg group-hover:shadow-xl transition-shadow duration-300">
            <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          </div>
          <div class="flex items-center gap-1 px-2 py-1 bg-green-100 text-green-700 text-xs font-semibold rounded-full">
            <svg class="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04L10 3.26l5.79 5.47a.75.75 0 01-1.08 1.04L10.75 5.612V16.25A.75.75 0 0110 17z" clip-rule="evenodd" />
            </svg>
            +15.2%
          </div>
        </div>
        <div>
          <p class="text-sm font-medium text-emerald-700 mb-1">Total Revenue</p>
          <p class="text-3xl font-bold text-gray-900 mb-2">$<%= number_with_delimiter(@ecommerce_stats[:total_revenue]&.round(2) || 0) %></p>
          <p class="text-xs text-gray-600">Last 30 days</p>
        </div>
      </div>
    </div>

    <!-- Total Orders -->
    <div class="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-50 to-blue-100 p-6 shadow-lg border border-blue-200/50 hover:shadow-xl hover:scale-105 transition-all duration-300">
      <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-blue-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      <div class="relative">
        <div class="flex items-center justify-between mb-4">
          <div class="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg group-hover:shadow-xl transition-shadow duration-300">
            <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
          </div>
          <div class="flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-700 text-xs font-semibold rounded-full">
            <svg class="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04L10 3.26l5.79 5.47a.75.75 0 01-1.08 1.04L10.75 5.612V16.25A.75.75 0 0110 17z" clip-rule="evenodd" />
            </svg>
            +8.7%
          </div>
        </div>
        <div>
          <p class="text-sm font-medium text-blue-700 mb-1">Total Orders</p>
          <p class="text-3xl font-bold text-gray-900 mb-2"><%= number_with_delimiter(@ecommerce_stats[:total_orders] || 0) %></p>
          <p class="text-xs text-gray-600">Orders processed</p>
        </div>
      </div>
    </div>

    <!-- Average Order Value -->
    <div class="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50 to-purple-100 p-6 shadow-lg border border-purple-200/50 hover:shadow-xl hover:scale-105 transition-all duration-300">
      <div class="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-purple-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      <div class="relative">
        <div class="flex items-center justify-between mb-4">
          <div class="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-purple-500 to-purple-600 shadow-lg group-hover:shadow-xl transition-shadow duration-300">
            <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <div class="flex items-center gap-1 px-2 py-1 bg-purple-100 text-purple-700 text-xs font-semibold rounded-full">
            <svg class="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04L10 3.26l5.79 5.47a.75.75 0 01-1.08 1.04L10.75 5.612V16.25A.75.75 0 0110 17z" clip-rule="evenodd" />
            </svg>
            +5.3%
          </div>
        </div>
        <div>
          <p class="text-sm font-medium text-purple-700 mb-1">Avg. Order Value</p>
          <p class="text-3xl font-bold text-gray-900 mb-2">$<%= number_with_delimiter(@ecommerce_stats[:average_order_value]&.round(2) || 0) %></p>
          <p class="text-xs text-gray-600">Per order average</p>
        </div>
      </div>
    </div>

    <!-- Conversion Rate -->
    <div class="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-orange-50 to-orange-100 p-6 shadow-lg border border-orange-200/50 hover:shadow-xl hover:scale-105 transition-all duration-300">
      <div class="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-orange-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      <div class="relative">
        <div class="flex items-center justify-between mb-4">
          <div class="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-orange-500 to-orange-600 shadow-lg group-hover:shadow-xl transition-shadow duration-300">
            <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
            </svg>
          </div>
          <div class="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
            <div class="h-full bg-gradient-to-r from-orange-500 to-orange-600 rounded-full transition-all duration-1000" style="width: <%= [@ecommerce_stats.dig(:conversion_metrics, :conversion_rate) || 0, 100].min %>%"></div>
          </div>
        </div>
        <div>
          <p class="text-sm font-medium text-orange-700 mb-1">Conversion Rate</p>
          <p class="text-3xl font-bold text-gray-900 mb-2"><%= @ecommerce_stats.dig(:conversion_metrics, :conversion_rate) || 0 %>%</p>
          <p class="text-xs text-gray-600">Customer conversion</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Charts Grid -->
  <div class="grid grid-cols-1 gap-8 xl:grid-cols-2 mb-8">
    <!-- Revenue Trend Chart -->
    <div class="bg-white/80 backdrop-blur-sm overflow-hidden shadow-xl rounded-2xl border border-gray-200/50">
      <div class="px-8 py-6 border-b border-gray-200/50 bg-gradient-to-r from-gray-50 to-white">
        <div class="flex items-center gap-3">
          <div class="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-emerald-500 to-green-600 shadow-lg">
            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
            </svg>
          </div>
          <div>
            <h3 class="text-xl font-bold text-gray-900">Revenue Trend</h3>
            <p class="text-sm text-gray-600">Daily revenue over the last 30 days</p>
          </div>
        </div>
      </div>
      <div class="p-8">
        <div class="h-64" id="revenue-chart">
          <% if @charts_data[:revenue_chart]&.any? %>
            <div class="flex items-center justify-center h-full">
              <canvas id="revenueChart" width="400" height="200"></canvas>
            </div>
          <% else %>
            <div class="flex items-center justify-center h-full text-gray-500">
              <div class="text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                <p class="mt-2 text-sm">No revenue data available</p>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Orders Trend Chart -->
    <div class="bg-white/80 backdrop-blur-sm overflow-hidden shadow-xl rounded-2xl border border-gray-200/50">
      <div class="px-8 py-6 border-b border-gray-200/50 bg-gradient-to-r from-gray-50 to-white">
        <div class="flex items-center gap-3">
          <div class="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg">
            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
          </div>
          <div>
            <h3 class="text-xl font-bold text-gray-900">Orders Trend</h3>
            <p class="text-sm text-gray-600">Daily orders over the last 30 days</p>
          </div>
        </div>
      </div>
      <div class="p-8">
        <div class="h-64" id="orders-chart">
          <% if @charts_data[:orders_chart]&.any? %>
            <div class="flex items-center justify-center h-full">
              <canvas id="ordersChart" width="400" height="200"></canvas>
            </div>
          <% else %>
            <div class="flex items-center justify-center h-full text-gray-500">
              <div class="text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
                <p class="mt-2 text-sm">No order data available</p>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Analytics Details Grid -->
  <div class="grid grid-cols-1 gap-8 xl:grid-cols-3">
    <!-- Top Products -->
    <div class="bg-white/80 backdrop-blur-sm overflow-hidden shadow-xl rounded-2xl border border-gray-200/50">
      <div class="px-8 py-6 border-b border-gray-200/50 bg-gradient-to-r from-gray-50 to-white">
        <div class="flex items-center gap-3">
          <div class="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-purple-500 to-purple-600 shadow-lg">
            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
            </svg>
          </div>
          <div>
            <h3 class="text-xl font-bold text-gray-900">Top Products</h3>
            <p class="text-sm text-gray-600">Best performing products</p>
          </div>
        </div>
      </div>
      <div class="p-8">
        <% if @ecommerce_stats[:top_products]&.any? %>
          <div class="space-y-4">
            <% @ecommerce_stats[:top_products].each_with_index do |(product_name, stats), index| %>
              <div class="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-white rounded-xl border border-gray-100 hover:shadow-md transition-all duration-200">
                <div class="flex items-center gap-3">
                  <div class="h-8 w-8 rounded-lg bg-gradient-to-br from-purple-100 to-purple-200 flex items-center justify-center">
                    <span class="text-sm font-bold text-purple-700"><%= index + 1 %></span>
                  </div>
                  <div>
                    <p class="font-semibold text-gray-900 text-sm"><%= product_name.truncate(25) %></p>
                    <p class="text-xs text-gray-500"><%= stats[:count] %> units sold</p>
                  </div>
                </div>
                <div class="text-right">
                  <p class="font-bold text-gray-900">$<%= number_with_delimiter(stats[:revenue].round(2)) %></p>
                  <p class="text-xs text-gray-500">Revenue</p>
                </div>
              </div>
            <% end %>
          </div>
        <% else %>
          <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
            </svg>
            <p class="mt-2 text-sm text-gray-500">No product data available</p>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Customer Segments -->
    <div class="bg-white/80 backdrop-blur-sm overflow-hidden shadow-xl rounded-2xl border border-gray-200/50">
      <div class="px-8 py-6 border-b border-gray-200/50 bg-gradient-to-r from-gray-50 to-white">
        <div class="flex items-center gap-3">
          <div class="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-cyan-500 to-cyan-600 shadow-lg">
            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          </div>
          <div>
            <h3 class="text-xl font-bold text-gray-900">Customer Segments</h3>
            <p class="text-sm text-gray-600">Customer breakdown</p>
          </div>
        </div>
      </div>
      <div class="p-8">
        <% if @ecommerce_stats[:customer_segments]&.any? %>
          <div class="space-y-4">
            <% @ecommerce_stats[:customer_segments].each do |segment, count| %>
              <div class="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-white rounded-xl border border-gray-100">
                <div class="flex items-center gap-3">
                  <div class="h-8 w-8 rounded-lg <%= case segment
                    when :vip then 'bg-gradient-to-br from-yellow-100 to-yellow-200'
                    when :returning then 'bg-gradient-to-br from-blue-100 to-blue-200'
                    when :new then 'bg-gradient-to-br from-green-100 to-green-200'
                    when :at_risk then 'bg-gradient-to-br from-red-100 to-red-200'
                    end %> flex items-center justify-center">
                    <% case segment %>
                    <% when :vip %>
                      <svg class="h-4 w-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    <% when :returning %>
                      <svg class="h-4 w-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                      </svg>
                    <% when :new %>
                      <svg class="h-4 w-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                      </svg>
                    <% when :at_risk %>
                      <svg class="h-4 w-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                      </svg>
                    <% end %>
                  </div>
                  <div>
                    <p class="font-semibold text-gray-900 text-sm capitalize"><%= segment.to_s.humanize %></p>
                    <p class="text-xs text-gray-500">Customers</p>
                  </div>
                </div>
                <div class="text-right">
                  <p class="font-bold text-gray-900"><%= number_with_delimiter(count) %></p>
                </div>
              </div>
            <% end %>
          </div>
        <% else %>
          <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            <p class="mt-2 text-sm text-gray-500">No customer data available</p>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Key Metrics -->
    <div class="bg-white/80 backdrop-blur-sm overflow-hidden shadow-xl rounded-2xl border border-gray-200/50">
      <div class="px-8 py-6 border-b border-gray-200/50 bg-gradient-to-r from-gray-50 to-white">
        <div class="flex items-center gap-3">
          <div class="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-indigo-500 to-indigo-600 shadow-lg">
            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <div>
            <h3 class="text-xl font-bold text-gray-900">Key Metrics</h3>
            <p class="text-sm text-gray-600">Performance indicators</p>
          </div>
        </div>
      </div>
      <div class="p-8">
        <div class="space-y-6">
          <div class="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl border border-blue-200">
            <div>
              <p class="text-sm font-medium text-blue-700">Repeat Purchase Rate</p>
              <p class="text-2xl font-bold text-gray-900"><%= @ecommerce_stats.dig(:conversion_metrics, :repeat_purchase_rate) || 0 %>%</p>
            </div>
            <div class="h-12 w-12 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
              <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </div>
          </div>

          <div class="flex items-center justify-between p-4 bg-gradient-to-r from-green-50 to-green-100 rounded-xl border border-green-200">
            <div>
              <p class="text-sm font-medium text-green-700">Total Customers</p>
              <p class="text-2xl font-bold text-gray-900"><%= number_with_delimiter(@ecommerce_stats[:total_customers] || 0) %></p>
            </div>
            <div class="h-12 w-12 rounded-xl bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center">
              <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
            </div>
          </div>

          <div class="flex items-center justify-between p-4 bg-gradient-to-r from-purple-50 to-purple-100 rounded-xl border border-purple-200">
            <div>
              <p class="text-sm font-medium text-purple-700">Platform Coverage</p>
              <p class="text-2xl font-bold text-gray-900"><%= @data_sources.where(source_type: %w[shopify woocommerce amazon_seller_central]).count %></p>
            </div>
            <div class="h-12 w-12 rounded-xl bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center">
              <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Chart.js Integration -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Revenue Chart
  <% if @charts_data[:revenue_chart]&.any? %>
    const revenueCtx = document.getElementById('revenueChart');
    if (revenueCtx) {
      new Chart(revenueCtx, {
        type: 'line',
        data: {
          labels: <%= @charts_data[:revenue_chart].map { |d| d[:x] }.to_json %>,
          datasets: [{
            label: 'Revenue ($)',
            data: <%= @charts_data[:revenue_chart].map { |d| d[:y] }.to_json %>,
            borderColor: 'rgb(16, 185, 129)',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            tension: 0.4,
            fill: true
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                callback: function(value) {
                  return '$' + value.toLocaleString();
                }
              }
            }
          }
        }
      });
    }
  <% end %>

  // Orders Chart
  <% if @charts_data[:orders_chart]&.any? %>
    const ordersCtx = document.getElementById('ordersChart');
    if (ordersCtx) {
      new Chart(ordersCtx, {
        type: 'bar',
        data: {
          labels: <%= @charts_data[:orders_chart].map { |d| d[:x] }.to_json %>,
          datasets: [{
            label: 'Orders',
            data: <%= @charts_data[:orders_chart].map { |d| d[:y] }.to_json %>,
            backgroundColor: 'rgba(59, 130, 246, 0.8)',
            borderColor: 'rgb(59, 130, 246)',
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                stepSize: 1
              }
            }
          }
        }
      });
    }
  <% end %>
});
</script>
<% end %>