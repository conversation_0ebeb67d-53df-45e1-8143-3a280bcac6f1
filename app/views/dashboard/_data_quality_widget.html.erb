<!-- Premium Data Quality Monitoring Widget with Glass Morphism -->
<div id="data-quality" class="group premium-card relative overflow-hidden rounded-4xl bg-white/30 backdrop-blur-2xl p-8 shadow-2xl border border-white/20 hover:shadow-purple-200/50 hover:shadow-4xl hover:-translate-y-3 transition-all duration-700 animate-float" style="animation-delay: 0.5s;">
  <!-- Premium floating decorations -->
  <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-400/20 to-pink-600/20 rounded-full blur-3xl transform translate-x-12 -translate-y-12 group-hover:scale-150 transition-all duration-700"></div>
  <div class="absolute inset-0 bg-gradient-to-br from-purple-500/10 via-pink-500/10 to-indigo-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
  <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -skew-x-12 opacity-0 group-hover:opacity-100 group-hover:animate-shimmer transition-opacity duration-500"></div>
  
  <div class="relative">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center gap-4">
        <div class="relative group/icon">
          <div class="absolute inset-0 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl blur-xl opacity-30 group-hover/icon:opacity-50 transition-opacity duration-500"></div>
          <div class="relative flex h-14 w-14 items-center justify-center rounded-2xl bg-gradient-to-br from-purple-500 via-pink-600 to-indigo-600 shadow-2xl ring-4 ring-white/30 group-hover/icon:scale-110 group-hover/icon:rotate-3 transition-all duration-500">
            <svg class="h-7 w-7 text-white group-hover/icon:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
              <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
          </div>
        </div>
        <div>
          <h3 class="text-2xl font-black bg-gradient-to-r from-gray-900 via-purple-900 to-pink-900 bg-clip-text text-transparent tracking-tight">Data Quality</h3>
          <p class="text-sm text-gray-600 font-medium">Real-time monitoring</p>
        </div>
      </div>
      
      <!-- Quality Status Badge -->
      <% quality_status = @data_quality_metrics[:overall][:quality_status] %>
      <% status_colors = {
        'excellent' => 'bg-emerald-100 text-emerald-800 border-emerald-200',
        'good' => 'bg-blue-100 text-blue-800 border-blue-200',
        'fair' => 'bg-amber-100 text-amber-800 border-amber-200',
        'poor' => 'bg-orange-100 text-orange-800 border-orange-200',
        'critical' => 'bg-red-100 text-red-800 border-red-200',
        'unknown' => 'bg-gray-100 text-gray-800 border-gray-200'
      } %>
      <span class="inline-flex items-center gap-2 px-4 py-2 rounded-2xl text-sm font-bold border <%= status_colors[quality_status] %>">
        <% if quality_status == 'excellent' %>
          <div class="h-2 w-2 bg-emerald-500 rounded-full animate-pulse"></div>
        <% elsif quality_status == 'good' %>
          <div class="h-2 w-2 bg-blue-500 rounded-full animate-pulse"></div>
        <% elsif quality_status == 'fair' %>
          <div class="h-2 w-2 bg-amber-500 rounded-full animate-pulse"></div>
        <% elsif quality_status == 'poor' %>
          <div class="h-2 w-2 bg-orange-500 rounded-full animate-pulse"></div>
        <% elsif quality_status == 'critical' %>
          <div class="h-2 w-2 bg-red-500 rounded-full animate-pulse"></div>
        <% else %>
          <div class="h-2 w-2 bg-gray-500 rounded-full"></div>
        <% end %>
        <%= quality_status.capitalize %>
      </span>
    </div>
    
    <!-- Overall Quality Score -->
    <div class="mb-8">
      <div class="flex items-end gap-4 mb-4">
        <div class="text-5xl font-black bg-gradient-to-r from-purple-600 via-pink-600 to-indigo-600 bg-clip-text text-transparent">
          <%= @data_quality_metrics[:overall][:overall_quality_score].round(1) %>%
        </div>
        <div class="text-lg text-gray-600 font-semibold mb-2">Overall Score</div>
      </div>
      
      <!-- Progress Bar -->
      <div class="w-full bg-gray-200 rounded-full h-3 shadow-inner">
        <div class="bg-gradient-to-r from-purple-500 via-pink-500 to-indigo-500 h-3 rounded-full transition-all duration-1000 shadow-lg" 
             style="width: <%= @data_quality_metrics[:overall][:overall_quality_score] %>%"></div>
      </div>
    </div>
    
    <!-- Quality Dimensions Grid -->
    <div class="grid grid-cols-2 gap-4 mb-6">
      <!-- Completeness -->
      <div class="bg-white/40 backdrop-blur-sm rounded-2xl p-4 border border-white/30 hover:bg-white/50 transition-all duration-300">
        <div class="flex items-center justify-between mb-2">
          <span class="text-sm font-semibold text-gray-700">Completeness</span>
          <svg class="h-4 w-4 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <div class="text-2xl font-bold text-gray-900 mb-1"><%= @data_quality_metrics[:overall][:completeness_score] %>%</div>
        <div class="w-full bg-gray-200 rounded-full h-2">
          <div class="bg-emerald-500 h-2 rounded-full transition-all duration-500" style="width: <%= @data_quality_metrics[:overall][:completeness_score] %>%"></div>
        </div>
      </div>
      
      <!-- Accuracy -->
      <div class="bg-white/40 backdrop-blur-sm rounded-2xl p-4 border border-white/30 hover:bg-white/50 transition-all duration-300">
        <div class="flex items-center justify-between mb-2">
          <span class="text-sm font-semibold text-gray-700">Accuracy</span>
          <svg class="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        </div>
        <div class="text-2xl font-bold text-gray-900 mb-1"><%= @data_quality_metrics[:overall][:accuracy_score] %>%</div>
        <div class="w-full bg-gray-200 rounded-full h-2">
          <div class="bg-blue-500 h-2 rounded-full transition-all duration-500" style="width: <%= @data_quality_metrics[:overall][:accuracy_score] %>%"></div>
        </div>
      </div>
      
      <!-- Freshness -->
      <div class="bg-white/40 backdrop-blur-sm rounded-2xl p-4 border border-white/30 hover:bg-white/50 transition-all duration-300">
        <div class="flex items-center justify-between mb-2">
          <span class="text-sm font-semibold text-gray-700">Freshness</span>
          <svg class="h-4 w-4 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <div class="text-2xl font-bold text-gray-900 mb-1"><%= @data_quality_metrics[:overall][:freshness_score] %>%</div>
        <div class="w-full bg-gray-200 rounded-full h-2">
          <div class="bg-amber-500 h-2 rounded-full transition-all duration-500" style="width: <%= @data_quality_metrics[:overall][:freshness_score] %>%"></div>
        </div>
      </div>
      
      <!-- Consistency -->
      <div class="bg-white/40 backdrop-blur-sm rounded-2xl p-4 border border-white/30 hover:bg-white/50 transition-all duration-300">
        <div class="flex items-center justify-between mb-2">
          <span class="text-sm font-semibold text-gray-700">Consistency</span>
          <svg class="h-4 w-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
          </svg>
        </div>
        <div class="text-2xl font-bold text-gray-900 mb-1"><%= @data_quality_metrics[:overall][:consistency_score] %>%</div>
        <div class="w-full bg-gray-200 rounded-full h-2">
          <div class="bg-purple-500 h-2 rounded-full transition-all duration-500" style="width: <%= @data_quality_metrics[:overall][:consistency_score] %>%"></div>
        </div>
      </div>
    </div>
    
    <!-- Quality Issues Alert -->
    <% if @data_quality_metrics[:overall][:quality_issues] > 0 %>
      <div class="bg-red-50 border border-red-200 rounded-2xl p-4 mb-4">
        <div class="flex items-center gap-3">
          <svg class="h-5 w-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <div>
            <p class="text-sm font-semibold text-red-800"><%= pluralize(@data_quality_metrics[:overall][:quality_issues], 'quality issue') %> detected</p>
            <p class="text-xs text-red-600">Review data sources for validation errors</p>
          </div>
        </div>
      </div>
    <% end %>
    
    <!-- Footer Stats -->
    <div class="flex items-center justify-between text-sm text-gray-600">
      <div class="flex items-center gap-2">
        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
        <span><%= number_with_delimiter(@data_quality_metrics[:overall][:total_records_analyzed]) %> records analyzed</span>
      </div>
      <div class="flex items-center gap-2">
        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <span>Updated <%= time_ago_in_words(@data_quality_metrics[:overall][:last_quality_check]) %> ago</span>
      </div>
    </div>
  </div>
</div>