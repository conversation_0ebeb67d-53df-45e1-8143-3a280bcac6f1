<% content_for :page_title, "Real-Time Analytics Dashboard" %>

<div class="min-h-screen bg-gradient-to-br from-slate-50 via-orange-50 to-amber-50">
  <!-- Premium Header with Glassmorphism -->
  <div class="relative bg-white/80 backdrop-blur-xl border-b border-white/20 shadow-xl">
    <div class="absolute inset-0 bg-gradient-to-r from-orange-600/10 to-amber-600/10"></div>
    <div class="relative px-4 sm:px-6 lg:px-8 py-8">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="space-y-2">
          <div class="flex items-center space-x-3">
            <div class="p-2 bg-gradient-to-br from-orange-500 to-amber-600 rounded-xl shadow-lg animate-pulse">
              <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 via-orange-900 to-amber-900 bg-clip-text text-transparent">
              Real-Time Analytics Dashboard
            </h1>
          </div>
          <p class="text-slate-600 font-medium">Monitor your data streams and analytics in real-time</p>
        </div>
        <div class="mt-4 sm:mt-0 flex items-center space-x-2">
          <div class="flex items-center space-x-2 px-4 py-2 bg-green-100/80 backdrop-blur-sm rounded-full border border-green-200/50">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span class="text-green-700 text-sm font-semibold">Live Streaming</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="px-4 sm:px-6 lg:px-8 py-8">

    <!-- Real-Time Metrics Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Active Data Sources -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-center justify-between mb-4">
            <div class="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
              <svg class="h-6 w-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
              </svg>
            </div>
            <div class="flex items-center space-x-2 text-xs text-blue-600">
              <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span>Live</span>
            </div>
          </div>
          <p class="text-sm font-semibold text-slate-600">Active Sources</p>
          <p class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mt-1">
            <%= @dashboard_data[:active_sources] || 0 %>
          </p>
        </div>
      </div>

      <!-- Real-Time Events -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-center justify-between mb-4">
            <div class="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:shadow-green-500/25 transition-all duration-300">
              <svg class="h-6 w-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div class="flex items-center space-x-2 text-xs text-green-600">
              <svg class="h-4 w-4 animate-bounce" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
              <span>+12%</span>
            </div>
          </div>
          <p class="text-sm font-semibold text-slate-600">Events/Min</p>
          <p class="text-3xl font-bold text-slate-900 mt-1">
            <%= @dashboard_data[:events_per_minute] || 0 %>
          </p>
          <p class="text-xs text-slate-500 mt-2">Real-time data flow</p>
        </div>
      </div>

      <!-- Processing Latency -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-yellow-500/5 to-amber-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-center justify-between mb-4">
            <div class="p-3 bg-gradient-to-br from-yellow-500 to-amber-600 rounded-xl shadow-lg group-hover:shadow-yellow-500/25 transition-all duration-300">
              <svg class="h-6 w-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
              </svg>
            </div>
          </div>
          <p class="text-sm font-semibold text-slate-600">Avg Latency</p>
          <p class="text-3xl font-bold text-slate-900 mt-1">
            <%= @dashboard_data[:avg_latency] || '0ms' %>
          </p>
          <p class="text-xs text-slate-500 mt-2">Processing speed</p>
        </div>
      </div>

      <!-- Error Rate -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-red-500/5 to-rose-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-center justify-between mb-4">
            <div class="p-3 bg-gradient-to-br from-red-500 to-rose-600 rounded-xl shadow-lg group-hover:shadow-red-500/25 transition-all duration-300">
              <svg class="h-6 w-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
              </svg>
            </div>
          </div>
          <p class="text-sm font-semibold text-slate-600">Error Rate</p>
          <p class="text-3xl font-bold <%=
            (@dashboard_data[:error_rate] || '0').to_f == 0 ? 'text-green-600' :
            (@dashboard_data[:error_rate] || '0').to_f <= 1 ? 'text-yellow-600' :
            'text-red-600'
          %> mt-1">
            <%= @dashboard_data[:error_rate] || '0%' %>
          </p>
          <p class="text-xs text-slate-500 mt-2">System health</p>
        </div>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
      <!-- Real-Time Data Flow Chart -->
      <div class="relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-cyan-500/5"></div>
        <div class="relative">
          <div class="px-6 py-4 border-b border-slate-200/50">
            <div class="flex items-center space-x-2">
              <div class="p-2 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-lg shadow-lg">
                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                </svg>
              </div>
              <h3 class="text-xl font-bold bg-gradient-to-r from-gray-900 to-blue-900 bg-clip-text text-transparent">
                Real-Time Data Flow
              </h3>
            </div>
          </div>
          <div class="p-6">
            <div class="h-64 flex items-center justify-center bg-gradient-to-br from-gray-50 to-blue-50 rounded-xl border border-gray-200/50">
              <div class="text-center">
                <div class="mb-4">
                  <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-100 to-cyan-100 rounded-full">
                    <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                </div>
                <p class="text-slate-600 font-medium">Real-time chart will be rendered here</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Performance Metrics -->
      <div class="relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5"></div>
        <div class="relative">
          <div class="px-6 py-4 border-b border-slate-200/50">
            <div class="flex items-center space-x-2">
              <div class="p-2 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg shadow-lg">
                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
              <h3 class="text-xl font-bold bg-gradient-to-r from-gray-900 to-purple-900 bg-clip-text text-transparent">
                Performance Metrics
              </h3>
            </div>
          </div>
          <div class="p-6">
            <div class="h-64 flex items-center justify-center bg-gradient-to-br from-gray-50 to-purple-50 rounded-xl border border-gray-200/50">
              <div class="text-center">
                <div class="mb-4">
                  <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-purple-100 to-pink-100 rounded-full">
                    <svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                </div>
                <p class="text-slate-600 font-medium">Performance chart will be rendered here</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Alerts and Anomalies -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Active Alerts -->
      <div class="relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-br from-red-500/5 to-rose-500/5"></div>
        <div class="relative">
          <div class="px-6 py-4 border-b border-slate-200/50">
            <div class="flex items-center space-x-2">
              <div class="p-2 bg-gradient-to-br from-red-500 to-rose-600 rounded-lg shadow-lg">
                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <h3 class="text-xl font-bold bg-gradient-to-r from-gray-900 to-red-900 bg-clip-text text-transparent">
                Active Alerts
              </h3>
            </div>
          </div>
          <div class="p-6">
            <% if @dashboard_data[:alerts]&.any? %>
              <div class="space-y-3">
                <% @dashboard_data[:alerts].each do |alert| %>
                  <div class="group relative bg-gradient-to-br from-red-50 to-rose-50 p-4 rounded-xl border border-red-200/50 hover:shadow-lg hover:-translate-y-0.5 transition-all duration-300">
                    <div class="flex items-start space-x-3">
                      <div class="flex-shrink-0">
                        <div class="p-2 bg-gradient-to-br from-red-500 to-rose-600 rounded-lg shadow-lg">
                          <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                          </svg>
                        </div>
                      </div>
                      <div class="flex-1">
                        <p class="text-sm font-bold text-red-800"><%= alert[:title] %></p>
                        <p class="text-sm text-red-600 mt-1"><%= alert[:description] %></p>
                      </div>
                    </div>
                  </div>
                <% end %>
              </div>
            <% else %>
              <div class="text-center py-12">
                <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-xl">
                  <svg class="h-10 w-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <h3 class="text-lg font-bold text-gray-900 mb-1">No active alerts</h3>
                <p class="text-slate-600">All systems are running normally.</p>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Recent Anomalies -->
      <div class="relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-br from-yellow-500/5 to-amber-500/5"></div>
        <div class="relative">
          <div class="px-6 py-4 border-b border-slate-200/50">
            <div class="flex items-center space-x-2">
              <div class="p-2 bg-gradient-to-br from-yellow-500 to-amber-600 rounded-lg shadow-lg">
                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.618 5.984A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016zM12 9v2m0 4h.01" />
                </svg>
              </div>
              <h3 class="text-xl font-bold bg-gradient-to-r from-gray-900 to-yellow-900 bg-clip-text text-transparent">
                Recent Anomalies
              </h3>
            </div>
          </div>
          <div class="p-6">
            <% if @dashboard_data[:anomalies]&.any? %>
              <div class="space-y-3">
                <% @dashboard_data[:anomalies].each do |anomaly| %>
                  <div class="group relative bg-gradient-to-br from-yellow-50 to-amber-50 p-4 rounded-xl border border-yellow-200/50 hover:shadow-lg hover:-translate-y-0.5 transition-all duration-300">
                    <div class="flex items-start space-x-3">
                      <div class="flex-shrink-0">
                        <div class="p-2 bg-gradient-to-br from-yellow-500 to-amber-600 rounded-lg shadow-lg">
                          <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                          </svg>
                        </div>
                      </div>
                      <div class="flex-1">
                        <p class="text-sm font-bold text-yellow-800"><%= anomaly[:title] %></p>
                        <p class="text-sm text-yellow-600 mt-1"><%= anomaly[:description] %></p>
                      </div>
                    </div>
                  </div>
                <% end %>
              </div>
            <% else %>
              <div class="text-center py-12">
                <div class="w-20 h-20 bg-gradient-to-br from-gray-200 to-gray-300 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg class="h-10 w-10 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                  </svg>
                </div>
                <h3 class="text-lg font-bold text-gray-900 mb-1">No anomalies detected</h3>
                <p class="text-slate-600">Data patterns are within normal ranges.</p>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Live Update Indicator -->
  <div class="fixed bottom-6 right-6 z-50">
    <div class="group relative">
      <div class="absolute inset-0 bg-gradient-to-r from-orange-600 to-amber-600 rounded-full blur-lg opacity-75 group-hover:opacity-100 transition-opacity duration-300 animate-pulse"></div>
      <div class="relative bg-white/90 backdrop-blur-xl rounded-full px-4 py-2 shadow-xl border border-white/20 flex items-center space-x-2">
        <div class="w-2 h-2 bg-gradient-to-r from-orange-500 to-amber-600 rounded-full animate-pulse"></div>
        <span class="text-sm font-semibold text-slate-700">Real-Time Active</span>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('turbo:load', () => {
    // Smooth scroll animations
    const animateOnScroll = () => {
      const elements = document.querySelectorAll('.relative.bg-white\\/80, .group.relative.bg-white\\/80');
      
      elements.forEach((element, index) => {
        const rect = element.getBoundingClientRect();
        const isVisible = rect.top <= window.innerHeight && rect.bottom >= 0;
        
        if (isVisible && !element.classList.contains('animate-in')) {
          setTimeout(() => {
            element.classList.add('animate-in');
            element.style.opacity = '0';
            element.style.transform = 'translateY(20px)';
            
            requestAnimationFrame(() => {
              element.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';
              element.style.opacity = '1';
              element.style.transform = 'translateY(0)';
            });
          }, index * 50);
        }
      });
    };
    
    // Initialize animations
    animateOnScroll();
    window.addEventListener('scroll', animateOnScroll);
    
    // Auto-refresh dashboard data every 30 seconds
    const intervalId = setInterval(() => {
      fetch('<%= live_data_ai_real_time_analytics_path %>')
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            // Update metrics here
            console.log('Dashboard data updated:', data.timestamp);
            
            // Add pulse effect to live indicator
            const liveIndicator = document.querySelector('.fixed.bottom-6.right-6');
            if (liveIndicator) {
              liveIndicator.classList.add('scale-110');
              setTimeout(() => {
                liveIndicator.classList.remove('scale-110');
              }, 300);
            }
          }
        })
        .catch(error => console.error('Error updating dashboard:', error));
    }, 30000);
    
    // Clean up interval on page leave
    document.addEventListener('turbo:before-visit', () => {
      clearInterval(intervalId);
    });
  });
</script>