<% content_for :page_title, "AI Business Intelligence" %>

<div class="min-h-screen bg-gradient-to-br from-slate-50 via-indigo-50 to-purple-50">
  <!-- Premium Header with Glassmorphism -->
  <div class="relative bg-white/80 backdrop-blur-xl border-b border-white/20 shadow-xl">
    <div class="absolute inset-0 bg-gradient-to-r from-indigo-600/10 to-purple-600/10"></div>
    <div class="relative px-4 sm:px-6 lg:px-8 py-8">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="space-y-2">
          <div class="flex items-center space-x-3">
            <div class="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl shadow-lg animate-pulse">
              <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a9 9 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
            </div>
            <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 via-indigo-900 to-purple-900 bg-clip-text text-transparent">
              AI Business Intelligence
            </h1>
          </div>
          <p class="text-slate-600 font-medium">Ask questions about your business data in natural language</p>
        </div>
        <div class="mt-4 sm:mt-0 flex items-center space-x-2">
          <div class="flex items-center space-x-2 px-4 py-2 bg-green-100/80 backdrop-blur-sm rounded-full border border-green-200/50">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span class="text-green-700 text-sm font-semibold">AI Ready</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="px-4 sm:px-6 lg:px-8 py-8" 
     data-controller="ai-query" 
     data-ai-query-process-url-value="<%= process_query_ai_queries_path %>"
     data-ai-query-suggestions-url-value="<%= suggestions_ai_queries_path %>">

    <!-- Main Query Interface -->
    <div class="mb-12">
      <div class="relative max-w-4xl mx-auto">
        <!-- Query Input Card -->
        <div class="relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
          <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-purple-500/5"></div>
          <div class="relative p-8">
            <div class="text-center mb-6">
              <h2 class="text-2xl font-bold bg-gradient-to-r from-gray-900 to-indigo-900 bg-clip-text text-transparent mb-3">
                What would you like to know about your business?
              </h2>
              <p class="text-slate-600 font-medium">
                Ask questions in plain English - our AI will analyze your data and provide insights
              </p>
            </div>

            <!-- Query Form -->
            <form data-action="submit->ai-query#submitQuery" class="space-y-4">
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <div class="p-1.5 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg shadow-lg">
                    <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                  </div>
                </div>
                
                <input type="text" 
                       data-ai-query-target="queryInput"
                       data-action="input->ai-query#handleInput focus->ai-query#showSuggestions"
                       placeholder="e.g., Show me customers who haven't ordered in 30 days..."
                       class="block w-full pl-14 pr-20 py-4 text-lg border border-white/20 rounded-xl bg-white/50 backdrop-blur-sm text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 hover:bg-white/70">
                
                <div class="absolute inset-y-0 right-0 flex items-center pr-4">
                  <button type="submit" 
                          data-ai-query-target="submitButton"
                          class="group inline-flex items-center px-5 py-2.5 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed">
                    <span data-ai-query-target="submitText">Ask AI</span>
                    <svg data-ai-query-target="loadingSpinner" class="hidden animate-spin ml-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                      <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" class="opacity-25"></circle>
                      <path fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" class="opacity-75"></path>
                    </svg>
                    <svg class="ml-2 h-4 w-4 group-hover:rotate-12 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                  </button>
                </div>
              </div>

              <!-- Query Suggestions Dropdown -->
              <div data-ai-query-target="suggestionsDropdown" class="hidden absolute z-10 w-full mt-1 bg-white/95 backdrop-blur-xl rounded-xl shadow-2xl border border-white/20 max-h-64 overflow-y-auto">
                <div data-ai-query-target="suggestionsList" class="py-2"></div>
              </div>
            </form>

            <!-- Quick Actions -->
            <div class="mt-6 flex flex-wrap gap-3 justify-center">
              <% ["Revenue analysis", "Customer insights", "Product performance", "Sales trends"].each do |suggestion| %>
                <button data-action="click->ai-query#selectSuggestion" 
                        data-query="<%= suggestion %>"
                        class="group inline-flex items-center px-4 py-2 bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 rounded-xl font-medium shadow-md hover:shadow-lg hover:-translate-y-0.5 transition-all duration-300">
                  <span class="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent font-semibold"><%= suggestion %></span>
                </button>
              <% end %>
            </div>
          </div>
        </div>

        <!-- Results Container -->
        <div data-ai-query-target="resultsContainer" class="hidden mt-8">
          <div class="relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden">
            <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-500/5"></div>
            <div class="relative">
              <div class="border-b border-slate-200/50 px-6 py-4">
                <h3 class="text-lg font-bold bg-gradient-to-r from-gray-900 to-blue-900 bg-clip-text text-transparent">AI Analysis Results</h3>
              </div>
              <div data-ai-query-target="resultsContent" class="p-6">
                <!-- Results will be inserted here -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Dashboard Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Data Summary Card -->
      <div class="lg:col-span-1">
        <div class="relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
          <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-500/5"></div>
          <div class="relative">
            <div class="px-6 py-4 border-b border-slate-200/50">
              <h3 class="text-lg font-bold bg-gradient-to-r from-gray-900 to-blue-900 bg-clip-text text-transparent flex items-center">
                <div class="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg shadow-lg mr-3">
                  <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4"></path>
                  </svg>
                </div>
                Your Data Overview
              </h3>
            </div>
            <div class="p-6 space-y-4">
              <% if @data_summary.present? %>
                <div class="grid grid-cols-2 gap-4">
                  <div class="group relative bg-gradient-to-br from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200/50 hover:shadow-lg hover:-translate-y-0.5 transition-all duration-300">
                    <div class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                      <%= number_with_delimiter(@data_summary[:customers] || 0) %>
                    </div>
                    <div class="text-sm font-medium text-slate-600">Customers</div>
                  </div>
                  <div class="group relative bg-gradient-to-br from-green-50 to-emerald-50 p-4 rounded-xl border border-green-200/50 hover:shadow-lg hover:-translate-y-0.5 transition-all duration-300">
                    <div class="text-2xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                      <%= number_with_delimiter(@data_summary[:orders] || 0) %>
                    </div>
                    <div class="text-sm font-medium text-slate-600">Orders</div>
                  </div>
                  <div class="group relative bg-gradient-to-br from-purple-50 to-pink-50 p-4 rounded-xl border border-purple-200/50 hover:shadow-lg hover:-translate-y-0.5 transition-all duration-300">
                    <div class="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      <%= number_with_delimiter(@data_summary[:products] || 0) %>
                    </div>
                    <div class="text-sm font-medium text-slate-600">Products</div>
                  </div>
                  <div class="group relative bg-gradient-to-br from-orange-50 to-amber-50 p-4 rounded-xl border border-orange-200/50 hover:shadow-lg hover:-translate-y-0.5 transition-all duration-300">
                    <div class="text-2xl font-bold bg-gradient-to-r from-orange-600 to-amber-600 bg-clip-text text-transparent">
                      <%= @data_summary[:data_sources] || 0 %>
                    </div>
                    <div class="text-sm font-medium text-slate-600">Data Sources</div>
                  </div>
                </div>
            
                <% if @data_summary[:date_range][:latest] %>
                  <div class="pt-4 border-t border-slate-200/50">
                    <div class="text-sm text-slate-600">
                      <span class="font-semibold">Last Updated:</span>
                      <%= time_ago_in_words(@data_summary[:date_range][:latest]) %> ago
                    </div>
                  </div>
                <% end %>
              <% else %>
                <div class="text-center py-8">
                  <div class="w-16 h-16 bg-gradient-to-br from-gray-200 to-gray-300 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="h-8 w-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                    </svg>
                  </div>
                  <p class="text-gray-500">Connect your first data source to get started</p>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>

      <!-- Popular Queries Section -->
      <div class="lg:col-span-2">
        <div class="relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
          <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-purple-500/5"></div>
          <div class="relative">
            <div class="px-6 py-4 border-b border-slate-200/50">
              <h3 class="text-lg font-bold bg-gradient-to-r from-gray-900 to-indigo-900 bg-clip-text text-transparent flex items-center">
                <div class="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg shadow-lg mr-3">
                  <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                  </svg>
                </div>
                Popular Business Questions
              </h3>
              <p class="text-sm text-slate-600 font-medium mt-1 ml-11">Click any question to analyze your data</p>
            </div>
            <div class="p-6">
              <% if @popular_queries.present? %>
                <div class="space-y-6">
                  <% @popular_queries.each do |category| %>
                    <div>
                      <h4 class="text-sm font-bold text-gray-900 mb-3 flex items-center">
                        <div class="w-2 h-2 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full mr-2"></div>
                        <%= category[:category] %>
                      </h4>
                      <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <% category[:queries].each do |query| %>
                          <button data-action="click->ai-query#selectSuggestion" 
                                  data-query="<%= query %>"
                                  class="group relative text-left p-4 bg-gradient-to-br from-white to-gray-50 rounded-xl border border-gray-200/50 hover:shadow-lg hover:-translate-y-0.5 transition-all duration-300">
                            <div class="flex items-start justify-between">
                              <span class="text-sm font-medium text-gray-700 group-hover:text-indigo-600 transition-colors duration-300">
                                <%= query %>
                              </span>
                              <svg class="w-4 h-4 text-gray-400 group-hover:text-indigo-500 transition-colors duration-200 flex-shrink-0 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                              </svg>
                            </div>
                          </button>
                        <% end %>
                      </div>
                    </div>
                  <% end %>
                </div>
              <% else %>
                <div class="text-center py-8">
                  <div class="w-16 h-16 bg-gradient-to-br from-gray-200 to-gray-300 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="h-8 w-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                  <p class="text-gray-500">Query examples will appear here</p>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Insights Section -->
    <% if @recent_insights.present? && @recent_insights.any? %>
      <div class="mt-8">
        <div class="relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden">
          <div class="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5"></div>
          <div class="relative">
            <div class="px-6 py-4 border-b border-slate-200/50">
              <h3 class="text-lg font-bold bg-gradient-to-r from-gray-900 to-green-900 bg-clip-text text-transparent flex items-center">
                <div class="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg shadow-lg mr-3">
                  <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a9 9 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                  </svg>
                </div>
                Recent AI Insights
              </h3>
            </div>
            <div class="p-6">
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <% @recent_insights.each do |insight| %>
                  <div class="group relative bg-gradient-to-br from-white to-gray-50 p-4 rounded-xl border border-gray-200/50 hover:shadow-lg hover:-translate-y-0.5 transition-all duration-300">
                    <div class="flex items-start justify-between mb-2">
                      <span class="inline-flex items-center px-3 py-1 text-xs font-semibold bg-gradient-to-r from-blue-400 to-indigo-500 text-white rounded-full shadow-lg">
                        <%= insight.insight_type.humanize %>
                      </span>
                      <span class="text-xs text-gray-500">
                        <%= time_ago_in_words(insight.created_at) %> ago
                      </span>
                    </div>
                    <h4 class="font-semibold text-gray-900 text-sm mb-2 group-hover:text-indigo-600 transition-colors duration-300">
                      <%= insight.title %>
                    </h4>
                    <p class="text-sm text-gray-600 line-clamp-2">
                      <%= insight.description %>
                    </p>
                    <% if insight.actionable? %>
                      <div class="mt-3 flex items-center text-xs font-semibold text-green-600">
                        <div class="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                        Actionable
                      </div>
                    <% end %>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      </div>
    <% end %>
  </div>
  
  <!-- Live AI Indicator -->
  <div class="fixed bottom-6 right-6 z-50">
    <div class="group relative">
      <div class="absolute inset-0 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-full blur-lg opacity-75 group-hover:opacity-100 transition-opacity duration-300 animate-pulse"></div>
      <div class="relative bg-white/90 backdrop-blur-xl rounded-full px-4 py-2 shadow-xl border border-white/20 flex items-center space-x-2">
        <div class="w-2 h-2 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full animate-pulse"></div>
        <span class="text-sm font-semibold text-slate-700">AI Query Active</span>
      </div>
    </div>
  </div>
</div>

<!-- Toast Notification Container -->
<div data-ai-query-target="toastContainer" 
     class="fixed top-4 right-4 z-50 space-y-2"
     style="pointer-events: none;">
  <!-- Toast notifications will be inserted here -->
</div>

<script>
document.addEventListener('turbo:load', () => {
  // Smooth scroll animations
  const animateOnScroll = () => {
    const elements = document.querySelectorAll('.relative.bg-white\\/80, .group.relative.bg-white\\/80');
    
    elements.forEach((element, index) => {
      const rect = element.getBoundingClientRect();
      const isVisible = rect.top <= window.innerHeight && rect.bottom >= 0;
      
      if (isVisible && !element.classList.contains('animate-in')) {
        setTimeout(() => {
          element.classList.add('animate-in');
          element.style.opacity = '0';
          element.style.transform = 'translateY(20px)';
          
          requestAnimationFrame(() => {
            element.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
          });
        }, index * 50);
      }
    });
  };
  
  // Initialize animations
  animateOnScroll();
  window.addEventListener('scroll', animateOnScroll);
  
  // Animate stat cards
  document.querySelectorAll('.group.relative.bg-gradient-to-br').forEach((card, index) => {
    card.addEventListener('mouseenter', () => {
      card.style.transform = 'translateY(-4px) scale(1.02)';
    });
    
    card.addEventListener('mouseleave', () => {
      card.style.transform = 'translateY(0) scale(1)';
    });
  });
});
</script>
