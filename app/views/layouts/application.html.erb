<!DOCTYPE html>
<html lang="en" class="h-full bg-gray-50">
  <head>
    <%= render 'shared/head_meta' %>
    <%= render 'shared/head_stylesheets' %>
    <%= render 'shared/head_fonts' %>
    <%= render 'shared/head_scripts' %>
  </head>

  <body class="h-full" data-controller="application dropdown-closer">
    <%= render 'shared/flash_messages_container' %>
    
    <% if user_signed_in? %>
      <%= render 'layouts/dashboard_layout' %>
    <% else %>
      <%= render 'layouts/landing_layout' %>
    <% end %>
    
    <!-- Command Palette -->
    <div data-controller="command-palette" 
         data-command-palette-target="modal" 
         class="fixed inset-0 z-50 hidden items-start justify-center pt-20 px-4">
      <div data-command-palette-target="backdrop" 
           data-action="click->command-palette#backdropClick"
           class="fixed inset-0 bg-black/50 backdrop-blur-sm"></div>
      <div class="relative w-full max-w-2xl bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 overflow-hidden">
        <div class="p-4 border-b border-gray-200/50">
          <input type="text" 
                 data-command-palette-target="input"
                 data-action="input->command-palette#filter keydown->command-palette#navigate"
                 class="w-full px-4 py-3 text-lg bg-transparent border-0 outline-none placeholder-gray-400"
                 placeholder="Type a command or search..." 
                 autocomplete="off">
        </div>
        <div data-command-palette-target="results" 
             class="max-h-96 overflow-y-auto p-2"></div>
      </div>
    </div>
  </body>
</html>