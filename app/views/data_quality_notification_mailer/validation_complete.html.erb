<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Data Quality Validation Complete</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
      }
      .header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 30px;
        border-radius: 8px 8px 0 0;
        text-align: center;
      }
      .content {
        background: #fff;
        padding: 30px;
        border: 1px solid #e1e5e9;
        border-top: none;
      }
      .success-box {
        background: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 6px;
        padding: 20px;
        margin: 20px 0;
      }
      .score-display {
        text-align: center;
        margin: 25px 0;
      }
      .score-circle {
        display: inline-block;
        width: 100px;
        height: 100px;
        border-radius: 50%;
        color: white;
        line-height: 100px;
        font-size: 28px;
        font-weight: bold;
        margin-bottom: 15px;
      }
      .score-excellent { background: #28a745; }
      .score-good { background: #17a2b8; }
      .score-warning { background: #ffc107; color: #333; }
      .score-danger { background: #dc3545; }
      .metrics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 15px;
        margin: 25px 0;
      }
      .metric-card {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 6px;
        text-align: center;
        border-left: 4px solid #007bff;
      }
      .metric-value {
        font-size: 24px;
        font-weight: bold;
        color: #495057;
      }
      .metric-label {
        font-size: 12px;
        color: #6c757d;
        text-transform: uppercase;
        margin-top: 5px;
      }
      .stats-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 20px;
        margin: 25px 0;
      }
      .stat-item {
        text-align: center;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 6px;
      }
      .stat-number {
        font-size: 32px;
        font-weight: bold;
        color: #007bff;
      }
      .stat-label {
        font-size: 14px;
        color: #6c757d;
        margin-top: 5px;
      }
      .summary-box {
        background: #e7f3ff;
        border: 1px solid #b8daff;
        border-radius: 6px;
        padding: 20px;
        margin: 20px 0;
      }
      .btn {
        display: inline-block;
        padding: 12px 24px;
        background: #007bff;
        color: white;
        text-decoration: none;
        border-radius: 6px;
        font-weight: 500;
        margin: 10px 5px;
      }
      .btn:hover {
        background: #0056b3;
      }
      .btn-success {
        background: #28a745;
      }
      .btn-success:hover {
        background: #1e7e34;
      }
      .footer {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 0 0 8px 8px;
        text-align: center;
        font-size: 14px;
        color: #6c757d;
      }
      .trend-indicator {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: bold;
        margin-left: 10px;
      }
      .trend-up {
        background: #d4edda;
        color: #155724;
      }
      .trend-down {
        background: #f8d7da;
        color: #721c24;
      }
      .trend-stable {
        background: #e2e3e5;
        color: #383d41;
      }
    </style>
  </head>
  <body>
    <div class="header">
      <h1>✅ Validation Complete</h1>
      <p>Data quality analysis has finished successfully</p>
    </div>
    
    <div class="content">
      <div class="success-box">
        <h3>📊 <%= @data_source.name %></h3>
        <p><strong>Organization:</strong> <%= @organization.name %></p>
        <p><strong>Validation Type:</strong> <%= @report.validation_type&.titleize || 'Full Analysis' %></p>
        <p><strong>Completed:</strong> <%= @report.completed_at&.strftime('%B %d, %Y at %I:%M %p') || @report.created_at.strftime('%B %d, %Y at %I:%M %p') %></p>
        <% if @report.duration %>
          <p><strong>Duration:</strong> <%= @report.duration_in_words %></p>
        <% end %>
      </div>
      
      <div class="score-display">
        <div class="score-circle <%= @report.overall_score >= 90 ? 'score-excellent' : @report.overall_score >= 80 ? 'score-good' : @report.overall_score >= 60 ? 'score-warning' : 'score-danger' %>">
          <%= @report.overall_score.round %>%
        </div>
        <h3>Overall Quality Score</h3>
        <p style="font-size: 18px; margin: 10px 0;">
          Status: <strong style="color: <%= @report.overall_score >= 80 ? '#28a745' : @report.overall_score >= 60 ? '#ffc107' : '#dc3545' %>">
            <%= @report.quality_status.titleize %>
          </strong>
          <% if @report.respond_to?(:quality_trend_compared_to_previous) %>
            <% trend = @report.quality_trend_compared_to_previous %>
            <% unless trend == 'no_data' %>
              <span class="trend-indicator trend-<%= trend == 'improving' ? 'up' : trend == 'declining' ? 'down' : 'stable' %>">
                <%= trend == 'improving' ? '↗ Improving' : trend == 'declining' ? '↘ Declining' : '→ Stable' %>
              </span>
            <% end %>
          <% end %>
        </p>
      </div>
      
      <div class="stats-row">
        <div class="stat-item">
          <div class="stat-number"><%= number_with_delimiter(@report.total_records) %></div>
          <div class="stat-label">Total Records</div>
        </div>
        <div class="stat-item">
          <div class="stat-number"><%= number_with_delimiter(@report.valid_records) %></div>
          <div class="stat-label">Valid Records</div>
        </div>
        <div class="stat-item">
          <div class="stat-number"><%= @report.issues_count %></div>
          <div class="stat-label">Issues Found</div>
        </div>
        <% if @report.respond_to?(:records_analyzed) && @report.records_analyzed %>
          <div class="stat-item">
            <div class="stat-number"><%= number_with_delimiter(@report.records_analyzed) %></div>
            <div class="stat-label">Records Analyzed</div>
          </div>
        <% end %>
      </div>
      
      <div class="metrics-grid">
        <div class="metric-card">
          <div class="metric-value"><%= @report.completeness_score.round %>%</div>
          <div class="metric-label">Completeness</div>
        </div>
        <div class="metric-card">
          <div class="metric-value"><%= @report.accuracy_score.round %>%</div>
          <div class="metric-label">Accuracy</div>
        </div>
        <div class="metric-card">
          <div class="metric-value"><%= @report.consistency_score.round %>%</div>
          <div class="metric-label">Consistency</div>
        </div>
        <div class="metric-card">
          <div class="metric-value"><%= @report.validity_score.round %>%</div>
          <div class="metric-label">Validity</div>
        </div>
        <div class="metric-card">
          <div class="metric-value"><%= @report.timeliness_score.round %>%</div>
          <div class="metric-label">Timeliness</div>
        </div>
        <% if @report.respond_to?(:uniqueness_score) && @report.uniqueness_score %>
          <div class="metric-card">
            <div class="metric-value"><%= @report.uniqueness_score.round %>%</div>
            <div class="metric-label">Uniqueness</div>
          </div>
        <% end %>
      </div>
      
      <% if @report.overall_score >= 80 %>
        <div class="summary-box">
          <h4>🎉 Great Job!</h4>
          <p><%= @report.summary_text if @report.respond_to?(:summary_text) %></p>
          <% if @report.recommendations.any? %>
            <p><strong>Next Steps:</strong> <%= @report.recommendations.first['description'] %></p>
          <% end %>
        </div>
      <% elsif @report.issues.any? %>
        <div class="summary-box">
          <h4>📋 Key Findings</h4>
          <% if @report.respond_to?(:high_priority_issues) && @report.high_priority_issues.any? %>
            <p><strong>High Priority Issues:</strong> <%= @report.high_priority_issues.count %> issues need immediate attention.</p>
          <% end %>
          <% if @report.recommendations.any? %>
            <p><strong>Top Recommendation:</strong> <%= @report.recommendations.first['title'] %></p>
          <% end %>
        </div>
      <% end %>
      
      <div style="text-align: center; margin-top: 30px;">
        <a href="<%= data_quality_url(@data_source) %>" class="btn btn-success">
          📊 View Full Report
        </a>
        <a href="<%= data_source_url(@data_source) %>" class="btn">
          ⚙️ Manage Data Source
        </a>
        <% if @report.overall_score < 80 %>
          <a href="<%= data_quality_url(@data_source, anchor: 'recommendations') %>" class="btn" style="background: #ffc107; color: #333;">
            💡 View Recommendations
          </a>
        <% end %>
      </div>
    </div>
    
    <div class="footer">
      <p>🔄 <strong>Next validation:</strong> Automatic validations run daily at 2:00 AM UTC</p>
      <p>This notification was sent because you have validation completion alerts enabled.</p>
      <p>To modify your notification preferences, visit your account settings.</p>
    </div>
  </body>
</html>