<!-- Step 3: Data Preview -->
<div class="wizard-step" id="step-3" data-data-source-wizard-target="step" style="display: none;" role="tabpanel" aria-labelledby="step-3-tab">
  <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
    <div class="mb-8">
      <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2 flex items-center">
        <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mr-3">
          <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
          </svg>
        </div>
        Data Preview
      </h3>
      <p class="text-gray-600 dark:text-gray-400">
        Preview your data to ensure everything looks correct before finalizing the setup.
      </p>
    </div>

    <!-- Preview Content -->
    <div class="preview-container" data-data-source-wizard-target="previewContainer">
      <!-- Loading State -->
      <div class="preview-loading" data-data-source-wizard-target="previewLoading" style="display: none;">
        <div class="flex items-center justify-center py-12">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
          <span class="ml-3 text-gray-600 dark:text-gray-400">Loading preview...</span>
        </div>
      </div>

      <!-- File Upload Interface -->
      <div class="file-upload-section" data-data-source-wizard-target="fileUploadSection" style="display: none;">
        <%= render 'file_upload_interface', config: file_config %>
      </div>

      <!-- API Data Preview -->
      <div class="api-preview-section" data-data-source-wizard-target="apiPreviewSection" style="display: none;">
        <%= render 'api_data_preview' %>
      </div>

      <!-- No Preview Available -->
      <div class="no-preview" data-data-source-wizard-target="noPreview">
        <div class="text-center py-12">
          <svg class="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Ready for Preview
          </h4>
          <p class="text-gray-600 dark:text-gray-400 mb-6">
            Complete the configuration in the previous step to see a preview of your data.
          </p>
          <button type="button" 
                  class="inline-flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white font-medium rounded-lg transition-colors duration-200"
                  data-action="click->data-source-wizard#goToStep"
                  data-step="2">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Configuration
          </button>
        </div>
      </div>

      <!-- Preview Error State -->
      <div class="preview-error" data-data-source-wizard-target="previewError" style="display: none;">
        <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <svg class="w-5 h-5 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-3 flex-1">
              <h4 class="text-sm font-medium text-red-800 dark:text-red-200 mb-1">
                Preview Error
              </h4>
              <div class="text-sm text-red-700 dark:text-red-300" data-data-source-wizard-target="previewErrorMessage">
                <!-- Error message will be inserted here -->
              </div>
              <div class="mt-4">
                <button type="button" 
                        class="inline-flex items-center px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"
                        data-action="click->data-source-wizard#retryPreview">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                  </svg>
                  Retry Preview
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Successful Preview -->
      <div class="preview-success" data-data-source-wizard-target="previewSuccess" style="display: none;">
        <!-- Data Table Preview -->
        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
          <div class="flex items-center justify-between mb-4">
            <h4 class="text-lg font-medium text-gray-900 dark:text-white flex items-center">
              <svg class="w-5 h-5 mr-2 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              Data Preview
            </h4>
            <div class="flex items-center space-x-4">
              <span class="text-sm text-gray-600 dark:text-gray-400" data-data-source-wizard-target="recordCount">
                <!-- Record count will be inserted here -->
              </span>
              <button type="button" 
                      class="text-sm text-indigo-600 dark:text-indigo-400 hover:text-indigo-700 dark:hover:text-indigo-300 font-medium"
                      data-action="click->data-source-wizard#refreshPreview">
                Refresh
              </button>
            </div>
          </div>
          
          <!-- Preview Table -->
          <div class="overflow-x-auto">
            <div data-data-source-wizard-target="previewTable" class="min-w-full">
              <!-- Table content will be dynamically inserted here -->
            </div>
          </div>
        </div>

        <!-- Data Quality Insights -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div class="flex items-center">
              <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-3">
                <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">Columns</p>
                <p class="text-lg font-semibold text-blue-600 dark:text-blue-400" data-data-source-wizard-target="columnCount">-</p>
              </div>
            </div>
          </div>
          
          <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div class="flex items-center">
              <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mr-3">
                <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">Data Quality</p>
                <p class="text-lg font-semibold text-green-600 dark:text-green-400" data-data-source-wizard-target="qualityScore">-</p>
              </div>
            </div>
          </div>
          
          <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div class="flex items-center">
              <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mr-3">
                <svg class="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">Est. Size</p>
                <p class="text-lg font-semibold text-purple-600 dark:text-purple-400" data-data-source-wizard-target="estimatedSize">-</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Column Mapping (if applicable) -->
        <div class="column-mapping-section" data-data-source-wizard-target="columnMapping" style="display: none;">
          <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
            <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
              <svg class="w-5 h-5 mr-2 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4"></path>
              </svg>
              Column Mapping
            </h4>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
              Map your source columns to the appropriate data types and formats.
            </p>
            <div data-data-source-wizard-target="columnMappingContent">
              <!-- Column mapping interface will be inserted here -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>