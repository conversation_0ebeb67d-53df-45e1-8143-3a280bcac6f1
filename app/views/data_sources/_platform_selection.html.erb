<!-- Platform Selection Step -->
<div class="px-8 py-8">
  <div class="text-center mb-8">
    <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">Choose Your Data Platform</h2>
    <p class="text-gray-600 dark:text-gray-300">Select the platform you want to connect to your data pipeline</p>
  </div>
  
  <!-- Available Platforms Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8" 
       role="radiogroup"
       aria-label="Select data source platform">
    <% data_source_configs.each do |key, config| %>
      <div class="platform-card group relative bg-white dark:bg-gray-700 rounded-xl border-2 border-gray-200 dark:border-gray-600 hover:border-indigo-500 dark:hover:border-indigo-400 transition-all duration-200 cursor-pointer transform hover:scale-105 hover:shadow-lg focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2"
           data-platform="<%= key %>"
           data-action="click->data-source-wizard#selectPlatform keydown->data-source-wizard#handleKeydown"
           role="radio"
           tabindex="0"
           aria-label="Select <%= config[:name] %> platform"
           data-data-source-wizard-target="platformCard">
        
        <!-- Hidden Radio Input -->
        <%= form.radio_button :source_type, key, 
              class: "sr-only",
              id: "platform_#{key}",
              data: { data_source_wizard_target: "platformInput" } %>
        
        <!-- Selection Indicator -->
        <div class="absolute top-4 right-4 w-6 h-6 rounded-full border-2 border-gray-300 dark:border-gray-500 group-hover:border-indigo-500 transition-colors duration-200"
             data-data-source-wizard-target="indicator"
             data-platform="<%= key %>">
          <div class="w-full h-full rounded-full bg-indigo-500 scale-0 transition-transform duration-200 flex items-center justify-center"
               data-data-source-wizard-target="checkmark"
               data-platform="<%= key %>">
            <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
        </div>
        
        <div class="p-6">
          <!-- Platform Header -->
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 rounded-lg bg-gradient-to-br <%= config.dig(:ui_metadata, :card_gradient) || 'from-indigo-500 to-blue-600' %> flex items-center justify-center mr-4">
              <% if config[:icon] %>
                <%= render "shared/icons/#{config[:icon]}", class: "w-6 h-6 text-white" %>
              <% else %>
                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              <% end %>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white"><%= config[:name] %></h3>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                           <% case config[:category]
                              when 'ecommerce' %>
                                bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                              <% when 'accounting' %>
                                bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                              <% when 'crm' %>
                                bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200
                              <% when 'marketing' %>
                                bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200
                              <% when 'file_upload' %>
                                bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200
                              <% else %>
                                bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200
                              <% end %>">
                <%= config[:category].humanize %>
              </span>
            </div>
          </div>
          
          <!-- Platform Description -->
          <p class="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-2"><%= config[:description] %></p>
          
          <!-- Platform Features -->
          <div class="space-y-2 mb-4">
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 mr-2 <%= config[:sync_type] == 'real_time' ? 'text-green-500' : 'text-blue-500' %>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<%= config[:sync_type] == 'real_time' ? 'M13 10V3L4 14h7v7l9-11h-7z' : 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z' %>"></path>
              </svg>
              <span class="text-gray-700 dark:text-gray-300">
                <%= config[:sync_type] == 'real_time' ? 'Real-time sync' : 'Scheduled sync' %>
              </span>
            </div>
            
            <div class="flex items-center text-sm">
              <svg class="w-4 h-4 mr-2 <%= config[:status] == 'production' ? 'text-green-500' : 'text-yellow-500' %>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<%= config[:status] == 'production' ? 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z' : 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z' %>"></path>
              </svg>
              <span class="text-gray-700 dark:text-gray-300">
                <%= config[:status] == 'production' ? 'Production ready' : config[:status].humanize %>
              </span>
            </div>
            
            <% if config[:features]&.any? %>
              <div class="flex items-center text-sm">
                <svg class="w-4 h-4 mr-2 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-gray-700 dark:text-gray-300">
                  <%= config[:features].first(2).join(', ') %>
                  <% if config[:features].length > 2 %>
                    <span class="text-gray-500">+<%= config[:features].length - 2 %> more</span>
                  <% end %>
                </span>
              </div>
            <% end %>
          </div>
          
          <!-- Priority Badge -->
          <% if config[:priority] && config[:priority] > 0 %>
            <div class="flex justify-end">
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                Popular
              </span>
            </div>
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
  
  <!-- Platform Selection Help -->
  <div class="text-center">
    <p class="text-sm text-gray-600 dark:text-gray-400">
      Don't see your platform? 
      <button type="button" 
              class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 font-medium underline"
              data-action="click->platform-selector#showRequestForm">
        Request a new integration
      </button>
    </p>
  </div>
</div>

<!-- Request Integration Modal (hidden by default) -->
<div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden" 
     data-platform-selector-target="requestModal"
     data-action="click->platform-selector#hideRequestForm">
  <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800"
       data-action="click->platform-selector#stopPropagation">
    <div class="mt-3">
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Request New Integration</h3>
      <form data-action="submit->platform-selector#submitRequest">
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Platform Name</label>
          <input type="text" 
                 class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                 placeholder="e.g., Salesforce, HubSpot"
                 data-platform-selector-target="requestPlatform"
                 required>
        </div>
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Use Case</label>
          <textarea rows="3" 
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Describe how you plan to use this integration"
                    data-platform-selector-target="requestUseCase"></textarea>
        </div>
        <div class="flex justify-end space-x-3">
          <button type="button" 
                  class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md"
                  data-action="click->platform-selector#hideRequestForm">
            Cancel
          </button>
          <button type="submit" 
                  class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 rounded-md">
            Submit Request
          </button>
        </div>
      </form>
    </div>
  </div>
</div>