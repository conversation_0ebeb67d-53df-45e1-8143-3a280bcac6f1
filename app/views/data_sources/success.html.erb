<% content_for :page_title, "Success!" %>

<div class="min-h-screen bg-gradient-to-br from-slate-50 via-green-50 to-emerald-50 flex items-center justify-center px-4"
     data-controller="confetti"
     data-confetti-trigger-value="auto">
  
  <div class="relative max-w-2xl w-full">
    <!-- Success Card -->
    <div class="relative bg-white/90 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 p-12 text-center">
      <div class="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5 rounded-3xl"></div>
      
      <div class="relative">
        <!-- Success Icon -->
        <div class="mb-8 flex justify-center">
          <div class="relative">
            <div class="w-32 h-32 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center shadow-2xl animate-bounce">
              <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="absolute inset-0 w-32 h-32 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full animate-ping opacity-30"></div>
          </div>
        </div>
        
        <!-- Success Message -->
        <h1 class="text-4xl font-bold bg-gradient-to-r from-gray-900 via-green-900 to-emerald-900 bg-clip-text text-transparent mb-4">
          Data Source Connected!
        </h1>
        
        <p class="text-xl text-slate-600 font-medium mb-8">
          <%= @data_source.name %> has been successfully connected
        </p>
        
        <!-- Stats -->
        <div class="grid grid-cols-3 gap-4 mb-8">
          <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-4">
            <div class="text-2xl font-bold text-blue-600 mb-1">
              <%= number_with_delimiter(@initial_sync_stats[:records] || 0) %>
            </div>
            <div class="text-sm text-slate-600">Records Found</div>
          </div>
          
          <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-4">
            <div class="text-2xl font-bold text-green-600 mb-1">
              <%= @initial_sync_stats[:tables] || 0 %>
            </div>
            <div class="text-sm text-slate-600">Data Tables</div>
          </div>
          
          <div class="bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-4">
            <div class="text-2xl font-bold text-purple-600 mb-1">
              <%= @initial_sync_stats[:sync_time] || '< 1 min' %>
            </div>
            <div class="text-sm text-slate-600">Sync Time</div>
          </div>
        </div>
        
        <!-- Next Steps -->
        <div class="bg-gradient-to-br from-gray-50 to-slate-50 rounded-xl p-6 mb-8 text-left">
          <h3 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
            <svg class="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
            </svg>
            What happens next?
          </h3>
          <ul class="space-y-2 text-sm text-slate-600">
            <li class="flex items-start">
              <svg class="w-5 h-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              Your data is being synchronized and will be ready for analysis shortly
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              Automatic syncs will run based on your configured schedule
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              You'll receive notifications about sync status and any issues
            </li>
          </ul>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <%= link_to data_source_path(@data_source), 
              class: "group inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300",
              data: {
                controller: "magnetic-button ripple-effect",
                action: "mousemove->magnetic-button#mouseMove mouseleave->magnetic-button#mouseLeave click->ripple-effect#click"
              } do %>
            <svg class="w-5 h-5 mr-2 group-hover:rotate-12 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
            View Data Source
          <% end %>
          
          <%= link_to analytics_path, 
              class: "group inline-flex items-center justify-center px-8 py-4 border border-slate-300/50 rounded-xl shadow-md text-slate-700 bg-white/70 backdrop-blur-sm hover:bg-white/90 hover:shadow-lg transition-all duration-300",
              data: {
                controller: "magnetic-button ripple-effect",
                action: "mousemove->magnetic-button#mouseMove mouseleave->magnetic-button#mouseLeave click->ripple-effect#click"
              } do %>
            <svg class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            Go to Analytics
          <% end %>
        </div>
      </div>
    </div>
    
    <!-- Floating particles animation -->
    <div class="absolute -top-10 -left-10 w-32 h-32 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full blur-3xl opacity-20 animate-pulse"></div>
    <div class="absolute -bottom-10 -right-10 w-40 h-40 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-full blur-3xl opacity-20 animate-pulse" style="animation-delay: 1s"></div>
    <div class="absolute top-1/2 -left-20 w-24 h-24 bg-gradient-to-br from-purple-400 to-pink-500 rounded-full blur-3xl opacity-20 animate-pulse" style="animation-delay: 2s"></div>
  </div>
</div>