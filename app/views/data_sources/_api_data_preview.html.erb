<!-- API Data Preview Interface -->
<div data-controller="api-data-preview"
     data-api-data-preview-source-type-value=""
     class="space-y-6">

  <!-- Connection Status -->
  <div class="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800 rounded-xl p-6">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <div class="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mr-4">
          <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
        </div>
        <div>
          <h4 class="font-semibold text-green-900 dark:text-green-100">Connection Established</h4>
          <p class="text-sm text-green-700 dark:text-green-300">Successfully connected to your data source</p>
        </div>
      </div>
      
      <button type="button" 
              data-action="click->api-data-preview#refreshPreview"
              class="inline-flex items-center px-3 py-2 border border-green-300 dark:border-green-600 text-green-700 dark:text-green-300 bg-white dark:bg-gray-800 hover:bg-green-50 dark:hover:bg-green-900/30 font-medium rounded-lg transition-colors duration-200">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
        Refresh Preview
      </button>
    </div>
  </div>

  <!-- Data Overview -->
  <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <!-- Data Types Available -->
    <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6">
      <h4 class="font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
        <svg class="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
        </svg>
        Available Data Types
      </h4>
      
      <div data-api-data-preview-target="dataTypes" class="space-y-3">
        <!-- Data types will be populated dynamically -->
        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div class="flex items-center">
            <div class="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
            <span class="text-sm font-medium text-gray-900 dark:text-white">Customers</span>
          </div>
          <span class="text-xs text-gray-500 dark:text-gray-400">2,450 records</span>
        </div>
        
        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div class="flex items-center">
            <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
            <span class="text-sm font-medium text-gray-900 dark:text-white">Orders</span>
          </div>
          <span class="text-xs text-gray-500 dark:text-gray-400">8,921 records</span>
        </div>
        
        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div class="flex items-center">
            <div class="w-3 h-3 bg-purple-500 rounded-full mr-3"></div>
            <span class="text-sm font-medium text-gray-900 dark:text-white">Products</span>
          </div>
          <span class="text-xs text-gray-500 dark:text-gray-400">156 records</span>
        </div>
      </div>
    </div>

    <!-- Sync Information -->
    <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6">
      <h4 class="font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
        <svg class="w-5 h-5 mr-2 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        Sync Information
      </h4>
      
      <div class="space-y-4">
        <div>
          <div class="flex justify-between text-sm mb-1">
            <span class="text-gray-600 dark:text-gray-400">Estimated Initial Sync</span>
            <span class="font-medium text-gray-900 dark:text-white">~15 minutes</span>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-1.5">
            <div class="bg-indigo-500 h-1.5 rounded-full" style="width: 0%"></div>
          </div>
        </div>
        
        <div class="text-sm text-gray-600 dark:text-gray-400">
          <div class="flex justify-between py-1">
            <span>Data Volume:</span>
            <span class="font-medium text-gray-900 dark:text-white">~2.3 MB</span>
          </div>
          <div class="flex justify-between py-1">
            <span>Last Updated:</span>
            <span class="font-medium text-gray-900 dark:text-white">2 hours ago</span>
          </div>
          <div class="flex justify-between py-1">
            <span>API Version:</span>
            <span class="font-medium text-gray-900 dark:text-white">2024-06</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Data Quality -->
    <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6">
      <h4 class="font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
        <svg class="w-5 h-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        Quality Score
      </h4>
      
      <div class="text-center">
        <div class="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">94%</div>
        <p class="text-sm text-gray-600 dark:text-gray-400">Excellent data quality</p>
        
        <div class="mt-4 space-y-2 text-xs text-left">
          <div class="flex items-center justify-between">
            <span class="text-gray-600 dark:text-gray-400">Completeness</span>
            <span class="font-medium text-green-600 dark:text-green-400">98%</span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-gray-600 dark:text-gray-400">Consistency</span>
            <span class="font-medium text-green-600 dark:text-green-400">95%</span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-gray-600 dark:text-gray-400">Accuracy</span>
            <span class="font-medium text-green-600 dark:text-green-400">91%</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Sample Data Preview -->
  <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between">
        <div>
          <h4 class="font-semibold text-gray-900 dark:text-white">Sample Data Preview</h4>
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Recent data from your connected source</p>
        </div>
        
        <!-- Data Type Selector -->
        <div data-controller="data-type-selector" class="flex space-x-2">
          <button type="button" 
                  data-action="click->data-type-selector#selectType"
                  data-type="customers"
                  class="px-3 py-1 text-sm font-medium text-white bg-blue-600 rounded-lg">
            Customers
          </button>
          <button type="button" 
                  data-action="click->data-type-selector#selectType"
                  data-type="orders"
                  class="px-3 py-1 text-sm font-medium text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors">
            Orders
          </button>
          <button type="button" 
                  data-action="click->data-type-selector#selectType"
                  data-type="products"
                  class="px-3 py-1 text-sm font-medium text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors">
            Products
          </button>
        </div>
      </div>
    </div>
    
    <!-- Sample Data Table -->
    <div class="overflow-x-auto">
      <div data-api-data-preview-target="sampleData" class="p-6">
        <!-- Default customers preview -->
        <div data-type="customers" class="space-y-4">
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Customer ID</th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Name</th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Email</th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Total Spent</th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Last Order</th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                <tr>
                  <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">#10123</td>
                  <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-white">John Doe</td>
                  <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400"><EMAIL></td>
                  <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-green-600 dark:text-green-400">$2,450.00</td>
                  <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">2024-06-20</td>
                </tr>
                <tr>
                  <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">#10124</td>
                  <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-white">Jane Smith</td>
                  <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400"><EMAIL></td>
                  <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-green-600 dark:text-green-400">$1,890.50</td>
                  <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">2024-06-19</td>
                </tr>
                <tr>
                  <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">#10125</td>
                  <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-white">Bob Johnson</td>
                  <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400"><EMAIL></td>
                  <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-green-600 dark:text-green-400">$3,125.75</td>
                  <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">2024-06-18</td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <div class="text-center pt-4">
            <p class="text-sm text-gray-600 dark:text-gray-400">
              Showing 3 of 2,450 customers • 
              <button type="button" class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 font-medium">
                View more sample data
              </button>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Field Mapping Configuration -->
  <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6">
    <h4 class="font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
      <svg class="w-5 h-5 mr-2 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4"></path>
      </svg>
      Field Mapping
    </h4>
    
    <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">
      Configure how your data fields should be mapped and processed
    </p>
    
    <!-- Field Mapping Controls -->
    <div class="space-y-4">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Customer ID Field</label>
          <select class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-indigo-500">
            <option>id (Recommended)</option>
            <option>customer_id</option>
            <option>external_id</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email Field</label>
          <select class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-indigo-500">
            <option>email (Recommended)</option>
            <option>email_address</option>
            <option>customer_email</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Date Format</label>
          <select class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-indigo-500">
            <option>Auto-detect (Recommended)</option>
            <option>YYYY-MM-DD</option>
            <option>MM/DD/YYYY</option>
            <option>DD/MM/YYYY</option>
          </select>
        </div>
      </div>
      
      <!-- Auto-mapping Status -->
      <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
        <div class="flex items-center">
          <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <span class="text-sm font-medium text-green-800 dark:text-green-200">
            Auto-mapping detected 95% of fields correctly
          </span>
        </div>
        <p class="text-sm text-green-700 dark:text-green-300 mt-1 ml-7">
          Our intelligent system has automatically mapped most of your data fields. Review the suggestions above.
        </p>
      </div>
    </div>
  </div>

  <!-- Sync Schedule Preview -->
  <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6">
    <h4 class="font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
      <svg class="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      Sync Schedule
    </h4>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <h5 class="font-medium text-gray-900 dark:text-white mb-3">Initial Sync</h5>
        <div class="space-y-2 text-sm">
          <div class="flex justify-between">
            <span class="text-gray-600 dark:text-gray-400">Estimated Duration:</span>
            <span class="font-medium text-gray-900 dark:text-white">~15 minutes</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600 dark:text-gray-400">Data Volume:</span>
            <span class="font-medium text-gray-900 dark:text-white">~2.3 MB</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600 dark:text-gray-400">Records:</span>
            <span class="font-medium text-gray-900 dark:text-white">11,527 total</span>
          </div>
        </div>
      </div>
      
      <div>
        <h5 class="font-medium text-gray-900 dark:text-white mb-3">Ongoing Sync</h5>
        <div class="space-y-2 text-sm">
          <div class="flex justify-between">
            <span class="text-gray-600 dark:text-gray-400">Frequency:</span>
            <span class="font-medium text-gray-900 dark:text-white">Daily at 2:00 AM</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600 dark:text-gray-400">Expected Duration:</span>
            <span class="font-medium text-gray-900 dark:text-white">~2-5 minutes</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600 dark:text-gray-400">Next Sync:</span>
            <span class="font-medium text-gray-900 dark:text-white">Tonight at 2:00 AM</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Action Buttons -->
  <div class="flex justify-between items-center pt-4">
    <button type="button" 
            data-action="click->api-data-preview#testConnection"
            class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 font-medium rounded-lg transition-colors duration-200">
      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
      </svg>
      Test Connection Again
    </button>
    
    <div class="flex space-x-3">
      <button type="button" 
              data-action="click->api-data-preview#downloadSample"
              class="inline-flex items-center px-4 py-2 border border-indigo-300 dark:border-indigo-600 text-indigo-700 dark:text-indigo-300 bg-indigo-50 dark:bg-indigo-900/20 hover:bg-indigo-100 dark:hover:bg-indigo-900/30 font-medium rounded-lg transition-colors duration-200">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
        Download Sample
      </button>
      
      <button type="button" 
              data-action="click->api-data-preview#acceptConfiguration"
              class="inline-flex items-center px-6 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors duration-200">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        Accept Configuration
      </button>
    </div>
  </div>
</div>
