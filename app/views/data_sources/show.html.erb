<% content_for :title, @data_source.name %>

<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50" data-controller="page-transition">
  <!-- Premium Header with Glassmorphism -->
  <div class="relative bg-white/80 backdrop-blur-xl border-b border-white/20 shadow-xl">
    <div class="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-indigo-600/10"></div>
    <div class="relative px-4 sm:px-6 lg:px-8 py-8">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
        <div class="flex items-start gap-4">
          <!-- Source Icon -->
          <div class="p-4 bg-gradient-to-br from-<%= 
            case @data_source.source_type
            when 'shopify' then 'green-500 to-emerald-600'
            when 'woocommerce' then 'purple-500 to-indigo-600'
            when 'amazon_seller_central' then 'orange-500 to-red-600'
            when 'quickbooks' then 'blue-500 to-indigo-600'
            when 'stripe' then 'indigo-500 to-purple-600'
            else 'gray-500 to-slate-600'
            end
          %> rounded-2xl shadow-2xl">
            <span class="text-2xl font-bold text-white">
              <%= @data_source.source_type.first.upcase %>
            </span>
          </div>
          
          <!-- Title and Status -->
          <div class="space-y-2">
            <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 via-blue-900 to-indigo-900 bg-clip-text text-transparent">
              <%= @data_source.name %>
            </h1>
            <div class="flex items-center gap-3">
              <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold <%= 
                case @data_source.status
                when 'connected' then 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border border-green-200/50'
                when 'syncing' then 'bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border border-blue-200/50'
                when 'error' then 'bg-gradient-to-r from-red-100 to-rose-100 text-red-800 border border-red-200/50'
                when 'disconnected' then 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border border-gray-200/50'
                else 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border border-gray-200/50'
                end
              %> shadow-lg">
                <% if @data_source.status == 'syncing' %>
                  <svg class="mr-2 h-4 w-4 animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                <% end %>
                <%= @data_source.status.humanize %>
              </span>
              <span class="text-sm text-slate-600 font-medium"><%= @data_source.source_type.humanize %> Integration</span>
            </div>
          </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex flex-wrap gap-3">
          <%= link_to data_sources_path, 
              class: "group inline-flex items-center px-5 py-3 border border-slate-300/50 rounded-xl shadow-lg text-sm font-semibold text-slate-700 bg-white/70 backdrop-blur-sm hover:bg-white/90 hover:shadow-xl transition-all duration-300",
              data: {
                controller: "magnetic-button ripple-effect",
                action: "mousemove->magnetic-button#mouseMove mouseleave->magnetic-button#mouseLeave click->ripple-effect#click"
              } do %>
            <svg class="h-4 w-4 mr-2 group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Sources
          <% end %>
          <%= link_to edit_data_source_path(@data_source), 
              class: "group inline-flex items-center px-5 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300",
              data: {
                controller: "magnetic-button ripple-effect",
                action: "mousemove->magnetic-button#mouseMove mouseleave->magnetic-button#mouseLeave click->ripple-effect#click"
              } do %>
            <svg class="h-4 w-4 mr-2 group-hover:rotate-12 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
            Edit Source
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Premium Stats Cards -->
  <div class="px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
      <!-- Total Records Card -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-center justify-between mb-4">
            <div class="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
              <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125" />
              </svg>
            </div>
            <div class="text-xs font-semibold text-blue-600 uppercase tracking-wider">Records</div>
          </div>
          <div class="text-3xl font-bold text-slate-900"><%= number_with_delimiter(@stats[:total_records]) %></div>
          <div class="text-sm text-slate-500 mt-1">Total synced</div>
        </div>
      </div>

      <!-- Successful Syncs Card -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-center justify-between mb-4">
            <div class="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:shadow-green-500/25 transition-all duration-300">
              <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="text-xs font-semibold text-green-600 uppercase tracking-wider">Success</div>
          </div>
          <div class="text-3xl font-bold text-slate-900"><%= @stats[:successful_syncs] %></div>
          <div class="text-sm text-slate-500 mt-1">Successful syncs</div>
        </div>
      </div>

      <!-- Failed Syncs Card -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-red-500/5 to-rose-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-center justify-between mb-4">
            <div class="p-3 bg-gradient-to-br from-red-500 to-rose-600 rounded-xl shadow-lg group-hover:shadow-red-500/25 transition-all duration-300">
              <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
              </svg>
            </div>
            <div class="text-xs font-semibold text-red-600 uppercase tracking-wider">Failed</div>
          </div>
          <div class="text-3xl font-bold text-slate-900"><%= @stats[:failed_syncs] %></div>
          <div class="text-sm text-slate-500 mt-1">Failed syncs</div>
        </div>
      </div>

      <!-- Connection Health Card -->
      <div class="group relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:-translate-y-1 transition-all duration-500">
        <div class="absolute inset-0 bg-gradient-to-br from-<%= 
          case @stats[:connection_health]
          when 'good' then 'green'
          when 'fair' then 'yellow'
          when 'poor' then 'orange'
          when 'disconnected' then 'red'
          else 'gray'
          end
        %>-500/5 to-<%= 
          case @stats[:connection_health]
          when 'good' then 'emerald'
          when 'fair' then 'amber'
          when 'poor' then 'red'
          when 'disconnected' then 'rose'
          else 'slate'
          end
        %>-500/5 rounded-2xl"></div>
        <div class="relative">
          <div class="flex items-center justify-between mb-4">
            <div class="p-3 bg-gradient-to-br from-<%= 
              case @stats[:connection_health]
              when 'good' then 'green-500 to-emerald-600'
              when 'fair' then 'yellow-500 to-amber-600'
              when 'poor' then 'orange-500 to-red-600'
              when 'disconnected' then 'red-500 to-rose-600'
              else 'gray-500 to-slate-600'
              end
            %> rounded-xl shadow-lg group-hover:shadow-<%= 
              case @stats[:connection_health]
              when 'good' then 'green'
              when 'fair' then 'yellow'
              when 'poor' then 'orange'
              when 'disconnected' then 'red'
              else 'gray'
              end
            %>-500/25 transition-all duration-300">
              <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" />
              </svg>
            </div>
            <div class="text-xs font-semibold text-<%= 
              case @stats[:connection_health]
              when 'good' then 'green'
              when 'fair' then 'yellow'
              when 'poor' then 'orange'
              when 'disconnected' then 'red'
              else 'gray'
              end
            %>-600 uppercase tracking-wider">Health</div>
          </div>
          <div class="text-2xl font-bold text-slate-900 capitalize"><%= @stats[:connection_health].humanize %></div>
          <div class="text-sm text-slate-500 mt-1">Connection status</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content Grid -->
  <div class="px-4 sm:px-6 lg:px-8 pb-8">
    <div class="grid grid-cols-1 gap-8 lg:grid-cols-3">
      
      <!-- Left Column - Details -->
      <div class="lg:col-span-2 space-y-8">
        
        <!-- Data Source Details Card -->
        <div class="relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
          <div class="absolute inset-0 bg-gradient-to-br from-slate-500/5 to-gray-500/5"></div>
          <div class="relative">
            <div class="px-6 py-5 border-b border-slate-200/50 bg-gradient-to-r from-slate-50/50 to-gray-50/50">
              <div class="flex items-center space-x-3">
                <div class="p-2 bg-gradient-to-br from-slate-600 to-gray-700 rounded-lg shadow-lg">
                  <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 class="text-lg font-bold text-slate-900">Source Details</h3>
              </div>
            </div>
            <div class="px-6 py-6">
              <dl class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div>
                  <dt class="text-sm font-semibold text-slate-600 uppercase tracking-wider">Source Type</dt>
                  <dd class="mt-2 text-base text-slate-900 font-medium"><%= @data_source.source_type.humanize %></dd>
                </div>
                <div>
                  <dt class="text-sm font-semibold text-slate-600 uppercase tracking-wider">Sync Frequency</dt>
                  <dd class="mt-2 text-base text-slate-900 font-medium"><%= @data_source.sync_frequency&.humanize || 'Not set' %></dd>
                </div>
                <div>
                  <dt class="text-sm font-semibold text-slate-600 uppercase tracking-wider">Last Sync</dt>
                  <dd class="mt-2 text-base text-slate-900 font-medium">
                    <%= @stats[:last_sync]&.strftime("%B %d, %Y at %l:%M %p") || 'Never' %>
                  </dd>
                </div>
                <div>
                  <dt class="text-sm font-semibold text-slate-600 uppercase tracking-wider">Next Sync</dt>
                  <dd class="mt-2 text-base text-slate-900 font-medium">
                    <%= @stats[:next_sync]&.strftime("%B %d, %Y at %l:%M %p") || 'Not scheduled' %>
                  </dd>
                </div>
                <div>
                  <dt class="text-sm font-semibold text-slate-600 uppercase tracking-wider">Data Freshness</dt>
                  <dd class="mt-2">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold <%= 
                      case @stats[:data_freshness]
                      when 'very_fresh' then 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border border-green-200/50'
                      when 'fresh' then 'bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border border-blue-200/50'
                      when 'moderate' then 'bg-gradient-to-r from-yellow-100 to-amber-100 text-yellow-800 border border-yellow-200/50'
                      when 'stale' then 'bg-gradient-to-r from-orange-100 to-red-100 text-orange-800 border border-orange-200/50'
                      when 'very_stale' then 'bg-gradient-to-r from-red-100 to-rose-100 text-red-800 border border-red-200/50'
                      else 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border border-gray-200/50'
                      end
                    %> shadow-sm">
                      <%= @stats[:data_freshness].humanize %>
                    </span>
                  </dd>
                </div>
                <% if @data_source.description.present? %>
                  <div class="sm:col-span-2">
                    <dt class="text-sm font-semibold text-slate-600 uppercase tracking-wider">Description</dt>
                    <dd class="mt-2 text-base text-slate-900"><%= @data_source.description %></dd>
                  </div>
                <% end %>
              </dl>
            </div>
          </div>
        </div>

        <!-- Recent Records Card -->
        <div class="relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
          <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-purple-500/5"></div>
          <div class="relative">
            <div class="px-6 py-5 border-b border-slate-200/50 bg-gradient-to-r from-indigo-50/50 to-purple-50/50">
              <div class="flex items-center space-x-3">
                <div class="p-2 bg-gradient-to-br from-indigo-600 to-purple-700 rounded-lg shadow-lg">
                  <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                  </svg>
                </div>
                <h3 class="text-lg font-bold text-slate-900">Recent Data Records</h3>
              </div>
            </div>
            <div class="relative">
              <% if defined?(@recent_records) && @recent_records.any? %>
                <div class="overflow-x-auto">
                  <table class="min-w-full divide-y divide-slate-200/50">
                    <thead class="bg-gradient-to-r from-slate-50/50 to-gray-50/50">
                      <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">Type</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">External ID</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">Status</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">Created</th>
                      </tr>
                    </thead>
                    <tbody class="bg-white/50 divide-y divide-slate-200/30">
                      <% @recent_records.each do |record| %>
                        <tr class="hover:bg-white/80 transition-colors duration-200">
                          <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-slate-900">
                            <%= record.record_type.humanize %>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-600 font-mono">
                            <%= record.external_id %>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-semibold <%= 
                              case record.processing_status
                              when 'processed' then 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800'
                              when 'processing' then 'bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800'
                              when 'pending' then 'bg-gradient-to-r from-yellow-100 to-amber-100 text-yellow-800'
                              when 'failed' then 'bg-gradient-to-r from-red-100 to-rose-100 text-red-800'
                              else 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800'
                              end
                            %>">
                              <%= record.processing_status.humanize %>
                            </span>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-600">
                            <%= record.created_at.strftime("%m/%d/%y %l:%M %p") %>
                          </td>
                        </tr>
                      <% end %>
                    </tbody>
                  </table>
                </div>
              <% else %>
                <div class="px-6 py-12 text-center">
                  <div class="mx-auto w-16 h-16 bg-gradient-to-br from-slate-100 to-slate-200 rounded-full flex items-center justify-center mb-4">
                    <svg class="h-8 w-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                    </svg>
                  </div>
                  <p class="text-sm text-slate-500">No data records found for this data source.</p>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Column - Actions and Jobs -->
      <div class="space-y-8">
        
        <!-- Quick Actions Card -->
        <div class="relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
          <div class="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5"></div>
          <div class="relative">
            <div class="px-6 py-5 border-b border-slate-200/50 bg-gradient-to-r from-purple-50/50 to-pink-50/50">
              <div class="flex items-center space-x-3">
                <div class="p-2 bg-gradient-to-br from-purple-600 to-pink-700 rounded-lg shadow-lg">
                  <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 class="text-lg font-bold text-slate-900">Quick Actions</h3>
              </div>
            </div>
            <div class="px-6 py-6 space-y-3">
              <%= link_to test_connection_data_source_path(@data_source), method: :post,
                  class: "group flex w-full items-center justify-center gap-3 px-4 py-3 bg-white/70 text-slate-700 font-semibold rounded-xl border border-slate-300/50 shadow-lg backdrop-blur-sm hover:bg-white/90 hover:shadow-xl transition-all duration-300",
                  data: {
                    controller: "magnetic-button ripple-effect",
                    action: "mousemove->magnetic-button#mouseMove mouseleave->magnetic-button#mouseLeave click->ripple-effect#click"
                  } do %>
                <svg class="h-5 w-5 text-slate-400 group-hover:text-green-500 transition-colors duration-300" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L8.53 10.96a.75.75 0 00-1.06 1.061l1.5 1.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
                </svg>
                Test Connection
              <% end %>
              
              <% if @data_source.connected? %>
                <%= link_to sync_now_data_source_path(@data_source), method: :post,
                    class: "group flex w-full items-center justify-center gap-3 px-4 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300",
                    data: { 
                      confirm: "Are you sure you want to trigger a manual sync?",
                      controller: "magnetic-button ripple-effect",
                      action: "mousemove->magnetic-button#mouseMove mouseleave->magnetic-button#mouseLeave click->ripple-effect#click"
                    } do %>
                  <svg class="h-5 w-5 group-hover:rotate-180 transition-transform duration-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M15.312 11.424a5.5 5.5 0 01-9.201 2.466l-.312-.311h2.433a.75.75 0 000-1.5H3.989a.75.75 0 00-.75.75v4.242a.75.75 0 001.5 0v-2.43l.31.31a7 7 0 0011.712-*********** 0 00-1.449-.39zm1.23-3.723a.75.75 0 00.219-.53V2.929a.75.75 0 00-1.5 0V5.36l-.31-.31A7 7 0 003.239 8.188a.75.75 0 101.448.389A5.5 5.5 0 0113.89 6.11l.311.31h-2.432a.75.75 0 000 1.5h4.243a.75.75 0 00.53-.219z" clip-rule="evenodd" />
                  </svg>
                  Sync Now
                <% end %>
              <% end %>
              
              <%= link_to edit_data_source_path(@data_source),
                  class: "group flex w-full items-center justify-center gap-3 px-4 py-3 bg-white/70 text-slate-700 font-semibold rounded-xl border border-slate-300/50 shadow-lg backdrop-blur-sm hover:bg-white/90 hover:shadow-xl transition-all duration-300",
                  data: {
                    controller: "magnetic-button ripple-effect",
                    action: "mousemove->magnetic-button#mouseMove mouseleave->magnetic-button#mouseLeave click->ripple-effect#click"
                  } do %>
                <svg class="h-5 w-5 text-slate-400 group-hover:text-indigo-500 transition-colors duration-300" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M2.695 14.763l-1.262 3.154a.5.5 0 00.65.65l3.155-1.262a4 4 0 001.343-.885L17.5 5.5a2.121 2.121 0 00-3-3L3.58 13.42a4 4 0 00-.885 1.343z" />
                </svg>
                Edit Settings
              <% end %>
              
              <%= link_to data_source_path(@data_source), method: :delete,
                  class: "group flex w-full items-center justify-center gap-3 px-4 py-3 bg-gradient-to-r from-red-600 to-rose-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300",
                  data: { 
                    confirm: "Are you sure you want to delete this data source? This action cannot be undone.",
                    controller: "magnetic-button ripple-effect",
                    action: "mousemove->magnetic-button#mouseMove mouseleave->magnetic-button#mouseLeave click->ripple-effect#click"
                  } do %>
                <svg class="h-5 w-5 group-hover:scale-110 transition-transform duration-300" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z" clip-rule="evenodd" />
                </svg>
                Delete Source
              <% end %>
            </div>
          </div>
        </div>

        <!-- Recent Jobs Card -->
        <div class="relative bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
          <div class="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-amber-500/5"></div>
          <div class="relative">
            <div class="px-6 py-5 border-b border-slate-200/50 bg-gradient-to-r from-orange-50/50 to-amber-50/50">
              <div class="flex items-center space-x-3">
                <div class="p-2 bg-gradient-to-br from-orange-600 to-amber-700 rounded-lg shadow-lg">
                  <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
                  </svg>
                </div>
                <h3 class="text-lg font-bold text-slate-900">Recent Jobs</h3>
              </div>
            </div>
            <div class="px-6 py-6">
              <% if defined?(@recent_jobs) && @recent_jobs.any? %>
                <ul role="list" class="space-y-4">
                  <% @recent_jobs.each do |job| %>
                    <li class="flex items-center justify-between p-3 bg-white/50 rounded-xl hover:bg-white/70 transition-colors duration-200">
                      <div class="flex items-center gap-3">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-semibold <%= 
                          case job.status
                          when 'completed' then 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800'
                          when 'running' then 'bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800'
                          when 'queued' then 'bg-gradient-to-r from-yellow-100 to-amber-100 text-yellow-800'
                          when 'failed' then 'bg-gradient-to-r from-red-100 to-rose-100 text-red-800'
                          else 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800'
                          end
                        %>">
                          <%= job.status.humanize %>
                        </span>
                        <div class="text-sm">
                          <p class="font-medium text-slate-900">Job #<%= job.id %></p>
                          <p class="text-slate-500">
                            <%= job.completed_at&.strftime("%m/%d/%y %l:%M %p") || job.created_at.strftime("%m/%d/%y %l:%M %p") %>
                          </p>
                        </div>
                      </div>
                      <div class="text-right text-sm">
                        <% if job.records_processed.to_i > 0 %>
                          <p class="font-semibold text-slate-900"><%= number_with_delimiter(job.records_processed) %> records</p>
                          <% if job.records_failed.to_i > 0 %>
                            <p class="text-red-600 text-xs"><%= job.records_failed %> failed</p>
                          <% end %>
                        <% end %>
                      </div>
                    </li>
                  <% end %>
                </ul>
              <% else %>
                <div class="text-center py-8">
                  <div class="mx-auto w-12 h-12 bg-gradient-to-br from-slate-100 to-slate-200 rounded-full flex items-center justify-center mb-3">
                    <svg class="h-6 w-6 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                  </div>
                  <p class="text-sm text-slate-500">No extraction jobs found.</p>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Enhanced JavaScript for Premium UX -->
<script>
  // Add smooth scroll behavior
  document.documentElement.style.scrollBehavior = 'smooth';

  // Add intersection observer for card animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.style.opacity = '1';
        entry.target.style.transform = 'translateY(0)';
      }
    });
  }, observerOptions);

  // Observe all cards for animation
  document.querySelectorAll('.relative.bg-white\\/80').forEach(card => {
    card.style.opacity = '0';
    card.style.transform = 'translateY(20px)';
    card.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';
    observer.observe(card);
  });
</script>