<!-- File Upload Interface -->
<div data-controller="advanced-file-upload"
     data-advanced-file-upload-max-size-value="<%= local_assigns[:config]&.dig(:max_file_size) || 100.megabytes %>"
     data-advanced-file-upload-accepted-types-value='<%= (local_assigns[:config]&.dig(:allowed_types) || [".csv", ".xlsx", ".json"]).to_json %>'
     data-advanced-file-upload-upload-url-value="<%= process_files_data_source_path('temp') %>"
     class="space-y-6">

  <!-- Enhanced Drop Zone -->
  <div class="relative">
    <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-2xl p-12 text-center transition-all duration-300 hover:border-indigo-400 hover:bg-indigo-50 dark:hover:bg-indigo-900/10"
         data-advanced-file-upload-target="dropZone"
         data-action="dragover->advanced-file-upload#handleDragOver 
                      dragenter->advanced-file-upload#handleDragEnter 
                      dragleave->advanced-file-upload#handleDragLeave 
                      drop->advanced-file-upload#handleDrop 
                      click->advanced-file-upload#openFilePicker">
      
      <!-- Upload Icon -->
      <div class="mb-6">
        <svg class="mx-auto h-16 w-16 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
        </svg>
      </div>
      
      <!-- Upload Text -->
      <div class="space-y-2">
        <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
          Drop your files here, or <span class="text-indigo-600 dark:text-indigo-400">browse</span>
        </h3>
        <p class="text-gray-600 dark:text-gray-400">
          Support for <%= (local_assigns[:config]&.dig(:allowed_types) || [".csv", ".xlsx", ".json"]).count %>+ formats including CSV, Excel, JSON, XML, Parquet, and more
        </p>
        <div class="flex items-center justify-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
          <div class="flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Up to <%= format_file_size(local_assigns[:config]&.dig(:max_file_size) || 100.megabytes) %>
          </div>
          <div class="flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Multiple files
          </div>
          <div class="flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
            </svg>
            Secure upload
          </div>
        </div>
      </div>
      
      <!-- Sample Files -->
      <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-600">
        <p class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Need sample files?</p>
        <div class="flex justify-center space-x-3">
          <%= link_to download_sample_csv_data_sources_path, 
                      class: "inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors",
                      onclick: "event.stopPropagation();" do %>
            <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Sample CSV
          <% end %>
          <%= link_to download_sample_excel_data_sources_path, 
                      class: "inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors",
                      onclick: "event.stopPropagation();" do %>
            <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Sample Excel
          <% end %>
          <%= link_to download_sample_json_data_sources_path, 
                      class: "inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors",
                      onclick: "event.stopPropagation();" do %>
            <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Sample JSON
          <% end %>
        </div>
      </div>
    </div>
    
    <!-- Hidden File Input -->
    <input type="file" 
           multiple 
           accept="<%= (local_assigns[:config]&.dig(:allowed_types) || [".csv", ".xlsx", ".json"]).join(',') %>"
           data-advanced-file-upload-target="fileInput"
           data-action="change->advanced-file-upload#handleFiles"
           class="hidden">
  </div>

  <!-- File List -->
  <div data-advanced-file-upload-target="fileList" class="space-y-3"></div>

  <!-- Upload Progress -->
  <div data-advanced-file-upload-target="progressContainer" class="hidden space-y-4">
    <div class="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-xl p-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="font-semibold text-gray-900 dark:text-white">Processing Files</h4>
        <span data-advanced-file-upload-target="progressText" class="text-sm text-gray-600 dark:text-gray-400">0%</span>
      </div>
      
      <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-3">
        <div data-advanced-file-upload-target="progressBar" 
             class="bg-gradient-to-r from-indigo-500 to-blue-600 h-3 rounded-full transition-all duration-300" 
             style="width: 0%"></div>
      </div>
      
      <div data-advanced-file-upload-target="currentFileStatus" class="mt-3 text-sm text-gray-600 dark:text-gray-400">
        <!-- Current file processing status -->
      </div>
    </div>
  </div>

  <!-- Enhanced Data Analysis Results -->
  <div data-advanced-file-upload-target="analysisResults" class="hidden space-y-6">
    
    <!-- Enhanced Preview Integration -->
    <div data-advanced-file-upload-target="enhancedPreviewContainer" class="space-y-6">
      <!-- This will be populated with the EnhancedDataPreviewComponent -->
    </div>

    <!-- Fallback File Analysis Summary (for compatibility) -->
    <div data-advanced-file-upload-target="basicAnalysisSummary" class="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800 rounded-xl p-6">
      <h4 class="font-semibold text-green-900 dark:text-green-100 mb-4 flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        Files Processed Successfully
      </h4>
      
      <!-- Quick Stats -->
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div class="text-center p-4 bg-white dark:bg-gray-800 rounded-lg">
          <div class="text-2xl font-bold text-blue-600 dark:text-blue-400" data-advanced-file-upload-target="totalRows">-</div>
          <div class="text-sm text-gray-600 dark:text-gray-400">Total Rows</div>
        </div>
        <div class="text-center p-4 bg-white dark:bg-gray-800 rounded-lg">
          <div class="text-2xl font-bold text-green-600 dark:text-green-400" data-advanced-file-upload-target="totalColumns">-</div>
          <div class="text-sm text-gray-600 dark:text-gray-400">Columns</div>
        </div>
        <div class="text-center p-4 bg-white dark:bg-gray-800 rounded-lg">
          <div class="text-2xl font-bold text-purple-600 dark:text-purple-400" data-advanced-file-upload-target="qualityScore">-</div>
          <div class="text-sm text-gray-600 dark:text-gray-400">Quality Score</div>
        </div>
        <div class="text-center p-4 bg-white dark:bg-gray-800 rounded-lg">
          <div class="text-2xl font-bold text-orange-600 dark:text-orange-400" data-advanced-file-upload-target="filesProcessed">-</div>
          <div class="text-sm text-gray-600 dark:text-gray-400">Files</div>
        </div>
      </div>
    </div>

    <!-- Data Preview -->
    <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h4 class="font-semibold text-gray-900 dark:text-white">Data Preview</h4>
        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">First few rows of your processed data</p>
      </div>
      
      <div class="overflow-x-auto">
        <div data-advanced-file-upload-target="dataPreview" class="p-6">
          <!-- Data preview table will be inserted here -->
        </div>
      </div>
    </div>

    <!-- Data Quality Insights -->
    <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6">
      <h4 class="font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
        <svg class="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4"></path>
        </svg>
        Data Quality Insights
      </h4>
      
      <div data-advanced-file-upload-target="qualityInsights" class="space-y-4">
        <!-- Quality insights will be populated here -->
      </div>
    </div>

    <!-- Column Analysis -->
    <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h4 class="font-semibold text-gray-900 dark:text-white">Column Analysis</h4>
        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Detailed analysis of each column in your data</p>
      </div>
      
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Column</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Type</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Completeness</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Unique Values</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Sample Values</th>
            </tr>
          </thead>
          <tbody data-advanced-file-upload-target="columnAnalysis" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            <!-- Column analysis rows will be populated here -->
          </tbody>
        </table>
      </div>
    </div>

    <!-- Processing Actions -->
    <div class="flex justify-between items-center pt-4">
      <button type="button" 
              data-action="click->advanced-file-upload#clearFiles"
              class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 font-medium rounded-lg transition-colors duration-200">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
        </svg>
        Clear Files
      </button>
      
      <div class="flex space-x-3">
        <button type="button" 
                data-action="click->advanced-file-upload#downloadProcessedData"
                class="inline-flex items-center px-4 py-2 border border-indigo-300 dark:border-indigo-600 text-indigo-700 dark:text-indigo-300 bg-indigo-50 dark:bg-indigo-900/20 hover:bg-indigo-100 dark:hover:bg-indigo-900/30 font-medium rounded-lg transition-colors duration-200">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          Download Processed
        </button>
        
        <button type="button" 
                data-action="click->advanced-file-upload#acceptFiles"
                class="inline-flex items-center px-6 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors duration-200">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          Accept & Continue
        </button>
      </div>
    </div>
  </div>

  <!-- Error Display -->
  <div data-advanced-file-upload-target="errorContainer" class="hidden">
    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-6">
      <div class="flex items-start">
        <svg class="w-6 h-6 text-red-400 mr-3 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
        <div>
          <h4 class="font-semibold text-red-800 dark:text-red-200 mb-2">Upload Error</h4>
          <div data-advanced-file-upload-target="errorMessage" class="text-red-700 dark:text-red-300"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Supported Formats -->
  <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-6">
    <h4 class="font-semibold text-blue-900 dark:text-blue-100 mb-3 flex items-center">
      <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      Supported File Formats
    </h4>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
      <div>
        <h5 class="font-medium text-blue-800 dark:text-blue-200 mb-2">Structured Data</h5>
        <ul class="space-y-1 text-blue-700 dark:text-blue-300">
          <li>• CSV (Comma-separated values)</li>
          <li>• Excel (.xls, .xlsx)</li>
          <li>• TSV (Tab-separated values)</li>
          <li>• Parquet (Columnar format)</li>
        </ul>
      </div>
      <div>
        <h5 class="font-medium text-blue-800 dark:text-blue-200 mb-2">Semi-Structured</h5>
        <ul class="space-y-1 text-blue-700 dark:text-blue-300">
          <li>• JSON (JavaScript Object)</li>
          <li>• XML (Extensible Markup)</li>
          <li>• YAML (.yml, .yaml)</li>
        </ul>
      </div>
      <div>
        <h5 class="font-medium text-blue-800 dark:text-blue-200 mb-2">Databases & Other</h5>
        <ul class="space-y-1 text-blue-700 dark:text-blue-300">
          <li>• SQLite databases</li>
          <li>• SQL script files</li>
          <li>• Plain text files</li>
        </ul>
      </div>
    </div>
  </div>
</div>
