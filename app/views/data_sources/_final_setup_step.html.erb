<!-- Step 4: Final Setup -->
<div class="wizard-step" id="step-4" data-data-source-wizard-target="step" style="display: none;" role="tabpanel" aria-labelledby="step-4-tab">
  <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
    <div class="mb-8">
      <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2 flex items-center">
        <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mr-3">
          <svg class="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        Final Setup
      </h3>
      <p class="text-gray-600 dark:text-gray-400">
        Review your configuration and choose your initial sync options.
      </p>
    </div>

    <!-- Configuration Summary -->
    <div class="mb-8">
      <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
        <svg class="w-5 h-5 mr-2 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
        </svg>
        Configuration Summary
      </h4>
      
      <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Platform Info -->
          <div class="summary-item">
            <div class="flex items-start">
              <div class="w-10 h-10 bg-white dark:bg-gray-600 rounded-lg flex items-center justify-center mr-3 shadow-sm">
                <div data-data-source-wizard-target="summaryPlatformIcon">
                  <!-- Platform icon will be inserted here -->
                </div>
              </div>
              <div class="flex-1">
                <h5 class="text-sm font-medium text-gray-900 dark:text-white mb-1">Platform</h5>
                <p class="text-sm text-gray-600 dark:text-gray-400" data-data-source-wizard-target="summaryPlatformName">
                  <!-- Platform name will be inserted here -->
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-500" data-data-source-wizard-target="summaryPlatformType">
                  <!-- Platform type will be inserted here -->
                </p>
              </div>
            </div>
          </div>

          <!-- Data Source Info -->
          <div class="summary-item">
            <div class="flex items-start">
              <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-3">
                <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                </svg>
              </div>
              <div class="flex-1">
                <h5 class="text-sm font-medium text-gray-900 dark:text-white mb-1">Data Source</h5>
                <p class="text-sm text-gray-600 dark:text-gray-400" data-data-source-wizard-target="summaryDataSourceName">
                  <!-- Data source name will be inserted here -->
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-500" data-data-source-wizard-target="summaryDataSourceDescription">
                  <!-- Data source description will be inserted here -->
                </p>
              </div>
            </div>
          </div>

          <!-- Sync Settings -->
          <div class="summary-item">
            <div class="flex items-start">
              <div class="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mr-3">
                <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
              </div>
              <div class="flex-1">
                <h5 class="text-sm font-medium text-gray-900 dark:text-white mb-1">Sync Frequency</h5>
                <p class="text-sm text-gray-600 dark:text-gray-400" data-data-source-wizard-target="summarySyncFrequency">
                  <!-- Sync frequency will be inserted here -->
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-500" data-data-source-wizard-target="summarySyncDescription">
                  <!-- Sync description will be inserted here -->
                </p>
              </div>
            </div>
          </div>

          <!-- Data Preview Summary -->
          <div class="summary-item">
            <div class="flex items-start">
              <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mr-3">
                <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
              </div>
              <div class="flex-1">
                <h5 class="text-sm font-medium text-gray-900 dark:text-white mb-1">Data Preview</h5>
                <p class="text-sm text-gray-600 dark:text-gray-400" data-data-source-wizard-target="summaryDataPreview">
                  <!-- Data preview summary will be inserted here -->
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-500" data-data-source-wizard-target="summaryDataQuality">
                  <!-- Data quality info will be inserted here -->
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Initial Sync Options -->
    <div class="mb-8">
      <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
        <svg class="w-5 h-5 mr-2 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        Initial Sync Options
      </h4>
      
      <div class="space-y-4">
        <!-- Perform Initial Sync -->
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <label class="flex items-start cursor-pointer">
            <%= form.check_box :perform_initial_sync, 
                class: "mt-1 mr-3 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 dark:border-gray-600 rounded",
                checked: true,
                data: {
                  data_source_wizard_target: "initialSyncField",
                  auto_save_target: "input"
                } %>
            <div class="flex-1">
              <div class="flex items-center">
                <h5 class="text-sm font-medium text-gray-900 dark:text-white mr-2">
                  Perform Initial Sync
                </h5>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                  Recommended
                </span>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Import all existing data from your source immediately after setup. This ensures your data warehouse is up-to-date from the start.
              </p>
              <div class="mt-2 text-xs text-gray-500 dark:text-gray-500">
                <span class="inline-flex items-center">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  Estimated time: <span data-data-source-wizard-target="estimatedSyncTime">5-15 minutes</span>
                </span>
              </div>
            </div>
          </label>
        </div>

        <!-- Enable Auto Sync -->
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <label class="flex items-start cursor-pointer">
            <%= form.check_box :enable_auto_sync, 
                class: "mt-1 mr-3 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 dark:border-gray-600 rounded",
                checked: true,
                data: {
                  data_source_wizard_target: "autoSyncField",
                  auto_save_target: "input"
                } %>
            <div class="flex-1">
              <div class="flex items-center">
                <h5 class="text-sm font-medium text-gray-900 dark:text-white mr-2">
                  Enable Automatic Sync
                </h5>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                  Recommended
                </span>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Automatically sync new and updated data based on your configured frequency. You can always pause or modify this later.
              </p>
              <div class="mt-2 text-xs text-gray-500 dark:text-gray-500">
                <span class="inline-flex items-center">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                  </svg>
                  Next sync: <span data-data-source-wizard-target="nextSyncTime">Based on frequency</span>
                </span>
              </div>
            </div>
          </label>
        </div>
      </div>
    </div>

    <!-- Advanced Options (Collapsible) -->
    <div class="advanced-options" data-controller="collapsible">
      <button type="button" 
              class="flex items-center justify-between w-full p-4 text-left bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200"
              data-action="click->collapsible#toggle"
              data-collapsible-target="trigger"
              aria-expanded="false">
        <span class="text-sm font-medium text-gray-900 dark:text-white flex items-center">
          <svg class="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
          </svg>
          Advanced Options
        </span>
        <svg class="w-4 h-4 text-gray-500 dark:text-gray-400 transform transition-transform duration-200" 
             data-collapsible-target="icon" 
             fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </button>
      
      <div class="overflow-hidden transition-all duration-300 max-h-0" data-collapsible-target="content">
        <div class="p-4 space-y-4">
          <!-- Notification Settings -->
          <div class="form-group">
            <label class="flex items-center cursor-pointer">
              <%= form.check_box :send_notifications, 
                  class: "mr-3 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 dark:border-gray-600 rounded",
                  data: { auto_save_target: "input" } %>
              <div>
                <span class="text-sm font-medium text-gray-900 dark:text-white">Send sync notifications</span>
                <p class="text-xs text-gray-500 dark:text-gray-400">Get notified about sync status and any issues</p>
              </div>
            </label>
          </div>

          <!-- Error Handling -->
          <div class="form-group">
            <%= form.label :error_handling, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" do %>
              Error Handling Strategy
            <% end %>
            <%= form.select :error_handling, 
                options_for_select([
                  ['Continue on errors (recommended)', 'continue'],
                  ['Stop on first error', 'stop'],
                  ['Retry failed records', 'retry']
                ], 'continue'),
                {},
                class: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white text-sm",
                data: { auto_save_target: "input" } %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>