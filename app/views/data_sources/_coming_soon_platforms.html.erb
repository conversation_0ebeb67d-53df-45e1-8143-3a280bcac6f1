<!-- Coming Soon Platforms Section -->
<div class="px-4 border-t border-gray-200 dark:border-gray-600 pt-8 mt-8">
  <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
    <svg class="w-5 h-5 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
    </svg>
    Coming Soon
  </h4>
  
  <% if data_source_configs.any? %>
    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-6">
      <% data_source_configs.each do |source_key, config| %>
        <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-xl opacity-75 hover:opacity-90 transition-opacity duration-200"
             data-tooltip="<%= config[:description] %>">
          <div class="w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-lg flex items-center justify-center mx-auto mb-2">
            <% if config[:icon] %>
              <%= render "shared/icons/#{config[:icon]}", class: "w-4 h-4 text-gray-400" %>
            <% else %>
              <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            <% end %>
          </div>
          <p class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"><%= config[:name] %></p>
          <p class="text-xs text-gray-500 dark:text-gray-400">
            <%= config[:estimated_release] || 'Q2 2025' %>
          </p>
        </div>
      <% end %>
    </div>
  <% else %>
    <div class="text-center py-8">
      <svg class="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
      </svg>
      <p class="text-gray-500 dark:text-gray-400">More integrations coming soon!</p>
    </div>
  <% end %>
  
  <!-- Request Integration CTA -->
  <div class="text-center bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-xl p-6">
    <h5 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
      Need a specific integration?
    </h5>
    <p class="text-gray-600 dark:text-gray-400 mb-4">
      We're constantly adding new platforms. Let us know what you need and we'll prioritize it.
    </p>
    <div class="flex flex-col sm:flex-row gap-3 justify-center">
      <a href="mailto:<EMAIL>" 
         class="inline-flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white font-medium rounded-lg transition-colors duration-200">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
        </svg>
        Email Request
      </a>
      <button type="button" 
              class="inline-flex items-center px-4 py-2 border border-indigo-600 text-indigo-600 dark:text-indigo-400 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 font-medium rounded-lg transition-colors duration-200"
              data-action="click->platform-selector#showRequestForm">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        Quick Request
      </button>
    </div>
  </div>
</div>