<% content_for :page_title, @organization.name %>
<% content_for :page_subtitle, "Manage organization settings, team members, and integrations" %>

<div class="dashboard-content organization-dashboard">
  <!-- Organization Overview -->
  <div class="org-overview-section">
    <div class="stats-grid">
      <!-- Active Users -->
      <div class="stat-card">
        <div class="stat-icon users">
          <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
        </div>
        <div class="stat-content">
          <p class="stat-label">Team Members</p>
          <p class="stat-value"><%= @users.count %></p>
          <p class="stat-detail">
            <% if @organization.max_users == Float::INFINITY %>
              Unlimited seats
            <% else %>
              of <%= @organization.max_users %> seats
            <% end %>
          </p>
        </div>
      </div>

      <!-- Data Sources -->
      <div class="stat-card">
        <div class="stat-icon sources">
          <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
          </svg>
        </div>
        <div class="stat-content">
          <p class="stat-label">Data Sources</p>
          <p class="stat-value"><%= @data_sources.count %></p>
          <p class="stat-detail">
            <% if @organization.max_data_sources == Float::INFINITY %>
              Unlimited sources
            <% else %>
              of <%= @organization.max_data_sources %> allowed
            <% end %>
          </p>
        </div>
      </div>

      <!-- Current Plan -->
      <div class="stat-card">
        <div class="stat-icon plan">
          <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
          </svg>
        </div>
        <div class="stat-content">
          <p class="stat-label">Current Plan</p>
          <p class="stat-value"><%= @organization.plan.humanize %></p>
          <div class="plan-status active">
            <span class="status-dot"></span>
            Active
          </div>
        </div>
      </div>

      <!-- Member Since -->
      <div class="stat-card">
        <div class="stat-icon member-since">
          <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        </div>
        <div class="stat-content">
          <p class="stat-label">Member Since</p>
          <p class="stat-value"><%= @organization.created_at.strftime("%b %Y") %></p>
          <p class="stat-detail"><%= time_ago_in_words(@organization.created_at) %> ago</p>
        </div>
      </div>
    </div>
  </div>

  <div class="org-content-grid">
    <!-- Organization Details -->
    <div class="org-main-content">
      <!-- Details Card -->
      <div class="org-details-card">
        <div class="card-header">
          <h2>Organization Details</h2>
          <%= link_to edit_organization_path, class: "btn btn--sm btn--outline" do %>
            <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            Edit Settings
          <% end %>
        </div>

        <div class="details-grid">
          <div class="detail-item">
            <span class="detail-label">Organization Name</span>
            <span class="detail-value"><%= @organization.name %></span>
          </div>

          <div class="detail-item">
            <span class="detail-label">Status</span>
            <div class="status-badge <%= @organization.active? ? 'active' : 'inactive' %>">
              <span class="status-dot"></span>
              <%= @organization.status.humanize %>
            </div>
          </div>

          <div class="detail-item">
            <span class="detail-label">Created Date</span>
            <span class="detail-value"><%= @organization.created_at.strftime("%B %d, %Y") %></span>
          </div>

          <div class="detail-item">
            <span class="detail-label">Organization ID</span>
            <span class="detail-value mono"><%= @organization.id %></span>
          </div>
        </div>
      </div>

      <!-- Plan Usage -->
      <div class="plan-usage-card">
        <div class="card-header">
          <h2>Plan Usage & Limits</h2>
          <%= link_to billing_organization_path, class: "btn btn--sm btn--primary" do %>
            Upgrade Plan
          <% end %>
        </div>

        <div class="usage-metrics">
          <!-- Data Usage -->
          <div class="usage-metric">
            <div class="metric-header">
              <span class="metric-name">Monthly Data Records</span>
              <span class="metric-value">
                <% if @organization.monthly_data_limit == Float::INFINITY %>
                  Unlimited
                <% else %>
                  45% used
                <% end %>
              </span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill primary" style="width: 45%"></div>
            </div>
            <div class="metric-details">
              <span>450K of <%= @organization.monthly_data_limit == Float::INFINITY ? "∞" : "1M" %> records</span>
            </div>
          </div>

          <!-- API Requests -->
          <div class="usage-metric">
            <div class="metric-header">
              <span class="metric-name">API Requests</span>
              <span class="metric-value">
                <% if @organization.monthly_api_requests_limit == Float::INFINITY %>
                  Unlimited
                <% else %>
                  32% used
                <% end %>
              </span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill info" style="width: 32%"></div>
            </div>
            <div class="metric-details">
              <span>3.2K of <%= @organization.monthly_api_requests_limit == Float::INFINITY ? "∞" : "10K" %> requests</span>
            </div>
          </div>

          <!-- Storage -->
          <div class="usage-metric">
            <div class="metric-header">
              <span class="metric-name">Storage Used</span>
              <span class="metric-value">2.4 GB</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill success" style="width: 24%"></div>
            </div>
            <div class="metric-details">
              <span>2.4 GB of 10 GB</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Activity -->
      <div class="recent-activity-card">
        <div class="card-header">
          <h2>Recent Activity</h2>
          <%= link_to audit_logs_organization_path, class: "btn btn--sm btn--outline" do %>
            View All
          <% end %>
        </div>

        <div class="activity-list">
          <div class="activity-item">
            <div class="activity-icon">
              <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
              </svg>
            </div>
            <div class="activity-content">
              <p class="activity-description">New team member added</p>
              <p class="activity-meta">John Doe joined as Developer • 2 hours ago</p>
            </div>
          </div>

          <div class="activity-item">
            <div class="activity-icon">
              <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
              </svg>
            </div>
            <div class="activity-content">
              <p class="activity-description">Data source connected</p>
              <p class="activity-meta">Shopify integration activated • 5 hours ago</p>
            </div>
          </div>

          <div class="activity-item">
            <div class="activity-icon">
              <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
            <div class="activity-content">
              <p class="activity-description">Security settings updated</p>
              <p class="activity-meta">2FA enabled for all users • Yesterday</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Sidebar -->
    <div class="org-sidebar">
      <!-- Quick Actions -->
      <div class="quick-actions-card">
        <h3>Quick Actions</h3>
        <div class="actions-list">
          <%= link_to users_path, class: "action-item" do %>
            <div class="action-icon">
              <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </div>
            <div class="action-content">
              <p class="action-title">Manage Team</p>
              <p class="action-description">Add or remove members</p>
            </div>
            <svg class="action-arrow" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          <% end %>

          <%= link_to usage_stats_organization_path, class: "action-item" do %>
            <div class="action-icon">
              <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <div class="action-content">
              <p class="action-title">Usage Statistics</p>
              <p class="action-description">View detailed analytics</p>
            </div>
            <svg class="action-arrow" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          <% end %>

          <%= link_to billing_organization_path, class: "action-item" do %>
            <div class="action-icon">
              <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
              </svg>
            </div>
            <div class="action-content">
              <p class="action-title">Billing & Invoices</p>
              <p class="action-description">Manage payments</p>
            </div>
            <svg class="action-arrow" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          <% end %>

          <%= link_to audit_logs_organization_path, class: "action-item" do %>
            <div class="action-icon">
              <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
              </svg>
            </div>
            <div class="action-content">
              <p class="action-title">Audit Logs</p>
              <p class="action-description">Security & activity logs</p>
            </div>
            <svg class="action-arrow" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          <% end %>
        </div>
      </div>

      <!-- Connected Integrations -->
      <div class="integrations-summary-card">
        <div class="card-header">
          <h3>Connected Integrations</h3>
          <%= link_to data_sources_path, class: "link-text" do %>
            View all
          <% end %>
        </div>

        <% if @data_sources.any? %>
          <div class="integration-list">
            <% @data_sources.limit(3).each do |source| %>
              <div class="integration-item">
                <div class="integration-icon <%= source.source_type %>">
                  <%= case source.source_type
                      when 'shopify' then '🛍️'
                      when 'stripe' then '💳'
                      when 'google_analytics' then '📊'
                      else '🔗'
                      end %>
                </div>
                <div class="integration-info">
                  <p class="integration-name"><%= source.name %></p>
                  <p class="integration-type"><%= source.source_type.humanize %></p>
                </div>
                <div class="integration-status connected">
                  <span class="status-dot"></span>
                </div>
              </div>
            <% end %>
          </div>
          <% if @data_sources.count > 3 %>
            <p class="more-integrations">+<%= @data_sources.count - 3 %> more integrations</p>
          <% end %>
        <% else %>
          <div class="empty-integrations">
            <p>No integrations connected yet</p>
            <%= link_to "Add Integration", data_sources_path, class: "btn btn--sm btn--primary" %>
          </div>
        <% end %>
      </div>

      <!-- Support Card -->
      <div class="support-card">
        <div class="support-icon">
          <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
          </svg>
        </div>
        <h3>Need Help?</h3>
        <p>Our support team is here to assist you with any questions.</p>
        <div class="support-actions">
          <a href="mailto:<EMAIL>" class="btn btn--sm btn--outline">Email Support</a>
          <a href="#" class="btn btn--sm btn--primary">Live Chat</a>
        </div>
      </div>
    </div>
  </div>
</div>