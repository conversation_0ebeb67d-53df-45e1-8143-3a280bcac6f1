<% content_for :page_title, "Billing & Subscription" %>
<% content_for :page_subtitle, "Manage your subscription and billing information" %>

<div class="dashboard-layout">
  <div class="dashboard-content billing-dashboard">
    <!-- Current Plan Section -->
    <div class="section">
      <div class="section-header">
        <h2>💳 Current Subscription</h2>
        <div class="header-actions">
          <% unless @organization.enterprise_plan? %>
            <button class="btn btn-primary">
              <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M7 11V7a5 5 0 0110 0v4M12 11v6M8 21h8a2 2 0 002-2v-5a2 2 0 00-2-2H8a2 2 0 00-2 2v5a2 2 0 002 2z" />
              </svg>
              Upgrade Plan
            </button>
          <% end %>
        </div>
      </div>

      <div class="subscription-overview">
        <div class="plan-details">
          <div class="plan-header">
            <div class="plan-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" />
              </svg>
            </div>
            <div class="plan-info">
              <h3><%= @organization.plan.humanize %> Plan</h3>
              <p class="plan-status">
                <span class="status-badge <%= @organization.active? ? 'status-success' : 'status-warning' %>">
                  <span class="status-dot"></span>
                  <%= @organization.status.humanize %>
                </span>
              </p>
            </div>
          </div>

          <div class="plan-pricing">
            <% if @organization.free_trial_plan? %>
              <div class="price-display">
                <span class="price">Free Trial</span>
                <span class="period">14 days remaining</span>
              </div>
            <% else %>
              <div class="price-display">
                <span class="currency">$</span>
                <span class="price">
                  <%= case @organization.plan
                      when 'starter' then '49'
                      when 'growth' then '149'
                      when 'scale' then '399'
                      when 'enterprise' then 'Custom'
                      else '0'
                    end %>
                </span>
                <span class="period">/month</span>
              </div>
            <% end %>
          </div>
        </div>

        <div class="plan-features">
          <div class="feature-card">
            <div class="feature-icon" style="background: rgba(var(--color-primary-rgb), 0.1);">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
            </div>
            <div class="feature-details">
              <h4>Data Records</h4>
              <p><%= @organization.monthly_data_limit == Float::INFINITY ? "Unlimited" : number_with_delimiter(@organization.monthly_data_limit) %> per month</p>
            </div>
          </div>

          <div class="feature-card">
            <div class="feature-icon" style="background: rgba(var(--color-success-rgb), 0.1);">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <div class="feature-details">
              <h4>API Requests</h4>
              <p><%= @organization.monthly_api_requests_limit == Float::INFINITY ? "Unlimited" : number_with_delimiter(@organization.monthly_api_requests_limit) %> per month</p>
            </div>
          </div>

          <div class="feature-card">
            <div class="feature-icon" style="background: rgba(var(--color-info-rgb), 0.1);">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2M9 7a4 4 0 1 0 0-8 4 4 0 0 0 0 8zM23 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75" />
              </svg>
            </div>
            <div class="feature-details">
              <h4>Team Members</h4>
              <p>Up to <%= @organization.max_users == Float::INFINITY ? "unlimited" : @organization.max_users %> users</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Usage Overview -->
    <div class="section">
      <div class="section-header">
        <h2>📊 Usage Overview</h2>
        <span class="text-secondary">Current billing period</span>
      </div>

      <div class="usage-cards">
        <div class="usage-card">
          <div class="usage-header">
            <h4>Data Storage</h4>
            <span class="usage-percentage">65%</span>
          </div>
          <div class="progress-bar">
            <div class="progress-fill" style="width: 65%;"></div>
          </div>
          <div class="usage-details">
            <span>6.5 GB used</span>
            <span class="text-secondary">of 10 GB</span>
          </div>
        </div>

        <div class="usage-card">
          <div class="usage-header">
            <h4>API Calls</h4>
            <span class="usage-percentage">42%</span>
          </div>
          <div class="progress-bar">
            <div class="progress-fill" style="width: 42%;"></div>
          </div>
          <div class="usage-details">
            <span>42,000 calls</span>
            <span class="text-secondary">of 100,000</span>
          </div>
        </div>

        <div class="usage-card">
          <div class="usage-header">
            <h4>Data Sources</h4>
            <span class="usage-percentage">40%</span>
          </div>
          <div class="progress-bar">
            <div class="progress-fill" style="width: 40%;"></div>
          </div>
          <div class="usage-details">
            <span>4 connected</span>
            <span class="text-secondary">of 10 allowed</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Payment Method -->
    <div class="section">
      <div class="section-header">
        <h2>💰 Payment Method</h2>
        <% if @organization.stripe_customer_id.present? %>
          <button class="btn btn-secondary">
            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path stroke-linecap="round" stroke-linejoin="round" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            Update
          </button>
        <% end %>
      </div>

      <div class="payment-method-section">
        <% if @organization.stripe_customer_id.present? %>
          <div class="payment-card active">
            <div class="card-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="7" width="18" height="10" rx="2" stroke-width="2"/>
                <path d="M3 10h18"/>
              </svg>
            </div>
            <div class="card-details">
              <div class="card-number">•••• •••• •••• 4242</div>
              <div class="card-meta">
                <span>Expires 12/24</span>
                <span class="status-badge status-success">
                  <span class="status-dot"></span>
                  Active
                </span>
              </div>
            </div>
          </div>
        <% else %>
          <div class="empty-state">
            <div class="empty-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="7" width="18" height="10" rx="2"/>
                <path d="M3 10h18"/>
              </svg>
            </div>
            <h3>No payment method</h3>
            <p>Add a payment method to upgrade your plan</p>
            <button class="btn btn-primary">
              <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4" />
              </svg>
              Add Payment Method
            </button>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Billing History -->
    <div class="section">
      <div class="section-header">
        <h2>🧾 Billing History</h2>
        <button class="btn btn-secondary">
          <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
          </svg>
          Download All
        </button>
      </div>

      <div class="billing-history">
        <% if false # Replace with actual billing history check %>
          <div class="invoice-list">
            <!-- Invoice items would go here -->
          </div>
        <% else %>
          <div class="empty-state">
            <div class="empty-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3>No invoices yet</h3>
            <p>Your billing history will appear here</p>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Available Plans -->
    <% unless @organization.enterprise_plan? %>
      <div class="section">
        <div class="section-header">
          <h2>🚀 Available Plans</h2>
          <span class="text-secondary">Choose the plan that fits your needs</span>
        </div>

        <div class="pricing-grid">
          <!-- Free Trial -->
          <div class="pricing-card <%= 'current' if @organization.free_trial_plan? %>">
            <div class="plan-badge">Free Trial</div>
            <div class="plan-price">
              <span class="price">$0</span>
              <span class="period">14 days</span>
            </div>
            <ul class="plan-features">
              <li>
                <svg class="check-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" />
                </svg>
                10,000 records/month
              </li>
              <li>
                <svg class="check-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" />
                </svg>
                2 team members
              </li>
              <li>
                <svg class="check-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" />
                </svg>
                2 data sources
              </li>
            </ul>
            <% if @organization.free_trial_plan? %>
              <button class="btn btn-primary" disabled>Current Plan</button>
            <% end %>
          </div>

          <!-- Starter -->
          <div class="pricing-card <%= 'current' if @organization.starter_plan? %>">
            <div class="plan-badge">Starter</div>
            <div class="plan-price">
              <span class="currency">$</span>
              <span class="price">49</span>
              <span class="period">/month</span>
            </div>
            <ul class="plan-features">
              <li>
                <svg class="check-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" />
                </svg>
                100,000 records/month
              </li>
              <li>
                <svg class="check-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" />
                </svg>
                5 team members
              </li>
              <li>
                <svg class="check-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" />
                </svg>
                5 data sources
              </li>
            </ul>
            <% if @organization.starter_plan? %>
              <button class="btn btn-primary" disabled>Current Plan</button>
            <% else %>
              <button class="btn btn-primary">Upgrade to Starter</button>
            <% end %>
          </div>

          <!-- Growth -->
          <div class="pricing-card <%= 'current' if @organization.growth_plan? %> featured">
            <div class="plan-badge">Growth</div>
            <div class="featured-label">Most Popular</div>
            <div class="plan-price">
              <span class="currency">$</span>
              <span class="price">149</span>
              <span class="period">/month</span>
            </div>
            <ul class="plan-features">
              <li>
                <svg class="check-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" />
                </svg>
                500,000 records/month
              </li>
              <li>
                <svg class="check-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" />
                </svg>
                20 team members
              </li>
              <li>
                <svg class="check-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" />
                </svg>
                15 data sources
              </li>
            </ul>
            <% if @organization.growth_plan? %>
              <button class="btn btn-primary" disabled>Current Plan</button>
            <% else %>
              <button class="btn btn-primary">Upgrade to Growth</button>
            <% end %>
          </div>

          <!-- Scale -->
          <div class="pricing-card <%= 'current' if @organization.scale_plan? %>">
            <div class="plan-badge">Scale</div>
            <div class="plan-price">
              <span class="currency">$</span>
              <span class="price">399</span>
              <span class="period">/month</span>
            </div>
            <ul class="plan-features">
              <li>
                <svg class="check-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" />
                </svg>
                2M records/month
              </li>
              <li>
                <svg class="check-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" />
                </svg>
                100 team members
              </li>
              <li>
                <svg class="check-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" />
                </svg>
                50 data sources
              </li>
            </ul>
            <% if @organization.scale_plan? %>
              <button class="btn btn-primary" disabled>Current Plan</button>
            <% else %>
              <button class="btn btn-primary">Upgrade to Scale</button>
            <% end %>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>