/* Integration Marketplace Styles */

.integration-marketplace {
  /* Uses dashboard-content padding */
}

/* Marketplace Header */
.marketplace-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--space-24);
  margin-bottom: var(--space-32);
  flex-wrap: wrap;
}

.search-section {
  flex: 1;
  display: flex;
  gap: var(--space-16);
  align-items: center;
  flex-wrap: wrap;
}

.search-box {
  position: relative;
  min-width: 300px;
  flex: 1;
  max-width: 500px;
}

.search-icon {
  position: absolute;
  left: var(--space-16);
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-text-secondary);
  pointer-events: none;
}

.search-input {
  width: 100%;
  padding: var(--space-12) var(--space-16) var(--space-12) calc(var(--space-16) + 28px);
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  color: var(--color-text);
  transition: all var(--duration-fast) var(--ease-standard);
}

.search-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: var(--focus-ring);
}

.filter-buttons {
  display: flex;
  gap: var(--space-8);
}

.filter-btn {
  padding: var(--space-8) var(--space-16);
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
}

.filter-btn:hover {
  border-color: var(--color-primary);
  color: var(--color-text);
}

.filter-btn.active {
  background: var(--color-primary);
  border-color: var(--color-primary);
  color: var(--color-btn-primary-text);
}

/* Integration Stats */
.integration-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-20);
  margin-bottom: var(--space-32);
}

.stat-card {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  display: flex;
  align-items: center;
  gap: var(--space-16);
  transition: all var(--duration-normal) var(--ease-standard);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-card.connected .stat-icon {
  background: rgba(var(--color-success-rgb), 0.1);
  color: var(--color-success);
}

.stat-card.syncing .stat-icon {
  background: rgba(var(--color-info-rgb), 0.1);
  color: var(--color-info);
}

.stat-card.errors .stat-icon {
  background: rgba(var(--color-error-rgb), 0.1);
  color: var(--color-error);
}

.stat-card.inactive .stat-icon {
  background: rgba(var(--color-text-secondary-rgb), 0.1);
  color: var(--color-text-secondary);
}

.stat-content p {
  margin: 0;
}

.stat-content .stat-value {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  line-height: 1;
  margin-bottom: var(--space-4);
}

.stat-content .stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* Integrations Section */
.integrations-section {
  margin-bottom: var(--space-32);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0 0 var(--space-24) 0;
}

/* Connected Integrations Grid */
.connected-integrations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
  gap: var(--space-24);
}

/* Integration Cards */
.integration-card {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  transition: all var(--duration-normal) var(--ease-standard);
  overflow: hidden;
}

.integration-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-primary);
}

/* Connected Card Specific */
.integration-card.connected-card {
  padding: var(--space-24);
}

.integration-header {
  display: flex;
  align-items: center;
  gap: var(--space-16);
  margin-bottom: var(--space-20);
}

.integration-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  flex-shrink: 0;
}

.integration-icon.shopify {
  background: rgba(var(--color-primary-rgb), 0.1);
}

.integration-icon.stripe {
  background: rgba(147, 51, 234, 0.1);
}

.integration-icon.google_analytics {
  background: rgba(251, 146, 60, 0.1);
}

.integration-icon.quickbooks {
  background: rgba(59, 130, 246, 0.1);
}

.integration-icon.mailchimp {
  background: rgba(250, 204, 21, 0.1);
}

.integration-info {
  flex: 1;
}

.integration-info h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0 0 var(--space-4) 0;
}

.integration-info p {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
}

.integration-status {
  display: flex;
  align-items: center;
  gap: var(--space-6);
  padding: var(--space-6) var(--space-12);
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

.integration-status.connected {
  color: var(--color-success);
  border-color: rgba(var(--color-success-rgb), 0.3);
  background: rgba(var(--color-success-rgb), 0.05);
}

.integration-status.syncing {
  color: var(--color-info);
  border-color: rgba(var(--color-info-rgb), 0.3);
  background: rgba(var(--color-info-rgb), 0.05);
}

.integration-status.syncing .status-dot {
  animation: pulse 2s infinite;
}

.integration-status.error {
  color: var(--color-error);
  border-color: rgba(var(--color-error-rgb), 0.3);
  background: rgba(var(--color-error-rgb), 0.05);
}

.integration-status.disconnected {
  color: var(--color-text-secondary);
}

/* Integration Stats */
.integration-card .integration-stats {
  background: var(--color-background);
  border-radius: var(--radius-md);
  padding: var(--space-16);
  margin-bottom: var(--space-20);
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-8) 0;
  font-size: var(--font-size-sm);
}

.stat-row:not(:last-child) {
  border-bottom: 1px solid var(--color-border);
}

.stat-row .stat-label {
  color: var(--color-text-secondary);
}

.stat-row .stat-value {
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

/* Integration Actions */
.integration-actions {
  display: flex;
  gap: var(--space-12);
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-8);
  padding: var(--space-10) var(--space-16);
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
}

.action-btn:hover {
  background: var(--color-surface);
  border-color: var(--color-primary);
}

.action-btn.primary {
  background: var(--color-primary);
  border-color: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.action-btn.primary:hover {
  background: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
}

/* Popular Integrations Grid */
.integrations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--space-20);
}

/* Integration Card (Popular) */
.integration-card:not(.connected-card) {
  display: flex;
  flex-direction: column;
  padding: var(--space-24);
  position: relative;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-20);
}

.icon-wrapper {
  width: 56px;
  height: 56px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
}

.icon-wrapper.primary {
  background: rgba(var(--color-primary-rgb), 0.1);
}

.icon-wrapper.purple {
  background: rgba(147, 51, 234, 0.1);
}

.icon-wrapper.blue {
  background: rgba(59, 130, 246, 0.1);
}

.icon-wrapper.orange {
  background: rgba(251, 146, 60, 0.1);
}

.icon-wrapper.yellow {
  background: rgba(250, 204, 21, 0.1);
}

.icon-wrapper.red {
  background: rgba(239, 68, 68, 0.1);
}

.coming-soon-badge {
  padding: var(--space-4) var(--space-12);
  background: var(--color-secondary);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
}

.card-content {
  flex: 1;
}

.card-content h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0 0 var(--space-8) 0;
}

.card-content p {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0 0 var(--space-20) 0;
}

.card-footer {
  margin-top: auto;
}

.integration-card.coming-soon {
  opacity: 0.7;
}

.integration-card.coming-soon:hover {
  transform: none;
  box-shadow: none;
  border-color: var(--color-card-border);
}

/* Empty State */
.empty-state-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  margin: var(--space-32) 0;
}

.empty-state {
  text-align: center;
  max-width: 400px;
}

.empty-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto var(--space-24);
  background: var(--color-secondary);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-secondary);
}

.empty-state h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0 0 var(--space-12) 0;
}

.empty-state p {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin: 0 0 var(--space-24) 0;
}

/* More Integrations */
.more-integrations {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-32);
  text-align: center;
  margin-top: var(--space-32);
}

.more-content h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0 0 var(--space-12) 0;
}

.more-content p {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin: 0 0 var(--space-24) 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .marketplace-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-section {
    flex-direction: column;
    gap: var(--space-12);
  }
  
  .search-box {
    min-width: 100%;
  }
  
  .filter-buttons {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .connected-integrations-grid {
    grid-template-columns: 1fr;
  }
  
  .integrations-grid {
    grid-template-columns: 1fr;
  }
  
  .integration-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 640px) {
  .integration-stats {
    grid-template-columns: 1fr;
  }
  
  .stat-card {
    padding: var(--space-16);
  }
  
  .integration-card.connected-card {
    padding: var(--space-16);
  }
  
  .integration-header {
    flex-wrap: wrap;
  }
  
  .integration-status {
    width: 100%;
    justify-content: center;
    margin-top: var(--space-12);
  }
}

/* Add missing RGB color variables */
:root {
  --color-text-secondary-rgb: 98, 108, 113;
}
