/* Organization Dashboard Styles */

.organization-dashboard {
  /* Inherits padding from dashboard-content */
}

/* Organization Overview Section */
.org-overview-section {
  margin-bottom: var(--space-32);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: var(--space-24);
}

.stat-card {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  display: flex;
  gap: var(--space-20);
  transition: all var(--duration-normal) var(--ease-standard);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary);
}

.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-icon.users {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.2));
  color: rgb(59, 130, 246);
}

.stat-icon.sources {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(34, 197, 94, 0.2));
  color: rgb(34, 197, 94);
}

.stat-icon.plan {
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.1), rgba(147, 51, 234, 0.2));
  color: rgb(147, 51, 234);
}

.stat-icon.member-since {
  background: linear-gradient(135deg, rgba(251, 146, 60, 0.1), rgba(251, 146, 60, 0.2));
  color: rgb(251, 146, 60);
}

.stat-content {
  flex: 1;
}

.stat-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  margin: 0 0 var(--space-8) 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stat-value {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin: 0 0 var(--space-8) 0;
}

.stat-detail {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
}

.plan-status {
  display: inline-flex;
  align-items: center;
  gap: var(--space-6);
  padding: var(--space-4) var(--space-12);
  background: rgba(var(--color-success-rgb), 0.1);
  border: 1px solid rgba(var(--color-success-rgb), 0.3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-success);
}

.plan-status .status-dot {
  width: 6px;
  height: 6px;
  background: var(--color-success);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

/* Content Grid */
.org-content-grid {
  display: grid;
  grid-template-columns: 1fr 380px;
  gap: var(--space-32);
}

.org-main-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-24);
}

/* Organization Details Card */
.org-details-card {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-24);
  border-bottom: 1px solid var(--color-border);
}

.card-header h2,
.card-header h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-24);
  padding: var(--space-24);
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.detail-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.detail-value {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

.detail-value.mono {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-6);
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  border: 1px solid;
}

.status-badge.active {
  background: rgba(var(--color-success-rgb), 0.1);
  border-color: rgba(var(--color-success-rgb), 0.3);
  color: var(--color-success);
}

.status-badge.inactive {
  background: rgba(var(--color-warning-rgb), 0.1);
  border-color: rgba(var(--color-warning-rgb), 0.3);
  color: var(--color-warning);
}

.status-badge .status-dot {
  width: 6px;
  height: 6px;
  background: currentColor;
  border-radius: 50%;
}

/* Plan Usage Card */
.plan-usage-card {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.usage-metrics {
  padding: var(--space-24);
  display: flex;
  flex-direction: column;
  gap: var(--space-24);
}

.usage-metric {
  display: flex;
  flex-direction: column;
  gap: var(--space-12);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

.metric-value {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
}

.progress-bar {
  height: 8px;
  background: rgba(var(--color-primary-rgb), 0.1);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--color-primary);
  border-radius: var(--radius-full);
  transition: width var(--duration-normal) var(--ease-standard);
}

.progress-fill.info {
  background: var(--color-info);
}

.progress-fill.success {
  background: var(--color-success);
}

.metric-details {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
}

/* Recent Activity Card */
.recent-activity-card {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.activity-list {
  padding: var(--space-24);
  display: flex;
  flex-direction: column;
  gap: var(--space-20);
}

.activity-item {
  display: flex;
  gap: var(--space-16);
}

.activity-icon {
  width: 40px;
  height: 40px;
  background: rgba(var(--color-primary-rgb), 0.1);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-primary);
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-description {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  margin: 0 0 var(--space-4) 0;
}

.activity-meta {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
}

/* Sidebar */
.org-sidebar {
  display: flex;
  flex-direction: column;
  gap: var(--space-24);
}

/* Quick Actions Card */
.quick-actions-card {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
}

.quick-actions-card h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0 0 var(--space-20) 0;
}

.actions-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-12);
}

.action-item {
  display: flex;
  align-items: center;
  gap: var(--space-12);
  padding: var(--space-12);
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  text-decoration: none;
  transition: all var(--duration-fast) var(--ease-standard);
}

.action-item:hover {
  background: var(--color-surface);
  border-color: var(--color-primary);
  transform: translateX(4px);
}

.action-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, rgba(var(--color-primary-rgb), 0.1), rgba(var(--color-primary-rgb), 0.2));
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-primary);
  flex-shrink: 0;
}

.action-content {
  flex: 1;
}

.action-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  margin: 0 0 var(--space-2) 0;
}

.action-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
}

.action-arrow {
  color: var(--color-text-secondary);
  transition: transform var(--duration-fast) var(--ease-standard);
}

.action-item:hover .action-arrow {
  transform: translateX(4px);
}

/* Integrations Summary Card */
.integrations-summary-card {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.integrations-summary-card .card-header {
  padding: var(--space-20) var(--space-24);
}

.link-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

.link-text:hover {
  color: var(--color-primary-hover);
  text-decoration: underline;
}

.integration-list {
  padding: 0 var(--space-24) var(--space-24);
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
}

.integration-item {
  display: flex;
  align-items: center;
  gap: var(--space-12);
}

.integration-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  flex-shrink: 0;
}

.integration-icon.shopify {
  background: rgba(var(--color-primary-rgb), 0.1);
}

.integration-icon.stripe {
  background: rgba(147, 51, 234, 0.1);
}

.integration-icon.google_analytics {
  background: rgba(251, 146, 60, 0.1);
}

.integration-info {
  flex: 1;
}

.integration-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  margin: 0 0 var(--space-2) 0;
}

.integration-type {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  margin: 0;
}

.integration-status {
  width: 12px;
  height: 12px;
  position: relative;
}

.integration-status .status-dot {
  width: 12px;
  height: 12px;
  background: var(--color-success);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.more-integrations {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  text-align: center;
  margin: var(--space-12) 0 0 0;
  padding-top: var(--space-12);
  border-top: 1px solid var(--color-border);
}

.empty-integrations {
  padding: var(--space-24);
  text-align: center;
}

.empty-integrations p {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0 0 var(--space-16) 0;
}

/* Support Card */
.support-card {
  background: linear-gradient(135deg, var(--color-surface) 0%, rgba(var(--color-primary-rgb), 0.05) 100%);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  text-align: center;
}

.support-icon {
  width: 64px;
  height: 64px;
  background: rgba(var(--color-primary-rgb), 0.1);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-primary);
  margin: 0 auto var(--space-16);
}

.support-card h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0 0 var(--space-8) 0;
}

.support-card p {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0 0 var(--space-20) 0;
}

.support-actions {
  display: flex;
  gap: var(--space-12);
}

.support-actions .btn {
  flex: 1;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .org-content-grid {
    grid-template-columns: 1fr;
  }
  
  .org-sidebar {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--space-24);
  }
  
  .support-card {
    grid-column: 1 / -1;
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .details-grid {
    grid-template-columns: 1fr;
  }
  
  .org-sidebar {
    grid-template-columns: 1fr;
  }
  
  .card-header {
    flex-wrap: wrap;
    gap: var(--space-12);
  }
  
  .support-actions {
    flex-direction: column;
  }
}

@media (max-width: 640px) {
  .stat-card {
    flex-direction: column;
    text-align: center;
  }
  
  .stat-icon {
    margin: 0 auto;
  }
}
