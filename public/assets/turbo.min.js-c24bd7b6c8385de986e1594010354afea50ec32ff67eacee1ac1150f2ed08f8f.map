{"version": 3, "file": "turbo.min.js", "sources": ["../../../node_modules/@hotwired/turbo/dist/turbo.es2017-esm.js", "../../javascript/turbo/cable.js", "../../javascript/turbo/snakeize.js", "../../javascript/turbo/cable_stream_source_element.js", "../../javascript/turbo/index.js", "../../javascript/turbo/fetch_requests.js", "../../../node_modules/@rails/actioncable/src/adapters.js", "../../../node_modules/@rails/actioncable/src/logger.js", "../../../node_modules/@rails/actioncable/src/connection_monitor.js", "../../../node_modules/@rails/actioncable/src/internal.js", "../../../node_modules/@rails/actioncable/src/connection.js", "../../../node_modules/@rails/actioncable/src/subscription.js", "../../../node_modules/@rails/actioncable/src/subscription_guarantor.js", "../../../node_modules/@rails/actioncable/src/subscriptions.js", "../../../node_modules/@rails/actioncable/src/consumer.js", "../../../node_modules/@rails/actioncable/src/index.js"], "sourcesContent": ["/*!\nTurbo 8.0.13\nCopyright © 2025 37signals LLC\n */\n/**\n * The MIT License (MIT)\n *\n * Copyright (c) 2019 <PERSON><PERSON>\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\n(function (prototype) {\n  if (typeof prototype.requestSubmit == \"function\") return\n\n  prototype.requestSubmit = function (submitter) {\n    if (submitter) {\n      validateSubmitter(submitter, this);\n      submitter.click();\n    } else {\n      submitter = document.createElement(\"input\");\n      submitter.type = \"submit\";\n      submitter.hidden = true;\n      this.appendChild(submitter);\n      submitter.click();\n      this.removeChild(submitter);\n    }\n  };\n\n  function validateSubmitter(submitter, form) {\n    submitter instanceof HTMLElement || raise(TypeError, \"parameter 1 is not of type 'HTMLElement'\");\n    submitter.type == \"submit\" || raise(TypeError, \"The specified element is not a submit button\");\n    submitter.form == form ||\n      raise(DOMException, \"The specified element is not owned by this form element\", \"NotFoundError\");\n  }\n\n  function raise(errorConstructor, message, name) {\n    throw new errorConstructor(\"Failed to execute 'requestSubmit' on 'HTMLFormElement': \" + message + \".\", name)\n  }\n})(HTMLFormElement.prototype);\n\nconst submittersByForm = new WeakMap();\n\nfunction findSubmitterFromClickTarget(target) {\n  const element = target instanceof Element ? target : target instanceof Node ? target.parentElement : null;\n  const candidate = element ? element.closest(\"input, button\") : null;\n  return candidate?.type == \"submit\" ? candidate : null\n}\n\nfunction clickCaptured(event) {\n  const submitter = findSubmitterFromClickTarget(event.target);\n\n  if (submitter && submitter.form) {\n    submittersByForm.set(submitter.form, submitter);\n  }\n}\n\n(function () {\n  if (\"submitter\" in Event.prototype) return\n\n  let prototype = window.Event.prototype;\n  // Certain versions of Safari 15 have a bug where they won't\n  // populate the submitter. This hurts TurboDrive's enable/disable detection.\n  // See https://bugs.webkit.org/show_bug.cgi?id=229660\n  if (\"SubmitEvent\" in window) {\n    const prototypeOfSubmitEvent = window.SubmitEvent.prototype;\n\n    if (/Apple Computer/.test(navigator.vendor) && !(\"submitter\" in prototypeOfSubmitEvent)) {\n      prototype = prototypeOfSubmitEvent;\n    } else {\n      return // polyfill not needed\n    }\n  }\n\n  addEventListener(\"click\", clickCaptured, true);\n\n  Object.defineProperty(prototype, \"submitter\", {\n    get() {\n      if (this.type == \"submit\" && this.target instanceof HTMLFormElement) {\n        return submittersByForm.get(this.target)\n      }\n    }\n  });\n})();\n\nconst FrameLoadingStyle = {\n  eager: \"eager\",\n  lazy: \"lazy\"\n};\n\n/**\n * Contains a fragment of HTML which is updated based on navigation within\n * it (e.g. via links or form submissions).\n *\n * @customElement turbo-frame\n * @example\n *   <turbo-frame id=\"messages\">\n *     <a href=\"/messages/expanded\">\n *       Show all expanded messages in this frame.\n *     </a>\n *\n *     <form action=\"/messages\">\n *       Show response from this form within this frame.\n *     </form>\n *   </turbo-frame>\n */\nclass FrameElement extends HTMLElement {\n  static delegateConstructor = undefined\n\n  loaded = Promise.resolve()\n\n  static get observedAttributes() {\n    return [\"disabled\", \"loading\", \"src\"]\n  }\n\n  constructor() {\n    super();\n    this.delegate = new FrameElement.delegateConstructor(this);\n  }\n\n  connectedCallback() {\n    this.delegate.connect();\n  }\n\n  disconnectedCallback() {\n    this.delegate.disconnect();\n  }\n\n  reload() {\n    return this.delegate.sourceURLReloaded()\n  }\n\n  attributeChangedCallback(name) {\n    if (name == \"loading\") {\n      this.delegate.loadingStyleChanged();\n    } else if (name == \"src\") {\n      this.delegate.sourceURLChanged();\n    } else if (name == \"disabled\") {\n      this.delegate.disabledChanged();\n    }\n  }\n\n  /**\n   * Gets the URL to lazily load source HTML from\n   */\n  get src() {\n    return this.getAttribute(\"src\")\n  }\n\n  /**\n   * Sets the URL to lazily load source HTML from\n   */\n  set src(value) {\n    if (value) {\n      this.setAttribute(\"src\", value);\n    } else {\n      this.removeAttribute(\"src\");\n    }\n  }\n\n  /**\n   * Gets the refresh mode for the frame.\n   */\n  get refresh() {\n    return this.getAttribute(\"refresh\")\n  }\n\n  /**\n   * Sets the refresh mode for the frame.\n   */\n  set refresh(value) {\n    if (value) {\n      this.setAttribute(\"refresh\", value);\n    } else {\n      this.removeAttribute(\"refresh\");\n    }\n  }\n\n  get shouldReloadWithMorph() {\n    return this.src && this.refresh === \"morph\"\n  }\n\n  /**\n   * Determines if the element is loading\n   */\n  get loading() {\n    return frameLoadingStyleFromString(this.getAttribute(\"loading\") || \"\")\n  }\n\n  /**\n   * Sets the value of if the element is loading\n   */\n  set loading(value) {\n    if (value) {\n      this.setAttribute(\"loading\", value);\n    } else {\n      this.removeAttribute(\"loading\");\n    }\n  }\n\n  /**\n   * Gets the disabled state of the frame.\n   *\n   * If disabled, no requests will be intercepted by the frame.\n   */\n  get disabled() {\n    return this.hasAttribute(\"disabled\")\n  }\n\n  /**\n   * Sets the disabled state of the frame.\n   *\n   * If disabled, no requests will be intercepted by the frame.\n   */\n  set disabled(value) {\n    if (value) {\n      this.setAttribute(\"disabled\", \"\");\n    } else {\n      this.removeAttribute(\"disabled\");\n    }\n  }\n\n  /**\n   * Gets the autoscroll state of the frame.\n   *\n   * If true, the frame will be scrolled into view automatically on update.\n   */\n  get autoscroll() {\n    return this.hasAttribute(\"autoscroll\")\n  }\n\n  /**\n   * Sets the autoscroll state of the frame.\n   *\n   * If true, the frame will be scrolled into view automatically on update.\n   */\n  set autoscroll(value) {\n    if (value) {\n      this.setAttribute(\"autoscroll\", \"\");\n    } else {\n      this.removeAttribute(\"autoscroll\");\n    }\n  }\n\n  /**\n   * Determines if the element has finished loading\n   */\n  get complete() {\n    return !this.delegate.isLoading\n  }\n\n  /**\n   * Gets the active state of the frame.\n   *\n   * If inactive, source changes will not be observed.\n   */\n  get isActive() {\n    return this.ownerDocument === document && !this.isPreview\n  }\n\n  /**\n   * Sets the active state of the frame.\n   *\n   * If inactive, source changes will not be observed.\n   */\n  get isPreview() {\n    return this.ownerDocument?.documentElement?.hasAttribute(\"data-turbo-preview\")\n  }\n}\n\nfunction frameLoadingStyleFromString(style) {\n  switch (style.toLowerCase()) {\n    case \"lazy\":\n      return FrameLoadingStyle.lazy\n    default:\n      return FrameLoadingStyle.eager\n  }\n}\n\nconst drive = {\n  enabled: true,\n  progressBarDelay: 500,\n  unvisitableExtensions: new Set(\n    [\n      \".7z\", \".aac\", \".apk\", \".avi\", \".bmp\", \".bz2\", \".css\", \".csv\", \".deb\", \".dmg\", \".doc\",\n      \".docx\", \".exe\", \".gif\", \".gz\", \".heic\", \".heif\", \".ico\", \".iso\", \".jpeg\", \".jpg\",\n      \".js\", \".json\", \".m4a\", \".mkv\", \".mov\", \".mp3\", \".mp4\", \".mpeg\", \".mpg\", \".msi\",\n      \".ogg\", \".ogv\", \".pdf\", \".pkg\", \".png\", \".ppt\", \".pptx\", \".rar\", \".rtf\",\n      \".svg\", \".tar\", \".tif\", \".tiff\", \".txt\", \".wav\", \".webm\", \".webp\", \".wma\", \".wmv\",\n      \".xls\", \".xlsx\", \".xml\", \".zip\"\n    ]\n  )\n};\n\nfunction activateScriptElement(element) {\n  if (element.getAttribute(\"data-turbo-eval\") == \"false\") {\n    return element\n  } else {\n    const createdScriptElement = document.createElement(\"script\");\n    const cspNonce = getCspNonce();\n    if (cspNonce) {\n      createdScriptElement.nonce = cspNonce;\n    }\n    createdScriptElement.textContent = element.textContent;\n    createdScriptElement.async = false;\n    copyElementAttributes(createdScriptElement, element);\n    return createdScriptElement\n  }\n}\n\nfunction copyElementAttributes(destinationElement, sourceElement) {\n  for (const { name, value } of sourceElement.attributes) {\n    destinationElement.setAttribute(name, value);\n  }\n}\n\nfunction createDocumentFragment(html) {\n  const template = document.createElement(\"template\");\n  template.innerHTML = html;\n  return template.content\n}\n\nfunction dispatch(eventName, { target, cancelable, detail } = {}) {\n  const event = new CustomEvent(eventName, {\n    cancelable,\n    bubbles: true,\n    composed: true,\n    detail\n  });\n\n  if (target && target.isConnected) {\n    target.dispatchEvent(event);\n  } else {\n    document.documentElement.dispatchEvent(event);\n  }\n\n  return event\n}\n\nfunction cancelEvent(event) {\n  event.preventDefault();\n  event.stopImmediatePropagation();\n}\n\nfunction nextRepaint() {\n  if (document.visibilityState === \"hidden\") {\n    return nextEventLoopTick()\n  } else {\n    return nextAnimationFrame()\n  }\n}\n\nfunction nextAnimationFrame() {\n  return new Promise((resolve) => requestAnimationFrame(() => resolve()))\n}\n\nfunction nextEventLoopTick() {\n  return new Promise((resolve) => setTimeout(() => resolve(), 0))\n}\n\nfunction nextMicrotask() {\n  return Promise.resolve()\n}\n\nfunction parseHTMLDocument(html = \"\") {\n  return new DOMParser().parseFromString(html, \"text/html\")\n}\n\nfunction unindent(strings, ...values) {\n  const lines = interpolate(strings, values).replace(/^\\n/, \"\").split(\"\\n\");\n  const match = lines[0].match(/^\\s+/);\n  const indent = match ? match[0].length : 0;\n  return lines.map((line) => line.slice(indent)).join(\"\\n\")\n}\n\nfunction interpolate(strings, values) {\n  return strings.reduce((result, string, i) => {\n    const value = values[i] == undefined ? \"\" : values[i];\n    return result + string + value\n  }, \"\")\n}\n\nfunction uuid() {\n  return Array.from({ length: 36 })\n    .map((_, i) => {\n      if (i == 8 || i == 13 || i == 18 || i == 23) {\n        return \"-\"\n      } else if (i == 14) {\n        return \"4\"\n      } else if (i == 19) {\n        return (Math.floor(Math.random() * 4) + 8).toString(16)\n      } else {\n        return Math.floor(Math.random() * 15).toString(16)\n      }\n    })\n    .join(\"\")\n}\n\nfunction getAttribute(attributeName, ...elements) {\n  for (const value of elements.map((element) => element?.getAttribute(attributeName))) {\n    if (typeof value == \"string\") return value\n  }\n\n  return null\n}\n\nfunction hasAttribute(attributeName, ...elements) {\n  return elements.some((element) => element && element.hasAttribute(attributeName))\n}\n\nfunction markAsBusy(...elements) {\n  for (const element of elements) {\n    if (element.localName == \"turbo-frame\") {\n      element.setAttribute(\"busy\", \"\");\n    }\n    element.setAttribute(\"aria-busy\", \"true\");\n  }\n}\n\nfunction clearBusyState(...elements) {\n  for (const element of elements) {\n    if (element.localName == \"turbo-frame\") {\n      element.removeAttribute(\"busy\");\n    }\n\n    element.removeAttribute(\"aria-busy\");\n  }\n}\n\nfunction waitForLoad(element, timeoutInMilliseconds = 2000) {\n  return new Promise((resolve) => {\n    const onComplete = () => {\n      element.removeEventListener(\"error\", onComplete);\n      element.removeEventListener(\"load\", onComplete);\n      resolve();\n    };\n\n    element.addEventListener(\"load\", onComplete, { once: true });\n    element.addEventListener(\"error\", onComplete, { once: true });\n    setTimeout(resolve, timeoutInMilliseconds);\n  })\n}\n\nfunction getHistoryMethodForAction(action) {\n  switch (action) {\n    case \"replace\":\n      return history.replaceState\n    case \"advance\":\n    case \"restore\":\n      return history.pushState\n  }\n}\n\nfunction isAction(action) {\n  return action == \"advance\" || action == \"replace\" || action == \"restore\"\n}\n\nfunction getVisitAction(...elements) {\n  const action = getAttribute(\"data-turbo-action\", ...elements);\n\n  return isAction(action) ? action : null\n}\n\nfunction getMetaElement(name) {\n  return document.querySelector(`meta[name=\"${name}\"]`)\n}\n\nfunction getMetaContent(name) {\n  const element = getMetaElement(name);\n  return element && element.content\n}\n\nfunction getCspNonce() {\n  const element = getMetaElement(\"csp-nonce\");\n\n  if (element) {\n    const { nonce, content } = element;\n    return nonce == \"\" ? content : nonce\n  }\n}\n\nfunction setMetaContent(name, content) {\n  let element = getMetaElement(name);\n\n  if (!element) {\n    element = document.createElement(\"meta\");\n    element.setAttribute(\"name\", name);\n\n    document.head.appendChild(element);\n  }\n\n  element.setAttribute(\"content\", content);\n\n  return element\n}\n\nfunction findClosestRecursively(element, selector) {\n  if (element instanceof Element) {\n    return (\n      element.closest(selector) || findClosestRecursively(element.assignedSlot || element.getRootNode()?.host, selector)\n    )\n  }\n}\n\nfunction elementIsFocusable(element) {\n  const inertDisabledOrHidden = \"[inert], :disabled, [hidden], details:not([open]), dialog:not([open])\";\n\n  return !!element && element.closest(inertDisabledOrHidden) == null && typeof element.focus == \"function\"\n}\n\nfunction queryAutofocusableElement(elementOrDocumentFragment) {\n  return Array.from(elementOrDocumentFragment.querySelectorAll(\"[autofocus]\")).find(elementIsFocusable)\n}\n\nasync function around(callback, reader) {\n  const before = reader();\n\n  callback();\n\n  await nextAnimationFrame();\n\n  const after = reader();\n\n  return [before, after]\n}\n\nfunction doesNotTargetIFrame(name) {\n  if (name === \"_blank\") {\n    return false\n  } else if (name) {\n    for (const element of document.getElementsByName(name)) {\n      if (element instanceof HTMLIFrameElement) return false\n    }\n\n    return true\n  } else {\n    return true\n  }\n}\n\nfunction findLinkFromClickTarget(target) {\n  return findClosestRecursively(target, \"a[href]:not([target^=_]):not([download])\")\n}\n\nfunction getLocationForLink(link) {\n  return expandURL(link.getAttribute(\"href\") || \"\")\n}\n\nfunction debounce(fn, delay) {\n  let timeoutId = null;\n\n  return (...args) => {\n    const callback = () => fn.apply(this, args);\n    clearTimeout(timeoutId);\n    timeoutId = setTimeout(callback, delay);\n  }\n}\n\nconst submitter = {\n  \"aria-disabled\": {\n    beforeSubmit: submitter => {\n      submitter.setAttribute(\"aria-disabled\", \"true\");\n      submitter.addEventListener(\"click\", cancelEvent);\n    },\n\n    afterSubmit: submitter => {\n      submitter.removeAttribute(\"aria-disabled\");\n      submitter.removeEventListener(\"click\", cancelEvent);\n    }\n  },\n\n  \"disabled\": {\n    beforeSubmit: submitter => submitter.disabled = true,\n    afterSubmit: submitter => submitter.disabled = false\n  }\n};\n\nclass Config {\n  #submitter = null\n\n  constructor(config) {\n    Object.assign(this, config);\n  }\n\n  get submitter() {\n    return this.#submitter\n  }\n\n  set submitter(value) {\n    this.#submitter = submitter[value] || value;\n  }\n}\n\nconst forms = new Config({\n  mode: \"on\",\n  submitter: \"disabled\"\n});\n\nconst config = {\n  drive,\n  forms\n};\n\nfunction expandURL(locatable) {\n  return new URL(locatable.toString(), document.baseURI)\n}\n\nfunction getAnchor(url) {\n  let anchorMatch;\n  if (url.hash) {\n    return url.hash.slice(1)\n    // eslint-disable-next-line no-cond-assign\n  } else if ((anchorMatch = url.href.match(/#(.*)$/))) {\n    return anchorMatch[1]\n  }\n}\n\nfunction getAction$1(form, submitter) {\n  const action = submitter?.getAttribute(\"formaction\") || form.getAttribute(\"action\") || form.action;\n\n  return expandURL(action)\n}\n\nfunction getExtension(url) {\n  return (getLastPathComponent(url).match(/\\.[^.]*$/) || [])[0] || \"\"\n}\n\nfunction isPrefixedBy(baseURL, url) {\n  const prefix = getPrefix(url);\n  return baseURL.href === expandURL(prefix).href || baseURL.href.startsWith(prefix)\n}\n\nfunction locationIsVisitable(location, rootLocation) {\n  return isPrefixedBy(location, rootLocation) && !config.drive.unvisitableExtensions.has(getExtension(location))\n}\n\nfunction getRequestURL(url) {\n  const anchor = getAnchor(url);\n  return anchor != null ? url.href.slice(0, -(anchor.length + 1)) : url.href\n}\n\nfunction toCacheKey(url) {\n  return getRequestURL(url)\n}\n\nfunction urlsAreEqual(left, right) {\n  return expandURL(left).href == expandURL(right).href\n}\n\nfunction getPathComponents(url) {\n  return url.pathname.split(\"/\").slice(1)\n}\n\nfunction getLastPathComponent(url) {\n  return getPathComponents(url).slice(-1)[0]\n}\n\nfunction getPrefix(url) {\n  return addTrailingSlash(url.origin + url.pathname)\n}\n\nfunction addTrailingSlash(value) {\n  return value.endsWith(\"/\") ? value : value + \"/\"\n}\n\nclass FetchResponse {\n  constructor(response) {\n    this.response = response;\n  }\n\n  get succeeded() {\n    return this.response.ok\n  }\n\n  get failed() {\n    return !this.succeeded\n  }\n\n  get clientError() {\n    return this.statusCode >= 400 && this.statusCode <= 499\n  }\n\n  get serverError() {\n    return this.statusCode >= 500 && this.statusCode <= 599\n  }\n\n  get redirected() {\n    return this.response.redirected\n  }\n\n  get location() {\n    return expandURL(this.response.url)\n  }\n\n  get isHTML() {\n    return this.contentType && this.contentType.match(/^(?:text\\/([^\\s;,]+\\b)?html|application\\/xhtml\\+xml)\\b/)\n  }\n\n  get statusCode() {\n    return this.response.status\n  }\n\n  get contentType() {\n    return this.header(\"Content-Type\")\n  }\n\n  get responseText() {\n    return this.response.clone().text()\n  }\n\n  get responseHTML() {\n    if (this.isHTML) {\n      return this.response.clone().text()\n    } else {\n      return Promise.resolve(undefined)\n    }\n  }\n\n  header(name) {\n    return this.response.headers.get(name)\n  }\n}\n\nclass LimitedSet extends Set {\n  constructor(maxSize) {\n    super();\n    this.maxSize = maxSize;\n  }\n\n  add(value) {\n    if (this.size >= this.maxSize) {\n      const iterator = this.values();\n      const oldestValue = iterator.next().value;\n      this.delete(oldestValue);\n    }\n    super.add(value);\n  }\n}\n\nconst recentRequests = new LimitedSet(20);\n\nconst nativeFetch = window.fetch;\n\nfunction fetchWithTurboHeaders(url, options = {}) {\n  const modifiedHeaders = new Headers(options.headers || {});\n  const requestUID = uuid();\n  recentRequests.add(requestUID);\n  modifiedHeaders.append(\"X-Turbo-Request-Id\", requestUID);\n\n  return nativeFetch(url, {\n    ...options,\n    headers: modifiedHeaders\n  })\n}\n\nfunction fetchMethodFromString(method) {\n  switch (method.toLowerCase()) {\n    case \"get\":\n      return FetchMethod.get\n    case \"post\":\n      return FetchMethod.post\n    case \"put\":\n      return FetchMethod.put\n    case \"patch\":\n      return FetchMethod.patch\n    case \"delete\":\n      return FetchMethod.delete\n  }\n}\n\nconst FetchMethod = {\n  get: \"get\",\n  post: \"post\",\n  put: \"put\",\n  patch: \"patch\",\n  delete: \"delete\"\n};\n\nfunction fetchEnctypeFromString(encoding) {\n  switch (encoding.toLowerCase()) {\n    case FetchEnctype.multipart:\n      return FetchEnctype.multipart\n    case FetchEnctype.plain:\n      return FetchEnctype.plain\n    default:\n      return FetchEnctype.urlEncoded\n  }\n}\n\nconst FetchEnctype = {\n  urlEncoded: \"application/x-www-form-urlencoded\",\n  multipart: \"multipart/form-data\",\n  plain: \"text/plain\"\n};\n\nclass FetchRequest {\n  abortController = new AbortController()\n  #resolveRequestPromise = (_value) => {}\n\n  constructor(delegate, method, location, requestBody = new URLSearchParams(), target = null, enctype = FetchEnctype.urlEncoded) {\n    const [url, body] = buildResourceAndBody(expandURL(location), method, requestBody, enctype);\n\n    this.delegate = delegate;\n    this.url = url;\n    this.target = target;\n    this.fetchOptions = {\n      credentials: \"same-origin\",\n      redirect: \"follow\",\n      method: method.toUpperCase(),\n      headers: { ...this.defaultHeaders },\n      body: body,\n      signal: this.abortSignal,\n      referrer: this.delegate.referrer?.href\n    };\n    this.enctype = enctype;\n  }\n\n  get method() {\n    return this.fetchOptions.method\n  }\n\n  set method(value) {\n    const fetchBody = this.isSafe ? this.url.searchParams : this.fetchOptions.body || new FormData();\n    const fetchMethod = fetchMethodFromString(value) || FetchMethod.get;\n\n    this.url.search = \"\";\n\n    const [url, body] = buildResourceAndBody(this.url, fetchMethod, fetchBody, this.enctype);\n\n    this.url = url;\n    this.fetchOptions.body = body;\n    this.fetchOptions.method = fetchMethod.toUpperCase();\n  }\n\n  get headers() {\n    return this.fetchOptions.headers\n  }\n\n  set headers(value) {\n    this.fetchOptions.headers = value;\n  }\n\n  get body() {\n    if (this.isSafe) {\n      return this.url.searchParams\n    } else {\n      return this.fetchOptions.body\n    }\n  }\n\n  set body(value) {\n    this.fetchOptions.body = value;\n  }\n\n  get location() {\n    return this.url\n  }\n\n  get params() {\n    return this.url.searchParams\n  }\n\n  get entries() {\n    return this.body ? Array.from(this.body.entries()) : []\n  }\n\n  cancel() {\n    this.abortController.abort();\n  }\n\n  async perform() {\n    const { fetchOptions } = this;\n    this.delegate.prepareRequest(this);\n    const event = await this.#allowRequestToBeIntercepted(fetchOptions);\n    try {\n      this.delegate.requestStarted(this);\n\n      if (event.detail.fetchRequest) {\n        this.response = event.detail.fetchRequest.response;\n      } else {\n        this.response = fetchWithTurboHeaders(this.url.href, fetchOptions);\n      }\n\n      const response = await this.response;\n      return await this.receive(response)\n    } catch (error) {\n      if (error.name !== \"AbortError\") {\n        if (this.#willDelegateErrorHandling(error)) {\n          this.delegate.requestErrored(this, error);\n        }\n        throw error\n      }\n    } finally {\n      this.delegate.requestFinished(this);\n    }\n  }\n\n  async receive(response) {\n    const fetchResponse = new FetchResponse(response);\n    const event = dispatch(\"turbo:before-fetch-response\", {\n      cancelable: true,\n      detail: { fetchResponse },\n      target: this.target\n    });\n    if (event.defaultPrevented) {\n      this.delegate.requestPreventedHandlingResponse(this, fetchResponse);\n    } else if (fetchResponse.succeeded) {\n      this.delegate.requestSucceededWithResponse(this, fetchResponse);\n    } else {\n      this.delegate.requestFailedWithResponse(this, fetchResponse);\n    }\n    return fetchResponse\n  }\n\n  get defaultHeaders() {\n    return {\n      Accept: \"text/html, application/xhtml+xml\"\n    }\n  }\n\n  get isSafe() {\n    return isSafe(this.method)\n  }\n\n  get abortSignal() {\n    return this.abortController.signal\n  }\n\n  acceptResponseType(mimeType) {\n    this.headers[\"Accept\"] = [mimeType, this.headers[\"Accept\"]].join(\", \");\n  }\n\n  async #allowRequestToBeIntercepted(fetchOptions) {\n    const requestInterception = new Promise((resolve) => (this.#resolveRequestPromise = resolve));\n    const event = dispatch(\"turbo:before-fetch-request\", {\n      cancelable: true,\n      detail: {\n        fetchOptions,\n        url: this.url,\n        resume: this.#resolveRequestPromise\n      },\n      target: this.target\n    });\n    this.url = event.detail.url;\n    if (event.defaultPrevented) await requestInterception;\n\n    return event\n  }\n\n  #willDelegateErrorHandling(error) {\n    const event = dispatch(\"turbo:fetch-request-error\", {\n      target: this.target,\n      cancelable: true,\n      detail: { request: this, error: error }\n    });\n\n    return !event.defaultPrevented\n  }\n}\n\nfunction isSafe(fetchMethod) {\n  return fetchMethodFromString(fetchMethod) == FetchMethod.get\n}\n\nfunction buildResourceAndBody(resource, method, requestBody, enctype) {\n  const searchParams =\n    Array.from(requestBody).length > 0 ? new URLSearchParams(entriesExcludingFiles(requestBody)) : resource.searchParams;\n\n  if (isSafe(method)) {\n    return [mergeIntoURLSearchParams(resource, searchParams), null]\n  } else if (enctype == FetchEnctype.urlEncoded) {\n    return [resource, searchParams]\n  } else {\n    return [resource, requestBody]\n  }\n}\n\nfunction entriesExcludingFiles(requestBody) {\n  const entries = [];\n\n  for (const [name, value] of requestBody) {\n    if (value instanceof File) continue\n    else entries.push([name, value]);\n  }\n\n  return entries\n}\n\nfunction mergeIntoURLSearchParams(url, requestBody) {\n  const searchParams = new URLSearchParams(entriesExcludingFiles(requestBody));\n\n  url.search = searchParams.toString();\n\n  return url\n}\n\nclass AppearanceObserver {\n  started = false\n\n  constructor(delegate, element) {\n    this.delegate = delegate;\n    this.element = element;\n    this.intersectionObserver = new IntersectionObserver(this.intersect);\n  }\n\n  start() {\n    if (!this.started) {\n      this.started = true;\n      this.intersectionObserver.observe(this.element);\n    }\n  }\n\n  stop() {\n    if (this.started) {\n      this.started = false;\n      this.intersectionObserver.unobserve(this.element);\n    }\n  }\n\n  intersect = (entries) => {\n    const lastEntry = entries.slice(-1)[0];\n    if (lastEntry?.isIntersecting) {\n      this.delegate.elementAppearedInViewport(this.element);\n    }\n  }\n}\n\nclass StreamMessage {\n  static contentType = \"text/vnd.turbo-stream.html\"\n\n  static wrap(message) {\n    if (typeof message == \"string\") {\n      return new this(createDocumentFragment(message))\n    } else {\n      return message\n    }\n  }\n\n  constructor(fragment) {\n    this.fragment = importStreamElements(fragment);\n  }\n}\n\nfunction importStreamElements(fragment) {\n  for (const element of fragment.querySelectorAll(\"turbo-stream\")) {\n    const streamElement = document.importNode(element, true);\n\n    for (const inertScriptElement of streamElement.templateElement.content.querySelectorAll(\"script\")) {\n      inertScriptElement.replaceWith(activateScriptElement(inertScriptElement));\n    }\n\n    element.replaceWith(streamElement);\n  }\n\n  return fragment\n}\n\nconst PREFETCH_DELAY = 100;\n\nclass PrefetchCache {\n  #prefetchTimeout = null\n  #prefetched = null\n\n  get(url) {\n    if (this.#prefetched && this.#prefetched.url === url && this.#prefetched.expire > Date.now()) {\n      return this.#prefetched.request\n    }\n  }\n\n  setLater(url, request, ttl) {\n    this.clear();\n\n    this.#prefetchTimeout = setTimeout(() => {\n      request.perform();\n      this.set(url, request, ttl);\n      this.#prefetchTimeout = null;\n    }, PREFETCH_DELAY);\n  }\n\n  set(url, request, ttl) {\n    this.#prefetched = { url, request, expire: new Date(new Date().getTime() + ttl) };\n  }\n\n  clear() {\n    if (this.#prefetchTimeout) clearTimeout(this.#prefetchTimeout);\n    this.#prefetched = null;\n  }\n}\n\nconst cacheTtl = 10 * 1000;\nconst prefetchCache = new PrefetchCache();\n\nconst FormSubmissionState = {\n  initialized: \"initialized\",\n  requesting: \"requesting\",\n  waiting: \"waiting\",\n  receiving: \"receiving\",\n  stopping: \"stopping\",\n  stopped: \"stopped\"\n};\n\nclass FormSubmission {\n  state = FormSubmissionState.initialized\n\n  static confirmMethod(message) {\n    return Promise.resolve(confirm(message))\n  }\n\n  constructor(delegate, formElement, submitter, mustRedirect = false) {\n    const method = getMethod(formElement, submitter);\n    const action = getAction(getFormAction(formElement, submitter), method);\n    const body = buildFormData(formElement, submitter);\n    const enctype = getEnctype(formElement, submitter);\n\n    this.delegate = delegate;\n    this.formElement = formElement;\n    this.submitter = submitter;\n    this.fetchRequest = new FetchRequest(this, method, action, body, formElement, enctype);\n    this.mustRedirect = mustRedirect;\n  }\n\n  get method() {\n    return this.fetchRequest.method\n  }\n\n  set method(value) {\n    this.fetchRequest.method = value;\n  }\n\n  get action() {\n    return this.fetchRequest.url.toString()\n  }\n\n  set action(value) {\n    this.fetchRequest.url = expandURL(value);\n  }\n\n  get body() {\n    return this.fetchRequest.body\n  }\n\n  get enctype() {\n    return this.fetchRequest.enctype\n  }\n\n  get isSafe() {\n    return this.fetchRequest.isSafe\n  }\n\n  get location() {\n    return this.fetchRequest.url\n  }\n\n  // The submission process\n\n  async start() {\n    const { initialized, requesting } = FormSubmissionState;\n    const confirmationMessage = getAttribute(\"data-turbo-confirm\", this.submitter, this.formElement);\n\n    if (typeof confirmationMessage === \"string\") {\n      const confirmMethod = typeof config.forms.confirm === \"function\" ?\n        config.forms.confirm :\n        FormSubmission.confirmMethod;\n\n      const answer = await confirmMethod(confirmationMessage, this.formElement, this.submitter);\n      if (!answer) {\n        return\n      }\n    }\n\n    if (this.state == initialized) {\n      this.state = requesting;\n      return this.fetchRequest.perform()\n    }\n  }\n\n  stop() {\n    const { stopping, stopped } = FormSubmissionState;\n    if (this.state != stopping && this.state != stopped) {\n      this.state = stopping;\n      this.fetchRequest.cancel();\n      return true\n    }\n  }\n\n  // Fetch request delegate\n\n  prepareRequest(request) {\n    if (!request.isSafe) {\n      const token = getCookieValue(getMetaContent(\"csrf-param\")) || getMetaContent(\"csrf-token\");\n      if (token) {\n        request.headers[\"X-CSRF-Token\"] = token;\n      }\n    }\n\n    if (this.requestAcceptsTurboStreamResponse(request)) {\n      request.acceptResponseType(StreamMessage.contentType);\n    }\n  }\n\n  requestStarted(_request) {\n    this.state = FormSubmissionState.waiting;\n    if (this.submitter) config.forms.submitter.beforeSubmit(this.submitter);\n    this.setSubmitsWith();\n    markAsBusy(this.formElement);\n    dispatch(\"turbo:submit-start\", {\n      target: this.formElement,\n      detail: { formSubmission: this }\n    });\n    this.delegate.formSubmissionStarted(this);\n  }\n\n  requestPreventedHandlingResponse(request, response) {\n    prefetchCache.clear();\n\n    this.result = { success: response.succeeded, fetchResponse: response };\n  }\n\n  requestSucceededWithResponse(request, response) {\n    if (response.clientError || response.serverError) {\n      this.delegate.formSubmissionFailedWithResponse(this, response);\n      return\n    }\n\n    prefetchCache.clear();\n\n    if (this.requestMustRedirect(request) && responseSucceededWithoutRedirect(response)) {\n      const error = new Error(\"Form responses must redirect to another location\");\n      this.delegate.formSubmissionErrored(this, error);\n    } else {\n      this.state = FormSubmissionState.receiving;\n      this.result = { success: true, fetchResponse: response };\n      this.delegate.formSubmissionSucceededWithResponse(this, response);\n    }\n  }\n\n  requestFailedWithResponse(request, response) {\n    this.result = { success: false, fetchResponse: response };\n    this.delegate.formSubmissionFailedWithResponse(this, response);\n  }\n\n  requestErrored(request, error) {\n    this.result = { success: false, error };\n    this.delegate.formSubmissionErrored(this, error);\n  }\n\n  requestFinished(_request) {\n    this.state = FormSubmissionState.stopped;\n    if (this.submitter) config.forms.submitter.afterSubmit(this.submitter);\n    this.resetSubmitterText();\n    clearBusyState(this.formElement);\n    dispatch(\"turbo:submit-end\", {\n      target: this.formElement,\n      detail: { formSubmission: this, ...this.result }\n    });\n    this.delegate.formSubmissionFinished(this);\n  }\n\n  // Private\n\n  setSubmitsWith() {\n    if (!this.submitter || !this.submitsWith) return\n\n    if (this.submitter.matches(\"button\")) {\n      this.originalSubmitText = this.submitter.innerHTML;\n      this.submitter.innerHTML = this.submitsWith;\n    } else if (this.submitter.matches(\"input\")) {\n      const input = this.submitter;\n      this.originalSubmitText = input.value;\n      input.value = this.submitsWith;\n    }\n  }\n\n  resetSubmitterText() {\n    if (!this.submitter || !this.originalSubmitText) return\n\n    if (this.submitter.matches(\"button\")) {\n      this.submitter.innerHTML = this.originalSubmitText;\n    } else if (this.submitter.matches(\"input\")) {\n      const input = this.submitter;\n      input.value = this.originalSubmitText;\n    }\n  }\n\n  requestMustRedirect(request) {\n    return !request.isSafe && this.mustRedirect\n  }\n\n  requestAcceptsTurboStreamResponse(request) {\n    return !request.isSafe || hasAttribute(\"data-turbo-stream\", this.submitter, this.formElement)\n  }\n\n  get submitsWith() {\n    return this.submitter?.getAttribute(\"data-turbo-submits-with\")\n  }\n}\n\nfunction buildFormData(formElement, submitter) {\n  const formData = new FormData(formElement);\n  const name = submitter?.getAttribute(\"name\");\n  const value = submitter?.getAttribute(\"value\");\n\n  if (name) {\n    formData.append(name, value || \"\");\n  }\n\n  return formData\n}\n\nfunction getCookieValue(cookieName) {\n  if (cookieName != null) {\n    const cookies = document.cookie ? document.cookie.split(\"; \") : [];\n    const cookie = cookies.find((cookie) => cookie.startsWith(cookieName));\n    if (cookie) {\n      const value = cookie.split(\"=\").slice(1).join(\"=\");\n      return value ? decodeURIComponent(value) : undefined\n    }\n  }\n}\n\nfunction responseSucceededWithoutRedirect(response) {\n  return response.statusCode == 200 && !response.redirected\n}\n\nfunction getFormAction(formElement, submitter) {\n  const formElementAction = typeof formElement.action === \"string\" ? formElement.action : null;\n\n  if (submitter?.hasAttribute(\"formaction\")) {\n    return submitter.getAttribute(\"formaction\") || \"\"\n  } else {\n    return formElement.getAttribute(\"action\") || formElementAction || \"\"\n  }\n}\n\nfunction getAction(formAction, fetchMethod) {\n  const action = expandURL(formAction);\n\n  if (isSafe(fetchMethod)) {\n    action.search = \"\";\n  }\n\n  return action\n}\n\nfunction getMethod(formElement, submitter) {\n  const method = submitter?.getAttribute(\"formmethod\") || formElement.getAttribute(\"method\") || \"\";\n  return fetchMethodFromString(method.toLowerCase()) || FetchMethod.get\n}\n\nfunction getEnctype(formElement, submitter) {\n  return fetchEnctypeFromString(submitter?.getAttribute(\"formenctype\") || formElement.enctype)\n}\n\nclass Snapshot {\n  constructor(element) {\n    this.element = element;\n  }\n\n  get activeElement() {\n    return this.element.ownerDocument.activeElement\n  }\n\n  get children() {\n    return [...this.element.children]\n  }\n\n  hasAnchor(anchor) {\n    return this.getElementForAnchor(anchor) != null\n  }\n\n  getElementForAnchor(anchor) {\n    return anchor ? this.element.querySelector(`[id='${anchor}'], a[name='${anchor}']`) : null\n  }\n\n  get isConnected() {\n    return this.element.isConnected\n  }\n\n  get firstAutofocusableElement() {\n    return queryAutofocusableElement(this.element)\n  }\n\n  get permanentElements() {\n    return queryPermanentElementsAll(this.element)\n  }\n\n  getPermanentElementById(id) {\n    return getPermanentElementById(this.element, id)\n  }\n\n  getPermanentElementMapForSnapshot(snapshot) {\n    const permanentElementMap = {};\n\n    for (const currentPermanentElement of this.permanentElements) {\n      const { id } = currentPermanentElement;\n      const newPermanentElement = snapshot.getPermanentElementById(id);\n      if (newPermanentElement) {\n        permanentElementMap[id] = [currentPermanentElement, newPermanentElement];\n      }\n    }\n\n    return permanentElementMap\n  }\n}\n\nfunction getPermanentElementById(node, id) {\n  return node.querySelector(`#${id}[data-turbo-permanent]`)\n}\n\nfunction queryPermanentElementsAll(node) {\n  return node.querySelectorAll(\"[id][data-turbo-permanent]\")\n}\n\nclass FormSubmitObserver {\n  started = false\n\n  constructor(delegate, eventTarget) {\n    this.delegate = delegate;\n    this.eventTarget = eventTarget;\n  }\n\n  start() {\n    if (!this.started) {\n      this.eventTarget.addEventListener(\"submit\", this.submitCaptured, true);\n      this.started = true;\n    }\n  }\n\n  stop() {\n    if (this.started) {\n      this.eventTarget.removeEventListener(\"submit\", this.submitCaptured, true);\n      this.started = false;\n    }\n  }\n\n  submitCaptured = () => {\n    this.eventTarget.removeEventListener(\"submit\", this.submitBubbled, false);\n    this.eventTarget.addEventListener(\"submit\", this.submitBubbled, false);\n  }\n\n  submitBubbled = (event) => {\n    if (!event.defaultPrevented) {\n      const form = event.target instanceof HTMLFormElement ? event.target : undefined;\n      const submitter = event.submitter || undefined;\n\n      if (\n        form &&\n        submissionDoesNotDismissDialog(form, submitter) &&\n        submissionDoesNotTargetIFrame(form, submitter) &&\n        this.delegate.willSubmitForm(form, submitter)\n      ) {\n        event.preventDefault();\n        event.stopImmediatePropagation();\n        this.delegate.formSubmitted(form, submitter);\n      }\n    }\n  }\n}\n\nfunction submissionDoesNotDismissDialog(form, submitter) {\n  const method = submitter?.getAttribute(\"formmethod\") || form.getAttribute(\"method\");\n\n  return method != \"dialog\"\n}\n\nfunction submissionDoesNotTargetIFrame(form, submitter) {\n  const target = submitter?.getAttribute(\"formtarget\") || form.getAttribute(\"target\");\n\n  return doesNotTargetIFrame(target)\n}\n\nclass View {\n  #resolveRenderPromise = (_value) => {}\n  #resolveInterceptionPromise = (_value) => {}\n\n  constructor(delegate, element) {\n    this.delegate = delegate;\n    this.element = element;\n  }\n\n  // Scrolling\n\n  scrollToAnchor(anchor) {\n    const element = this.snapshot.getElementForAnchor(anchor);\n    if (element) {\n      this.scrollToElement(element);\n      this.focusElement(element);\n    } else {\n      this.scrollToPosition({ x: 0, y: 0 });\n    }\n  }\n\n  scrollToAnchorFromLocation(location) {\n    this.scrollToAnchor(getAnchor(location));\n  }\n\n  scrollToElement(element) {\n    element.scrollIntoView();\n  }\n\n  focusElement(element) {\n    if (element instanceof HTMLElement) {\n      if (element.hasAttribute(\"tabindex\")) {\n        element.focus();\n      } else {\n        element.setAttribute(\"tabindex\", \"-1\");\n        element.focus();\n        element.removeAttribute(\"tabindex\");\n      }\n    }\n  }\n\n  scrollToPosition({ x, y }) {\n    this.scrollRoot.scrollTo(x, y);\n  }\n\n  scrollToTop() {\n    this.scrollToPosition({ x: 0, y: 0 });\n  }\n\n  get scrollRoot() {\n    return window\n  }\n\n  // Rendering\n\n  async render(renderer) {\n    const { isPreview, shouldRender, willRender, newSnapshot: snapshot } = renderer;\n\n    // A workaround to ignore tracked element mismatch reloads when performing\n    // a promoted Visit from a frame navigation\n    const shouldInvalidate = willRender;\n\n    if (shouldRender) {\n      try {\n        this.renderPromise = new Promise((resolve) => (this.#resolveRenderPromise = resolve));\n        this.renderer = renderer;\n        await this.prepareToRenderSnapshot(renderer);\n\n        const renderInterception = new Promise((resolve) => (this.#resolveInterceptionPromise = resolve));\n        const options = { resume: this.#resolveInterceptionPromise, render: this.renderer.renderElement, renderMethod: this.renderer.renderMethod };\n        const immediateRender = this.delegate.allowsImmediateRender(snapshot, options);\n        if (!immediateRender) await renderInterception;\n\n        await this.renderSnapshot(renderer);\n        this.delegate.viewRenderedSnapshot(snapshot, isPreview, this.renderer.renderMethod);\n        this.delegate.preloadOnLoadLinksForView(this.element);\n        this.finishRenderingSnapshot(renderer);\n      } finally {\n        delete this.renderer;\n        this.#resolveRenderPromise(undefined);\n        delete this.renderPromise;\n      }\n    } else if (shouldInvalidate) {\n      this.invalidate(renderer.reloadReason);\n    }\n  }\n\n  invalidate(reason) {\n    this.delegate.viewInvalidated(reason);\n  }\n\n  async prepareToRenderSnapshot(renderer) {\n    this.markAsPreview(renderer.isPreview);\n    await renderer.prepareToRender();\n  }\n\n  markAsPreview(isPreview) {\n    if (isPreview) {\n      this.element.setAttribute(\"data-turbo-preview\", \"\");\n    } else {\n      this.element.removeAttribute(\"data-turbo-preview\");\n    }\n  }\n\n  markVisitDirection(direction) {\n    this.element.setAttribute(\"data-turbo-visit-direction\", direction);\n  }\n\n  unmarkVisitDirection() {\n    this.element.removeAttribute(\"data-turbo-visit-direction\");\n  }\n\n  async renderSnapshot(renderer) {\n    await renderer.render();\n  }\n\n  finishRenderingSnapshot(renderer) {\n    renderer.finishRendering();\n  }\n}\n\nclass FrameView extends View {\n  missing() {\n    this.element.innerHTML = `<strong class=\"turbo-frame-error\">Content missing</strong>`;\n  }\n\n  get snapshot() {\n    return new Snapshot(this.element)\n  }\n}\n\nclass LinkInterceptor {\n  constructor(delegate, element) {\n    this.delegate = delegate;\n    this.element = element;\n  }\n\n  start() {\n    this.element.addEventListener(\"click\", this.clickBubbled);\n    document.addEventListener(\"turbo:click\", this.linkClicked);\n    document.addEventListener(\"turbo:before-visit\", this.willVisit);\n  }\n\n  stop() {\n    this.element.removeEventListener(\"click\", this.clickBubbled);\n    document.removeEventListener(\"turbo:click\", this.linkClicked);\n    document.removeEventListener(\"turbo:before-visit\", this.willVisit);\n  }\n\n  clickBubbled = (event) => {\n    if (this.clickEventIsSignificant(event)) {\n      this.clickEvent = event;\n    } else {\n      delete this.clickEvent;\n    }\n  }\n\n  linkClicked = (event) => {\n    if (this.clickEvent && this.clickEventIsSignificant(event)) {\n      if (this.delegate.shouldInterceptLinkClick(event.target, event.detail.url, event.detail.originalEvent)) {\n        this.clickEvent.preventDefault();\n        event.preventDefault();\n        this.delegate.linkClickIntercepted(event.target, event.detail.url, event.detail.originalEvent);\n      }\n    }\n    delete this.clickEvent;\n  }\n\n  willVisit = (_event) => {\n    delete this.clickEvent;\n  }\n\n  clickEventIsSignificant(event) {\n    const target = event.composed ? event.target?.parentElement : event.target;\n    const element = findLinkFromClickTarget(target) || target;\n\n    return element instanceof Element && element.closest(\"turbo-frame, html\") == this.element\n  }\n}\n\nclass LinkClickObserver {\n  started = false\n\n  constructor(delegate, eventTarget) {\n    this.delegate = delegate;\n    this.eventTarget = eventTarget;\n  }\n\n  start() {\n    if (!this.started) {\n      this.eventTarget.addEventListener(\"click\", this.clickCaptured, true);\n      this.started = true;\n    }\n  }\n\n  stop() {\n    if (this.started) {\n      this.eventTarget.removeEventListener(\"click\", this.clickCaptured, true);\n      this.started = false;\n    }\n  }\n\n  clickCaptured = () => {\n    this.eventTarget.removeEventListener(\"click\", this.clickBubbled, false);\n    this.eventTarget.addEventListener(\"click\", this.clickBubbled, false);\n  }\n\n  clickBubbled = (event) => {\n    if (event instanceof MouseEvent && this.clickEventIsSignificant(event)) {\n      const target = (event.composedPath && event.composedPath()[0]) || event.target;\n      const link = findLinkFromClickTarget(target);\n      if (link && doesNotTargetIFrame(link.target)) {\n        const location = getLocationForLink(link);\n        if (this.delegate.willFollowLinkToLocation(link, location, event)) {\n          event.preventDefault();\n          this.delegate.followedLinkToLocation(link, location);\n        }\n      }\n    }\n  }\n\n  clickEventIsSignificant(event) {\n    return !(\n      (event.target && event.target.isContentEditable) ||\n      event.defaultPrevented ||\n      event.which > 1 ||\n      event.altKey ||\n      event.ctrlKey ||\n      event.metaKey ||\n      event.shiftKey\n    )\n  }\n}\n\nclass FormLinkClickObserver {\n  constructor(delegate, element) {\n    this.delegate = delegate;\n    this.linkInterceptor = new LinkClickObserver(this, element);\n  }\n\n  start() {\n    this.linkInterceptor.start();\n  }\n\n  stop() {\n    this.linkInterceptor.stop();\n  }\n\n  // Link hover observer delegate\n\n  canPrefetchRequestToLocation(link, location) {\n    return false\n  }\n\n  prefetchAndCacheRequestToLocation(link, location) {\n    return\n  }\n\n  // Link click observer delegate\n\n  willFollowLinkToLocation(link, location, originalEvent) {\n    return (\n      this.delegate.willSubmitFormLinkToLocation(link, location, originalEvent) &&\n      (link.hasAttribute(\"data-turbo-method\") || link.hasAttribute(\"data-turbo-stream\"))\n    )\n  }\n\n  followedLinkToLocation(link, location) {\n    const form = document.createElement(\"form\");\n\n    const type = \"hidden\";\n    for (const [name, value] of location.searchParams) {\n      form.append(Object.assign(document.createElement(\"input\"), { type, name, value }));\n    }\n\n    const action = Object.assign(location, { search: \"\" });\n    form.setAttribute(\"data-turbo\", \"true\");\n    form.setAttribute(\"action\", action.href);\n    form.setAttribute(\"hidden\", \"\");\n\n    const method = link.getAttribute(\"data-turbo-method\");\n    if (method) form.setAttribute(\"method\", method);\n\n    const turboFrame = link.getAttribute(\"data-turbo-frame\");\n    if (turboFrame) form.setAttribute(\"data-turbo-frame\", turboFrame);\n\n    const turboAction = getVisitAction(link);\n    if (turboAction) form.setAttribute(\"data-turbo-action\", turboAction);\n\n    const turboConfirm = link.getAttribute(\"data-turbo-confirm\");\n    if (turboConfirm) form.setAttribute(\"data-turbo-confirm\", turboConfirm);\n\n    const turboStream = link.hasAttribute(\"data-turbo-stream\");\n    if (turboStream) form.setAttribute(\"data-turbo-stream\", \"\");\n\n    this.delegate.submittedFormLinkToLocation(link, location, form);\n\n    document.body.appendChild(form);\n    form.addEventListener(\"turbo:submit-end\", () => form.remove(), { once: true });\n    requestAnimationFrame(() => form.requestSubmit());\n  }\n}\n\nclass Bardo {\n  static async preservingPermanentElements(delegate, permanentElementMap, callback) {\n    const bardo = new this(delegate, permanentElementMap);\n    bardo.enter();\n    await callback();\n    bardo.leave();\n  }\n\n  constructor(delegate, permanentElementMap) {\n    this.delegate = delegate;\n    this.permanentElementMap = permanentElementMap;\n  }\n\n  enter() {\n    for (const id in this.permanentElementMap) {\n      const [currentPermanentElement, newPermanentElement] = this.permanentElementMap[id];\n      this.delegate.enteringBardo(currentPermanentElement, newPermanentElement);\n      this.replaceNewPermanentElementWithPlaceholder(newPermanentElement);\n    }\n  }\n\n  leave() {\n    for (const id in this.permanentElementMap) {\n      const [currentPermanentElement] = this.permanentElementMap[id];\n      this.replaceCurrentPermanentElementWithClone(currentPermanentElement);\n      this.replacePlaceholderWithPermanentElement(currentPermanentElement);\n      this.delegate.leavingBardo(currentPermanentElement);\n    }\n  }\n\n  replaceNewPermanentElementWithPlaceholder(permanentElement) {\n    const placeholder = createPlaceholderForPermanentElement(permanentElement);\n    permanentElement.replaceWith(placeholder);\n  }\n\n  replaceCurrentPermanentElementWithClone(permanentElement) {\n    const clone = permanentElement.cloneNode(true);\n    permanentElement.replaceWith(clone);\n  }\n\n  replacePlaceholderWithPermanentElement(permanentElement) {\n    const placeholder = this.getPlaceholderById(permanentElement.id);\n    placeholder?.replaceWith(permanentElement);\n  }\n\n  getPlaceholderById(id) {\n    return this.placeholders.find((element) => element.content == id)\n  }\n\n  get placeholders() {\n    return [...document.querySelectorAll(\"meta[name=turbo-permanent-placeholder][content]\")]\n  }\n}\n\nfunction createPlaceholderForPermanentElement(permanentElement) {\n  const element = document.createElement(\"meta\");\n  element.setAttribute(\"name\", \"turbo-permanent-placeholder\");\n  element.setAttribute(\"content\", permanentElement.id);\n  return element\n}\n\nclass Renderer {\n  #activeElement = null\n\n  static renderElement(currentElement, newElement) {\n    // Abstract method\n  }\n\n  constructor(currentSnapshot, newSnapshot, isPreview, willRender = true) {\n    this.currentSnapshot = currentSnapshot;\n    this.newSnapshot = newSnapshot;\n    this.isPreview = isPreview;\n    this.willRender = willRender;\n    this.renderElement = this.constructor.renderElement;\n    this.promise = new Promise((resolve, reject) => (this.resolvingFunctions = { resolve, reject }));\n  }\n\n  get shouldRender() {\n    return true\n  }\n\n  get shouldAutofocus() {\n    return true\n  }\n\n  get reloadReason() {\n    return\n  }\n\n  prepareToRender() {\n    return\n  }\n\n  render() {\n    // Abstract method\n  }\n\n  finishRendering() {\n    if (this.resolvingFunctions) {\n      this.resolvingFunctions.resolve();\n      delete this.resolvingFunctions;\n    }\n  }\n\n  async preservingPermanentElements(callback) {\n    await Bardo.preservingPermanentElements(this, this.permanentElementMap, callback);\n  }\n\n  focusFirstAutofocusableElement() {\n    if (this.shouldAutofocus) {\n      const element = this.connectedSnapshot.firstAutofocusableElement;\n      if (element) {\n        element.focus();\n      }\n    }\n  }\n\n  // Bardo delegate\n\n  enteringBardo(currentPermanentElement) {\n    if (this.#activeElement) return\n\n    if (currentPermanentElement.contains(this.currentSnapshot.activeElement)) {\n      this.#activeElement = this.currentSnapshot.activeElement;\n    }\n  }\n\n  leavingBardo(currentPermanentElement) {\n    if (currentPermanentElement.contains(this.#activeElement) && this.#activeElement instanceof HTMLElement) {\n      this.#activeElement.focus();\n\n      this.#activeElement = null;\n    }\n  }\n\n  get connectedSnapshot() {\n    return this.newSnapshot.isConnected ? this.newSnapshot : this.currentSnapshot\n  }\n\n  get currentElement() {\n    return this.currentSnapshot.element\n  }\n\n  get newElement() {\n    return this.newSnapshot.element\n  }\n\n  get permanentElementMap() {\n    return this.currentSnapshot.getPermanentElementMapForSnapshot(this.newSnapshot)\n  }\n\n  get renderMethod() {\n    return \"replace\"\n  }\n}\n\nclass FrameRenderer extends Renderer {\n  static renderElement(currentElement, newElement) {\n    const destinationRange = document.createRange();\n    destinationRange.selectNodeContents(currentElement);\n    destinationRange.deleteContents();\n\n    const frameElement = newElement;\n    const sourceRange = frameElement.ownerDocument?.createRange();\n    if (sourceRange) {\n      sourceRange.selectNodeContents(frameElement);\n      currentElement.appendChild(sourceRange.extractContents());\n    }\n  }\n\n  constructor(delegate, currentSnapshot, newSnapshot, renderElement, isPreview, willRender = true) {\n    super(currentSnapshot, newSnapshot, renderElement, isPreview, willRender);\n    this.delegate = delegate;\n  }\n\n  get shouldRender() {\n    return true\n  }\n\n  async render() {\n    await nextRepaint();\n    this.preservingPermanentElements(() => {\n      this.loadFrameElement();\n    });\n    this.scrollFrameIntoView();\n    await nextRepaint();\n    this.focusFirstAutofocusableElement();\n    await nextRepaint();\n    this.activateScriptElements();\n  }\n\n  loadFrameElement() {\n    this.delegate.willRenderFrame(this.currentElement, this.newElement);\n    this.renderElement(this.currentElement, this.newElement);\n  }\n\n  scrollFrameIntoView() {\n    if (this.currentElement.autoscroll || this.newElement.autoscroll) {\n      const element = this.currentElement.firstElementChild;\n      const block = readScrollLogicalPosition(this.currentElement.getAttribute(\"data-autoscroll-block\"), \"end\");\n      const behavior = readScrollBehavior(this.currentElement.getAttribute(\"data-autoscroll-behavior\"), \"auto\");\n\n      if (element) {\n        element.scrollIntoView({ block, behavior });\n        return true\n      }\n    }\n    return false\n  }\n\n  activateScriptElements() {\n    for (const inertScriptElement of this.newScriptElements) {\n      const activatedScriptElement = activateScriptElement(inertScriptElement);\n      inertScriptElement.replaceWith(activatedScriptElement);\n    }\n  }\n\n  get newScriptElements() {\n    return this.currentElement.querySelectorAll(\"script\")\n  }\n}\n\nfunction readScrollLogicalPosition(value, defaultValue) {\n  if (value == \"end\" || value == \"start\" || value == \"center\" || value == \"nearest\") {\n    return value\n  } else {\n    return defaultValue\n  }\n}\n\nfunction readScrollBehavior(value, defaultValue) {\n  if (value == \"auto\" || value == \"smooth\") {\n    return value\n  } else {\n    return defaultValue\n  }\n}\n\n/**\n * @typedef {object} ConfigHead\n *\n * @property {'merge' | 'append' | 'morph' | 'none'} [style]\n * @property {boolean} [block]\n * @property {boolean} [ignore]\n * @property {function(Element): boolean} [shouldPreserve]\n * @property {function(Element): boolean} [shouldReAppend]\n * @property {function(Element): boolean} [shouldRemove]\n * @property {function(Element, {added: Node[], kept: Element[], removed: Element[]}): void} [afterHeadMorphed]\n */\n\n/**\n * @typedef {object} ConfigCallbacks\n *\n * @property {function(Node): boolean} [beforeNodeAdded]\n * @property {function(Node): void} [afterNodeAdded]\n * @property {function(Element, Node): boolean} [beforeNodeMorphed]\n * @property {function(Element, Node): void} [afterNodeMorphed]\n * @property {function(Element): boolean} [beforeNodeRemoved]\n * @property {function(Element): void} [afterNodeRemoved]\n * @property {function(string, Element, \"update\" | \"remove\"): boolean} [beforeAttributeUpdated]\n */\n\n/**\n * @typedef {object} Config\n *\n * @property {'outerHTML' | 'innerHTML'} [morphStyle]\n * @property {boolean} [ignoreActive]\n * @property {boolean} [ignoreActiveValue]\n * @property {boolean} [restoreFocus]\n * @property {ConfigCallbacks} [callbacks]\n * @property {ConfigHead} [head]\n */\n\n/**\n * @typedef {function} NoOp\n *\n * @returns {void}\n */\n\n/**\n * @typedef {object} ConfigHeadInternal\n *\n * @property {'merge' | 'append' | 'morph' | 'none'} style\n * @property {boolean} [block]\n * @property {boolean} [ignore]\n * @property {(function(Element): boolean) | NoOp} shouldPreserve\n * @property {(function(Element): boolean) | NoOp} shouldReAppend\n * @property {(function(Element): boolean) | NoOp} shouldRemove\n * @property {(function(Element, {added: Node[], kept: Element[], removed: Element[]}): void) | NoOp} afterHeadMorphed\n */\n\n/**\n * @typedef {object} ConfigCallbacksInternal\n *\n * @property {(function(Node): boolean) | NoOp} beforeNodeAdded\n * @property {(function(Node): void) | NoOp} afterNodeAdded\n * @property {(function(Node, Node): boolean) | NoOp} beforeNodeMorphed\n * @property {(function(Node, Node): void) | NoOp} afterNodeMorphed\n * @property {(function(Node): boolean) | NoOp} beforeNodeRemoved\n * @property {(function(Node): void) | NoOp} afterNodeRemoved\n * @property {(function(string, Element, \"update\" | \"remove\"): boolean) | NoOp} beforeAttributeUpdated\n */\n\n/**\n * @typedef {object} ConfigInternal\n *\n * @property {'outerHTML' | 'innerHTML'} morphStyle\n * @property {boolean} [ignoreActive]\n * @property {boolean} [ignoreActiveValue]\n * @property {boolean} [restoreFocus]\n * @property {ConfigCallbacksInternal} callbacks\n * @property {ConfigHeadInternal} head\n */\n\n/**\n * @typedef {Object} IdSets\n * @property {Set<string>} persistentIds\n * @property {Map<Node, Set<string>>} idMap\n */\n\n/**\n * @typedef {Function} Morph\n *\n * @param {Element | Document} oldNode\n * @param {Element | Node | HTMLCollection | Node[] | string | null} newContent\n * @param {Config} [config]\n * @returns {undefined | Node[]}\n */\n\n// base IIFE to define idiomorph\n/**\n *\n * @type {{defaults: ConfigInternal, morph: Morph}}\n */\nvar Idiomorph = (function () {\n\n  /**\n   * @typedef {object} MorphContext\n   *\n   * @property {Element} target\n   * @property {Element} newContent\n   * @property {ConfigInternal} config\n   * @property {ConfigInternal['morphStyle']} morphStyle\n   * @property {ConfigInternal['ignoreActive']} ignoreActive\n   * @property {ConfigInternal['ignoreActiveValue']} ignoreActiveValue\n   * @property {ConfigInternal['restoreFocus']} restoreFocus\n   * @property {Map<Node, Set<string>>} idMap\n   * @property {Set<string>} persistentIds\n   * @property {ConfigInternal['callbacks']} callbacks\n   * @property {ConfigInternal['head']} head\n   * @property {HTMLDivElement} pantry\n   */\n\n  //=============================================================================\n  // AND NOW IT BEGINS...\n  //=============================================================================\n\n  const noOp = () => {};\n  /**\n   * Default configuration values, updatable by users now\n   * @type {ConfigInternal}\n   */\n  const defaults = {\n    morphStyle: \"outerHTML\",\n    callbacks: {\n      beforeNodeAdded: noOp,\n      afterNodeAdded: noOp,\n      beforeNodeMorphed: noOp,\n      afterNodeMorphed: noOp,\n      beforeNodeRemoved: noOp,\n      afterNodeRemoved: noOp,\n      beforeAttributeUpdated: noOp,\n    },\n    head: {\n      style: \"merge\",\n      shouldPreserve: (elt) => elt.getAttribute(\"im-preserve\") === \"true\",\n      shouldReAppend: (elt) => elt.getAttribute(\"im-re-append\") === \"true\",\n      shouldRemove: noOp,\n      afterHeadMorphed: noOp,\n    },\n    restoreFocus: true,\n  };\n\n  /**\n   * Core idiomorph function for morphing one DOM tree to another\n   *\n   * @param {Element | Document} oldNode\n   * @param {Element | Node | HTMLCollection | Node[] | string | null} newContent\n   * @param {Config} [config]\n   * @returns {Promise<Node[]> | Node[]}\n   */\n  function morph(oldNode, newContent, config = {}) {\n    oldNode = normalizeElement(oldNode);\n    const newNode = normalizeParent(newContent);\n    const ctx = createMorphContext(oldNode, newNode, config);\n\n    const morphedNodes = saveAndRestoreFocus(ctx, () => {\n      return withHeadBlocking(\n        ctx,\n        oldNode,\n        newNode,\n        /** @param {MorphContext} ctx */ (ctx) => {\n          if (ctx.morphStyle === \"innerHTML\") {\n            morphChildren(ctx, oldNode, newNode);\n            return Array.from(oldNode.childNodes);\n          } else {\n            return morphOuterHTML(ctx, oldNode, newNode);\n          }\n        },\n      );\n    });\n\n    ctx.pantry.remove();\n    return morphedNodes;\n  }\n\n  /**\n   * Morph just the outerHTML of the oldNode to the newContent\n   * We have to be careful because the oldNode could have siblings which need to be untouched\n   * @param {MorphContext} ctx\n   * @param {Element} oldNode\n   * @param {Element} newNode\n   * @returns {Node[]}\n   */\n  function morphOuterHTML(ctx, oldNode, newNode) {\n    const oldParent = normalizeParent(oldNode);\n\n    // basis for calulating which nodes were morphed\n    // since there may be unmorphed sibling nodes\n    let childNodes = Array.from(oldParent.childNodes);\n    const index = childNodes.indexOf(oldNode);\n    // how many elements are to the right of the oldNode\n    const rightMargin = childNodes.length - (index + 1);\n\n    morphChildren(\n      ctx,\n      oldParent,\n      newNode,\n      // these two optional params are the secret sauce\n      oldNode, // start point for iteration\n      oldNode.nextSibling, // end point for iteration\n    );\n\n    // return just the morphed nodes\n    childNodes = Array.from(oldParent.childNodes);\n    return childNodes.slice(index, childNodes.length - rightMargin);\n  }\n\n  /**\n   * @param {MorphContext} ctx\n   * @param {Function} fn\n   * @returns {Promise<Node[]> | Node[]}\n   */\n  function saveAndRestoreFocus(ctx, fn) {\n    if (!ctx.config.restoreFocus) return fn();\n    let activeElement =\n      /** @type {HTMLInputElement|HTMLTextAreaElement|null} */ (\n        document.activeElement\n      );\n\n    // don't bother if the active element is not an input or textarea\n    if (\n      !(\n        activeElement instanceof HTMLInputElement ||\n        activeElement instanceof HTMLTextAreaElement\n      )\n    ) {\n      return fn();\n    }\n\n    const { id: activeElementId, selectionStart, selectionEnd } = activeElement;\n\n    const results = fn();\n\n    if (activeElementId && activeElementId !== document.activeElement?.id) {\n      activeElement = ctx.target.querySelector(`#${activeElementId}`);\n      activeElement?.focus();\n    }\n    if (activeElement && !activeElement.selectionEnd && selectionEnd) {\n      activeElement.setSelectionRange(selectionStart, selectionEnd);\n    }\n\n    return results;\n  }\n\n  const morphChildren = (function () {\n    /**\n     * This is the core algorithm for matching up children.  The idea is to use id sets to try to match up\n     * nodes as faithfully as possible.  We greedily match, which allows us to keep the algorithm fast, but\n     * by using id sets, we are able to better match up with content deeper in the DOM.\n     *\n     * Basic algorithm:\n     * - for each node in the new content:\n     *   - search self and siblings for an id set match, falling back to a soft match\n     *   - if match found\n     *     - remove any nodes up to the match:\n     *       - pantry persistent nodes\n     *       - delete the rest\n     *     - morph the match\n     *   - elsif no match found, and node is persistent\n     *     - find its match by querying the old root (future) and pantry (past)\n     *     - move it and its children here\n     *     - morph it\n     *   - else\n     *     - create a new node from scratch as a last result\n     *\n     * @param {MorphContext} ctx the merge context\n     * @param {Element} oldParent the old content that we are merging the new content into\n     * @param {Element} newParent the parent element of the new content\n     * @param {Node|null} [insertionPoint] the point in the DOM we start morphing at (defaults to first child)\n     * @param {Node|null} [endPoint] the point in the DOM we stop morphing at (defaults to after last child)\n     */\n    function morphChildren(\n      ctx,\n      oldParent,\n      newParent,\n      insertionPoint = null,\n      endPoint = null,\n    ) {\n      // normalize\n      if (\n        oldParent instanceof HTMLTemplateElement &&\n        newParent instanceof HTMLTemplateElement\n      ) {\n        // @ts-ignore we can pretend the DocumentFragment is an Element\n        oldParent = oldParent.content;\n        // @ts-ignore ditto\n        newParent = newParent.content;\n      }\n      insertionPoint ||= oldParent.firstChild;\n\n      // run through all the new content\n      for (const newChild of newParent.childNodes) {\n        // once we reach the end of the old parent content skip to the end and insert the rest\n        if (insertionPoint && insertionPoint != endPoint) {\n          const bestMatch = findBestMatch(\n            ctx,\n            newChild,\n            insertionPoint,\n            endPoint,\n          );\n          if (bestMatch) {\n            // if the node to morph is not at the insertion point then remove/move up to it\n            if (bestMatch !== insertionPoint) {\n              removeNodesBetween(ctx, insertionPoint, bestMatch);\n            }\n            morphNode(bestMatch, newChild, ctx);\n            insertionPoint = bestMatch.nextSibling;\n            continue;\n          }\n        }\n\n        // if the matching node is elsewhere in the original content\n        if (newChild instanceof Element && ctx.persistentIds.has(newChild.id)) {\n          // move it and all its children here and morph\n          const movedChild = moveBeforeById(\n            oldParent,\n            newChild.id,\n            insertionPoint,\n            ctx,\n          );\n          morphNode(movedChild, newChild, ctx);\n          insertionPoint = movedChild.nextSibling;\n          continue;\n        }\n\n        // last resort: insert the new node from scratch\n        const insertedNode = createNode(\n          oldParent,\n          newChild,\n          insertionPoint,\n          ctx,\n        );\n        // could be null if beforeNodeAdded prevented insertion\n        if (insertedNode) {\n          insertionPoint = insertedNode.nextSibling;\n        }\n      }\n\n      // remove any remaining old nodes that didn't match up with new content\n      while (insertionPoint && insertionPoint != endPoint) {\n        const tempNode = insertionPoint;\n        insertionPoint = insertionPoint.nextSibling;\n        removeNode(ctx, tempNode);\n      }\n    }\n\n    /**\n     * This performs the action of inserting a new node while handling situations where the node contains\n     * elements with persistent ids and possible state info we can still preserve by moving in and then morphing\n     *\n     * @param {Element} oldParent\n     * @param {Node} newChild\n     * @param {Node|null} insertionPoint\n     * @param {MorphContext} ctx\n     * @returns {Node|null}\n     */\n    function createNode(oldParent, newChild, insertionPoint, ctx) {\n      if (ctx.callbacks.beforeNodeAdded(newChild) === false) return null;\n      if (ctx.idMap.has(newChild)) {\n        // node has children with ids with possible state so create a dummy elt of same type and apply full morph algorithm\n        const newEmptyChild = document.createElement(\n          /** @type {Element} */ (newChild).tagName,\n        );\n        oldParent.insertBefore(newEmptyChild, insertionPoint);\n        morphNode(newEmptyChild, newChild, ctx);\n        ctx.callbacks.afterNodeAdded(newEmptyChild);\n        return newEmptyChild;\n      } else {\n        // optimisation: no id state to preserve so we can just insert a clone of the newChild and its descendants\n        const newClonedChild = document.importNode(newChild, true); // importNode to not mutate newParent\n        oldParent.insertBefore(newClonedChild, insertionPoint);\n        ctx.callbacks.afterNodeAdded(newClonedChild);\n        return newClonedChild;\n      }\n    }\n\n    //=============================================================================\n    // Matching Functions\n    //=============================================================================\n    const findBestMatch = (function () {\n      /**\n       * Scans forward from the startPoint to the endPoint looking for a match\n       * for the node. It looks for an id set match first, then a soft match.\n       * We abort softmatching if we find two future soft matches, to reduce churn.\n       * @param {Node} node\n       * @param {MorphContext} ctx\n       * @param {Node | null} startPoint\n       * @param {Node | null} endPoint\n       * @returns {Node | null}\n       */\n      function findBestMatch(ctx, node, startPoint, endPoint) {\n        let softMatch = null;\n        let nextSibling = node.nextSibling;\n        let siblingSoftMatchCount = 0;\n\n        let cursor = startPoint;\n        while (cursor && cursor != endPoint) {\n          // soft matching is a prerequisite for id set matching\n          if (isSoftMatch(cursor, node)) {\n            if (isIdSetMatch(ctx, cursor, node)) {\n              return cursor; // found an id set match, we're done!\n            }\n\n            // we haven't yet saved a soft match fallback\n            if (softMatch === null) {\n              // the current soft match will hard match something else in the future, leave it\n              if (!ctx.idMap.has(cursor)) {\n                // save this as the fallback if we get through the loop without finding a hard match\n                softMatch = cursor;\n              }\n            }\n          }\n          if (\n            softMatch === null &&\n            nextSibling &&\n            isSoftMatch(cursor, nextSibling)\n          ) {\n            // The next new node has a soft match with this node, so\n            // increment the count of future soft matches\n            siblingSoftMatchCount++;\n            nextSibling = nextSibling.nextSibling;\n\n            // If there are two future soft matches, block soft matching for this node to allow\n            // future siblings to soft match. This is to reduce churn in the DOM when an element\n            // is prepended.\n            if (siblingSoftMatchCount >= 2) {\n              softMatch = undefined;\n            }\n          }\n\n          // if the current node contains active element, stop looking for better future matches,\n          // because if one is found, this node will be moved to the pantry, reparenting it and thus losing focus\n          if (cursor.contains(document.activeElement)) break;\n\n          cursor = cursor.nextSibling;\n        }\n\n        return softMatch || null;\n      }\n\n      /**\n       *\n       * @param {MorphContext} ctx\n       * @param {Node} oldNode\n       * @param {Node} newNode\n       * @returns {boolean}\n       */\n      function isIdSetMatch(ctx, oldNode, newNode) {\n        let oldSet = ctx.idMap.get(oldNode);\n        let newSet = ctx.idMap.get(newNode);\n\n        if (!newSet || !oldSet) return false;\n\n        for (const id of oldSet) {\n          // a potential match is an id in the new and old nodes that\n          // has not already been merged into the DOM\n          // But the newNode content we call this on has not been\n          // merged yet and we don't allow duplicate IDs so it is simple\n          if (newSet.has(id)) {\n            return true;\n          }\n        }\n        return false;\n      }\n\n      /**\n       *\n       * @param {Node} oldNode\n       * @param {Node} newNode\n       * @returns {boolean}\n       */\n      function isSoftMatch(oldNode, newNode) {\n        // ok to cast: if one is not element, `id` and `tagName` will be undefined and we'll just compare that.\n        const oldElt = /** @type {Element} */ (oldNode);\n        const newElt = /** @type {Element} */ (newNode);\n\n        return (\n          oldElt.nodeType === newElt.nodeType &&\n          oldElt.tagName === newElt.tagName &&\n          // If oldElt has an `id` with possible state and it doesn't match newElt.id then avoid morphing.\n          // We'll still match an anonymous node with an IDed newElt, though, because if it got this far,\n          // its not persistent, and new nodes can't have any hidden state.\n          (!oldElt.id || oldElt.id === newElt.id)\n        );\n      }\n\n      return findBestMatch;\n    })();\n\n    //=============================================================================\n    // DOM Manipulation Functions\n    //=============================================================================\n\n    /**\n     * Gets rid of an unwanted DOM node; strategy depends on nature of its reuse:\n     * - Persistent nodes will be moved to the pantry for later reuse\n     * - Other nodes will have their hooks called, and then are removed\n     * @param {MorphContext} ctx\n     * @param {Node} node\n     */\n    function removeNode(ctx, node) {\n      // are we going to id set match this later?\n      if (ctx.idMap.has(node)) {\n        // skip callbacks and move to pantry\n        moveBefore(ctx.pantry, node, null);\n      } else {\n        // remove for realsies\n        if (ctx.callbacks.beforeNodeRemoved(node) === false) return;\n        node.parentNode?.removeChild(node);\n        ctx.callbacks.afterNodeRemoved(node);\n      }\n    }\n\n    /**\n     * Remove nodes between the start and end nodes\n     * @param {MorphContext} ctx\n     * @param {Node} startInclusive\n     * @param {Node} endExclusive\n     * @returns {Node|null}\n     */\n    function removeNodesBetween(ctx, startInclusive, endExclusive) {\n      /** @type {Node | null} */\n      let cursor = startInclusive;\n      // remove nodes until the endExclusive node\n      while (cursor && cursor !== endExclusive) {\n        let tempNode = /** @type {Node} */ (cursor);\n        cursor = cursor.nextSibling;\n        removeNode(ctx, tempNode);\n      }\n      return cursor;\n    }\n\n    /**\n     * Search for an element by id within the document and pantry, and move it using moveBefore.\n     *\n     * @param {Element} parentNode - The parent node to which the element will be moved.\n     * @param {string} id - The ID of the element to be moved.\n     * @param {Node | null} after - The reference node to insert the element before.\n     *                              If `null`, the element is appended as the last child.\n     * @param {MorphContext} ctx\n     * @returns {Element} The found element\n     */\n    function moveBeforeById(parentNode, id, after, ctx) {\n      const target =\n        /** @type {Element} - will always be found */\n        (\n          ctx.target.querySelector(`#${id}`) ||\n            ctx.pantry.querySelector(`#${id}`)\n        );\n      removeElementFromAncestorsIdMaps(target, ctx);\n      moveBefore(parentNode, target, after);\n      return target;\n    }\n\n    /**\n     * Removes an element from its ancestors' id maps. This is needed when an element is moved from the\n     * \"future\" via `moveBeforeId`. Otherwise, its erstwhile ancestors could be mistakenly moved to the\n     * pantry rather than being deleted, preventing their removal hooks from being called.\n     *\n     * @param {Element} element - element to remove from its ancestors' id maps\n     * @param {MorphContext} ctx\n     */\n    function removeElementFromAncestorsIdMaps(element, ctx) {\n      const id = element.id;\n      /** @ts-ignore - safe to loop in this way **/\n      while ((element = element.parentNode)) {\n        let idSet = ctx.idMap.get(element);\n        if (idSet) {\n          idSet.delete(id);\n          if (!idSet.size) {\n            ctx.idMap.delete(element);\n          }\n        }\n      }\n    }\n\n    /**\n     * Moves an element before another element within the same parent.\n     * Uses the proposed `moveBefore` API if available (and working), otherwise falls back to `insertBefore`.\n     * This is essentialy a forward-compat wrapper.\n     *\n     * @param {Element} parentNode - The parent node containing the after element.\n     * @param {Node} element - The element to be moved.\n     * @param {Node | null} after - The reference node to insert `element` before.\n     *                              If `null`, `element` is appended as the last child.\n     */\n    function moveBefore(parentNode, element, after) {\n      // @ts-ignore - use proposed moveBefore feature\n      if (parentNode.moveBefore) {\n        try {\n          // @ts-ignore - use proposed moveBefore feature\n          parentNode.moveBefore(element, after);\n        } catch (e) {\n          // fall back to insertBefore as some browsers may fail on moveBefore when trying to move Dom disconnected nodes to pantry\n          parentNode.insertBefore(element, after);\n        }\n      } else {\n        parentNode.insertBefore(element, after);\n      }\n    }\n\n    return morphChildren;\n  })();\n\n  //=============================================================================\n  // Single Node Morphing Code\n  //=============================================================================\n  const morphNode = (function () {\n    /**\n     * @param {Node} oldNode root node to merge content into\n     * @param {Node} newContent new content to merge\n     * @param {MorphContext} ctx the merge context\n     * @returns {Node | null} the element that ended up in the DOM\n     */\n    function morphNode(oldNode, newContent, ctx) {\n      if (ctx.ignoreActive && oldNode === document.activeElement) {\n        // don't morph focused element\n        return null;\n      }\n\n      if (ctx.callbacks.beforeNodeMorphed(oldNode, newContent) === false) {\n        return oldNode;\n      }\n\n      if (oldNode instanceof HTMLHeadElement && ctx.head.ignore) ; else if (\n        oldNode instanceof HTMLHeadElement &&\n        ctx.head.style !== \"morph\"\n      ) {\n        // ok to cast: if newContent wasn't also a <head>, it would've got caught in the `!isSoftMatch` branch above\n        handleHeadElement(\n          oldNode,\n          /** @type {HTMLHeadElement} */ (newContent),\n          ctx,\n        );\n      } else {\n        morphAttributes(oldNode, newContent, ctx);\n        if (!ignoreValueOfActiveElement(oldNode, ctx)) {\n          // @ts-ignore newContent can be a node here because .firstChild will be null\n          morphChildren(ctx, oldNode, newContent);\n        }\n      }\n      ctx.callbacks.afterNodeMorphed(oldNode, newContent);\n      return oldNode;\n    }\n\n    /**\n     * syncs the oldNode to the newNode, copying over all attributes and\n     * inner element state from the newNode to the oldNode\n     *\n     * @param {Node} oldNode the node to copy attributes & state to\n     * @param {Node} newNode the node to copy attributes & state from\n     * @param {MorphContext} ctx the merge context\n     */\n    function morphAttributes(oldNode, newNode, ctx) {\n      let type = newNode.nodeType;\n\n      // if is an element type, sync the attributes from the\n      // new node into the new node\n      if (type === 1 /* element type */) {\n        const oldElt = /** @type {Element} */ (oldNode);\n        const newElt = /** @type {Element} */ (newNode);\n\n        const oldAttributes = oldElt.attributes;\n        const newAttributes = newElt.attributes;\n        for (const newAttribute of newAttributes) {\n          if (ignoreAttribute(newAttribute.name, oldElt, \"update\", ctx)) {\n            continue;\n          }\n          if (oldElt.getAttribute(newAttribute.name) !== newAttribute.value) {\n            oldElt.setAttribute(newAttribute.name, newAttribute.value);\n          }\n        }\n        // iterate backwards to avoid skipping over items when a delete occurs\n        for (let i = oldAttributes.length - 1; 0 <= i; i--) {\n          const oldAttribute = oldAttributes[i];\n\n          // toAttributes is a live NamedNodeMap, so iteration+mutation is unsafe\n          // e.g. custom element attribute callbacks can remove other attributes\n          if (!oldAttribute) continue;\n\n          if (!newElt.hasAttribute(oldAttribute.name)) {\n            if (ignoreAttribute(oldAttribute.name, oldElt, \"remove\", ctx)) {\n              continue;\n            }\n            oldElt.removeAttribute(oldAttribute.name);\n          }\n        }\n\n        if (!ignoreValueOfActiveElement(oldElt, ctx)) {\n          syncInputValue(oldElt, newElt, ctx);\n        }\n      }\n\n      // sync text nodes\n      if (type === 8 /* comment */ || type === 3 /* text */) {\n        if (oldNode.nodeValue !== newNode.nodeValue) {\n          oldNode.nodeValue = newNode.nodeValue;\n        }\n      }\n    }\n\n    /**\n     * NB: many bothans died to bring us information:\n     *\n     *  https://github.com/patrick-steele-idem/morphdom/blob/master/src/specialElHandlers.js\n     *  https://github.com/choojs/nanomorph/blob/master/lib/morph.jsL113\n     *\n     * @param {Element} oldElement the element to sync the input value to\n     * @param {Element} newElement the element to sync the input value from\n     * @param {MorphContext} ctx the merge context\n     */\n    function syncInputValue(oldElement, newElement, ctx) {\n      if (\n        oldElement instanceof HTMLInputElement &&\n        newElement instanceof HTMLInputElement &&\n        newElement.type !== \"file\"\n      ) {\n        let newValue = newElement.value;\n        let oldValue = oldElement.value;\n\n        // sync boolean attributes\n        syncBooleanAttribute(oldElement, newElement, \"checked\", ctx);\n        syncBooleanAttribute(oldElement, newElement, \"disabled\", ctx);\n\n        if (!newElement.hasAttribute(\"value\")) {\n          if (!ignoreAttribute(\"value\", oldElement, \"remove\", ctx)) {\n            oldElement.value = \"\";\n            oldElement.removeAttribute(\"value\");\n          }\n        } else if (oldValue !== newValue) {\n          if (!ignoreAttribute(\"value\", oldElement, \"update\", ctx)) {\n            oldElement.setAttribute(\"value\", newValue);\n            oldElement.value = newValue;\n          }\n        }\n        // TODO: QUESTION(1cg): this used to only check `newElement` unlike the other branches -- why?\n        // did I break something?\n      } else if (\n        oldElement instanceof HTMLOptionElement &&\n        newElement instanceof HTMLOptionElement\n      ) {\n        syncBooleanAttribute(oldElement, newElement, \"selected\", ctx);\n      } else if (\n        oldElement instanceof HTMLTextAreaElement &&\n        newElement instanceof HTMLTextAreaElement\n      ) {\n        let newValue = newElement.value;\n        let oldValue = oldElement.value;\n        if (ignoreAttribute(\"value\", oldElement, \"update\", ctx)) {\n          return;\n        }\n        if (newValue !== oldValue) {\n          oldElement.value = newValue;\n        }\n        if (\n          oldElement.firstChild &&\n          oldElement.firstChild.nodeValue !== newValue\n        ) {\n          oldElement.firstChild.nodeValue = newValue;\n        }\n      }\n    }\n\n    /**\n     * @param {Element} oldElement element to write the value to\n     * @param {Element} newElement element to read the value from\n     * @param {string} attributeName the attribute name\n     * @param {MorphContext} ctx the merge context\n     */\n    function syncBooleanAttribute(oldElement, newElement, attributeName, ctx) {\n      // @ts-ignore this function is only used on boolean attrs that are reflected as dom properties\n      const newLiveValue = newElement[attributeName],\n        // @ts-ignore ditto\n        oldLiveValue = oldElement[attributeName];\n      if (newLiveValue !== oldLiveValue) {\n        const ignoreUpdate = ignoreAttribute(\n          attributeName,\n          oldElement,\n          \"update\",\n          ctx,\n        );\n        if (!ignoreUpdate) {\n          // update attribute's associated DOM property\n          // @ts-ignore this function is only used on boolean attrs that are reflected as dom properties\n          oldElement[attributeName] = newElement[attributeName];\n        }\n        if (newLiveValue) {\n          if (!ignoreUpdate) {\n            // https://developer.mozilla.org/en-US/docs/Glossary/Boolean/HTML\n            // this is the correct way to set a boolean attribute to \"true\"\n            oldElement.setAttribute(attributeName, \"\");\n          }\n        } else {\n          if (!ignoreAttribute(attributeName, oldElement, \"remove\", ctx)) {\n            oldElement.removeAttribute(attributeName);\n          }\n        }\n      }\n    }\n\n    /**\n     * @param {string} attr the attribute to be mutated\n     * @param {Element} element the element that is going to be updated\n     * @param {\"update\" | \"remove\"} updateType\n     * @param {MorphContext} ctx the merge context\n     * @returns {boolean} true if the attribute should be ignored, false otherwise\n     */\n    function ignoreAttribute(attr, element, updateType, ctx) {\n      if (\n        attr === \"value\" &&\n        ctx.ignoreActiveValue &&\n        element === document.activeElement\n      ) {\n        return true;\n      }\n      return (\n        ctx.callbacks.beforeAttributeUpdated(attr, element, updateType) ===\n        false\n      );\n    }\n\n    /**\n     * @param {Node} possibleActiveElement\n     * @param {MorphContext} ctx\n     * @returns {boolean}\n     */\n    function ignoreValueOfActiveElement(possibleActiveElement, ctx) {\n      return (\n        !!ctx.ignoreActiveValue &&\n        possibleActiveElement === document.activeElement &&\n        possibleActiveElement !== document.body\n      );\n    }\n\n    return morphNode;\n  })();\n\n  //=============================================================================\n  // Head Management Functions\n  //=============================================================================\n  /**\n   * @param {MorphContext} ctx\n   * @param {Element} oldNode\n   * @param {Element} newNode\n   * @param {function} callback\n   * @returns {Node[] | Promise<Node[]>}\n   */\n  function withHeadBlocking(ctx, oldNode, newNode, callback) {\n    if (ctx.head.block) {\n      const oldHead = oldNode.querySelector(\"head\");\n      const newHead = newNode.querySelector(\"head\");\n      if (oldHead && newHead) {\n        const promises = handleHeadElement(oldHead, newHead, ctx);\n        // when head promises resolve, proceed ignoring the head tag\n        return Promise.all(promises).then(() => {\n          const newCtx = Object.assign(ctx, {\n            head: {\n              block: false,\n              ignore: true,\n            },\n          });\n          return callback(newCtx);\n        });\n      }\n    }\n    // just proceed if we not head blocking\n    return callback(ctx);\n  }\n\n  /**\n   *  The HEAD tag can be handled specially, either w/ a 'merge' or 'append' style\n   *\n   * @param {Element} oldHead\n   * @param {Element} newHead\n   * @param {MorphContext} ctx\n   * @returns {Promise<void>[]}\n   */\n  function handleHeadElement(oldHead, newHead, ctx) {\n    let added = [];\n    let removed = [];\n    let preserved = [];\n    let nodesToAppend = [];\n\n    // put all new head elements into a Map, by their outerHTML\n    let srcToNewHeadNodes = new Map();\n    for (const newHeadChild of newHead.children) {\n      srcToNewHeadNodes.set(newHeadChild.outerHTML, newHeadChild);\n    }\n\n    // for each elt in the current head\n    for (const currentHeadElt of oldHead.children) {\n      // If the current head element is in the map\n      let inNewContent = srcToNewHeadNodes.has(currentHeadElt.outerHTML);\n      let isReAppended = ctx.head.shouldReAppend(currentHeadElt);\n      let isPreserved = ctx.head.shouldPreserve(currentHeadElt);\n      if (inNewContent || isPreserved) {\n        if (isReAppended) {\n          // remove the current version and let the new version replace it and re-execute\n          removed.push(currentHeadElt);\n        } else {\n          // this element already exists and should not be re-appended, so remove it from\n          // the new content map, preserving it in the DOM\n          srcToNewHeadNodes.delete(currentHeadElt.outerHTML);\n          preserved.push(currentHeadElt);\n        }\n      } else {\n        if (ctx.head.style === \"append\") {\n          // we are appending and this existing element is not new content\n          // so if and only if it is marked for re-append do we do anything\n          if (isReAppended) {\n            removed.push(currentHeadElt);\n            nodesToAppend.push(currentHeadElt);\n          }\n        } else {\n          // if this is a merge, we remove this content since it is not in the new head\n          if (ctx.head.shouldRemove(currentHeadElt) !== false) {\n            removed.push(currentHeadElt);\n          }\n        }\n      }\n    }\n\n    // Push the remaining new head elements in the Map into the\n    // nodes to append to the head tag\n    nodesToAppend.push(...srcToNewHeadNodes.values());\n\n    let promises = [];\n    for (const newNode of nodesToAppend) {\n      // TODO: This could theoretically be null, based on type\n      let newElt = /** @type {ChildNode} */ (\n        document.createRange().createContextualFragment(newNode.outerHTML)\n          .firstChild\n      );\n      if (ctx.callbacks.beforeNodeAdded(newElt) !== false) {\n        if (\n          (\"href\" in newElt && newElt.href) ||\n          (\"src\" in newElt && newElt.src)\n        ) {\n          /** @type {(result?: any) => void} */ let resolve;\n          let promise = new Promise(function (_resolve) {\n            resolve = _resolve;\n          });\n          newElt.addEventListener(\"load\", function () {\n            resolve();\n          });\n          promises.push(promise);\n        }\n        oldHead.appendChild(newElt);\n        ctx.callbacks.afterNodeAdded(newElt);\n        added.push(newElt);\n      }\n    }\n\n    // remove all removed elements, after we have appended the new elements to avoid\n    // additional network requests for things like style sheets\n    for (const removedElement of removed) {\n      if (ctx.callbacks.beforeNodeRemoved(removedElement) !== false) {\n        oldHead.removeChild(removedElement);\n        ctx.callbacks.afterNodeRemoved(removedElement);\n      }\n    }\n\n    ctx.head.afterHeadMorphed(oldHead, {\n      added: added,\n      kept: preserved,\n      removed: removed,\n    });\n    return promises;\n  }\n\n  //=============================================================================\n  // Create Morph Context Functions\n  //=============================================================================\n  const createMorphContext = (function () {\n    /**\n     *\n     * @param {Element} oldNode\n     * @param {Element} newContent\n     * @param {Config} config\n     * @returns {MorphContext}\n     */\n    function createMorphContext(oldNode, newContent, config) {\n      const { persistentIds, idMap } = createIdMaps(oldNode, newContent);\n\n      const mergedConfig = mergeDefaults(config);\n      const morphStyle = mergedConfig.morphStyle || \"outerHTML\";\n      if (![\"innerHTML\", \"outerHTML\"].includes(morphStyle)) {\n        throw `Do not understand how to morph style ${morphStyle}`;\n      }\n\n      return {\n        target: oldNode,\n        newContent: newContent,\n        config: mergedConfig,\n        morphStyle: morphStyle,\n        ignoreActive: mergedConfig.ignoreActive,\n        ignoreActiveValue: mergedConfig.ignoreActiveValue,\n        restoreFocus: mergedConfig.restoreFocus,\n        idMap: idMap,\n        persistentIds: persistentIds,\n        pantry: createPantry(),\n        callbacks: mergedConfig.callbacks,\n        head: mergedConfig.head,\n      };\n    }\n\n    /**\n     * Deep merges the config object and the Idiomorph.defaults object to\n     * produce a final configuration object\n     * @param {Config} config\n     * @returns {ConfigInternal}\n     */\n    function mergeDefaults(config) {\n      let finalConfig = Object.assign({}, defaults);\n\n      // copy top level stuff into final config\n      Object.assign(finalConfig, config);\n\n      // copy callbacks into final config (do this to deep merge the callbacks)\n      finalConfig.callbacks = Object.assign(\n        {},\n        defaults.callbacks,\n        config.callbacks,\n      );\n\n      // copy head config into final config  (do this to deep merge the head)\n      finalConfig.head = Object.assign({}, defaults.head, config.head);\n\n      return finalConfig;\n    }\n\n    /**\n     * @returns {HTMLDivElement}\n     */\n    function createPantry() {\n      const pantry = document.createElement(\"div\");\n      pantry.hidden = true;\n      document.body.insertAdjacentElement(\"afterend\", pantry);\n      return pantry;\n    }\n\n    /**\n     * Returns all elements with an ID contained within the root element and its descendants\n     *\n     * @param {Element} root\n     * @returns {Element[]}\n     */\n    function findIdElements(root) {\n      let elements = Array.from(root.querySelectorAll(\"[id]\"));\n      if (root.id) {\n        elements.push(root);\n      }\n      return elements;\n    }\n\n    /**\n     * A bottom-up algorithm that populates a map of Element -> IdSet.\n     * The idSet for a given element is the set of all IDs contained within its subtree.\n     * As an optimzation, we filter these IDs through the given list of persistent IDs,\n     * because we don't need to bother considering IDed elements that won't be in the new content.\n     *\n     * @param {Map<Node, Set<string>>} idMap\n     * @param {Set<string>} persistentIds\n     * @param {Element} root\n     * @param {Element[]} elements\n     */\n    function populateIdMapWithTree(idMap, persistentIds, root, elements) {\n      for (const elt of elements) {\n        if (persistentIds.has(elt.id)) {\n          /** @type {Element|null} */\n          let current = elt;\n          // walk up the parent hierarchy of that element, adding the id\n          // of element to the parent's id set\n          while (current) {\n            let idSet = idMap.get(current);\n            // if the id set doesn't exist, create it and insert it in the map\n            if (idSet == null) {\n              idSet = new Set();\n              idMap.set(current, idSet);\n            }\n            idSet.add(elt.id);\n\n            if (current === root) break;\n            current = current.parentElement;\n          }\n        }\n      }\n    }\n\n    /**\n     * This function computes a map of nodes to all ids contained within that node (inclusive of the\n     * node).  This map can be used to ask if two nodes have intersecting sets of ids, which allows\n     * for a looser definition of \"matching\" than tradition id matching, and allows child nodes\n     * to contribute to a parent nodes matching.\n     *\n     * @param {Element} oldContent  the old content that will be morphed\n     * @param {Element} newContent  the new content to morph to\n     * @returns {IdSets}\n     */\n    function createIdMaps(oldContent, newContent) {\n      const oldIdElements = findIdElements(oldContent);\n      const newIdElements = findIdElements(newContent);\n\n      const persistentIds = createPersistentIds(oldIdElements, newIdElements);\n\n      /** @type {Map<Node, Set<string>>} */\n      let idMap = new Map();\n      populateIdMapWithTree(idMap, persistentIds, oldContent, oldIdElements);\n\n      /** @ts-ignore - if newContent is a duck-typed parent, pass its single child node as the root to halt upwards iteration */\n      const newRoot = newContent.__idiomorphRoot || newContent;\n      populateIdMapWithTree(idMap, persistentIds, newRoot, newIdElements);\n\n      return { persistentIds, idMap };\n    }\n\n    /**\n     * This function computes the set of ids that persist between the two contents excluding duplicates\n     *\n     * @param {Element[]} oldIdElements\n     * @param {Element[]} newIdElements\n     * @returns {Set<string>}\n     */\n    function createPersistentIds(oldIdElements, newIdElements) {\n      let duplicateIds = new Set();\n\n      /** @type {Map<string, string>} */\n      let oldIdTagNameMap = new Map();\n      for (const { id, tagName } of oldIdElements) {\n        if (oldIdTagNameMap.has(id)) {\n          duplicateIds.add(id);\n        } else {\n          oldIdTagNameMap.set(id, tagName);\n        }\n      }\n\n      let persistentIds = new Set();\n      for (const { id, tagName } of newIdElements) {\n        if (persistentIds.has(id)) {\n          duplicateIds.add(id);\n        } else if (oldIdTagNameMap.get(id) === tagName) {\n          persistentIds.add(id);\n        }\n        // skip if tag types mismatch because its not possible to morph one tag into another\n      }\n\n      for (const id of duplicateIds) {\n        persistentIds.delete(id);\n      }\n      return persistentIds;\n    }\n\n    return createMorphContext;\n  })();\n\n  //=============================================================================\n  // HTML Normalization Functions\n  //=============================================================================\n  const { normalizeElement, normalizeParent } = (function () {\n    /** @type {WeakSet<Node>} */\n    const generatedByIdiomorph = new WeakSet();\n\n    /**\n     *\n     * @param {Element | Document} content\n     * @returns {Element}\n     */\n    function normalizeElement(content) {\n      if (content instanceof Document) {\n        return content.documentElement;\n      } else {\n        return content;\n      }\n    }\n\n    /**\n     *\n     * @param {null | string | Node | HTMLCollection | Node[] | Document & {generatedByIdiomorph:boolean}} newContent\n     * @returns {Element}\n     */\n    function normalizeParent(newContent) {\n      if (newContent == null) {\n        return document.createElement(\"div\"); // dummy parent element\n      } else if (typeof newContent === \"string\") {\n        return normalizeParent(parseContent(newContent));\n      } else if (\n        generatedByIdiomorph.has(/** @type {Element} */ (newContent))\n      ) {\n        // the template tag created by idiomorph parsing can serve as a dummy parent\n        return /** @type {Element} */ (newContent);\n      } else if (newContent instanceof Node) {\n        if (newContent.parentNode) {\n          // we can't use the parent directly because newContent may have siblings\n          // that we don't want in the morph, and reparenting might be expensive (TODO is it?),\n          // so we create a duck-typed parent node instead.\n          return createDuckTypedParent(newContent);\n        } else {\n          // a single node is added as a child to a dummy parent\n          const dummyParent = document.createElement(\"div\");\n          dummyParent.append(newContent);\n          return dummyParent;\n        }\n      } else {\n        // all nodes in the array or HTMLElement collection are consolidated under\n        // a single dummy parent element\n        const dummyParent = document.createElement(\"div\");\n        for (const elt of [...newContent]) {\n          dummyParent.append(elt);\n        }\n        return dummyParent;\n      }\n    }\n\n    /**\n     * Creates a fake duck-typed parent element to wrap a single node, without actually reparenting it.\n     * \"If it walks like a duck, and quacks like a duck, then it must be a duck!\" -- James Whitcomb Riley (1849–1916)\n     *\n     * @param {Node} newContent\n     * @returns {Element}\n     */\n    function createDuckTypedParent(newContent) {\n      return /** @type {Element} */ (\n        /** @type {unknown} */ ({\n          childNodes: [newContent],\n          /** @ts-ignore - cover your eyes for a minute, tsc */\n          querySelectorAll: (s) => {\n            /** @ts-ignore */\n            const elements = newContent.querySelectorAll(s);\n            /** @ts-ignore */\n            return newContent.matches(s) ? [newContent, ...elements] : elements;\n          },\n          /** @ts-ignore */\n          insertBefore: (n, r) => newContent.parentNode.insertBefore(n, r),\n          /** @ts-ignore */\n          moveBefore: (n, r) => newContent.parentNode.moveBefore(n, r),\n          // for later use with populateIdMapWithTree to halt upwards iteration\n          get __idiomorphRoot() {\n            return newContent;\n          },\n        })\n      );\n    }\n\n    /**\n     *\n     * @param {string} newContent\n     * @returns {Node | null | DocumentFragment}\n     */\n    function parseContent(newContent) {\n      let parser = new DOMParser();\n\n      // remove svgs to avoid false-positive matches on head, etc.\n      let contentWithSvgsRemoved = newContent.replace(\n        /<svg(\\s[^>]*>|>)([\\s\\S]*?)<\\/svg>/gim,\n        \"\",\n      );\n\n      // if the newContent contains a html, head or body tag, we can simply parse it w/o wrapping\n      if (\n        contentWithSvgsRemoved.match(/<\\/html>/) ||\n        contentWithSvgsRemoved.match(/<\\/head>/) ||\n        contentWithSvgsRemoved.match(/<\\/body>/)\n      ) {\n        let content = parser.parseFromString(newContent, \"text/html\");\n        // if it is a full HTML document, return the document itself as the parent container\n        if (contentWithSvgsRemoved.match(/<\\/html>/)) {\n          generatedByIdiomorph.add(content);\n          return content;\n        } else {\n          // otherwise return the html element as the parent container\n          let htmlElement = content.firstChild;\n          if (htmlElement) {\n            generatedByIdiomorph.add(htmlElement);\n          }\n          return htmlElement;\n        }\n      } else {\n        // if it is partial HTML, wrap it in a template tag to provide a parent element and also to help\n        // deal with touchy tags like tr, tbody, etc.\n        let responseDoc = parser.parseFromString(\n          \"<body><template>\" + newContent + \"</template></body>\",\n          \"text/html\",\n        );\n        let content = /** @type {HTMLTemplateElement} */ (\n          responseDoc.body.querySelector(\"template\")\n        ).content;\n        generatedByIdiomorph.add(content);\n        return content;\n      }\n    }\n\n    return { normalizeElement, normalizeParent };\n  })();\n\n  //=============================================================================\n  // This is what ends up becoming the Idiomorph global object\n  //=============================================================================\n  return {\n    morph,\n    defaults,\n  };\n})();\n\nfunction morphElements(currentElement, newElement, { callbacks, ...options } = {}) {\n  Idiomorph.morph(currentElement, newElement, {\n    ...options,\n    callbacks: new DefaultIdiomorphCallbacks(callbacks)\n  });\n}\n\nfunction morphChildren(currentElement, newElement) {\n  morphElements(currentElement, newElement.childNodes, {\n    morphStyle: \"innerHTML\"\n  });\n}\n\nclass DefaultIdiomorphCallbacks {\n  #beforeNodeMorphed\n\n  constructor({ beforeNodeMorphed } = {}) {\n    this.#beforeNodeMorphed = beforeNodeMorphed || (() => true);\n  }\n\n  beforeNodeAdded = (node) => {\n    return !(node.id && node.hasAttribute(\"data-turbo-permanent\") && document.getElementById(node.id))\n  }\n\n  beforeNodeMorphed = (currentElement, newElement) => {\n    if (currentElement instanceof Element) {\n      if (!currentElement.hasAttribute(\"data-turbo-permanent\") && this.#beforeNodeMorphed(currentElement, newElement)) {\n        const event = dispatch(\"turbo:before-morph-element\", {\n          cancelable: true,\n          target: currentElement,\n          detail: { currentElement, newElement }\n        });\n\n        return !event.defaultPrevented\n      } else {\n        return false\n      }\n    }\n  }\n\n  beforeAttributeUpdated = (attributeName, target, mutationType) => {\n    const event = dispatch(\"turbo:before-morph-attribute\", {\n      cancelable: true,\n      target,\n      detail: { attributeName, mutationType }\n    });\n\n    return !event.defaultPrevented\n  }\n\n  beforeNodeRemoved = (node) => {\n    return this.beforeNodeMorphed(node)\n  }\n\n  afterNodeMorphed = (currentElement, newElement) => {\n    if (currentElement instanceof Element) {\n      dispatch(\"turbo:morph-element\", {\n        target: currentElement,\n        detail: { currentElement, newElement }\n      });\n    }\n  }\n}\n\nclass MorphingFrameRenderer extends FrameRenderer {\n  static renderElement(currentElement, newElement) {\n    dispatch(\"turbo:before-frame-morph\", {\n      target: currentElement,\n      detail: { currentElement, newElement }\n    });\n\n    morphChildren(currentElement, newElement);\n  }\n\n  async preservingPermanentElements(callback) {\n    return await callback()\n  }\n}\n\nclass ProgressBar {\n  static animationDuration = 300 /*ms*/\n\n  static get defaultCSS() {\n    return unindent`\n      .turbo-progress-bar {\n        position: fixed;\n        display: block;\n        top: 0;\n        left: 0;\n        height: 3px;\n        background: #0076ff;\n        z-index: 2147483647;\n        transition:\n          width ${ProgressBar.animationDuration}ms ease-out,\n          opacity ${ProgressBar.animationDuration / 2}ms ${ProgressBar.animationDuration / 2}ms ease-in;\n        transform: translate3d(0, 0, 0);\n      }\n    `\n  }\n\n  hiding = false\n  value = 0\n  visible = false\n\n  constructor() {\n    this.stylesheetElement = this.createStylesheetElement();\n    this.progressElement = this.createProgressElement();\n    this.installStylesheetElement();\n    this.setValue(0);\n  }\n\n  show() {\n    if (!this.visible) {\n      this.visible = true;\n      this.installProgressElement();\n      this.startTrickling();\n    }\n  }\n\n  hide() {\n    if (this.visible && !this.hiding) {\n      this.hiding = true;\n      this.fadeProgressElement(() => {\n        this.uninstallProgressElement();\n        this.stopTrickling();\n        this.visible = false;\n        this.hiding = false;\n      });\n    }\n  }\n\n  setValue(value) {\n    this.value = value;\n    this.refresh();\n  }\n\n  // Private\n\n  installStylesheetElement() {\n    document.head.insertBefore(this.stylesheetElement, document.head.firstChild);\n  }\n\n  installProgressElement() {\n    this.progressElement.style.width = \"0\";\n    this.progressElement.style.opacity = \"1\";\n    document.documentElement.insertBefore(this.progressElement, document.body);\n    this.refresh();\n  }\n\n  fadeProgressElement(callback) {\n    this.progressElement.style.opacity = \"0\";\n    setTimeout(callback, ProgressBar.animationDuration * 1.5);\n  }\n\n  uninstallProgressElement() {\n    if (this.progressElement.parentNode) {\n      document.documentElement.removeChild(this.progressElement);\n    }\n  }\n\n  startTrickling() {\n    if (!this.trickleInterval) {\n      this.trickleInterval = window.setInterval(this.trickle, ProgressBar.animationDuration);\n    }\n  }\n\n  stopTrickling() {\n    window.clearInterval(this.trickleInterval);\n    delete this.trickleInterval;\n  }\n\n  trickle = () => {\n    this.setValue(this.value + Math.random() / 100);\n  }\n\n  refresh() {\n    requestAnimationFrame(() => {\n      this.progressElement.style.width = `${10 + this.value * 90}%`;\n    });\n  }\n\n  createStylesheetElement() {\n    const element = document.createElement(\"style\");\n    element.type = \"text/css\";\n    element.textContent = ProgressBar.defaultCSS;\n    const cspNonce = getCspNonce();\n    if (cspNonce) {\n      element.nonce = cspNonce;\n    }\n    return element\n  }\n\n  createProgressElement() {\n    const element = document.createElement(\"div\");\n    element.className = \"turbo-progress-bar\";\n    return element\n  }\n}\n\nclass HeadSnapshot extends Snapshot {\n  detailsByOuterHTML = this.children\n    .filter((element) => !elementIsNoscript(element))\n    .map((element) => elementWithoutNonce(element))\n    .reduce((result, element) => {\n      const { outerHTML } = element;\n      const details =\n        outerHTML in result\n          ? result[outerHTML]\n          : {\n              type: elementType(element),\n              tracked: elementIsTracked(element),\n              elements: []\n            };\n      return {\n        ...result,\n        [outerHTML]: {\n          ...details,\n          elements: [...details.elements, element]\n        }\n      }\n    }, {})\n\n  get trackedElementSignature() {\n    return Object.keys(this.detailsByOuterHTML)\n      .filter((outerHTML) => this.detailsByOuterHTML[outerHTML].tracked)\n      .join(\"\")\n  }\n\n  getScriptElementsNotInSnapshot(snapshot) {\n    return this.getElementsMatchingTypeNotInSnapshot(\"script\", snapshot)\n  }\n\n  getStylesheetElementsNotInSnapshot(snapshot) {\n    return this.getElementsMatchingTypeNotInSnapshot(\"stylesheet\", snapshot)\n  }\n\n  getElementsMatchingTypeNotInSnapshot(matchedType, snapshot) {\n    return Object.keys(this.detailsByOuterHTML)\n      .filter((outerHTML) => !(outerHTML in snapshot.detailsByOuterHTML))\n      .map((outerHTML) => this.detailsByOuterHTML[outerHTML])\n      .filter(({ type }) => type == matchedType)\n      .map(({ elements: [element] }) => element)\n  }\n\n  get provisionalElements() {\n    return Object.keys(this.detailsByOuterHTML).reduce((result, outerHTML) => {\n      const { type, tracked, elements } = this.detailsByOuterHTML[outerHTML];\n      if (type == null && !tracked) {\n        return [...result, ...elements]\n      } else if (elements.length > 1) {\n        return [...result, ...elements.slice(1)]\n      } else {\n        return result\n      }\n    }, [])\n  }\n\n  getMetaValue(name) {\n    const element = this.findMetaElementByName(name);\n    return element ? element.getAttribute(\"content\") : null\n  }\n\n  findMetaElementByName(name) {\n    return Object.keys(this.detailsByOuterHTML).reduce((result, outerHTML) => {\n      const {\n        elements: [element]\n      } = this.detailsByOuterHTML[outerHTML];\n      return elementIsMetaElementWithName(element, name) ? element : result\n    }, undefined | undefined)\n  }\n}\n\nfunction elementType(element) {\n  if (elementIsScript(element)) {\n    return \"script\"\n  } else if (elementIsStylesheet(element)) {\n    return \"stylesheet\"\n  }\n}\n\nfunction elementIsTracked(element) {\n  return element.getAttribute(\"data-turbo-track\") == \"reload\"\n}\n\nfunction elementIsScript(element) {\n  const tagName = element.localName;\n  return tagName == \"script\"\n}\n\nfunction elementIsNoscript(element) {\n  const tagName = element.localName;\n  return tagName == \"noscript\"\n}\n\nfunction elementIsStylesheet(element) {\n  const tagName = element.localName;\n  return tagName == \"style\" || (tagName == \"link\" && element.getAttribute(\"rel\") == \"stylesheet\")\n}\n\nfunction elementIsMetaElementWithName(element, name) {\n  const tagName = element.localName;\n  return tagName == \"meta\" && element.getAttribute(\"name\") == name\n}\n\nfunction elementWithoutNonce(element) {\n  if (element.hasAttribute(\"nonce\")) {\n    element.setAttribute(\"nonce\", \"\");\n  }\n\n  return element\n}\n\nclass PageSnapshot extends Snapshot {\n  static fromHTMLString(html = \"\") {\n    return this.fromDocument(parseHTMLDocument(html))\n  }\n\n  static fromElement(element) {\n    return this.fromDocument(element.ownerDocument)\n  }\n\n  static fromDocument({ documentElement, body, head }) {\n    return new this(documentElement, body, new HeadSnapshot(head))\n  }\n\n  constructor(documentElement, body, headSnapshot) {\n    super(body);\n    this.documentElement = documentElement;\n    this.headSnapshot = headSnapshot;\n  }\n\n  clone() {\n    const clonedElement = this.element.cloneNode(true);\n\n    const selectElements = this.element.querySelectorAll(\"select\");\n    const clonedSelectElements = clonedElement.querySelectorAll(\"select\");\n\n    for (const [index, source] of selectElements.entries()) {\n      const clone = clonedSelectElements[index];\n      for (const option of clone.selectedOptions) option.selected = false;\n      for (const option of source.selectedOptions) clone.options[option.index].selected = true;\n    }\n\n    for (const clonedPasswordInput of clonedElement.querySelectorAll('input[type=\"password\"]')) {\n      clonedPasswordInput.value = \"\";\n    }\n\n    return new PageSnapshot(this.documentElement, clonedElement, this.headSnapshot)\n  }\n\n  get lang() {\n    return this.documentElement.getAttribute(\"lang\")\n  }\n\n  get headElement() {\n    return this.headSnapshot.element\n  }\n\n  get rootLocation() {\n    const root = this.getSetting(\"root\") ?? \"/\";\n    return expandURL(root)\n  }\n\n  get cacheControlValue() {\n    return this.getSetting(\"cache-control\")\n  }\n\n  get isPreviewable() {\n    return this.cacheControlValue != \"no-preview\"\n  }\n\n  get isCacheable() {\n    return this.cacheControlValue != \"no-cache\"\n  }\n\n  get isVisitable() {\n    return this.getSetting(\"visit-control\") != \"reload\"\n  }\n\n  get prefersViewTransitions() {\n    return this.headSnapshot.getMetaValue(\"view-transition\") === \"same-origin\"\n  }\n\n  get shouldMorphPage() {\n    return this.getSetting(\"refresh-method\") === \"morph\"\n  }\n\n  get shouldPreserveScrollPosition() {\n    return this.getSetting(\"refresh-scroll\") === \"preserve\"\n  }\n\n  // Private\n\n  getSetting(name) {\n    return this.headSnapshot.getMetaValue(`turbo-${name}`)\n  }\n}\n\nclass ViewTransitioner {\n  #viewTransitionStarted = false\n  #lastOperation = Promise.resolve()\n\n  renderChange(useViewTransition, render) {\n    if (useViewTransition && this.viewTransitionsAvailable && !this.#viewTransitionStarted) {\n      this.#viewTransitionStarted = true;\n      this.#lastOperation = this.#lastOperation.then(async () => {\n        await document.startViewTransition(render).finished;\n      });\n    } else {\n      this.#lastOperation = this.#lastOperation.then(render);\n    }\n\n    return this.#lastOperation\n  }\n\n  get viewTransitionsAvailable() {\n    return document.startViewTransition\n  }\n}\n\nconst defaultOptions = {\n  action: \"advance\",\n  historyChanged: false,\n  visitCachedSnapshot: () => {},\n  willRender: true,\n  updateHistory: true,\n  shouldCacheSnapshot: true,\n  acceptsStreamResponse: false\n};\n\nconst TimingMetric = {\n  visitStart: \"visitStart\",\n  requestStart: \"requestStart\",\n  requestEnd: \"requestEnd\",\n  visitEnd: \"visitEnd\"\n};\n\nconst VisitState = {\n  initialized: \"initialized\",\n  started: \"started\",\n  canceled: \"canceled\",\n  failed: \"failed\",\n  completed: \"completed\"\n};\n\nconst SystemStatusCode = {\n  networkFailure: 0,\n  timeoutFailure: -1,\n  contentTypeMismatch: -2\n};\n\nconst Direction = {\n  advance: \"forward\",\n  restore: \"back\",\n  replace: \"none\"\n};\n\nclass Visit {\n  identifier = uuid() // Required by turbo-ios\n  timingMetrics = {}\n\n  followedRedirect = false\n  historyChanged = false\n  scrolled = false\n  shouldCacheSnapshot = true\n  acceptsStreamResponse = false\n  snapshotCached = false\n  state = VisitState.initialized\n  viewTransitioner = new ViewTransitioner()\n\n  constructor(delegate, location, restorationIdentifier, options = {}) {\n    this.delegate = delegate;\n    this.location = location;\n    this.restorationIdentifier = restorationIdentifier || uuid();\n\n    const {\n      action,\n      historyChanged,\n      referrer,\n      snapshot,\n      snapshotHTML,\n      response,\n      visitCachedSnapshot,\n      willRender,\n      updateHistory,\n      shouldCacheSnapshot,\n      acceptsStreamResponse,\n      direction\n    } = {\n      ...defaultOptions,\n      ...options\n    };\n    this.action = action;\n    this.historyChanged = historyChanged;\n    this.referrer = referrer;\n    this.snapshot = snapshot;\n    this.snapshotHTML = snapshotHTML;\n    this.response = response;\n    this.isSamePage = this.delegate.locationWithActionIsSamePage(this.location, this.action);\n    this.isPageRefresh = this.view.isPageRefresh(this);\n    this.visitCachedSnapshot = visitCachedSnapshot;\n    this.willRender = willRender;\n    this.updateHistory = updateHistory;\n    this.scrolled = !willRender;\n    this.shouldCacheSnapshot = shouldCacheSnapshot;\n    this.acceptsStreamResponse = acceptsStreamResponse;\n    this.direction = direction || Direction[action];\n  }\n\n  get adapter() {\n    return this.delegate.adapter\n  }\n\n  get view() {\n    return this.delegate.view\n  }\n\n  get history() {\n    return this.delegate.history\n  }\n\n  get restorationData() {\n    return this.history.getRestorationDataForIdentifier(this.restorationIdentifier)\n  }\n\n  get silent() {\n    return this.isSamePage\n  }\n\n  start() {\n    if (this.state == VisitState.initialized) {\n      this.recordTimingMetric(TimingMetric.visitStart);\n      this.state = VisitState.started;\n      this.adapter.visitStarted(this);\n      this.delegate.visitStarted(this);\n    }\n  }\n\n  cancel() {\n    if (this.state == VisitState.started) {\n      if (this.request) {\n        this.request.cancel();\n      }\n      this.cancelRender();\n      this.state = VisitState.canceled;\n    }\n  }\n\n  complete() {\n    if (this.state == VisitState.started) {\n      this.recordTimingMetric(TimingMetric.visitEnd);\n      this.adapter.visitCompleted(this);\n      this.state = VisitState.completed;\n      this.followRedirect();\n\n      if (!this.followedRedirect) {\n        this.delegate.visitCompleted(this);\n      }\n    }\n  }\n\n  fail() {\n    if (this.state == VisitState.started) {\n      this.state = VisitState.failed;\n      this.adapter.visitFailed(this);\n      this.delegate.visitCompleted(this);\n    }\n  }\n\n  changeHistory() {\n    if (!this.historyChanged && this.updateHistory) {\n      const actionForHistory = this.location.href === this.referrer?.href ? \"replace\" : this.action;\n      const method = getHistoryMethodForAction(actionForHistory);\n      this.history.update(method, this.location, this.restorationIdentifier);\n      this.historyChanged = true;\n    }\n  }\n\n  issueRequest() {\n    if (this.hasPreloadedResponse()) {\n      this.simulateRequest();\n    } else if (this.shouldIssueRequest() && !this.request) {\n      this.request = new FetchRequest(this, FetchMethod.get, this.location);\n      this.request.perform();\n    }\n  }\n\n  simulateRequest() {\n    if (this.response) {\n      this.startRequest();\n      this.recordResponse();\n      this.finishRequest();\n    }\n  }\n\n  startRequest() {\n    this.recordTimingMetric(TimingMetric.requestStart);\n    this.adapter.visitRequestStarted(this);\n  }\n\n  recordResponse(response = this.response) {\n    this.response = response;\n    if (response) {\n      const { statusCode } = response;\n      if (isSuccessful(statusCode)) {\n        this.adapter.visitRequestCompleted(this);\n      } else {\n        this.adapter.visitRequestFailedWithStatusCode(this, statusCode);\n      }\n    }\n  }\n\n  finishRequest() {\n    this.recordTimingMetric(TimingMetric.requestEnd);\n    this.adapter.visitRequestFinished(this);\n  }\n\n  loadResponse() {\n    if (this.response) {\n      const { statusCode, responseHTML } = this.response;\n      this.render(async () => {\n        if (this.shouldCacheSnapshot) this.cacheSnapshot();\n        if (this.view.renderPromise) await this.view.renderPromise;\n\n        if (isSuccessful(statusCode) && responseHTML != null) {\n          const snapshot = PageSnapshot.fromHTMLString(responseHTML);\n          await this.renderPageSnapshot(snapshot, false);\n\n          this.adapter.visitRendered(this);\n          this.complete();\n        } else {\n          await this.view.renderError(PageSnapshot.fromHTMLString(responseHTML), this);\n          this.adapter.visitRendered(this);\n          this.fail();\n        }\n      });\n    }\n  }\n\n  getCachedSnapshot() {\n    const snapshot = this.view.getCachedSnapshotForLocation(this.location) || this.getPreloadedSnapshot();\n\n    if (snapshot && (!getAnchor(this.location) || snapshot.hasAnchor(getAnchor(this.location)))) {\n      if (this.action == \"restore\" || snapshot.isPreviewable) {\n        return snapshot\n      }\n    }\n  }\n\n  getPreloadedSnapshot() {\n    if (this.snapshotHTML) {\n      return PageSnapshot.fromHTMLString(this.snapshotHTML)\n    }\n  }\n\n  hasCachedSnapshot() {\n    return this.getCachedSnapshot() != null\n  }\n\n  loadCachedSnapshot() {\n    const snapshot = this.getCachedSnapshot();\n    if (snapshot) {\n      const isPreview = this.shouldIssueRequest();\n      this.render(async () => {\n        this.cacheSnapshot();\n        if (this.isSamePage || this.isPageRefresh) {\n          this.adapter.visitRendered(this);\n        } else {\n          if (this.view.renderPromise) await this.view.renderPromise;\n\n          await this.renderPageSnapshot(snapshot, isPreview);\n\n          this.adapter.visitRendered(this);\n          if (!isPreview) {\n            this.complete();\n          }\n        }\n      });\n    }\n  }\n\n  followRedirect() {\n    if (this.redirectedToLocation && !this.followedRedirect && this.response?.redirected) {\n      this.adapter.visitProposedToLocation(this.redirectedToLocation, {\n        action: \"replace\",\n        response: this.response,\n        shouldCacheSnapshot: false,\n        willRender: false\n      });\n      this.followedRedirect = true;\n    }\n  }\n\n  goToSamePageAnchor() {\n    if (this.isSamePage) {\n      this.render(async () => {\n        this.cacheSnapshot();\n        this.performScroll();\n        this.changeHistory();\n        this.adapter.visitRendered(this);\n      });\n    }\n  }\n\n  // Fetch request delegate\n\n  prepareRequest(request) {\n    if (this.acceptsStreamResponse) {\n      request.acceptResponseType(StreamMessage.contentType);\n    }\n  }\n\n  requestStarted() {\n    this.startRequest();\n  }\n\n  requestPreventedHandlingResponse(_request, _response) {}\n\n  async requestSucceededWithResponse(request, response) {\n    const responseHTML = await response.responseHTML;\n    const { redirected, statusCode } = response;\n    if (responseHTML == undefined) {\n      this.recordResponse({\n        statusCode: SystemStatusCode.contentTypeMismatch,\n        redirected\n      });\n    } else {\n      this.redirectedToLocation = response.redirected ? response.location : undefined;\n      this.recordResponse({ statusCode: statusCode, responseHTML, redirected });\n    }\n  }\n\n  async requestFailedWithResponse(request, response) {\n    const responseHTML = await response.responseHTML;\n    const { redirected, statusCode } = response;\n    if (responseHTML == undefined) {\n      this.recordResponse({\n        statusCode: SystemStatusCode.contentTypeMismatch,\n        redirected\n      });\n    } else {\n      this.recordResponse({ statusCode: statusCode, responseHTML, redirected });\n    }\n  }\n\n  requestErrored(_request, _error) {\n    this.recordResponse({\n      statusCode: SystemStatusCode.networkFailure,\n      redirected: false\n    });\n  }\n\n  requestFinished() {\n    this.finishRequest();\n  }\n\n  // Scrolling\n\n  performScroll() {\n    if (!this.scrolled && !this.view.forceReloaded && !this.view.shouldPreserveScrollPosition(this)) {\n      if (this.action == \"restore\") {\n        this.scrollToRestoredPosition() || this.scrollToAnchor() || this.view.scrollToTop();\n      } else {\n        this.scrollToAnchor() || this.view.scrollToTop();\n      }\n      if (this.isSamePage) {\n        this.delegate.visitScrolledToSamePageLocation(this.view.lastRenderedLocation, this.location);\n      }\n\n      this.scrolled = true;\n    }\n  }\n\n  scrollToRestoredPosition() {\n    const { scrollPosition } = this.restorationData;\n    if (scrollPosition) {\n      this.view.scrollToPosition(scrollPosition);\n      return true\n    }\n  }\n\n  scrollToAnchor() {\n    const anchor = getAnchor(this.location);\n    if (anchor != null) {\n      this.view.scrollToAnchor(anchor);\n      return true\n    }\n  }\n\n  // Instrumentation\n\n  recordTimingMetric(metric) {\n    this.timingMetrics[metric] = new Date().getTime();\n  }\n\n  getTimingMetrics() {\n    return { ...this.timingMetrics }\n  }\n\n  // Private\n\n  hasPreloadedResponse() {\n    return typeof this.response == \"object\"\n  }\n\n  shouldIssueRequest() {\n    if (this.isSamePage) {\n      return false\n    } else if (this.action == \"restore\") {\n      return !this.hasCachedSnapshot()\n    } else {\n      return this.willRender\n    }\n  }\n\n  cacheSnapshot() {\n    if (!this.snapshotCached) {\n      this.view.cacheSnapshot(this.snapshot).then((snapshot) => snapshot && this.visitCachedSnapshot(snapshot));\n      this.snapshotCached = true;\n    }\n  }\n\n  async render(callback) {\n    this.cancelRender();\n    await new Promise((resolve) => {\n      this.frame =\n        document.visibilityState === \"hidden\" ? setTimeout(() => resolve(), 0) : requestAnimationFrame(() => resolve());\n    });\n    await callback();\n    delete this.frame;\n  }\n\n  async renderPageSnapshot(snapshot, isPreview) {\n    await this.viewTransitioner.renderChange(this.view.shouldTransitionTo(snapshot), async () => {\n      await this.view.renderPage(snapshot, isPreview, this.willRender, this);\n      this.performScroll();\n    });\n  }\n\n  cancelRender() {\n    if (this.frame) {\n      cancelAnimationFrame(this.frame);\n      delete this.frame;\n    }\n  }\n}\n\nfunction isSuccessful(statusCode) {\n  return statusCode >= 200 && statusCode < 300\n}\n\nclass BrowserAdapter {\n  progressBar = new ProgressBar()\n\n  constructor(session) {\n    this.session = session;\n  }\n\n  visitProposedToLocation(location, options) {\n    if (locationIsVisitable(location, this.navigator.rootLocation)) {\n      this.navigator.startVisit(location, options?.restorationIdentifier || uuid(), options);\n    } else {\n      window.location.href = location.toString();\n    }\n  }\n\n  visitStarted(visit) {\n    this.location = visit.location;\n    visit.loadCachedSnapshot();\n    visit.issueRequest();\n    visit.goToSamePageAnchor();\n  }\n\n  visitRequestStarted(visit) {\n    this.progressBar.setValue(0);\n    if (visit.hasCachedSnapshot() || visit.action != \"restore\") {\n      this.showVisitProgressBarAfterDelay();\n    } else {\n      this.showProgressBar();\n    }\n  }\n\n  visitRequestCompleted(visit) {\n    visit.loadResponse();\n  }\n\n  visitRequestFailedWithStatusCode(visit, statusCode) {\n    switch (statusCode) {\n      case SystemStatusCode.networkFailure:\n      case SystemStatusCode.timeoutFailure:\n      case SystemStatusCode.contentTypeMismatch:\n        return this.reload({\n          reason: \"request_failed\",\n          context: {\n            statusCode\n          }\n        })\n      default:\n        return visit.loadResponse()\n    }\n  }\n\n  visitRequestFinished(_visit) {}\n\n  visitCompleted(_visit) {\n    this.progressBar.setValue(1);\n    this.hideVisitProgressBar();\n  }\n\n  pageInvalidated(reason) {\n    this.reload(reason);\n  }\n\n  visitFailed(_visit) {\n    this.progressBar.setValue(1);\n    this.hideVisitProgressBar();\n  }\n\n  visitRendered(_visit) {}\n\n  // Link prefetching\n\n  linkPrefetchingIsEnabledForLocation(location) {\n    return true\n  }\n\n  // Form Submission Delegate\n\n  formSubmissionStarted(_formSubmission) {\n    this.progressBar.setValue(0);\n    this.showFormProgressBarAfterDelay();\n  }\n\n  formSubmissionFinished(_formSubmission) {\n    this.progressBar.setValue(1);\n    this.hideFormProgressBar();\n  }\n\n  // Private\n\n  showVisitProgressBarAfterDelay() {\n    this.visitProgressBarTimeout = window.setTimeout(this.showProgressBar, this.session.progressBarDelay);\n  }\n\n  hideVisitProgressBar() {\n    this.progressBar.hide();\n    if (this.visitProgressBarTimeout != null) {\n      window.clearTimeout(this.visitProgressBarTimeout);\n      delete this.visitProgressBarTimeout;\n    }\n  }\n\n  showFormProgressBarAfterDelay() {\n    if (this.formProgressBarTimeout == null) {\n      this.formProgressBarTimeout = window.setTimeout(this.showProgressBar, this.session.progressBarDelay);\n    }\n  }\n\n  hideFormProgressBar() {\n    this.progressBar.hide();\n    if (this.formProgressBarTimeout != null) {\n      window.clearTimeout(this.formProgressBarTimeout);\n      delete this.formProgressBarTimeout;\n    }\n  }\n\n  showProgressBar = () => {\n    this.progressBar.show();\n  }\n\n  reload(reason) {\n    dispatch(\"turbo:reload\", { detail: reason });\n\n    window.location.href = this.location?.toString() || window.location.href;\n  }\n\n  get navigator() {\n    return this.session.navigator\n  }\n}\n\nclass CacheObserver {\n  selector = \"[data-turbo-temporary]\"\n  deprecatedSelector = \"[data-turbo-cache=false]\"\n\n  started = false\n\n  start() {\n    if (!this.started) {\n      this.started = true;\n      addEventListener(\"turbo:before-cache\", this.removeTemporaryElements, false);\n    }\n  }\n\n  stop() {\n    if (this.started) {\n      this.started = false;\n      removeEventListener(\"turbo:before-cache\", this.removeTemporaryElements, false);\n    }\n  }\n\n  removeTemporaryElements = (_event) => {\n    for (const element of this.temporaryElements) {\n      element.remove();\n    }\n  }\n\n  get temporaryElements() {\n    return [...document.querySelectorAll(this.selector), ...this.temporaryElementsWithDeprecation]\n  }\n\n  get temporaryElementsWithDeprecation() {\n    const elements = document.querySelectorAll(this.deprecatedSelector);\n\n    if (elements.length) {\n      console.warn(\n        `The ${this.deprecatedSelector} selector is deprecated and will be removed in a future version. Use ${this.selector} instead.`\n      );\n    }\n\n    return [...elements]\n  }\n}\n\nclass FrameRedirector {\n  constructor(session, element) {\n    this.session = session;\n    this.element = element;\n    this.linkInterceptor = new LinkInterceptor(this, element);\n    this.formSubmitObserver = new FormSubmitObserver(this, element);\n  }\n\n  start() {\n    this.linkInterceptor.start();\n    this.formSubmitObserver.start();\n  }\n\n  stop() {\n    this.linkInterceptor.stop();\n    this.formSubmitObserver.stop();\n  }\n\n  // Link interceptor delegate\n\n  shouldInterceptLinkClick(element, _location, _event) {\n    return this.#shouldRedirect(element)\n  }\n\n  linkClickIntercepted(element, url, event) {\n    const frame = this.#findFrameElement(element);\n    if (frame) {\n      frame.delegate.linkClickIntercepted(element, url, event);\n    }\n  }\n\n  // Form submit observer delegate\n\n  willSubmitForm(element, submitter) {\n    return (\n      element.closest(\"turbo-frame\") == null &&\n      this.#shouldSubmit(element, submitter) &&\n      this.#shouldRedirect(element, submitter)\n    )\n  }\n\n  formSubmitted(element, submitter) {\n    const frame = this.#findFrameElement(element, submitter);\n    if (frame) {\n      frame.delegate.formSubmitted(element, submitter);\n    }\n  }\n\n  #shouldSubmit(form, submitter) {\n    const action = getAction$1(form, submitter);\n    const meta = this.element.ownerDocument.querySelector(`meta[name=\"turbo-root\"]`);\n    const rootLocation = expandURL(meta?.content ?? \"/\");\n\n    return this.#shouldRedirect(form, submitter) && locationIsVisitable(action, rootLocation)\n  }\n\n  #shouldRedirect(element, submitter) {\n    const isNavigatable =\n      element instanceof HTMLFormElement\n        ? this.session.submissionIsNavigatable(element, submitter)\n        : this.session.elementIsNavigatable(element);\n\n    if (isNavigatable) {\n      const frame = this.#findFrameElement(element, submitter);\n      return frame ? frame != element.closest(\"turbo-frame\") : false\n    } else {\n      return false\n    }\n  }\n\n  #findFrameElement(element, submitter) {\n    const id = submitter?.getAttribute(\"data-turbo-frame\") || element.getAttribute(\"data-turbo-frame\");\n    if (id && id != \"_top\") {\n      const frame = this.element.querySelector(`#${id}:not([disabled])`);\n      if (frame instanceof FrameElement) {\n        return frame\n      }\n    }\n  }\n}\n\nclass History {\n  location\n  restorationIdentifier = uuid()\n  restorationData = {}\n  started = false\n  pageLoaded = false\n  currentIndex = 0\n\n  constructor(delegate) {\n    this.delegate = delegate;\n  }\n\n  start() {\n    if (!this.started) {\n      addEventListener(\"popstate\", this.onPopState, false);\n      addEventListener(\"load\", this.onPageLoad, false);\n      this.currentIndex = history.state?.turbo?.restorationIndex || 0;\n      this.started = true;\n      this.replace(new URL(window.location.href));\n    }\n  }\n\n  stop() {\n    if (this.started) {\n      removeEventListener(\"popstate\", this.onPopState, false);\n      removeEventListener(\"load\", this.onPageLoad, false);\n      this.started = false;\n    }\n  }\n\n  push(location, restorationIdentifier) {\n    this.update(history.pushState, location, restorationIdentifier);\n  }\n\n  replace(location, restorationIdentifier) {\n    this.update(history.replaceState, location, restorationIdentifier);\n  }\n\n  update(method, location, restorationIdentifier = uuid()) {\n    if (method === history.pushState) ++this.currentIndex;\n\n    const state = { turbo: { restorationIdentifier, restorationIndex: this.currentIndex } };\n    method.call(history, state, \"\", location.href);\n    this.location = location;\n    this.restorationIdentifier = restorationIdentifier;\n  }\n\n  // Restoration data\n\n  getRestorationDataForIdentifier(restorationIdentifier) {\n    return this.restorationData[restorationIdentifier] || {}\n  }\n\n  updateRestorationData(additionalData) {\n    const { restorationIdentifier } = this;\n    const restorationData = this.restorationData[restorationIdentifier];\n    this.restorationData[restorationIdentifier] = {\n      ...restorationData,\n      ...additionalData\n    };\n  }\n\n  // Scroll restoration\n\n  assumeControlOfScrollRestoration() {\n    if (!this.previousScrollRestoration) {\n      this.previousScrollRestoration = history.scrollRestoration ?? \"auto\";\n      history.scrollRestoration = \"manual\";\n    }\n  }\n\n  relinquishControlOfScrollRestoration() {\n    if (this.previousScrollRestoration) {\n      history.scrollRestoration = this.previousScrollRestoration;\n      delete this.previousScrollRestoration;\n    }\n  }\n\n  // Event handlers\n\n  onPopState = (event) => {\n    if (this.shouldHandlePopState()) {\n      const { turbo } = event.state || {};\n      if (turbo) {\n        this.location = new URL(window.location.href);\n        const { restorationIdentifier, restorationIndex } = turbo;\n        this.restorationIdentifier = restorationIdentifier;\n        const direction = restorationIndex > this.currentIndex ? \"forward\" : \"back\";\n        this.delegate.historyPoppedToLocationWithRestorationIdentifierAndDirection(this.location, restorationIdentifier, direction);\n        this.currentIndex = restorationIndex;\n      }\n    }\n  }\n\n  onPageLoad = async (_event) => {\n    await nextMicrotask();\n    this.pageLoaded = true;\n  }\n\n  // Private\n\n  shouldHandlePopState() {\n    // Safari dispatches a popstate event after window's load event, ignore it\n    return this.pageIsLoaded()\n  }\n\n  pageIsLoaded() {\n    return this.pageLoaded || document.readyState == \"complete\"\n  }\n}\n\nclass LinkPrefetchObserver {\n  started = false\n  #prefetchedLink = null\n\n  constructor(delegate, eventTarget) {\n    this.delegate = delegate;\n    this.eventTarget = eventTarget;\n  }\n\n  start() {\n    if (this.started) return\n\n    if (this.eventTarget.readyState === \"loading\") {\n      this.eventTarget.addEventListener(\"DOMContentLoaded\", this.#enable, { once: true });\n    } else {\n      this.#enable();\n    }\n  }\n\n  stop() {\n    if (!this.started) return\n\n    this.eventTarget.removeEventListener(\"mouseenter\", this.#tryToPrefetchRequest, {\n      capture: true,\n      passive: true\n    });\n    this.eventTarget.removeEventListener(\"mouseleave\", this.#cancelRequestIfObsolete, {\n      capture: true,\n      passive: true\n    });\n\n    this.eventTarget.removeEventListener(\"turbo:before-fetch-request\", this.#tryToUsePrefetchedRequest, true);\n    this.started = false;\n  }\n\n  #enable = () => {\n    this.eventTarget.addEventListener(\"mouseenter\", this.#tryToPrefetchRequest, {\n      capture: true,\n      passive: true\n    });\n    this.eventTarget.addEventListener(\"mouseleave\", this.#cancelRequestIfObsolete, {\n      capture: true,\n      passive: true\n    });\n\n    this.eventTarget.addEventListener(\"turbo:before-fetch-request\", this.#tryToUsePrefetchedRequest, true);\n    this.started = true;\n  }\n\n  #tryToPrefetchRequest = (event) => {\n    if (getMetaContent(\"turbo-prefetch\") === \"false\") return\n\n    const target = event.target;\n    const isLink = target.matches && target.matches(\"a[href]:not([target^=_]):not([download])\");\n\n    if (isLink && this.#isPrefetchable(target)) {\n      const link = target;\n      const location = getLocationForLink(link);\n\n      if (this.delegate.canPrefetchRequestToLocation(link, location)) {\n        this.#prefetchedLink = link;\n\n        const fetchRequest = new FetchRequest(\n          this,\n          FetchMethod.get,\n          location,\n          new URLSearchParams(),\n          target\n        );\n\n        prefetchCache.setLater(location.toString(), fetchRequest, this.#cacheTtl);\n      }\n    }\n  }\n\n  #cancelRequestIfObsolete = (event) => {\n    if (event.target === this.#prefetchedLink) this.#cancelPrefetchRequest();\n  }\n\n  #cancelPrefetchRequest = () => {\n    prefetchCache.clear();\n    this.#prefetchedLink = null;\n  }\n\n  #tryToUsePrefetchedRequest = (event) => {\n    if (event.target.tagName !== \"FORM\" && event.detail.fetchOptions.method === \"GET\") {\n      const cached = prefetchCache.get(event.detail.url.toString());\n\n      if (cached) {\n        // User clicked link, use cache response\n        event.detail.fetchRequest = cached;\n      }\n\n      prefetchCache.clear();\n    }\n  }\n\n  prepareRequest(request) {\n    const link = request.target;\n\n    request.headers[\"X-Sec-Purpose\"] = \"prefetch\";\n\n    const turboFrame = link.closest(\"turbo-frame\");\n    const turboFrameTarget = link.getAttribute(\"data-turbo-frame\") || turboFrame?.getAttribute(\"target\") || turboFrame?.id;\n\n    if (turboFrameTarget && turboFrameTarget !== \"_top\") {\n      request.headers[\"Turbo-Frame\"] = turboFrameTarget;\n    }\n  }\n\n  // Fetch request interface\n\n  requestSucceededWithResponse() {}\n\n  requestStarted(fetchRequest) {}\n\n  requestErrored(fetchRequest) {}\n\n  requestFinished(fetchRequest) {}\n\n  requestPreventedHandlingResponse(fetchRequest, fetchResponse) {}\n\n  requestFailedWithResponse(fetchRequest, fetchResponse) {}\n\n  get #cacheTtl() {\n    return Number(getMetaContent(\"turbo-prefetch-cache-time\")) || cacheTtl\n  }\n\n  #isPrefetchable(link) {\n    const href = link.getAttribute(\"href\");\n\n    if (!href) return false\n\n    if (unfetchableLink(link)) return false\n    if (linkToTheSamePage(link)) return false\n    if (linkOptsOut(link)) return false\n    if (nonSafeLink(link)) return false\n    if (eventPrevented(link)) return false\n\n    return true\n  }\n}\n\nconst unfetchableLink = (link) => {\n  return link.origin !== document.location.origin || ![\"http:\", \"https:\"].includes(link.protocol) || link.hasAttribute(\"target\")\n};\n\nconst linkToTheSamePage = (link) => {\n  return (link.pathname + link.search === document.location.pathname + document.location.search) || link.href.startsWith(\"#\")\n};\n\nconst linkOptsOut = (link) => {\n  if (link.getAttribute(\"data-turbo-prefetch\") === \"false\") return true\n  if (link.getAttribute(\"data-turbo\") === \"false\") return true\n\n  const turboPrefetchParent = findClosestRecursively(link, \"[data-turbo-prefetch]\");\n  if (turboPrefetchParent && turboPrefetchParent.getAttribute(\"data-turbo-prefetch\") === \"false\") return true\n\n  return false\n};\n\nconst nonSafeLink = (link) => {\n  const turboMethod = link.getAttribute(\"data-turbo-method\");\n  if (turboMethod && turboMethod.toLowerCase() !== \"get\") return true\n\n  if (isUJS(link)) return true\n  if (link.hasAttribute(\"data-turbo-confirm\")) return true\n  if (link.hasAttribute(\"data-turbo-stream\")) return true\n\n  return false\n};\n\nconst isUJS = (link) => {\n  return link.hasAttribute(\"data-remote\") || link.hasAttribute(\"data-behavior\") || link.hasAttribute(\"data-confirm\") || link.hasAttribute(\"data-method\")\n};\n\nconst eventPrevented = (link) => {\n  const event = dispatch(\"turbo:before-prefetch\", { target: link, cancelable: true });\n  return event.defaultPrevented\n};\n\nclass Navigator {\n  constructor(delegate) {\n    this.delegate = delegate;\n  }\n\n  proposeVisit(location, options = {}) {\n    if (this.delegate.allowsVisitingLocationWithAction(location, options.action)) {\n      this.delegate.visitProposedToLocation(location, options);\n    }\n  }\n\n  startVisit(locatable, restorationIdentifier, options = {}) {\n    this.stop();\n    this.currentVisit = new Visit(this, expandURL(locatable), restorationIdentifier, {\n      referrer: this.location,\n      ...options\n    });\n    this.currentVisit.start();\n  }\n\n  submitForm(form, submitter) {\n    this.stop();\n    this.formSubmission = new FormSubmission(this, form, submitter, true);\n\n    this.formSubmission.start();\n  }\n\n  stop() {\n    if (this.formSubmission) {\n      this.formSubmission.stop();\n      delete this.formSubmission;\n    }\n\n    if (this.currentVisit) {\n      this.currentVisit.cancel();\n      delete this.currentVisit;\n    }\n  }\n\n  get adapter() {\n    return this.delegate.adapter\n  }\n\n  get view() {\n    return this.delegate.view\n  }\n\n  get rootLocation() {\n    return this.view.snapshot.rootLocation\n  }\n\n  get history() {\n    return this.delegate.history\n  }\n\n  // Form submission delegate\n\n  formSubmissionStarted(formSubmission) {\n    // Not all adapters implement formSubmissionStarted\n    if (typeof this.adapter.formSubmissionStarted === \"function\") {\n      this.adapter.formSubmissionStarted(formSubmission);\n    }\n  }\n\n  async formSubmissionSucceededWithResponse(formSubmission, fetchResponse) {\n    if (formSubmission == this.formSubmission) {\n      const responseHTML = await fetchResponse.responseHTML;\n      if (responseHTML) {\n        const shouldCacheSnapshot = formSubmission.isSafe;\n        if (!shouldCacheSnapshot) {\n          this.view.clearSnapshotCache();\n        }\n\n        const { statusCode, redirected } = fetchResponse;\n        const action = this.#getActionForFormSubmission(formSubmission, fetchResponse);\n        const visitOptions = {\n          action,\n          shouldCacheSnapshot,\n          response: { statusCode, responseHTML, redirected }\n        };\n        this.proposeVisit(fetchResponse.location, visitOptions);\n      }\n    }\n  }\n\n  async formSubmissionFailedWithResponse(formSubmission, fetchResponse) {\n    const responseHTML = await fetchResponse.responseHTML;\n\n    if (responseHTML) {\n      const snapshot = PageSnapshot.fromHTMLString(responseHTML);\n      if (fetchResponse.serverError) {\n        await this.view.renderError(snapshot, this.currentVisit);\n      } else {\n        await this.view.renderPage(snapshot, false, true, this.currentVisit);\n      }\n      if(!snapshot.shouldPreserveScrollPosition) {\n        this.view.scrollToTop();\n      }\n      this.view.clearSnapshotCache();\n    }\n  }\n\n  formSubmissionErrored(formSubmission, error) {\n    console.error(error);\n  }\n\n  formSubmissionFinished(formSubmission) {\n    // Not all adapters implement formSubmissionFinished\n    if (typeof this.adapter.formSubmissionFinished === \"function\") {\n      this.adapter.formSubmissionFinished(formSubmission);\n    }\n  }\n\n  // Link prefetching\n\n  linkPrefetchingIsEnabledForLocation(location) {\n    // Not all adapters implement linkPrefetchingIsEnabledForLocation\n    if (typeof this.adapter.linkPrefetchingIsEnabledForLocation === \"function\") {\n      return this.adapter.linkPrefetchingIsEnabledForLocation(location)\n    }\n\n    return true\n  }\n\n  // Visit delegate\n\n  visitStarted(visit) {\n    this.delegate.visitStarted(visit);\n  }\n\n  visitCompleted(visit) {\n    this.delegate.visitCompleted(visit);\n    delete this.currentVisit;\n  }\n\n  locationWithActionIsSamePage(location, action) {\n    const anchor = getAnchor(location);\n    const currentAnchor = getAnchor(this.view.lastRenderedLocation);\n    const isRestorationToTop = action === \"restore\" && typeof anchor === \"undefined\";\n\n    return (\n      action !== \"replace\" &&\n      getRequestURL(location) === getRequestURL(this.view.lastRenderedLocation) &&\n      (isRestorationToTop || (anchor != null && anchor !== currentAnchor))\n    )\n  }\n\n  visitScrolledToSamePageLocation(oldURL, newURL) {\n    this.delegate.visitScrolledToSamePageLocation(oldURL, newURL);\n  }\n\n  // Visits\n\n  get location() {\n    return this.history.location\n  }\n\n  get restorationIdentifier() {\n    return this.history.restorationIdentifier\n  }\n\n  #getActionForFormSubmission(formSubmission, fetchResponse) {\n    const { submitter, formElement } = formSubmission;\n    return getVisitAction(submitter, formElement) || this.#getDefaultAction(fetchResponse)\n  }\n\n  #getDefaultAction(fetchResponse) {\n    const sameLocationRedirect = fetchResponse.redirected && fetchResponse.location.href === this.location?.href;\n    return sameLocationRedirect ? \"replace\" : \"advance\"\n  }\n}\n\nconst PageStage = {\n  initial: 0,\n  loading: 1,\n  interactive: 2,\n  complete: 3\n};\n\nclass PageObserver {\n  stage = PageStage.initial\n  started = false\n\n  constructor(delegate) {\n    this.delegate = delegate;\n  }\n\n  start() {\n    if (!this.started) {\n      if (this.stage == PageStage.initial) {\n        this.stage = PageStage.loading;\n      }\n      document.addEventListener(\"readystatechange\", this.interpretReadyState, false);\n      addEventListener(\"pagehide\", this.pageWillUnload, false);\n      this.started = true;\n    }\n  }\n\n  stop() {\n    if (this.started) {\n      document.removeEventListener(\"readystatechange\", this.interpretReadyState, false);\n      removeEventListener(\"pagehide\", this.pageWillUnload, false);\n      this.started = false;\n    }\n  }\n\n  interpretReadyState = () => {\n    const { readyState } = this;\n    if (readyState == \"interactive\") {\n      this.pageIsInteractive();\n    } else if (readyState == \"complete\") {\n      this.pageIsComplete();\n    }\n  }\n\n  pageIsInteractive() {\n    if (this.stage == PageStage.loading) {\n      this.stage = PageStage.interactive;\n      this.delegate.pageBecameInteractive();\n    }\n  }\n\n  pageIsComplete() {\n    this.pageIsInteractive();\n    if (this.stage == PageStage.interactive) {\n      this.stage = PageStage.complete;\n      this.delegate.pageLoaded();\n    }\n  }\n\n  pageWillUnload = () => {\n    this.delegate.pageWillUnload();\n  }\n\n  get readyState() {\n    return document.readyState\n  }\n}\n\nclass ScrollObserver {\n  started = false\n\n  constructor(delegate) {\n    this.delegate = delegate;\n  }\n\n  start() {\n    if (!this.started) {\n      addEventListener(\"scroll\", this.onScroll, false);\n      this.onScroll();\n      this.started = true;\n    }\n  }\n\n  stop() {\n    if (this.started) {\n      removeEventListener(\"scroll\", this.onScroll, false);\n      this.started = false;\n    }\n  }\n\n  onScroll = () => {\n    this.updatePosition({ x: window.pageXOffset, y: window.pageYOffset });\n  }\n\n  // Private\n\n  updatePosition(position) {\n    this.delegate.scrollPositionChanged(position);\n  }\n}\n\nclass StreamMessageRenderer {\n  render({ fragment }) {\n    Bardo.preservingPermanentElements(this, getPermanentElementMapForFragment(fragment), () => {\n      withAutofocusFromFragment(fragment, () => {\n        withPreservedFocus(() => {\n          document.documentElement.appendChild(fragment);\n        });\n      });\n    });\n  }\n\n  // Bardo delegate\n\n  enteringBardo(currentPermanentElement, newPermanentElement) {\n    newPermanentElement.replaceWith(currentPermanentElement.cloneNode(true));\n  }\n\n  leavingBardo() {}\n}\n\nfunction getPermanentElementMapForFragment(fragment) {\n  const permanentElementsInDocument = queryPermanentElementsAll(document.documentElement);\n  const permanentElementMap = {};\n  for (const permanentElementInDocument of permanentElementsInDocument) {\n    const { id } = permanentElementInDocument;\n\n    for (const streamElement of fragment.querySelectorAll(\"turbo-stream\")) {\n      const elementInStream = getPermanentElementById(streamElement.templateElement.content, id);\n\n      if (elementInStream) {\n        permanentElementMap[id] = [permanentElementInDocument, elementInStream];\n      }\n    }\n  }\n\n  return permanentElementMap\n}\n\nasync function withAutofocusFromFragment(fragment, callback) {\n  const generatedID = `turbo-stream-autofocus-${uuid()}`;\n  const turboStreams = fragment.querySelectorAll(\"turbo-stream\");\n  const elementWithAutofocus = firstAutofocusableElementInStreams(turboStreams);\n  let willAutofocusId = null;\n\n  if (elementWithAutofocus) {\n    if (elementWithAutofocus.id) {\n      willAutofocusId = elementWithAutofocus.id;\n    } else {\n      willAutofocusId = generatedID;\n    }\n\n    elementWithAutofocus.id = willAutofocusId;\n  }\n\n  callback();\n  await nextRepaint();\n\n  const hasNoActiveElement = document.activeElement == null || document.activeElement == document.body;\n\n  if (hasNoActiveElement && willAutofocusId) {\n    const elementToAutofocus = document.getElementById(willAutofocusId);\n\n    if (elementIsFocusable(elementToAutofocus)) {\n      elementToAutofocus.focus();\n    }\n    if (elementToAutofocus && elementToAutofocus.id == generatedID) {\n      elementToAutofocus.removeAttribute(\"id\");\n    }\n  }\n}\n\nasync function withPreservedFocus(callback) {\n  const [activeElementBeforeRender, activeElementAfterRender] = await around(callback, () => document.activeElement);\n\n  const restoreFocusTo = activeElementBeforeRender && activeElementBeforeRender.id;\n\n  if (restoreFocusTo) {\n    const elementToFocus = document.getElementById(restoreFocusTo);\n\n    if (elementIsFocusable(elementToFocus) && elementToFocus != activeElementAfterRender) {\n      elementToFocus.focus();\n    }\n  }\n}\n\nfunction firstAutofocusableElementInStreams(nodeListOfStreamElements) {\n  for (const streamElement of nodeListOfStreamElements) {\n    const elementWithAutofocus = queryAutofocusableElement(streamElement.templateElement.content);\n\n    if (elementWithAutofocus) return elementWithAutofocus\n  }\n\n  return null\n}\n\nclass StreamObserver {\n  sources = new Set()\n  #started = false\n\n  constructor(delegate) {\n    this.delegate = delegate;\n  }\n\n  start() {\n    if (!this.#started) {\n      this.#started = true;\n      addEventListener(\"turbo:before-fetch-response\", this.inspectFetchResponse, false);\n    }\n  }\n\n  stop() {\n    if (this.#started) {\n      this.#started = false;\n      removeEventListener(\"turbo:before-fetch-response\", this.inspectFetchResponse, false);\n    }\n  }\n\n  connectStreamSource(source) {\n    if (!this.streamSourceIsConnected(source)) {\n      this.sources.add(source);\n      source.addEventListener(\"message\", this.receiveMessageEvent, false);\n    }\n  }\n\n  disconnectStreamSource(source) {\n    if (this.streamSourceIsConnected(source)) {\n      this.sources.delete(source);\n      source.removeEventListener(\"message\", this.receiveMessageEvent, false);\n    }\n  }\n\n  streamSourceIsConnected(source) {\n    return this.sources.has(source)\n  }\n\n  inspectFetchResponse = (event) => {\n    const response = fetchResponseFromEvent(event);\n    if (response && fetchResponseIsStream(response)) {\n      event.preventDefault();\n      this.receiveMessageResponse(response);\n    }\n  }\n\n  receiveMessageEvent = (event) => {\n    if (this.#started && typeof event.data == \"string\") {\n      this.receiveMessageHTML(event.data);\n    }\n  }\n\n  async receiveMessageResponse(response) {\n    const html = await response.responseHTML;\n    if (html) {\n      this.receiveMessageHTML(html);\n    }\n  }\n\n  receiveMessageHTML(html) {\n    this.delegate.receivedMessageFromStream(StreamMessage.wrap(html));\n  }\n}\n\nfunction fetchResponseFromEvent(event) {\n  const fetchResponse = event.detail?.fetchResponse;\n  if (fetchResponse instanceof FetchResponse) {\n    return fetchResponse\n  }\n}\n\nfunction fetchResponseIsStream(response) {\n  const contentType = response.contentType ?? \"\";\n  return contentType.startsWith(StreamMessage.contentType)\n}\n\nclass ErrorRenderer extends Renderer {\n  static renderElement(currentElement, newElement) {\n    const { documentElement, body } = document;\n\n    documentElement.replaceChild(newElement, body);\n  }\n\n  async render() {\n    this.replaceHeadAndBody();\n    this.activateScriptElements();\n  }\n\n  replaceHeadAndBody() {\n    const { documentElement, head } = document;\n    documentElement.replaceChild(this.newHead, head);\n    this.renderElement(this.currentElement, this.newElement);\n  }\n\n  activateScriptElements() {\n    for (const replaceableElement of this.scriptElements) {\n      const parentNode = replaceableElement.parentNode;\n      if (parentNode) {\n        const element = activateScriptElement(replaceableElement);\n        parentNode.replaceChild(element, replaceableElement);\n      }\n    }\n  }\n\n  get newHead() {\n    return this.newSnapshot.headSnapshot.element\n  }\n\n  get scriptElements() {\n    return document.documentElement.querySelectorAll(\"script\")\n  }\n}\n\nclass PageRenderer extends Renderer {\n  static renderElement(currentElement, newElement) {\n    if (document.body && newElement instanceof HTMLBodyElement) {\n      document.body.replaceWith(newElement);\n    } else {\n      document.documentElement.appendChild(newElement);\n    }\n  }\n\n  get shouldRender() {\n    return this.newSnapshot.isVisitable && this.trackedElementsAreIdentical\n  }\n\n  get reloadReason() {\n    if (!this.newSnapshot.isVisitable) {\n      return {\n        reason: \"turbo_visit_control_is_reload\"\n      }\n    }\n\n    if (!this.trackedElementsAreIdentical) {\n      return {\n        reason: \"tracked_element_mismatch\"\n      }\n    }\n  }\n\n  async prepareToRender() {\n    this.#setLanguage();\n    await this.mergeHead();\n  }\n\n  async render() {\n    if (this.willRender) {\n      await this.replaceBody();\n    }\n  }\n\n  finishRendering() {\n    super.finishRendering();\n    if (!this.isPreview) {\n      this.focusFirstAutofocusableElement();\n    }\n  }\n\n  get currentHeadSnapshot() {\n    return this.currentSnapshot.headSnapshot\n  }\n\n  get newHeadSnapshot() {\n    return this.newSnapshot.headSnapshot\n  }\n\n  get newElement() {\n    return this.newSnapshot.element\n  }\n\n  #setLanguage() {\n    const { documentElement } = this.currentSnapshot;\n    const { lang } = this.newSnapshot;\n\n    if (lang) {\n      documentElement.setAttribute(\"lang\", lang);\n    } else {\n      documentElement.removeAttribute(\"lang\");\n    }\n  }\n\n  async mergeHead() {\n    const mergedHeadElements = this.mergeProvisionalElements();\n    const newStylesheetElements = this.copyNewHeadStylesheetElements();\n    this.copyNewHeadScriptElements();\n\n    await mergedHeadElements;\n    await newStylesheetElements;\n\n    if (this.willRender) {\n      this.removeUnusedDynamicStylesheetElements();\n    }\n  }\n\n  async replaceBody() {\n    await this.preservingPermanentElements(async () => {\n      this.activateNewBody();\n      await this.assignNewBody();\n    });\n  }\n\n  get trackedElementsAreIdentical() {\n    return this.currentHeadSnapshot.trackedElementSignature == this.newHeadSnapshot.trackedElementSignature\n  }\n\n  async copyNewHeadStylesheetElements() {\n    const loadingElements = [];\n\n    for (const element of this.newHeadStylesheetElements) {\n      loadingElements.push(waitForLoad(element));\n\n      document.head.appendChild(element);\n    }\n\n    await Promise.all(loadingElements);\n  }\n\n  copyNewHeadScriptElements() {\n    for (const element of this.newHeadScriptElements) {\n      document.head.appendChild(activateScriptElement(element));\n    }\n  }\n\n  removeUnusedDynamicStylesheetElements() {\n    for (const element of this.unusedDynamicStylesheetElements) {\n      document.head.removeChild(element);\n    }\n  }\n\n  async mergeProvisionalElements() {\n    const newHeadElements = [...this.newHeadProvisionalElements];\n\n    for (const element of this.currentHeadProvisionalElements) {\n      if (!this.isCurrentElementInElementList(element, newHeadElements)) {\n        document.head.removeChild(element);\n      }\n    }\n\n    for (const element of newHeadElements) {\n      document.head.appendChild(element);\n    }\n  }\n\n  isCurrentElementInElementList(element, elementList) {\n    for (const [index, newElement] of elementList.entries()) {\n      // if title element...\n      if (element.tagName == \"TITLE\") {\n        if (newElement.tagName != \"TITLE\") {\n          continue\n        }\n        if (element.innerHTML == newElement.innerHTML) {\n          elementList.splice(index, 1);\n          return true\n        }\n      }\n\n      // if any other element...\n      if (newElement.isEqualNode(element)) {\n        elementList.splice(index, 1);\n        return true\n      }\n    }\n\n    return false\n  }\n\n  removeCurrentHeadProvisionalElements() {\n    for (const element of this.currentHeadProvisionalElements) {\n      document.head.removeChild(element);\n    }\n  }\n\n  copyNewHeadProvisionalElements() {\n    for (const element of this.newHeadProvisionalElements) {\n      document.head.appendChild(element);\n    }\n  }\n\n  activateNewBody() {\n    document.adoptNode(this.newElement);\n    this.activateNewBodyScriptElements();\n  }\n\n  activateNewBodyScriptElements() {\n    for (const inertScriptElement of this.newBodyScriptElements) {\n      const activatedScriptElement = activateScriptElement(inertScriptElement);\n      inertScriptElement.replaceWith(activatedScriptElement);\n    }\n  }\n\n  async assignNewBody() {\n    await this.renderElement(this.currentElement, this.newElement);\n  }\n\n  get unusedDynamicStylesheetElements() {\n    return this.oldHeadStylesheetElements.filter((element) => {\n      return element.getAttribute(\"data-turbo-track\") === \"dynamic\"\n    })\n  }\n\n  get oldHeadStylesheetElements() {\n    return this.currentHeadSnapshot.getStylesheetElementsNotInSnapshot(this.newHeadSnapshot)\n  }\n\n  get newHeadStylesheetElements() {\n    return this.newHeadSnapshot.getStylesheetElementsNotInSnapshot(this.currentHeadSnapshot)\n  }\n\n  get newHeadScriptElements() {\n    return this.newHeadSnapshot.getScriptElementsNotInSnapshot(this.currentHeadSnapshot)\n  }\n\n  get currentHeadProvisionalElements() {\n    return this.currentHeadSnapshot.provisionalElements\n  }\n\n  get newHeadProvisionalElements() {\n    return this.newHeadSnapshot.provisionalElements\n  }\n\n  get newBodyScriptElements() {\n    return this.newElement.querySelectorAll(\"script\")\n  }\n}\n\nclass MorphingPageRenderer extends PageRenderer {\n  static renderElement(currentElement, newElement) {\n    morphElements(currentElement, newElement, {\n      callbacks: {\n        beforeNodeMorphed: element => !canRefreshFrame(element)\n      }\n    });\n\n    for (const frame of currentElement.querySelectorAll(\"turbo-frame\")) {\n      if (canRefreshFrame(frame)) frame.reload();\n    }\n\n    dispatch(\"turbo:morph\", { detail: { currentElement, newElement } });\n  }\n\n  async preservingPermanentElements(callback) {\n    return await callback()\n  }\n\n  get renderMethod() {\n    return \"morph\"\n  }\n\n  get shouldAutofocus() {\n    return false\n  }\n}\n\nfunction canRefreshFrame(frame) {\n  return frame instanceof FrameElement &&\n    frame.src &&\n    frame.refresh === \"morph\" &&\n    !frame.closest(\"[data-turbo-permanent]\")\n}\n\nclass SnapshotCache {\n  keys = []\n  snapshots = {}\n\n  constructor(size) {\n    this.size = size;\n  }\n\n  has(location) {\n    return toCacheKey(location) in this.snapshots\n  }\n\n  get(location) {\n    if (this.has(location)) {\n      const snapshot = this.read(location);\n      this.touch(location);\n      return snapshot\n    }\n  }\n\n  put(location, snapshot) {\n    this.write(location, snapshot);\n    this.touch(location);\n    return snapshot\n  }\n\n  clear() {\n    this.snapshots = {};\n  }\n\n  // Private\n\n  read(location) {\n    return this.snapshots[toCacheKey(location)]\n  }\n\n  write(location, snapshot) {\n    this.snapshots[toCacheKey(location)] = snapshot;\n  }\n\n  touch(location) {\n    const key = toCacheKey(location);\n    const index = this.keys.indexOf(key);\n    if (index > -1) this.keys.splice(index, 1);\n    this.keys.unshift(key);\n    this.trim();\n  }\n\n  trim() {\n    for (const key of this.keys.splice(this.size)) {\n      delete this.snapshots[key];\n    }\n  }\n}\n\nclass PageView extends View {\n  snapshotCache = new SnapshotCache(10)\n  lastRenderedLocation = new URL(location.href)\n  forceReloaded = false\n\n  shouldTransitionTo(newSnapshot) {\n    return this.snapshot.prefersViewTransitions && newSnapshot.prefersViewTransitions\n  }\n\n  renderPage(snapshot, isPreview = false, willRender = true, visit) {\n    const shouldMorphPage = this.isPageRefresh(visit) && this.snapshot.shouldMorphPage;\n    const rendererClass = shouldMorphPage ? MorphingPageRenderer : PageRenderer;\n\n    const renderer = new rendererClass(this.snapshot, snapshot, isPreview, willRender);\n\n    if (!renderer.shouldRender) {\n      this.forceReloaded = true;\n    } else {\n      visit?.changeHistory();\n    }\n\n    return this.render(renderer)\n  }\n\n  renderError(snapshot, visit) {\n    visit?.changeHistory();\n    const renderer = new ErrorRenderer(this.snapshot, snapshot, false);\n    return this.render(renderer)\n  }\n\n  clearSnapshotCache() {\n    this.snapshotCache.clear();\n  }\n\n  async cacheSnapshot(snapshot = this.snapshot) {\n    if (snapshot.isCacheable) {\n      this.delegate.viewWillCacheSnapshot();\n      const { lastRenderedLocation: location } = this;\n      await nextEventLoopTick();\n      const cachedSnapshot = snapshot.clone();\n      this.snapshotCache.put(location, cachedSnapshot);\n      return cachedSnapshot\n    }\n  }\n\n  getCachedSnapshotForLocation(location) {\n    return this.snapshotCache.get(location)\n  }\n\n  isPageRefresh(visit) {\n    return !visit || (this.lastRenderedLocation.pathname === visit.location.pathname && visit.action === \"replace\")\n  }\n\n  shouldPreserveScrollPosition(visit) {\n    return this.isPageRefresh(visit) && this.snapshot.shouldPreserveScrollPosition\n  }\n\n  get snapshot() {\n    return PageSnapshot.fromElement(this.element)\n  }\n}\n\nclass Preloader {\n  selector = \"a[data-turbo-preload]\"\n\n  constructor(delegate, snapshotCache) {\n    this.delegate = delegate;\n    this.snapshotCache = snapshotCache;\n  }\n\n  start() {\n    if (document.readyState === \"loading\") {\n      document.addEventListener(\"DOMContentLoaded\", this.#preloadAll);\n    } else {\n      this.preloadOnLoadLinksForView(document.body);\n    }\n  }\n\n  stop() {\n    document.removeEventListener(\"DOMContentLoaded\", this.#preloadAll);\n  }\n\n  preloadOnLoadLinksForView(element) {\n    for (const link of element.querySelectorAll(this.selector)) {\n      if (this.delegate.shouldPreloadLink(link)) {\n        this.preloadURL(link);\n      }\n    }\n  }\n\n  async preloadURL(link) {\n    const location = new URL(link.href);\n\n    if (this.snapshotCache.has(location)) {\n      return\n    }\n\n    const fetchRequest = new FetchRequest(this, FetchMethod.get, location, new URLSearchParams(), link);\n    await fetchRequest.perform();\n  }\n\n  // Fetch request delegate\n\n  prepareRequest(fetchRequest) {\n    fetchRequest.headers[\"X-Sec-Purpose\"] = \"prefetch\";\n  }\n\n  async requestSucceededWithResponse(fetchRequest, fetchResponse) {\n    try {\n      const responseHTML = await fetchResponse.responseHTML;\n      const snapshot = PageSnapshot.fromHTMLString(responseHTML);\n\n      this.snapshotCache.put(fetchRequest.url, snapshot);\n    } catch (_) {\n      // If we cannot preload that is ok!\n    }\n  }\n\n  requestStarted(fetchRequest) {}\n\n  requestErrored(fetchRequest) {}\n\n  requestFinished(fetchRequest) {}\n\n  requestPreventedHandlingResponse(fetchRequest, fetchResponse) {}\n\n  requestFailedWithResponse(fetchRequest, fetchResponse) {}\n\n  #preloadAll = () => {\n    this.preloadOnLoadLinksForView(document.body);\n  }\n}\n\nclass Cache {\n  constructor(session) {\n    this.session = session;\n  }\n\n  clear() {\n    this.session.clearCache();\n  }\n\n  resetCacheControl() {\n    this.#setCacheControl(\"\");\n  }\n\n  exemptPageFromCache() {\n    this.#setCacheControl(\"no-cache\");\n  }\n\n  exemptPageFromPreview() {\n    this.#setCacheControl(\"no-preview\");\n  }\n\n  #setCacheControl(value) {\n    setMetaContent(\"turbo-cache-control\", value);\n  }\n}\n\nclass Session {\n  navigator = new Navigator(this)\n  history = new History(this)\n  view = new PageView(this, document.documentElement)\n  adapter = new BrowserAdapter(this)\n\n  pageObserver = new PageObserver(this)\n  cacheObserver = new CacheObserver()\n  linkPrefetchObserver = new LinkPrefetchObserver(this, document)\n  linkClickObserver = new LinkClickObserver(this, window)\n  formSubmitObserver = new FormSubmitObserver(this, document)\n  scrollObserver = new ScrollObserver(this)\n  streamObserver = new StreamObserver(this)\n  formLinkClickObserver = new FormLinkClickObserver(this, document.documentElement)\n  frameRedirector = new FrameRedirector(this, document.documentElement)\n  streamMessageRenderer = new StreamMessageRenderer()\n  cache = new Cache(this)\n\n  enabled = true\n  started = false\n  #pageRefreshDebouncePeriod = 150\n\n  constructor(recentRequests) {\n    this.recentRequests = recentRequests;\n    this.preloader = new Preloader(this, this.view.snapshotCache);\n    this.debouncedRefresh = this.refresh;\n    this.pageRefreshDebouncePeriod = this.pageRefreshDebouncePeriod;\n  }\n\n  start() {\n    if (!this.started) {\n      this.pageObserver.start();\n      this.cacheObserver.start();\n      this.linkPrefetchObserver.start();\n      this.formLinkClickObserver.start();\n      this.linkClickObserver.start();\n      this.formSubmitObserver.start();\n      this.scrollObserver.start();\n      this.streamObserver.start();\n      this.frameRedirector.start();\n      this.history.start();\n      this.preloader.start();\n      this.started = true;\n      this.enabled = true;\n    }\n  }\n\n  disable() {\n    this.enabled = false;\n  }\n\n  stop() {\n    if (this.started) {\n      this.pageObserver.stop();\n      this.cacheObserver.stop();\n      this.linkPrefetchObserver.stop();\n      this.formLinkClickObserver.stop();\n      this.linkClickObserver.stop();\n      this.formSubmitObserver.stop();\n      this.scrollObserver.stop();\n      this.streamObserver.stop();\n      this.frameRedirector.stop();\n      this.history.stop();\n      this.preloader.stop();\n      this.started = false;\n    }\n  }\n\n  registerAdapter(adapter) {\n    this.adapter = adapter;\n  }\n\n  visit(location, options = {}) {\n    const frameElement = options.frame ? document.getElementById(options.frame) : null;\n\n    if (frameElement instanceof FrameElement) {\n      const action = options.action || getVisitAction(frameElement);\n\n      frameElement.delegate.proposeVisitIfNavigatedWithAction(frameElement, action);\n      frameElement.src = location.toString();\n    } else {\n      this.navigator.proposeVisit(expandURL(location), options);\n    }\n  }\n\n  refresh(url, requestId) {\n    const isRecentRequest = requestId && this.recentRequests.has(requestId);\n    const isCurrentUrl = url === document.baseURI;\n    if (!isRecentRequest && !this.navigator.currentVisit && isCurrentUrl) {\n      this.visit(url, { action: \"replace\", shouldCacheSnapshot: false });\n    }\n  }\n\n  connectStreamSource(source) {\n    this.streamObserver.connectStreamSource(source);\n  }\n\n  disconnectStreamSource(source) {\n    this.streamObserver.disconnectStreamSource(source);\n  }\n\n  renderStreamMessage(message) {\n    this.streamMessageRenderer.render(StreamMessage.wrap(message));\n  }\n\n  clearCache() {\n    this.view.clearSnapshotCache();\n  }\n\n  setProgressBarDelay(delay) {\n    console.warn(\n      \"Please replace `session.setProgressBarDelay(delay)` with `session.progressBarDelay = delay`. The function is deprecated and will be removed in a future version of Turbo.`\"\n    );\n\n    this.progressBarDelay = delay;\n  }\n\n  set progressBarDelay(delay) {\n    config.drive.progressBarDelay = delay;\n  }\n\n  get progressBarDelay() {\n    return config.drive.progressBarDelay\n  }\n\n  set drive(value) {\n    config.drive.enabled = value;\n  }\n\n  get drive() {\n    return config.drive.enabled\n  }\n\n  set formMode(value) {\n    config.forms.mode = value;\n  }\n\n  get formMode() {\n    return config.forms.mode\n  }\n\n  get location() {\n    return this.history.location\n  }\n\n  get restorationIdentifier() {\n    return this.history.restorationIdentifier\n  }\n\n  get pageRefreshDebouncePeriod() {\n    return this.#pageRefreshDebouncePeriod\n  }\n\n  set pageRefreshDebouncePeriod(value) {\n    this.refresh = debounce(this.debouncedRefresh.bind(this), value);\n    this.#pageRefreshDebouncePeriod = value;\n  }\n\n  // Preloader delegate\n\n  shouldPreloadLink(element) {\n    const isUnsafe = element.hasAttribute(\"data-turbo-method\");\n    const isStream = element.hasAttribute(\"data-turbo-stream\");\n    const frameTarget = element.getAttribute(\"data-turbo-frame\");\n    const frame = frameTarget == \"_top\" ?\n      null :\n      document.getElementById(frameTarget) || findClosestRecursively(element, \"turbo-frame:not([disabled])\");\n\n    if (isUnsafe || isStream || frame instanceof FrameElement) {\n      return false\n    } else {\n      const location = new URL(element.href);\n\n      return this.elementIsNavigatable(element) && locationIsVisitable(location, this.snapshot.rootLocation)\n    }\n  }\n\n  // History delegate\n\n  historyPoppedToLocationWithRestorationIdentifierAndDirection(location, restorationIdentifier, direction) {\n    if (this.enabled) {\n      this.navigator.startVisit(location, restorationIdentifier, {\n        action: \"restore\",\n        historyChanged: true,\n        direction\n      });\n    } else {\n      this.adapter.pageInvalidated({\n        reason: \"turbo_disabled\"\n      });\n    }\n  }\n\n  // Scroll observer delegate\n\n  scrollPositionChanged(position) {\n    this.history.updateRestorationData({ scrollPosition: position });\n  }\n\n  // Form click observer delegate\n\n  willSubmitFormLinkToLocation(link, location) {\n    return this.elementIsNavigatable(link) && locationIsVisitable(location, this.snapshot.rootLocation)\n  }\n\n  submittedFormLinkToLocation() {}\n\n  // Link hover observer delegate\n\n  canPrefetchRequestToLocation(link, location) {\n    return (\n      this.elementIsNavigatable(link) &&\n      locationIsVisitable(location, this.snapshot.rootLocation) &&\n      this.navigator.linkPrefetchingIsEnabledForLocation(location)\n    )\n  }\n\n  // Link click observer delegate\n\n  willFollowLinkToLocation(link, location, event) {\n    return (\n      this.elementIsNavigatable(link) &&\n      locationIsVisitable(location, this.snapshot.rootLocation) &&\n      this.applicationAllowsFollowingLinkToLocation(link, location, event)\n    )\n  }\n\n  followedLinkToLocation(link, location) {\n    const action = this.getActionForLink(link);\n    const acceptsStreamResponse = link.hasAttribute(\"data-turbo-stream\");\n\n    this.visit(location.href, { action, acceptsStreamResponse });\n  }\n\n  // Navigator delegate\n\n  allowsVisitingLocationWithAction(location, action) {\n    return this.locationWithActionIsSamePage(location, action) || this.applicationAllowsVisitingLocation(location)\n  }\n\n  visitProposedToLocation(location, options) {\n    extendURLWithDeprecatedProperties(location);\n    this.adapter.visitProposedToLocation(location, options);\n  }\n\n  // Visit delegate\n\n  visitStarted(visit) {\n    if (!visit.acceptsStreamResponse) {\n      markAsBusy(document.documentElement);\n      this.view.markVisitDirection(visit.direction);\n    }\n    extendURLWithDeprecatedProperties(visit.location);\n    if (!visit.silent) {\n      this.notifyApplicationAfterVisitingLocation(visit.location, visit.action);\n    }\n  }\n\n  visitCompleted(visit) {\n    this.view.unmarkVisitDirection();\n    clearBusyState(document.documentElement);\n    this.notifyApplicationAfterPageLoad(visit.getTimingMetrics());\n  }\n\n  locationWithActionIsSamePage(location, action) {\n    return this.navigator.locationWithActionIsSamePage(location, action)\n  }\n\n  visitScrolledToSamePageLocation(oldURL, newURL) {\n    this.notifyApplicationAfterVisitingSamePageLocation(oldURL, newURL);\n  }\n\n  // Form submit observer delegate\n\n  willSubmitForm(form, submitter) {\n    const action = getAction$1(form, submitter);\n\n    return (\n      this.submissionIsNavigatable(form, submitter) &&\n      locationIsVisitable(expandURL(action), this.snapshot.rootLocation)\n    )\n  }\n\n  formSubmitted(form, submitter) {\n    this.navigator.submitForm(form, submitter);\n  }\n\n  // Page observer delegate\n\n  pageBecameInteractive() {\n    this.view.lastRenderedLocation = this.location;\n    this.notifyApplicationAfterPageLoad();\n  }\n\n  pageLoaded() {\n    this.history.assumeControlOfScrollRestoration();\n  }\n\n  pageWillUnload() {\n    this.history.relinquishControlOfScrollRestoration();\n  }\n\n  // Stream observer delegate\n\n  receivedMessageFromStream(message) {\n    this.renderStreamMessage(message);\n  }\n\n  // Page view delegate\n\n  viewWillCacheSnapshot() {\n    if (!this.navigator.currentVisit?.silent) {\n      this.notifyApplicationBeforeCachingSnapshot();\n    }\n  }\n\n  allowsImmediateRender({ element }, options) {\n    const event = this.notifyApplicationBeforeRender(element, options);\n    const {\n      defaultPrevented,\n      detail: { render }\n    } = event;\n\n    if (this.view.renderer && render) {\n      this.view.renderer.renderElement = render;\n    }\n\n    return !defaultPrevented\n  }\n\n  viewRenderedSnapshot(_snapshot, _isPreview, renderMethod) {\n    this.view.lastRenderedLocation = this.history.location;\n    this.notifyApplicationAfterRender(renderMethod);\n  }\n\n  preloadOnLoadLinksForView(element) {\n    this.preloader.preloadOnLoadLinksForView(element);\n  }\n\n  viewInvalidated(reason) {\n    this.adapter.pageInvalidated(reason);\n  }\n\n  // Frame element\n\n  frameLoaded(frame) {\n    this.notifyApplicationAfterFrameLoad(frame);\n  }\n\n  frameRendered(fetchResponse, frame) {\n    this.notifyApplicationAfterFrameRender(fetchResponse, frame);\n  }\n\n  // Application events\n\n  applicationAllowsFollowingLinkToLocation(link, location, ev) {\n    const event = this.notifyApplicationAfterClickingLinkToLocation(link, location, ev);\n    return !event.defaultPrevented\n  }\n\n  applicationAllowsVisitingLocation(location) {\n    const event = this.notifyApplicationBeforeVisitingLocation(location);\n    return !event.defaultPrevented\n  }\n\n  notifyApplicationAfterClickingLinkToLocation(link, location, event) {\n    return dispatch(\"turbo:click\", {\n      target: link,\n      detail: { url: location.href, originalEvent: event },\n      cancelable: true\n    })\n  }\n\n  notifyApplicationBeforeVisitingLocation(location) {\n    return dispatch(\"turbo:before-visit\", {\n      detail: { url: location.href },\n      cancelable: true\n    })\n  }\n\n  notifyApplicationAfterVisitingLocation(location, action) {\n    return dispatch(\"turbo:visit\", { detail: { url: location.href, action } })\n  }\n\n  notifyApplicationBeforeCachingSnapshot() {\n    return dispatch(\"turbo:before-cache\")\n  }\n\n  notifyApplicationBeforeRender(newBody, options) {\n    return dispatch(\"turbo:before-render\", {\n      detail: { newBody, ...options },\n      cancelable: true\n    })\n  }\n\n  notifyApplicationAfterRender(renderMethod) {\n    return dispatch(\"turbo:render\", { detail: { renderMethod } })\n  }\n\n  notifyApplicationAfterPageLoad(timing = {}) {\n    return dispatch(\"turbo:load\", {\n      detail: { url: this.location.href, timing }\n    })\n  }\n\n  notifyApplicationAfterVisitingSamePageLocation(oldURL, newURL) {\n    dispatchEvent(\n      new HashChangeEvent(\"hashchange\", {\n        oldURL: oldURL.toString(),\n        newURL: newURL.toString()\n      })\n    );\n  }\n\n  notifyApplicationAfterFrameLoad(frame) {\n    return dispatch(\"turbo:frame-load\", { target: frame })\n  }\n\n  notifyApplicationAfterFrameRender(fetchResponse, frame) {\n    return dispatch(\"turbo:frame-render\", {\n      detail: { fetchResponse },\n      target: frame,\n      cancelable: true\n    })\n  }\n\n  // Helpers\n\n  submissionIsNavigatable(form, submitter) {\n    if (config.forms.mode == \"off\") {\n      return false\n    } else {\n      const submitterIsNavigatable = submitter ? this.elementIsNavigatable(submitter) : true;\n\n      if (config.forms.mode == \"optin\") {\n        return submitterIsNavigatable && form.closest('[data-turbo=\"true\"]') != null\n      } else {\n        return submitterIsNavigatable && this.elementIsNavigatable(form)\n      }\n    }\n  }\n\n  elementIsNavigatable(element) {\n    const container = findClosestRecursively(element, \"[data-turbo]\");\n    const withinFrame = findClosestRecursively(element, \"turbo-frame\");\n\n    // Check if Drive is enabled on the session or we're within a Frame.\n    if (config.drive.enabled || withinFrame) {\n      // Element is navigatable by default, unless `data-turbo=\"false\"`.\n      if (container) {\n        return container.getAttribute(\"data-turbo\") != \"false\"\n      } else {\n        return true\n      }\n    } else {\n      // Element isn't navigatable by default, unless `data-turbo=\"true\"`.\n      if (container) {\n        return container.getAttribute(\"data-turbo\") == \"true\"\n      } else {\n        return false\n      }\n    }\n  }\n\n  // Private\n\n  getActionForLink(link) {\n    return getVisitAction(link) || \"advance\"\n  }\n\n  get snapshot() {\n    return this.view.snapshot\n  }\n}\n\n// Older versions of the Turbo Native adapters referenced the\n// `Location#absoluteURL` property in their implementations of\n// the `Adapter#visitProposedToLocation()` and `#visitStarted()`\n// methods. The Location class has since been removed in favor\n// of the DOM URL API, and accordingly all Adapter methods now\n// receive URL objects.\n//\n// We alias #absoluteURL to #toString() here to avoid crashing\n// older adapters which do not expect URL objects. We should\n// consider removing this support at some point in the future.\n\nfunction extendURLWithDeprecatedProperties(url) {\n  Object.defineProperties(url, deprecatedLocationPropertyDescriptors);\n}\n\nconst deprecatedLocationPropertyDescriptors = {\n  absoluteURL: {\n    get() {\n      return this.toString()\n    }\n  }\n};\n\nconst session = new Session(recentRequests);\nconst { cache, navigator: navigator$1 } = session;\n\n/**\n * Starts the main session.\n * This initialises any necessary observers such as those to monitor\n * link interactions.\n */\nfunction start() {\n  session.start();\n}\n\n/**\n * Registers an adapter for the main session.\n *\n * @param adapter Adapter to register\n */\nfunction registerAdapter(adapter) {\n  session.registerAdapter(adapter);\n}\n\n/**\n * Performs an application visit to the given location.\n *\n * @param location Location to visit (a URL or path)\n * @param options Options to apply\n * @param options.action Type of history navigation to apply (\"restore\",\n * \"replace\" or \"advance\")\n * @param options.historyChanged Specifies whether the browser history has\n * already been changed for this visit or not\n * @param options.referrer Specifies the referrer of this visit such that\n * navigations to the same page will not result in a new history entry.\n * @param options.snapshotHTML Cached snapshot to render\n * @param options.response Response of the specified location\n */\nfunction visit(location, options) {\n  session.visit(location, options);\n}\n\n/**\n * Connects a stream source to the main session.\n *\n * @param source Stream source to connect\n */\nfunction connectStreamSource(source) {\n  session.connectStreamSource(source);\n}\n\n/**\n * Disconnects a stream source from the main session.\n *\n * @param source Stream source to disconnect\n */\nfunction disconnectStreamSource(source) {\n  session.disconnectStreamSource(source);\n}\n\n/**\n * Renders a stream message to the main session by appending it to the\n * current document.\n *\n * @param message Message to render\n */\nfunction renderStreamMessage(message) {\n  session.renderStreamMessage(message);\n}\n\n/**\n * Removes all entries from the Turbo Drive page cache.\n * Call this when state has changed on the server that may affect cached pages.\n *\n * @deprecated since version 7.2.0 in favor of `Turbo.cache.clear()`\n */\nfunction clearCache() {\n  console.warn(\n    \"Please replace `Turbo.clearCache()` with `Turbo.cache.clear()`. The top-level function is deprecated and will be removed in a future version of Turbo.`\"\n  );\n  session.clearCache();\n}\n\n/**\n * Sets the delay after which the progress bar will appear during navigation.\n *\n * The progress bar appears after 500ms by default.\n *\n * Note that this method has no effect when used with the iOS or Android\n * adapters.\n *\n * @param delay Time to delay in milliseconds\n */\nfunction setProgressBarDelay(delay) {\n  console.warn(\n    \"Please replace `Turbo.setProgressBarDelay(delay)` with `Turbo.config.drive.progressBarDelay = delay`. The top-level function is deprecated and will be removed in a future version of Turbo.`\"\n  );\n  config.drive.progressBarDelay = delay;\n}\n\nfunction setConfirmMethod(confirmMethod) {\n  console.warn(\n    \"Please replace `Turbo.setConfirmMethod(confirmMethod)` with `Turbo.config.forms.confirm = confirmMethod`. The top-level function is deprecated and will be removed in a future version of Turbo.`\"\n  );\n  config.forms.confirm = confirmMethod;\n}\n\nfunction setFormMode(mode) {\n  console.warn(\n    \"Please replace `Turbo.setFormMode(mode)` with `Turbo.config.forms.mode = mode`. The top-level function is deprecated and will be removed in a future version of Turbo.`\"\n  );\n  config.forms.mode = mode;\n}\n\nvar Turbo = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  navigator: navigator$1,\n  session: session,\n  cache: cache,\n  PageRenderer: PageRenderer,\n  PageSnapshot: PageSnapshot,\n  FrameRenderer: FrameRenderer,\n  fetch: fetchWithTurboHeaders,\n  config: config,\n  start: start,\n  registerAdapter: registerAdapter,\n  visit: visit,\n  connectStreamSource: connectStreamSource,\n  disconnectStreamSource: disconnectStreamSource,\n  renderStreamMessage: renderStreamMessage,\n  clearCache: clearCache,\n  setProgressBarDelay: setProgressBarDelay,\n  setConfirmMethod: setConfirmMethod,\n  setFormMode: setFormMode\n});\n\nclass TurboFrameMissingError extends Error {}\n\nclass FrameController {\n  fetchResponseLoaded = (_fetchResponse) => Promise.resolve()\n  #currentFetchRequest = null\n  #resolveVisitPromise = () => {}\n  #connected = false\n  #hasBeenLoaded = false\n  #ignoredAttributes = new Set()\n  #shouldMorphFrame = false\n  action = null\n\n  constructor(element) {\n    this.element = element;\n    this.view = new FrameView(this, this.element);\n    this.appearanceObserver = new AppearanceObserver(this, this.element);\n    this.formLinkClickObserver = new FormLinkClickObserver(this, this.element);\n    this.linkInterceptor = new LinkInterceptor(this, this.element);\n    this.restorationIdentifier = uuid();\n    this.formSubmitObserver = new FormSubmitObserver(this, this.element);\n  }\n\n  // Frame delegate\n\n  connect() {\n    if (!this.#connected) {\n      this.#connected = true;\n      if (this.loadingStyle == FrameLoadingStyle.lazy) {\n        this.appearanceObserver.start();\n      } else {\n        this.#loadSourceURL();\n      }\n      this.formLinkClickObserver.start();\n      this.linkInterceptor.start();\n      this.formSubmitObserver.start();\n    }\n  }\n\n  disconnect() {\n    if (this.#connected) {\n      this.#connected = false;\n      this.appearanceObserver.stop();\n      this.formLinkClickObserver.stop();\n      this.linkInterceptor.stop();\n      this.formSubmitObserver.stop();\n    }\n  }\n\n  disabledChanged() {\n    if (this.loadingStyle == FrameLoadingStyle.eager) {\n      this.#loadSourceURL();\n    }\n  }\n\n  sourceURLChanged() {\n    if (this.#isIgnoringChangesTo(\"src\")) return\n\n    if (this.element.isConnected) {\n      this.complete = false;\n    }\n\n    if (this.loadingStyle == FrameLoadingStyle.eager || this.#hasBeenLoaded) {\n      this.#loadSourceURL();\n    }\n  }\n\n  sourceURLReloaded() {\n    const { refresh, src } = this.element;\n\n    this.#shouldMorphFrame = src && refresh === \"morph\";\n\n    this.element.removeAttribute(\"complete\");\n    this.element.src = null;\n    this.element.src = src;\n    return this.element.loaded\n  }\n\n  loadingStyleChanged() {\n    if (this.loadingStyle == FrameLoadingStyle.lazy) {\n      this.appearanceObserver.start();\n    } else {\n      this.appearanceObserver.stop();\n      this.#loadSourceURL();\n    }\n  }\n\n  async #loadSourceURL() {\n    if (this.enabled && this.isActive && !this.complete && this.sourceURL) {\n      this.element.loaded = this.#visit(expandURL(this.sourceURL));\n      this.appearanceObserver.stop();\n      await this.element.loaded;\n      this.#hasBeenLoaded = true;\n    }\n  }\n\n  async loadResponse(fetchResponse) {\n    if (fetchResponse.redirected || (fetchResponse.succeeded && fetchResponse.isHTML)) {\n      this.sourceURL = fetchResponse.response.url;\n    }\n\n    try {\n      const html = await fetchResponse.responseHTML;\n      if (html) {\n        const document = parseHTMLDocument(html);\n        const pageSnapshot = PageSnapshot.fromDocument(document);\n\n        if (pageSnapshot.isVisitable) {\n          await this.#loadFrameResponse(fetchResponse, document);\n        } else {\n          await this.#handleUnvisitableFrameResponse(fetchResponse);\n        }\n      }\n    } finally {\n      this.#shouldMorphFrame = false;\n      this.fetchResponseLoaded = () => Promise.resolve();\n    }\n  }\n\n  // Appearance observer delegate\n\n  elementAppearedInViewport(element) {\n    this.proposeVisitIfNavigatedWithAction(element, getVisitAction(element));\n    this.#loadSourceURL();\n  }\n\n  // Form link click observer delegate\n\n  willSubmitFormLinkToLocation(link) {\n    return this.#shouldInterceptNavigation(link)\n  }\n\n  submittedFormLinkToLocation(link, _location, form) {\n    const frame = this.#findFrameElement(link);\n    if (frame) form.setAttribute(\"data-turbo-frame\", frame.id);\n  }\n\n  // Link interceptor delegate\n\n  shouldInterceptLinkClick(element, _location, _event) {\n    return this.#shouldInterceptNavigation(element)\n  }\n\n  linkClickIntercepted(element, location) {\n    this.#navigateFrame(element, location);\n  }\n\n  // Form submit observer delegate\n\n  willSubmitForm(element, submitter) {\n    return element.closest(\"turbo-frame\") == this.element && this.#shouldInterceptNavigation(element, submitter)\n  }\n\n  formSubmitted(element, submitter) {\n    if (this.formSubmission) {\n      this.formSubmission.stop();\n    }\n\n    this.formSubmission = new FormSubmission(this, element, submitter);\n    const { fetchRequest } = this.formSubmission;\n    this.prepareRequest(fetchRequest);\n    this.formSubmission.start();\n  }\n\n  // Fetch request delegate\n\n  prepareRequest(request) {\n    request.headers[\"Turbo-Frame\"] = this.id;\n\n    if (this.currentNavigationElement?.hasAttribute(\"data-turbo-stream\")) {\n      request.acceptResponseType(StreamMessage.contentType);\n    }\n  }\n\n  requestStarted(_request) {\n    markAsBusy(this.element);\n  }\n\n  requestPreventedHandlingResponse(_request, _response) {\n    this.#resolveVisitPromise();\n  }\n\n  async requestSucceededWithResponse(request, response) {\n    await this.loadResponse(response);\n    this.#resolveVisitPromise();\n  }\n\n  async requestFailedWithResponse(request, response) {\n    await this.loadResponse(response);\n    this.#resolveVisitPromise();\n  }\n\n  requestErrored(request, error) {\n    console.error(error);\n    this.#resolveVisitPromise();\n  }\n\n  requestFinished(_request) {\n    clearBusyState(this.element);\n  }\n\n  // Form submission delegate\n\n  formSubmissionStarted({ formElement }) {\n    markAsBusy(formElement, this.#findFrameElement(formElement));\n  }\n\n  formSubmissionSucceededWithResponse(formSubmission, response) {\n    const frame = this.#findFrameElement(formSubmission.formElement, formSubmission.submitter);\n\n    frame.delegate.proposeVisitIfNavigatedWithAction(frame, getVisitAction(formSubmission.submitter, formSubmission.formElement, frame));\n    frame.delegate.loadResponse(response);\n\n    if (!formSubmission.isSafe) {\n      session.clearCache();\n    }\n  }\n\n  formSubmissionFailedWithResponse(formSubmission, fetchResponse) {\n    this.element.delegate.loadResponse(fetchResponse);\n    session.clearCache();\n  }\n\n  formSubmissionErrored(formSubmission, error) {\n    console.error(error);\n  }\n\n  formSubmissionFinished({ formElement }) {\n    clearBusyState(formElement, this.#findFrameElement(formElement));\n  }\n\n  // View delegate\n\n  allowsImmediateRender({ element: newFrame }, options) {\n    const event = dispatch(\"turbo:before-frame-render\", {\n      target: this.element,\n      detail: { newFrame, ...options },\n      cancelable: true\n    });\n\n    const {\n      defaultPrevented,\n      detail: { render }\n    } = event;\n\n    if (this.view.renderer && render) {\n      this.view.renderer.renderElement = render;\n    }\n\n    return !defaultPrevented\n  }\n\n  viewRenderedSnapshot(_snapshot, _isPreview, _renderMethod) {}\n\n  preloadOnLoadLinksForView(element) {\n    session.preloadOnLoadLinksForView(element);\n  }\n\n  viewInvalidated() {}\n\n  // Frame renderer delegate\n\n  willRenderFrame(currentElement, _newElement) {\n    this.previousFrameElement = currentElement.cloneNode(true);\n  }\n\n  visitCachedSnapshot = ({ element }) => {\n    const frame = element.querySelector(\"#\" + this.element.id);\n\n    if (frame && this.previousFrameElement) {\n      frame.replaceChildren(...this.previousFrameElement.children);\n    }\n\n    delete this.previousFrameElement;\n  }\n\n  // Private\n\n  async #loadFrameResponse(fetchResponse, document) {\n    const newFrameElement = await this.extractForeignFrameElement(document.body);\n    const rendererClass = this.#shouldMorphFrame ? MorphingFrameRenderer : FrameRenderer;\n\n    if (newFrameElement) {\n      const snapshot = new Snapshot(newFrameElement);\n      const renderer = new rendererClass(this, this.view.snapshot, snapshot, false, false);\n      if (this.view.renderPromise) await this.view.renderPromise;\n      this.changeHistory();\n\n      await this.view.render(renderer);\n      this.complete = true;\n      session.frameRendered(fetchResponse, this.element);\n      session.frameLoaded(this.element);\n      await this.fetchResponseLoaded(fetchResponse);\n    } else if (this.#willHandleFrameMissingFromResponse(fetchResponse)) {\n      this.#handleFrameMissingFromResponse(fetchResponse);\n    }\n  }\n\n  async #visit(url) {\n    const request = new FetchRequest(this, FetchMethod.get, url, new URLSearchParams(), this.element);\n\n    this.#currentFetchRequest?.cancel();\n    this.#currentFetchRequest = request;\n\n    return new Promise((resolve) => {\n      this.#resolveVisitPromise = () => {\n        this.#resolveVisitPromise = () => {};\n        this.#currentFetchRequest = null;\n        resolve();\n      };\n      request.perform();\n    })\n  }\n\n  #navigateFrame(element, url, submitter) {\n    const frame = this.#findFrameElement(element, submitter);\n\n    frame.delegate.proposeVisitIfNavigatedWithAction(frame, getVisitAction(submitter, element, frame));\n\n    this.#withCurrentNavigationElement(element, () => {\n      frame.src = url;\n    });\n  }\n\n  proposeVisitIfNavigatedWithAction(frame, action = null) {\n    this.action = action;\n\n    if (this.action) {\n      const pageSnapshot = PageSnapshot.fromElement(frame).clone();\n      const { visitCachedSnapshot } = frame.delegate;\n\n      frame.delegate.fetchResponseLoaded = async (fetchResponse) => {\n        if (frame.src) {\n          const { statusCode, redirected } = fetchResponse;\n          const responseHTML = await fetchResponse.responseHTML;\n          const response = { statusCode, redirected, responseHTML };\n          const options = {\n            response,\n            visitCachedSnapshot,\n            willRender: false,\n            updateHistory: false,\n            restorationIdentifier: this.restorationIdentifier,\n            snapshot: pageSnapshot\n          };\n\n          if (this.action) options.action = this.action;\n\n          session.visit(frame.src, options);\n        }\n      };\n    }\n  }\n\n  changeHistory() {\n    if (this.action) {\n      const method = getHistoryMethodForAction(this.action);\n      session.history.update(method, expandURL(this.element.src || \"\"), this.restorationIdentifier);\n    }\n  }\n\n  async #handleUnvisitableFrameResponse(fetchResponse) {\n    console.warn(\n      `The response (${fetchResponse.statusCode}) from <turbo-frame id=\"${this.element.id}\"> is performing a full page visit due to turbo-visit-control.`\n    );\n\n    await this.#visitResponse(fetchResponse.response);\n  }\n\n  #willHandleFrameMissingFromResponse(fetchResponse) {\n    this.element.setAttribute(\"complete\", \"\");\n\n    const response = fetchResponse.response;\n    const visit = async (url, options) => {\n      if (url instanceof Response) {\n        this.#visitResponse(url);\n      } else {\n        session.visit(url, options);\n      }\n    };\n\n    const event = dispatch(\"turbo:frame-missing\", {\n      target: this.element,\n      detail: { response, visit },\n      cancelable: true\n    });\n\n    return !event.defaultPrevented\n  }\n\n  #handleFrameMissingFromResponse(fetchResponse) {\n    this.view.missing();\n    this.#throwFrameMissingError(fetchResponse);\n  }\n\n  #throwFrameMissingError(fetchResponse) {\n    const message = `The response (${fetchResponse.statusCode}) did not contain the expected <turbo-frame id=\"${this.element.id}\"> and will be ignored. To perform a full page visit instead, set turbo-visit-control to reload.`;\n    throw new TurboFrameMissingError(message)\n  }\n\n  async #visitResponse(response) {\n    const wrapped = new FetchResponse(response);\n    const responseHTML = await wrapped.responseHTML;\n    const { location, redirected, statusCode } = wrapped;\n\n    return session.visit(location, { response: { redirected, statusCode, responseHTML } })\n  }\n\n  #findFrameElement(element, submitter) {\n    const id = getAttribute(\"data-turbo-frame\", submitter, element) || this.element.getAttribute(\"target\");\n    return getFrameElementById(id) ?? this.element\n  }\n\n  async extractForeignFrameElement(container) {\n    let element;\n    const id = CSS.escape(this.id);\n\n    try {\n      element = activateElement(container.querySelector(`turbo-frame#${id}`), this.sourceURL);\n      if (element) {\n        return element\n      }\n\n      element = activateElement(container.querySelector(`turbo-frame[src][recurse~=${id}]`), this.sourceURL);\n      if (element) {\n        await element.loaded;\n        return await this.extractForeignFrameElement(element)\n      }\n    } catch (error) {\n      console.error(error);\n      return new FrameElement()\n    }\n\n    return null\n  }\n\n  #formActionIsVisitable(form, submitter) {\n    const action = getAction$1(form, submitter);\n\n    return locationIsVisitable(expandURL(action), this.rootLocation)\n  }\n\n  #shouldInterceptNavigation(element, submitter) {\n    const id = getAttribute(\"data-turbo-frame\", submitter, element) || this.element.getAttribute(\"target\");\n\n    if (element instanceof HTMLFormElement && !this.#formActionIsVisitable(element, submitter)) {\n      return false\n    }\n\n    if (!this.enabled || id == \"_top\") {\n      return false\n    }\n\n    if (id) {\n      const frameElement = getFrameElementById(id);\n      if (frameElement) {\n        return !frameElement.disabled\n      }\n    }\n\n    if (!session.elementIsNavigatable(element)) {\n      return false\n    }\n\n    if (submitter && !session.elementIsNavigatable(submitter)) {\n      return false\n    }\n\n    return true\n  }\n\n  // Computed properties\n\n  get id() {\n    return this.element.id\n  }\n\n  get enabled() {\n    return !this.element.disabled\n  }\n\n  get sourceURL() {\n    if (this.element.src) {\n      return this.element.src\n    }\n  }\n\n  set sourceURL(sourceURL) {\n    this.#ignoringChangesToAttribute(\"src\", () => {\n      this.element.src = sourceURL ?? null;\n    });\n  }\n\n  get loadingStyle() {\n    return this.element.loading\n  }\n\n  get isLoading() {\n    return this.formSubmission !== undefined || this.#resolveVisitPromise() !== undefined\n  }\n\n  get complete() {\n    return this.element.hasAttribute(\"complete\")\n  }\n\n  set complete(value) {\n    if (value) {\n      this.element.setAttribute(\"complete\", \"\");\n    } else {\n      this.element.removeAttribute(\"complete\");\n    }\n  }\n\n  get isActive() {\n    return this.element.isActive && this.#connected\n  }\n\n  get rootLocation() {\n    const meta = this.element.ownerDocument.querySelector(`meta[name=\"turbo-root\"]`);\n    const root = meta?.content ?? \"/\";\n    return expandURL(root)\n  }\n\n  #isIgnoringChangesTo(attributeName) {\n    return this.#ignoredAttributes.has(attributeName)\n  }\n\n  #ignoringChangesToAttribute(attributeName, callback) {\n    this.#ignoredAttributes.add(attributeName);\n    callback();\n    this.#ignoredAttributes.delete(attributeName);\n  }\n\n  #withCurrentNavigationElement(element, callback) {\n    this.currentNavigationElement = element;\n    callback();\n    delete this.currentNavigationElement;\n  }\n}\n\nfunction getFrameElementById(id) {\n  if (id != null) {\n    const element = document.getElementById(id);\n    if (element instanceof FrameElement) {\n      return element\n    }\n  }\n}\n\nfunction activateElement(element, currentURL) {\n  if (element) {\n    const src = element.getAttribute(\"src\");\n    if (src != null && currentURL != null && urlsAreEqual(src, currentURL)) {\n      throw new Error(`Matching <turbo-frame id=\"${element.id}\"> element has a source URL which references itself`)\n    }\n    if (element.ownerDocument !== document) {\n      element = document.importNode(element, true);\n    }\n\n    if (element instanceof FrameElement) {\n      element.connectedCallback();\n      element.disconnectedCallback();\n      return element\n    }\n  }\n}\n\nconst StreamActions = {\n  after() {\n    this.targetElements.forEach((e) => e.parentElement?.insertBefore(this.templateContent, e.nextSibling));\n  },\n\n  append() {\n    this.removeDuplicateTargetChildren();\n    this.targetElements.forEach((e) => e.append(this.templateContent));\n  },\n\n  before() {\n    this.targetElements.forEach((e) => e.parentElement?.insertBefore(this.templateContent, e));\n  },\n\n  prepend() {\n    this.removeDuplicateTargetChildren();\n    this.targetElements.forEach((e) => e.prepend(this.templateContent));\n  },\n\n  remove() {\n    this.targetElements.forEach((e) => e.remove());\n  },\n\n  replace() {\n    const method = this.getAttribute(\"method\");\n\n    this.targetElements.forEach((targetElement) => {\n      if (method === \"morph\") {\n        morphElements(targetElement, this.templateContent);\n      } else {\n        targetElement.replaceWith(this.templateContent);\n      }\n    });\n  },\n\n  update() {\n    const method = this.getAttribute(\"method\");\n\n    this.targetElements.forEach((targetElement) => {\n      if (method === \"morph\") {\n        morphChildren(targetElement, this.templateContent);\n      } else {\n        targetElement.innerHTML = \"\";\n        targetElement.append(this.templateContent);\n      }\n    });\n  },\n\n  refresh() {\n    session.refresh(this.baseURI, this.requestId);\n  }\n};\n\n// <turbo-stream action=replace target=id><template>...\n\n/**\n * Renders updates to the page from a stream of messages.\n *\n * Using the `action` attribute, this can be configured one of eight ways:\n *\n * - `after` - inserts the result after the target\n * - `append` - appends the result to the target\n * - `before` - inserts the result before the target\n * - `prepend` - prepends the result to the target\n * - `refresh` - initiates a page refresh\n * - `remove` - removes the target\n * - `replace` - replaces the outer HTML of the target\n * - `update` - replaces the inner HTML of the target\n *\n * @customElement turbo-stream\n * @example\n *   <turbo-stream action=\"append\" target=\"dom_id\">\n *     <template>\n *       Content to append to target designated with the dom_id.\n *     </template>\n *   </turbo-stream>\n */\nclass StreamElement extends HTMLElement {\n  static async renderElement(newElement) {\n    await newElement.performAction();\n  }\n\n  async connectedCallback() {\n    try {\n      await this.render();\n    } catch (error) {\n      console.error(error);\n    } finally {\n      this.disconnect();\n    }\n  }\n\n  async render() {\n    return (this.renderPromise ??= (async () => {\n      const event = this.beforeRenderEvent;\n\n      if (this.dispatchEvent(event)) {\n        await nextRepaint();\n        await event.detail.render(this);\n      }\n    })())\n  }\n\n  disconnect() {\n    try {\n      this.remove();\n      // eslint-disable-next-line no-empty\n    } catch {}\n  }\n\n  /**\n   * Removes duplicate children (by ID)\n   */\n  removeDuplicateTargetChildren() {\n    this.duplicateChildren.forEach((c) => c.remove());\n  }\n\n  /**\n   * Gets the list of duplicate children (i.e. those with the same ID)\n   */\n  get duplicateChildren() {\n    const existingChildren = this.targetElements.flatMap((e) => [...e.children]).filter((c) => !!c.getAttribute(\"id\"));\n    const newChildrenIds = [...(this.templateContent?.children || [])].filter((c) => !!c.getAttribute(\"id\")).map((c) => c.getAttribute(\"id\"));\n\n    return existingChildren.filter((c) => newChildrenIds.includes(c.getAttribute(\"id\")))\n  }\n\n  /**\n   * Gets the action function to be performed.\n   */\n  get performAction() {\n    if (this.action) {\n      const actionFunction = StreamActions[this.action];\n      if (actionFunction) {\n        return actionFunction\n      }\n      this.#raise(\"unknown action\");\n    }\n    this.#raise(\"action attribute is missing\");\n  }\n\n  /**\n   * Gets the target elements which the template will be rendered to.\n   */\n  get targetElements() {\n    if (this.target) {\n      return this.targetElementsById\n    } else if (this.targets) {\n      return this.targetElementsByQuery\n    } else {\n      this.#raise(\"target or targets attribute is missing\");\n    }\n  }\n\n  /**\n   * Gets the contents of the main `<template>`.\n   */\n  get templateContent() {\n    return this.templateElement.content.cloneNode(true)\n  }\n\n  /**\n   * Gets the main `<template>` used for rendering\n   */\n  get templateElement() {\n    if (this.firstElementChild === null) {\n      const template = this.ownerDocument.createElement(\"template\");\n      this.appendChild(template);\n      return template\n    } else if (this.firstElementChild instanceof HTMLTemplateElement) {\n      return this.firstElementChild\n    }\n    this.#raise(\"first child element must be a <template> element\");\n  }\n\n  /**\n   * Gets the current action.\n   */\n  get action() {\n    return this.getAttribute(\"action\")\n  }\n\n  /**\n   * Gets the current target (an element ID) to which the result will\n   * be rendered.\n   */\n  get target() {\n    return this.getAttribute(\"target\")\n  }\n\n  /**\n   * Gets the current \"targets\" selector (a CSS selector)\n   */\n  get targets() {\n    return this.getAttribute(\"targets\")\n  }\n\n  /**\n   * Reads the request-id attribute\n   */\n  get requestId() {\n    return this.getAttribute(\"request-id\")\n  }\n\n  #raise(message) {\n    throw new Error(`${this.description}: ${message}`)\n  }\n\n  get description() {\n    return (this.outerHTML.match(/<[^>]+>/) ?? [])[0] ?? \"<turbo-stream>\"\n  }\n\n  get beforeRenderEvent() {\n    return new CustomEvent(\"turbo:before-stream-render\", {\n      bubbles: true,\n      cancelable: true,\n      detail: { newStream: this, render: StreamElement.renderElement }\n    })\n  }\n\n  get targetElementsById() {\n    const element = this.ownerDocument?.getElementById(this.target);\n\n    if (element !== null) {\n      return [element]\n    } else {\n      return []\n    }\n  }\n\n  get targetElementsByQuery() {\n    const elements = this.ownerDocument?.querySelectorAll(this.targets);\n\n    if (elements.length !== 0) {\n      return Array.prototype.slice.call(elements)\n    } else {\n      return []\n    }\n  }\n}\n\nclass StreamSourceElement extends HTMLElement {\n  streamSource = null\n\n  connectedCallback() {\n    this.streamSource = this.src.match(/^ws{1,2}:/) ? new WebSocket(this.src) : new EventSource(this.src);\n\n    connectStreamSource(this.streamSource);\n  }\n\n  disconnectedCallback() {\n    if (this.streamSource) {\n      this.streamSource.close();\n\n      disconnectStreamSource(this.streamSource);\n    }\n  }\n\n  get src() {\n    return this.getAttribute(\"src\") || \"\"\n  }\n}\n\nFrameElement.delegateConstructor = FrameController;\n\nif (customElements.get(\"turbo-frame\") === undefined) {\n  customElements.define(\"turbo-frame\", FrameElement);\n}\n\nif (customElements.get(\"turbo-stream\") === undefined) {\n  customElements.define(\"turbo-stream\", StreamElement);\n}\n\nif (customElements.get(\"turbo-stream-source\") === undefined) {\n  customElements.define(\"turbo-stream-source\", StreamSourceElement);\n}\n\n(() => {\n  let element = document.currentScript;\n  if (!element) return\n  if (element.hasAttribute(\"data-turbo-suppress-warning\")) return\n\n  element = element.parentElement;\n  while (element) {\n    if (element == document.body) {\n      return console.warn(\n        unindent`\n        You are loading Turbo from a <script> element inside the <body> element. This is probably not what you meant to do!\n\n        Load your application’s JavaScript bundle inside the <head> element instead. <script> elements in <body> are evaluated with each page change.\n\n        For more information, see: https://turbo.hotwired.dev/handbook/building#working-with-script-elements\n\n        ——\n        Suppress this warning by adding a \"data-turbo-suppress-warning\" attribute to: %s\n      `,\n        element.outerHTML\n      )\n    }\n\n    element = element.parentElement;\n  }\n})();\n\nwindow.Turbo = { ...Turbo, StreamActions };\nstart();\n\nexport { FetchEnctype, FetchMethod, FetchRequest, FetchResponse, FrameElement, FrameLoadingStyle, FrameRenderer, PageRenderer, PageSnapshot, StreamActions, StreamElement, StreamSourceElement, cache, clearCache, config, connectStreamSource, disconnectStreamSource, fetchWithTurboHeaders as fetch, fetchEnctypeFromString, fetchMethodFromString, isSafe, navigator$1 as navigator, registerAdapter, renderStreamMessage, session, setConfirmMethod, setFormMode, setProgressBarDelay, start, visit };\n", "let consumer\n\nexport async function getConsumer() {\n  return consumer || setConsumer(createConsumer().then(setConsumer))\n}\n\nexport function setConsumer(newConsumer) {\n  return consumer = newConsumer\n}\n\nexport async function createConsumer() {\n  const { createConsumer } = await import(/* webpackChunkName: \"actioncable\" */ \"@rails/actioncable/src\")\n  return createConsumer()\n}\n\nexport async function subscribeTo(channel, mixin) {\n  const { subscriptions } = await getConsumer()\n  return subscriptions.create(channel, mixin)\n}\n", "// Based on https://github.com/nathan7/snakeize\n//\n// This software is released under the MIT license:\n// Permission is hereby granted, free of charge, to any person obtaining a copy of\n// this software and associated documentation files (the \"Software\"), to deal in\n// the Software without restriction, including without limitation the rights to\n// use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\n// the Software, and to permit persons to whom the Software is furnished to do so,\n// subject to the following conditions:\n\n// The above copyright notice and this permission notice shall be included in all\n// copies or substantial portions of the Software.\n\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\n// FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\n// COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\n// IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n// CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\nexport default function walk (obj) {\n    if (!obj || typeof obj !== 'object') return obj;\n    if (obj instanceof Date || obj instanceof RegExp) return obj;\n    if (Array.isArray(obj)) return obj.map(walk);\n    return Object.keys(obj).reduce(function (acc, key) {\n        var camel = key[0].toLowerCase() + key.slice(1).replace(/([A-Z]+)/g, function (m, x) {\n            return '_' + x.toLowerCase();\n        });\n        acc[camel] = walk(obj[key]);\n        return acc;\n    }, {});\n};", "import { connectStreamSource, disconnectStreamSource } from \"@hotwired/turbo\"\nimport { subscribeTo } from \"./cable\"\nimport snakeize from \"./snakeize\"\n\nclass TurboCableStreamSourceElement extends HTMLElement {\n  static observedAttributes = [\"channel\", \"signed-stream-name\"]\n\n  async connectedCallback() {\n    connectStreamSource(this)\n    this.subscription = await subscribeTo(this.channel, {\n      received: this.dispatchMessageEvent.bind(this),\n      connected: this.subscriptionConnected.bind(this),\n      disconnected: this.subscriptionDisconnected.bind(this)\n    })\n  }\n\n  disconnectedCallback() {\n    disconnectStreamSource(this)\n    if (this.subscription) this.subscription.unsubscribe()\n    this.subscriptionDisconnected()\n  }\n\n  attributeChangedCallback() {\n    if (this.subscription) {\n      this.disconnectedCallback()\n      this.connectedCallback()\n    }\n  }\n\n  dispatchMessageEvent(data) {\n    const event = new MessageEvent(\"message\", { data })\n    return this.dispatchEvent(event)\n  }\n\n  subscriptionConnected() {\n    this.setAttribute(\"connected\", \"\")\n  }\n\n  subscriptionDisconnected() {\n    this.removeAttribute(\"connected\")\n  }\n\n  get channel() {\n    const channel = this.getAttribute(\"channel\")\n    const signed_stream_name = this.getAttribute(\"signed-stream-name\")\n    return { channel, signed_stream_name, ...snakeize({ ...this.dataset }) }\n  }\n}\n\n\nif (customElements.get(\"turbo-cable-stream-source\") === undefined) {\n  customElements.define(\"turbo-cable-stream-source\", TurboCableStreamSourceElement)\n}\n", "import \"./cable_stream_source_element\"\n\nimport * as Turbo from \"@hotwired/turbo\"\nexport { Turbo }\n\nimport * as cable from \"./cable\"\nexport { cable }\n\nimport { encodeMethodIntoRequestBody } from \"./fetch_requests\"\n\nwindow.Turbo = Turbo\n\naddEventListener(\"turbo:before-fetch-request\", encodeMethodIntoRequestBody)\n", "export function encodeMethodIntoRequestBody(event) {\n  if (event.target instanceof HTMLFormElement) {\n    const { target: form, detail: { fetchOptions } } = event\n\n    form.addEventListener(\"turbo:submit-start\", ({ detail: { formSubmission: { submitter } } }) => {\n      const body = isBodyInit(fetchOptions.body) ? fetchOptions.body : new URLSearchParams()\n      const method = determineFetchMethod(submitter, body, form)\n\n      if (!/get/i.test(method)) {\n        if (/post/i.test(method)) {\n          body.delete(\"_method\")\n        } else {\n          body.set(\"_method\", method)\n        }\n\n        fetchOptions.method = \"post\"\n      }\n    }, { once: true })\n  }\n}\n\nfunction determineFetchMethod(submitter, body, form) {\n  const formMethod = determineFormMethod(submitter)\n  const overrideMethod = body.get(\"_method\")\n  const method = form.getAttribute(\"method\") || \"get\"\n\n  if (typeof formMethod == \"string\") {\n    return formMethod\n  } else if (typeof overrideMethod == \"string\") {\n    return overrideMethod\n  } else {\n    return method\n  }\n}\n\nfunction determineFormMethod(submitter) {\n  if (submitter instanceof HTMLButtonElement || submitter instanceof HTMLInputElement) {\n    // Rails 7 ActionView::Helpers::FormBuilder#button method has an override\n    // for formmethod if the button does not have name or value attributes\n    // set, which is the default. This means that if you use <%= f.button\n    // formmethod: :delete %>, it will generate a <button name=\"_method\"\n    // value=\"delete\" formmethod=\"post\">. Therefore, if the submitter's name\n    // is already _method, it's value attribute already contains the desired\n    // method.\n    if (submitter.name === '_method') {\n      return submitter.value\n    } else if (submitter.hasAttribute(\"formmethod\")) {\n      return submitter.formMethod\n    } else {\n      return null\n    }\n  } else {\n    return null\n  }\n}\n\nfunction isBodyInit(body) {\n  return body instanceof FormData || body instanceof URLSearchParams\n}\n", "export default {\n  logger: typeof console !== \"undefined\" ? console : undefined,\n  WebSocket: typeof WebSocket !== \"undefined\" ? WebSocket : undefined,\n}\n", "import adapters from \"./adapters\"\n\n// The logger is disabled by default. You can enable it with:\n//\n//   ActionCable.logger.enabled = true\n//\n//   Example:\n//\n//   import * as ActionCable from '@rails/actioncable'\n//\n//   ActionCable.logger.enabled = true\n//   ActionCable.logger.log('Connection Established.')\n//\n\nexport default {\n  log(...messages) {\n    if (this.enabled) {\n      messages.push(Date.now())\n      adapters.logger.log(\"[ActionCable]\", ...messages)\n    }\n  },\n}\n", "import logger from \"./logger\"\n\n// Responsible for ensuring the cable connection is in good health by validating the heartbeat pings sent from the server, and attempting\n// revival reconnections if things go astray. Internal class, not intended for direct user manipulation.\n\nconst now = () => new Date().getTime()\n\nconst secondsSince = time => (now() - time) / 1000\n\nclass ConnectionMonitor {\n  constructor(connection) {\n    this.visibilityDidChange = this.visibilityDidChange.bind(this)\n    this.connection = connection\n    this.reconnectAttempts = 0\n  }\n\n  start() {\n    if (!this.isRunning()) {\n      this.startedAt = now()\n      delete this.stoppedAt\n      this.startPolling()\n      addEventListener(\"visibilitychange\", this.visibilityDidChange)\n      logger.log(`ConnectionMonitor started. stale threshold = ${this.constructor.staleThreshold} s`)\n    }\n  }\n\n  stop() {\n    if (this.isRunning()) {\n      this.stoppedAt = now()\n      this.stopPolling()\n      removeEventListener(\"visibilitychange\", this.visibilityDidChange)\n      logger.log(\"ConnectionMonitor stopped\")\n    }\n  }\n\n  isRunning() {\n    return this.startedAt && !this.stoppedAt\n  }\n\n  recordMessage() {\n    this.pingedAt = now()\n  }\n\n  recordConnect() {\n    this.reconnectAttempts = 0\n    delete this.disconnectedAt\n    logger.log(\"ConnectionMonitor recorded connect\")\n  }\n\n  recordDisconnect() {\n    this.disconnectedAt = now()\n    logger.log(\"ConnectionMonitor recorded disconnect\")\n  }\n\n  // Private\n\n  startPolling() {\n    this.stopPolling()\n    this.poll()\n  }\n\n  stopPolling() {\n    clearTimeout(this.pollTimeout)\n  }\n\n  poll() {\n    this.pollTimeout = setTimeout(() => {\n      this.reconnectIfStale()\n      this.poll()\n    }\n    , this.getPollInterval())\n  }\n\n  getPollInterval() {\n    const { staleThreshold, reconnectionBackoffRate } = this.constructor\n    const backoff = Math.pow(1 + reconnectionBackoffRate, Math.min(this.reconnectAttempts, 10))\n    const jitterMax = this.reconnectAttempts === 0 ? 1.0 : reconnectionBackoffRate\n    const jitter = jitterMax * Math.random()\n    return staleThreshold * 1000 * backoff * (1 + jitter)\n  }\n\n  reconnectIfStale() {\n    if (this.connectionIsStale()) {\n      logger.log(`ConnectionMonitor detected stale connection. reconnectAttempts = ${this.reconnectAttempts}, time stale = ${secondsSince(this.refreshedAt)} s, stale threshold = ${this.constructor.staleThreshold} s`)\n      this.reconnectAttempts++\n      if (this.disconnectedRecently()) {\n        logger.log(`ConnectionMonitor skipping reopening recent disconnect. time disconnected = ${secondsSince(this.disconnectedAt)} s`)\n      } else {\n        logger.log(\"ConnectionMonitor reopening\")\n        this.connection.reopen()\n      }\n    }\n  }\n\n  get refreshedAt() {\n    return this.pingedAt ? this.pingedAt : this.startedAt\n  }\n\n  connectionIsStale() {\n    return secondsSince(this.refreshedAt) > this.constructor.staleThreshold\n  }\n\n  disconnectedRecently() {\n    return this.disconnectedAt && (secondsSince(this.disconnectedAt) < this.constructor.staleThreshold)\n  }\n\n  visibilityDidChange() {\n    if (document.visibilityState === \"visible\") {\n      setTimeout(() => {\n        if (this.connectionIsStale() || !this.connection.isOpen()) {\n          logger.log(`ConnectionMonitor reopening stale connection on visibilitychange. visibilityState = ${document.visibilityState}`)\n          this.connection.reopen()\n        }\n      }\n      , 200)\n    }\n  }\n\n}\n\nConnectionMonitor.staleThreshold = 6 // Server::Connections::BEAT_INTERVAL * 2 (missed two pings)\nConnectionMonitor.reconnectionBackoffRate = 0.15\n\nexport default ConnectionMonitor\n", "export default {\n  \"message_types\": {\n    \"welcome\": \"welcome\",\n    \"disconnect\": \"disconnect\",\n    \"ping\": \"ping\",\n    \"confirmation\": \"confirm_subscription\",\n    \"rejection\": \"reject_subscription\"\n  },\n  \"disconnect_reasons\": {\n    \"unauthorized\": \"unauthorized\",\n    \"invalid_request\": \"invalid_request\",\n    \"server_restart\": \"server_restart\",\n    \"remote\": \"remote\"\n  },\n  \"default_mount_path\": \"/cable\",\n  \"protocols\": [\n    \"actioncable-v1-json\",\n    \"actioncable-unsupported\"\n  ]\n}\n", "import adapters from \"./adapters\"\nimport ConnectionMonitor from \"./connection_monitor\"\nimport INTERNAL from \"./internal\"\nimport logger from \"./logger\"\n\n// Encapsulate the cable connection held by the consumer. This is an internal class not intended for direct user manipulation.\n\nconst {message_types, protocols} = INTERNAL\nconst supportedProtocols = protocols.slice(0, protocols.length - 1)\n\nconst indexOf = [].indexOf\n\nclass Connection {\n  constructor(consumer) {\n    this.open = this.open.bind(this)\n    this.consumer = consumer\n    this.subscriptions = this.consumer.subscriptions\n    this.monitor = new ConnectionMonitor(this)\n    this.disconnected = true\n  }\n\n  send(data) {\n    if (this.isOpen()) {\n      this.webSocket.send(JSON.stringify(data))\n      return true\n    } else {\n      return false\n    }\n  }\n\n  open() {\n    if (this.isActive()) {\n      logger.log(`Attempted to open WebSocket, but existing socket is ${this.getState()}`)\n      return false\n    } else {\n      const socketProtocols = [...protocols, ...this.consumer.subprotocols || []]\n      logger.log(`Opening WebSocket, current state is ${this.getState()}, subprotocols: ${socketProtocols}`)\n      if (this.webSocket) { this.uninstallEventHandlers() }\n      this.webSocket = new adapters.WebSocket(this.consumer.url, socketProtocols)\n      this.installEventHandlers()\n      this.monitor.start()\n      return true\n    }\n  }\n\n  close({allowReconnect} = {allowReconnect: true}) {\n    if (!allowReconnect) { this.monitor.stop() }\n    // Avoid closing websockets in a \"connecting\" state due to Safari 15.1+ bug. See: https://github.com/rails/rails/issues/43835#issuecomment-1002288478\n    if (this.isOpen()) {\n      return this.webSocket.close()\n    }\n  }\n\n  reopen() {\n    logger.log(`Reopening WebSocket, current state is ${this.getState()}`)\n    if (this.isActive()) {\n      try {\n        return this.close()\n      } catch (error) {\n        logger.log(\"Failed to reopen WebSocket\", error)\n      }\n      finally {\n        logger.log(`Reopening WebSocket in ${this.constructor.reopenDelay}ms`)\n        setTimeout(this.open, this.constructor.reopenDelay)\n      }\n    } else {\n      return this.open()\n    }\n  }\n\n  getProtocol() {\n    if (this.webSocket) {\n      return this.webSocket.protocol\n    }\n  }\n\n  isOpen() {\n    return this.isState(\"open\")\n  }\n\n  isActive() {\n    return this.isState(\"open\", \"connecting\")\n  }\n\n  triedToReconnect() {\n    return this.monitor.reconnectAttempts > 0\n  }\n\n  // Private\n\n  isProtocolSupported() {\n    return indexOf.call(supportedProtocols, this.getProtocol()) >= 0\n  }\n\n  isState(...states) {\n    return indexOf.call(states, this.getState()) >= 0\n  }\n\n  getState() {\n    if (this.webSocket) {\n      for (let state in adapters.WebSocket) {\n        if (adapters.WebSocket[state] === this.webSocket.readyState) {\n          return state.toLowerCase()\n        }\n      }\n    }\n    return null\n  }\n\n  installEventHandlers() {\n    for (let eventName in this.events) {\n      const handler = this.events[eventName].bind(this)\n      this.webSocket[`on${eventName}`] = handler\n    }\n  }\n\n  uninstallEventHandlers() {\n    for (let eventName in this.events) {\n      this.webSocket[`on${eventName}`] = function() {}\n    }\n  }\n\n}\n\nConnection.reopenDelay = 500\n\nConnection.prototype.events = {\n  message(event) {\n    if (!this.isProtocolSupported()) { return }\n    const {identifier, message, reason, reconnect, type} = JSON.parse(event.data)\n    this.monitor.recordMessage()\n    switch (type) {\n      case message_types.welcome:\n        if (this.triedToReconnect()) {\n          this.reconnectAttempted = true\n        }\n        this.monitor.recordConnect()\n        return this.subscriptions.reload()\n      case message_types.disconnect:\n        logger.log(`Disconnecting. Reason: ${reason}`)\n        return this.close({allowReconnect: reconnect})\n      case message_types.ping:\n        return null\n      case message_types.confirmation:\n        this.subscriptions.confirmSubscription(identifier)\n        if (this.reconnectAttempted) {\n          this.reconnectAttempted = false\n          return this.subscriptions.notify(identifier, \"connected\", {reconnected: true})\n        } else {\n          return this.subscriptions.notify(identifier, \"connected\", {reconnected: false})\n        }\n      case message_types.rejection:\n        return this.subscriptions.reject(identifier)\n      default:\n        return this.subscriptions.notify(identifier, \"received\", message)\n    }\n  },\n\n  open() {\n    logger.log(`WebSocket onopen event, using '${this.getProtocol()}' subprotocol`)\n    this.disconnected = false\n    if (!this.isProtocolSupported()) {\n      logger.log(\"Protocol is unsupported. Stopping monitor and disconnecting.\")\n      return this.close({allowReconnect: false})\n    }\n  },\n\n  close(event) {\n    logger.log(\"WebSocket onclose event\")\n    if (this.disconnected) { return }\n    this.disconnected = true\n    this.monitor.recordDisconnect()\n    return this.subscriptions.notifyAll(\"disconnected\", {willAttemptReconnect: this.monitor.isRunning()})\n  },\n\n  error() {\n    logger.log(\"WebSocket onerror event\")\n  }\n}\n\nexport default Connection\n", "// A new subscription is created through the ActionCable.Subscriptions instance available on the consumer.\n// It provides a number of callbacks and a method for calling remote procedure calls on the corresponding\n// Channel instance on the server side.\n//\n// An example demonstrates the basic functionality:\n//\n//   App.appearance = App.cable.subscriptions.create(\"AppearanceChannel\", {\n//     connected() {\n//       // Called once the subscription has been successfully completed\n//     },\n//\n//     disconnected({ willAttemptReconnect: boolean }) {\n//       // Called when the client has disconnected with the server.\n//       // The object will have an `willAttemptReconnect` property which\n//       // says whether the client has the intention of attempting\n//       // to reconnect.\n//     },\n//\n//     appear() {\n//       this.perform('appear', {appearing_on: this.appearingOn()})\n//     },\n//\n//     away() {\n//       this.perform('away')\n//     },\n//\n//     appearingOn() {\n//       $('main').data('appearing-on')\n//     }\n//   })\n//\n// The methods #appear and #away forward their intent to the remote AppearanceChannel instance on the server\n// by calling the `perform` method with the first parameter being the action (which maps to AppearanceChannel#appear/away).\n// The second parameter is a hash that'll get JSON encoded and made available on the server in the data parameter.\n//\n// This is how the server component would look:\n//\n//   class AppearanceChannel < ApplicationActionCable::Channel\n//     def subscribed\n//       current_user.appear\n//     end\n//\n//     def unsubscribed\n//       current_user.disappear\n//     end\n//\n//     def appear(data)\n//       current_user.appear on: data['appearing_on']\n//     end\n//\n//     def away\n//       current_user.away\n//     end\n//   end\n//\n// The \"AppearanceChannel\" name is automatically mapped between the client-side subscription creation and the server-side Ruby class name.\n// The AppearanceChannel#appear/away public methods are exposed automatically to client-side invocation through the perform method.\n\nconst extend = function(object, properties) {\n  if (properties != null) {\n    for (let key in properties) {\n      const value = properties[key]\n      object[key] = value\n    }\n  }\n  return object\n}\n\nexport default class Subscription {\n  constructor(consumer, params = {}, mixin) {\n    this.consumer = consumer\n    this.identifier = JSON.stringify(params)\n    extend(this, mixin)\n  }\n\n  // Perform a channel action with the optional data passed as an attribute\n  perform(action, data = {}) {\n    data.action = action\n    return this.send(data)\n  }\n\n  send(data) {\n    return this.consumer.send({command: \"message\", identifier: this.identifier, data: JSON.stringify(data)})\n  }\n\n  unsubscribe() {\n    return this.consumer.subscriptions.remove(this)\n  }\n}\n", "import logger from \"./logger\"\n\n// Responsible for ensuring channel subscribe command is confirmed, retrying until confirmation is received.\n// Internal class, not intended for direct user manipulation.\n\nclass SubscriptionGuarantor {\n  constructor(subscriptions) {\n    this.subscriptions = subscriptions\n    this.pendingSubscriptions = []\n  }\n\n  guarantee(subscription) {\n    if(this.pendingSubscriptions.indexOf(subscription) == -1){ \n      logger.log(`SubscriptionGuarantor guaranteeing ${subscription.identifier}`)\n      this.pendingSubscriptions.push(subscription) \n    }\n    else {\n      logger.log(`SubscriptionGuarantor already guaranteeing ${subscription.identifier}`)\n    }\n    this.startGuaranteeing()\n  }\n\n  forget(subscription) {\n    logger.log(`SubscriptionGuarantor forgetting ${subscription.identifier}`)\n    this.pendingSubscriptions = (this.pendingSubscriptions.filter((s) => s !== subscription))\n  }\n\n  startGuaranteeing() {\n    this.stopGuaranteeing()\n    this.retrySubscribing()\n  }\n  \n  stopGuaranteeing() {\n    clearTimeout(this.retryTimeout)\n  }\n\n  retrySubscribing() {\n    this.retryTimeout = setTimeout(() => {\n      if (this.subscriptions && typeof(this.subscriptions.subscribe) === \"function\") {\n        this.pendingSubscriptions.map((subscription) => {\n          logger.log(`SubscriptionGuarantor resubscribing ${subscription.identifier}`)\n          this.subscriptions.subscribe(subscription)\n        })\n      }\n    }\n    , 500)\n  }\n}\n\nexport default SubscriptionGuarantor", "import Subscription from \"./subscription\"\nimport SubscriptionGuarantor from \"./subscription_guarantor\"\nimport logger from \"./logger\"\n\n// Collection class for creating (and internally managing) channel subscriptions.\n// The only method intended to be triggered by the user is ActionCable.Subscriptions#create,\n// and it should be called through the consumer like so:\n//\n//   App = {}\n//   App.cable = ActionCable.createConsumer(\"ws://example.com/accounts/1\")\n//   App.appearance = App.cable.subscriptions.create(\"AppearanceChannel\")\n//\n// For more details on how you'd configure an actual channel subscription, see ActionCable.Subscription.\n\nexport default class Subscriptions {\n  constructor(consumer) {\n    this.consumer = consumer\n    this.guarantor = new SubscriptionGuarantor(this)\n    this.subscriptions = []\n  }\n\n  create(channelName, mixin) {\n    const channel = channelName\n    const params = typeof channel === \"object\" ? channel : {channel}\n    const subscription = new Subscription(this.consumer, params, mixin)\n    return this.add(subscription)\n  }\n\n  // Private\n\n  add(subscription) {\n    this.subscriptions.push(subscription)\n    this.consumer.ensureActiveConnection()\n    this.notify(subscription, \"initialized\")\n    this.subscribe(subscription)\n    return subscription\n  }\n\n  remove(subscription) {\n    this.forget(subscription)\n    if (!this.findAll(subscription.identifier).length) {\n      this.sendCommand(subscription, \"unsubscribe\")\n    }\n    return subscription\n  }\n\n  reject(identifier) {\n    return this.findAll(identifier).map((subscription) => {\n      this.forget(subscription)\n      this.notify(subscription, \"rejected\")\n      return subscription\n    })\n  }\n\n  forget(subscription) {\n    this.guarantor.forget(subscription)\n    this.subscriptions = (this.subscriptions.filter((s) => s !== subscription))\n    return subscription\n  }\n\n  findAll(identifier) {\n    return this.subscriptions.filter((s) => s.identifier === identifier)\n  }\n\n  reload() {\n    return this.subscriptions.map((subscription) =>\n      this.subscribe(subscription))\n  }\n\n  notifyAll(callbackName, ...args) {\n    return this.subscriptions.map((subscription) =>\n      this.notify(subscription, callbackName, ...args))\n  }\n\n  notify(subscription, callbackName, ...args) {\n    let subscriptions\n    if (typeof subscription === \"string\") {\n      subscriptions = this.findAll(subscription)\n    } else {\n      subscriptions = [subscription]\n    }\n\n    return subscriptions.map((subscription) =>\n      (typeof subscription[callbackName] === \"function\" ? subscription[callbackName](...args) : undefined))\n  }\n\n  subscribe(subscription) {\n    if (this.sendCommand(subscription, \"subscribe\")) {\n      this.guarantor.guarantee(subscription)\n    }\n  }\n\n  confirmSubscription(identifier) {\n    logger.log(`Subscription confirmed ${identifier}`)\n    this.findAll(identifier).map((subscription) =>\n      this.guarantor.forget(subscription))\n  }\n\n  sendCommand(subscription, command) {\n    const {identifier} = subscription\n    return this.consumer.send({command, identifier})\n  }\n}\n", "import Connection from \"./connection\"\nimport Subscriptions from \"./subscriptions\"\n\n// The ActionCable.Consumer establishes the connection to a server-side Ruby Connection object. Once established,\n// the ActionCable.ConnectionMonitor will ensure that its properly maintained through heartbeats and checking for stale updates.\n// The Consumer instance is also the gateway to establishing subscriptions to desired channels through the #createSubscription\n// method.\n//\n// The following example shows how this can be set up:\n//\n//   App = {}\n//   App.cable = ActionCable.createConsumer(\"ws://example.com/accounts/1\")\n//   App.appearance = App.cable.subscriptions.create(\"AppearanceChannel\")\n//\n// For more details on how you'd configure an actual channel subscription, see ActionCable.Subscription.\n//\n// When a consumer is created, it automatically connects with the server.\n//\n// To disconnect from the server, call\n//\n//   App.cable.disconnect()\n//\n// and to restart the connection:\n//\n//   App.cable.connect()\n//\n// Any channel subscriptions which existed prior to disconnecting will\n// automatically resubscribe.\n\nexport default class Consumer {\n  constructor(url) {\n    this._url = url\n    this.subscriptions = new Subscriptions(this)\n    this.connection = new Connection(this)\n    this.subprotocols = []\n  }\n\n  get url() {\n    return createWebSocketURL(this._url)\n  }\n\n  send(data) {\n    return this.connection.send(data)\n  }\n\n  connect() {\n    return this.connection.open()\n  }\n\n  disconnect() {\n    return this.connection.close({allowReconnect: false})\n  }\n\n  ensureActiveConnection() {\n    if (!this.connection.isActive()) {\n      return this.connection.open()\n    }\n  }\n\n  addSubProtocol(subprotocol) {\n    this.subprotocols = [...this.subprotocols, subprotocol]\n  }\n}\n\nexport function createWebSocketURL(url) {\n  if (typeof url === \"function\") {\n    url = url()\n  }\n\n  if (url && !/^wss?:/i.test(url)) {\n    const a = document.createElement(\"a\")\n    a.href = url\n    // Fix populating Location properties in IE. Otherwise, protocol will be blank.\n    a.href = a.href // eslint-disable-line\n    a.protocol = a.protocol.replace(\"http\", \"ws\")\n    return a.href\n  } else {\n    return url\n  }\n}\n", "import Connection from \"./connection\"\nimport ConnectionMonitor from \"./connection_monitor\"\nimport Consumer, { createWebSocketURL } from \"./consumer\"\nimport INTERNAL from \"./internal\"\nimport Subscription from \"./subscription\"\nimport Subscriptions from \"./subscriptions\"\nimport SubscriptionGuarantor from \"./subscription_guarantor\"\nimport adapters from \"./adapters\"\nimport logger from \"./logger\"\n\nexport {\n  Connection,\n  ConnectionMonitor,\n  Consumer,\n  INTERNAL,\n  Subscription,\n  Subscriptions,\n  SubscriptionGuarantor,\n  adapters,\n  createWebSocketURL,\n  logger,\n}\n\nexport function createConsumer(url = getConfig(\"url\") || INTERNAL.default_mount_path) {\n  return new Consumer(url)\n}\n\nexport function getConfig(name) {\n  const element = document.head.querySelector(`meta[name='action-cable-${name}']`)\n  if (element) {\n    return element.getAttribute(\"content\")\n  }\n}\n"], "names": ["prototype", "raise", "errorConstructor", "message", "name", "requestSubmit", "submitter", "form", "HTMLElement", "TypeError", "type", "DOMException", "validateSubmitter", "this", "click", "document", "createElement", "hidden", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "HTMLFormElement", "submittersByForm", "WeakMap", "clickCaptured", "event", "target", "element", "Element", "Node", "parentElement", "candidate", "closest", "findSubmitterFromClickTarget", "set", "Event", "window", "prototypeOfSubmitEvent", "SubmitEvent", "test", "navigator", "vendor", "addEventListener", "Object", "defineProperty", "get", "FrameLoadingStyle", "eager", "lazy", "FrameElement", "static", "undefined", "loaded", "Promise", "resolve", "observedAttributes", "constructor", "super", "delegate", "delegateConstructor", "connectedCallback", "connect", "disconnectedCallback", "disconnect", "reload", "sourceURLReloaded", "attributeChangedCallback", "loadingStyleChanged", "sourceURLChanged", "disabled<PERSON><PERSON>ed", "src", "getAttribute", "value", "setAttribute", "removeAttribute", "refresh", "shouldReloadWithMorph", "loading", "style", "toLowerCase", "frameLoadingStyleFromString", "disabled", "hasAttribute", "autoscroll", "complete", "isLoading", "isActive", "ownerDocument", "isPreview", "documentElement", "drive", "enabled", "progressBarDelay", "unvisitableExtensions", "Set", "activateScriptElement", "createdScriptElement", "cspNonce", "getCspNonce", "nonce", "textContent", "async", "destinationElement", "sourceElement", "attributes", "copyElementAttributes", "dispatch", "eventName", "cancelable", "detail", "CustomEvent", "bubbles", "composed", "isConnected", "dispatchEvent", "cancelEvent", "preventDefault", "stopImmediatePropagation", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visibilityState", "nextEventLoopTick", "nextAnimationFrame", "requestAnimationFrame", "setTimeout", "parseHTMLDocument", "html", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "unindent", "strings", "values", "lines", "reduce", "result", "string", "i", "interpolate", "replace", "split", "match", "indent", "length", "map", "line", "slice", "join", "uuid", "Array", "from", "_", "Math", "floor", "random", "toString", "attributeName", "elements", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "localName", "clearBusyState", "waitForLoad", "timeoutInMilliseconds", "onComplete", "removeEventListener", "once", "getHistoryMethodForAction", "action", "history", "replaceState", "pushState", "getVisitAction", "isAction", "getMetaElement", "querySelector", "getMetaContent", "content", "findClosestRecursively", "selector", "assignedSlot", "getRootNode", "host", "elementIsFocusable", "focus", "queryAutofocusableElement", "elementOrDocumentFragment", "querySelectorAll", "find", "doesNotTargetIFrame", "getElementsByName", "HTMLIFrameElement", "findLinkFromClickTarget", "getLocationForLink", "link", "expandURL", "beforeSubmit", "afterSubmit", "forms", "config", "assign", "mode", "locatable", "URL", "baseURI", "getAnchor", "url", "anchorMatch", "hash", "href", "getAction$1", "getExtension", "pathname", "getPathComponents", "getLastPathComponent", "isPrefixedBy", "baseURL", "prefix", "origin", "endsWith", "getPrefix", "startsWith", "locationIsVisitable", "location", "rootLocation", "has", "getRequestURL", "anchor", "to<PERSON><PERSON><PERSON><PERSON>", "FetchResponse", "response", "succeeded", "ok", "failed", "clientError", "statusCode", "serverError", "redirected", "isHTML", "contentType", "status", "header", "responseText", "clone", "text", "responseHTML", "headers", "LimitedSet", "maxSize", "add", "size", "oldestValue", "next", "delete", "recentRequests", "nativeFetch", "fetch", "fetchWithTurboHeaders", "options", "modifiedHeaders", "Headers", "requestUID", "append", "fetchMethodFromString", "method", "FetchMethod", "post", "put", "patch", "fetchEnctypeFromString", "encoding", "FetchEnctype", "multipart", "plain", "urlEncoded", "FetchRequest", "abortController", "AbortController", "resolveRequestPromise", "_value", "requestBody", "URLSearchParams", "enctype", "body", "buildResourceAndBody", "fetchOptions", "credentials", "redirect", "toUpperCase", "defaultHeaders", "signal", "abortSignal", "referrer", "fetchBody", "isSafe", "searchParams", "FormData", "fetch<PERSON><PERSON><PERSON>", "search", "params", "entries", "cancel", "abort", "perform", "prepareRequest", "allowRequestToBeIntercepted", "requestStarted", "fetchRequest", "receive", "error", "willDelegateErrorHandling", "requestErrored", "requestFinished", "fetchResponse", "defaultPrevented", "requestPreventedHandlingResponse", "requestSucceededWithResponse", "requestFailedWithResponse", "Accept", "acceptResponseType", "mimeType", "requestInterception", "resume", "request", "resource", "entriesExcludingFiles", "mergeIntoURLSearchParams", "File", "push", "AppearanceObserver", "started", "intersectionObserver", "IntersectionObserver", "intersect", "start", "observe", "stop", "unobserve", "lastEntry", "isIntersecting", "elementAppearedInViewport", "StreamMessage", "wrap", "template", "innerHTML", "createDocumentFragment", "fragment", "streamElement", "importNode", "inertScriptElement", "templateElement", "replaceWith", "importStreamElements", "prefetchCache", "prefetchTimeout", "prefetched", "expire", "Date", "now", "setLater", "ttl", "clear", "getTime", "clearTimeout", "FormSubmissionState", "initialized", "requesting", "waiting", "receiving", "stopping", "stopped", "FormSubmission", "state", "confirm<PERSON>ethod", "confirm", "formElement", "mustRedirect", "getMethod", "formAction", "getAction", "formElementAction", "getFormAction", "formData", "buildFormData", "getEnctype", "confirmationMessage", "token", "cookieName", "cookie", "decodeURIComponent", "getCookieValue", "requestAcceptsTurboStreamResponse", "_request", "setSubmitsWith", "formSubmission", "formSubmissionStarted", "success", "formSubmissionFailedWithResponse", "requestMustRedirect", "responseSucceededWithoutRedirect", "Error", "formSubmissionErrored", "formSubmissionSucceededWithResponse", "resetSubmitterText", "formSubmissionFinished", "submitsWith", "matches", "originalSubmitText", "input", "some", "Snapshot", "activeElement", "children", "hasAnchor", "getElementForAnchor", "firstAutofocusableElement", "permanentElements", "queryPermanentElementsAll", "getPermanentElementById", "id", "getPermanentElementMapForSnapshot", "snapshot", "permanentElementMap", "currentPermanentElement", "newPermanentElement", "node", "FormSubmitObserver", "eventTarget", "submitCaptured", "submitBubbled", "submissionDoesNotDismissDialog", "submissionDoesNotTargetIFrame", "willSubmitForm", "formSubmitted", "View", "resolveRenderPromise", "resolveInterceptionPromise", "scrollToAnchor", "scrollToElement", "focusElement", "scrollToPosition", "x", "y", "scrollToAnchorFromLocation", "scrollIntoView", "scrollRoot", "scrollTo", "scrollToTop", "render", "renderer", "shouldRender", "<PERSON><PERSON><PERSON>", "newSnapshot", "shouldInvalidate", "renderPromise", "prepareToRenderSnapshot", "renderInterception", "renderElement", "renderMethod", "allowsImmediateRender", "renderSnapshot", "viewRenderedSnapshot", "preloadOnLoadLinksForView", "finishRenderingSnapshot", "invalidate", "reloadReason", "reason", "viewInvalidated", "markAsPreview", "prepareToRender", "markVisitDirection", "direction", "unmarkVisitDirection", "finishRendering", "FrameView", "missing", "LinkInterceptor", "clickBubbled", "linkClicked", "willVisit", "clickEventIsSignificant", "clickEvent", "shouldInterceptLinkClick", "originalEvent", "linkClickIntercepted", "_event", "LinkClickObserver", "MouseEvent", "<PERSON><PERSON><PERSON>", "willFollowLinkToLocation", "followedLinkToLocation", "isContentEditable", "which", "altKey", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "FormLinkClickObserver", "linkInterceptor", "canPrefetchRequestToLocation", "prefetchAndCacheRequestToLocation", "willSubmitFormLinkToLocation", "turboFrame", "turboAction", "turboConfirm", "submittedFormLinkToLocation", "remove", "<PERSON><PERSON>", "preservingPermanentElements", "callback", "bardo", "enter", "leave", "enteringBardo", "replaceNewPermanentElementWithPlaceholder", "replaceCurrentPermanentElementWithClone", "replacePlaceholderWithPermanentElement", "leavingBardo", "permanentElement", "placeholder", "createPlaceholderForPermanentElement", "cloneNode", "getPlaceholderById", "placeholders", "<PERSON><PERSON><PERSON>", "currentElement", "newElement", "currentSnapshot", "promise", "reject", "resolvingFunctions", "shouldAutofocus", "focusFirstAutofocusableElement", "connectedSnapshot", "contains", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "destinationRange", "createRange", "selectNodeContents", "deleteContents", "frameElement", "sourceRange", "extractContents", "loadFrameElement", "scrollFrameIntoView", "activateScriptElements", "will<PERSON><PERSON><PERSON><PERSON><PERSON>", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "block", "defaultValue", "behavior", "readScrollBehavior", "newScriptElements", "activatedScriptElement", "Idiomorph", "noOp", "defaults", "morphStyle", "callbacks", "beforeNodeAdded", "afterNodeAdded", "beforeNodeMorphed", "afterNodeMorphed", "beforeNodeRemoved", "afterNodeRemoved", "beforeAttributeUpdated", "head", "shouldPreserve", "elt", "shouldReAppend", "<PERSON><PERSON><PERSON><PERSON>", "afterHeadMorphed", "restoreFocus", "morph<PERSON><PERSON><PERSON><PERSON>", "createNode", "old<PERSON>arent", "<PERSON><PERSON><PERSON><PERSON>", "insertionPoint", "ctx", "idMap", "newEmpty<PERSON><PERSON><PERSON>", "tagName", "insertBefore", "morphNode", "newCloned<PERSON>hild", "findBestMatch", "isIdSetMatch", "oldNode", "newNode", "oldSet", "newSet", "isSoftMatch", "oldElt", "newElt", "nodeType", "startPoint", "endPoint", "softMatch", "nextS<PERSON>ling", "siblingSoftMatchCount", "cursor", "removeNode", "moveBefore", "pantry", "parentNode", "removeNodesBetween", "startInclusive", "endExclusive", "tempNode", "moveBeforeById", "after", "idSet", "removeElementFromAncestorsIdMaps", "e", "newParent", "HTMLTemplateElement", "<PERSON><PERSON><PERSON><PERSON>", "childNodes", "bestMatch", "persistentIds", "<PERSON><PERSON><PERSON><PERSON>", "insertedNode", "syncBooleanAttribute", "oldElement", "newLiveValue", "ignoreUpdate", "ignoreAttribute", "attr", "updateType", "ignoreActiveValue", "ignoreValueOfActiveElement", "possibleActiveElement", "newContent", "ignoreActive", "HTMLHeadElement", "ignore", "handleHeadElement", "oldAttributes", "newAttributes", "newAttribute", "oldAttribute", "HTMLInputElement", "newValue", "oldValue", "HTMLOptionElement", "HTMLTextAreaElement", "nodeValue", "syncInputValue", "morphAttributes", "oldHead", "newHead", "added", "removed", "preserved", "nodesToAppend", "srcToNewHeadNodes", "Map", "newHeadChild", "outerHTML", "currentHeadElt", "inNew<PERSON><PERSON>nt", "isReAppended", "isPreserved", "promises", "createContextualFragment", "_resolve", "removedElement", "kept", "createMorphContext", "createPantry", "insertAdjacentElement", "findIdElements", "root", "populateIdMapWithTree", "current", "<PERSON><PERSON><PERSON><PERSON>", "oldIdElements", "newIdElements", "duplicateIds", "oldIdTagNameMap", "createPersistentIds", "newRoot", "__idiomorphRoot", "createIdMaps", "mergedConfig", "finalConfig", "mergeDefaults", "includes", "normalizeElement", "normalizeParent", "generatedByIdiomorph", "WeakSet", "Document", "parser", "contentWithSvgsRemoved", "htmlElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "s", "n", "r", "createDuckTypedParent", "dummy<PERSON>arent", "morph", "morphedNodes", "fn", "activeElementId", "selectionStart", "selectionEnd", "results", "setSelectionRange", "saveAndRestoreFocus", "all", "then", "newCtx", "withHeadBlocking", "index", "indexOf", "<PERSON><PERSON><PERSON><PERSON>", "morphOuterHTML", "morphElements", "DefaultIdiomorphCallbacks", "getElementById", "mutationType", "Morp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ProgressBar", "defaultCSS", "animationDuration", "hiding", "visible", "stylesheetElement", "createStylesheetElement", "progressElement", "createProgressElement", "installStylesheetElement", "setValue", "show", "installProgressElement", "startTrickling", "hide", "fadeProgressElement", "uninstallProgressElement", "stopTrickling", "width", "opacity", "trickleInterval", "setInterval", "trickle", "clearInterval", "className", "HeadSnapshot", "detailsByOuterHTML", "filter", "elementIsNoscript", "elementWithoutNonce", "details", "elementType", "tracked", "elementIsTracked", "trackedElementSignature", "keys", "getScriptElementsNotInSnapshot", "getElementsMatchingTypeNotInSnapshot", "getStylesheetElementsNotInSnapshot", "matchedType", "provisionalElements", "getMetaValue", "findMetaElementByName", "elementIsMetaElementWithName", "elementIsScript", "elementIsStylesheet", "PageSnapshot", "fromHTMLString", "fromDocument", "fromElement", "headSnapshot", "clonedElement", "selectElements", "clonedSelectElements", "source", "option", "selectedOptions", "selected", "clonedPasswordInput", "lang", "headElement", "getSetting", "cacheControlValue", "isPreviewable", "isCacheable", "isVisitable", "prefersViewTransitions", "shouldMorphPage", "shouldPreserveScrollPosition", "ViewTransitioner", "viewTransitionStarted", "lastOperation", "renderChange", "useViewTransition", "viewTransitionsAvailable", "startViewTransition", "finished", "defaultOptions", "historyChanged", "visitCachedSnapshot", "updateHistory", "shouldCacheSnapshot", "acceptsStreamResponse", "TimingMetric", "VisitState", "SystemStatusCode", "Direction", "advance", "restore", "Visit", "identifier", "timingMetrics", "followedRedirect", "scrolled", "snapshotCached", "viewTransitioner", "restorationIdentifier", "snapshotHTML", "isSamePage", "locationWithActionIsSamePage", "isPageRefresh", "view", "adapter", "restorationData", "getRestorationDataForIdentifier", "silent", "recordTimingMetric", "visitStarted", "cancelRender", "visitCompleted", "followRedirect", "fail", "visitFailed", "changeHistory", "update", "issueRequest", "hasPreloadedResponse", "simulateRequest", "shouldIssueRequest", "startRequest", "recordResponse", "finishRequest", "visitRequestStarted", "isSuccessful", "visitRequestCompleted", "visitRequestFailedWithStatusCode", "visitRequestFinished", "loadResponse", "cacheSnapshot", "renderPageSnapshot", "visitRendered", "renderError", "getCachedSnapshot", "getCachedSnapshotForLocation", "getPreloadedSnapshot", "hasCachedSnapshot", "loadCachedSnapshot", "redirectedToLocation", "visitProposedToLocation", "goToSamePageAnchor", "performScroll", "_response", "_error", "forceReloaded", "scrollToRestoredPosition", "visitScrolledToSamePageLocation", "lastRenderedLocation", "scrollPosition", "metric", "getTimingMetrics", "frame", "shouldTransitionTo", "renderPage", "cancelAnimationFrame", "BrowserAdapter", "progressBar", "session", "startVisit", "visit", "showVisitProgressBarAfterDelay", "showProgressBar", "context", "_visit", "hideVisitProgressBar", "pageInvalidated", "linkPrefetchingIsEnabledForLocation", "_formSubmission", "showFormProgressBarAfterDelay", "hideFormProgressBar", "visitProgressBarTimeout", "formProgressBarTimeout", "CacheObserver", "deprecatedSelector", "removeTemporaryElements", "temporaryElements", "temporaryElementsWithDeprecation", "console", "warn", "FrameRedirector", "formSubmitObserver", "_location", "shouldRedirect", "findFrameElement", "shouldSubmit", "meta", "submissionIsNavigatable", "elementIsNavigatable", "History", "pageLoaded", "currentIndex", "onPopState", "onPageLoad", "turbo", "restorationIndex", "call", "updateRestorationData", "additionalData", "assumeControlOfScrollRestoration", "previousScrollRestoration", "scrollRestoration", "relinquishControlOfScrollRestoration", "shouldHandlePopState", "historyPoppedToLocationWithRestorationIdentifierAndDirection", "pageIsLoaded", "readyState", "LinkPrefetchObserver", "prefetchedLink", "enable", "tryToPrefetchRequest", "capture", "passive", "cancelRequestIfObsolete", "tryToUsePrefetchedRequest", "isPrefetchable", "cacheTtl", "cancelPrefetchRequest", "cached", "turboFrameTarget", "Number", "unfetchableLink", "linkToTheSamePage", "linkOptsOut", "nonSafeLink", "eventPrevented", "protocol", "turboPrefetchParent", "turboMethod", "isUJS", "Navigator", "proposeVisit", "allowsVisitingLocationWithAction", "currentVisit", "submitForm", "clearSnapshotCache", "visitOptions", "getActionForFormSubmission", "currentAnchor", "isRestorationToTop", "oldURL", "newURL", "getDefaultAction", "PageStage", "PageObserver", "stage", "interpretReadyState", "pageWillUnload", "pageIsInteractive", "pageIsComplete", "pageBecameInteractive", "ScrollObserver", "onScroll", "updatePosition", "pageXOffset", "pageYOffset", "position", "scrollPositionChanged", "StreamMessageRenderer", "permanentElementsInDocument", "permanentElementInDocument", "elementInStream", "getPermanentElementMapForFragment", "generatedID", "turboStreams", "elementWithAutofocus", "nodeListOfStreamElements", "firstAutofocusableElementInStreams", "willAutofocusId", "elementToAutofocus", "withAutofocusFromFragment", "activeElementBeforeRender", "activeElementAfterRender", "reader", "before", "around", "restoreFocusTo", "elementToFocus", "withPreservedFocus", "StreamObserver", "sources", "inspectFetchResponse", "connectStreamSource", "streamSourceIsConnected", "receiveMessageEvent", "disconnectStreamSource", "fetchResponseFromEvent", "fetchResponseIsStream", "receiveMessageResponse", "data", "receiveMessageHTML", "receivedMessageFromStream", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "replaceHeadAndBody", "replaceableElement", "scriptElements", "<PERSON><PERSON><PERSON><PERSON>", "HTMLBodyElement", "trackedElementsAreIdentical", "setLanguage", "mergeHead", "replaceBody", "currentHeadSnapshot", "newHeadSnapshot", "mergedHeadElements", "mergeProvisionalElements", "newStylesheetElements", "copyNewHeadStylesheetElements", "copyNewHeadScriptElements", "removeUnusedDynamicStylesheetElements", "activateNewBody", "assignNewBody", "loadingElements", "newHeadStylesheetElements", "newHeadScriptElements", "unusedDynamicStylesheetElements", "newHeadElements", "newHeadProvisionalElements", "currentHeadProvisionalElements", "isCurrentElementInElementList", "elementList", "splice", "isEqualNode", "removeCurrentHeadProvisionalElements", "copyNewHeadProvisionalElements", "adoptNode", "activateNewBodyScriptElements", "newBodyScriptElements", "oldHeadStylesheetElements", "Morp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "canRefreshFrame", "SnapshotCache", "snapshots", "read", "touch", "write", "key", "unshift", "trim", "<PERSON><PERSON><PERSON><PERSON>", "snapshotCache", "viewWillCacheSnapshot", "cachedSnapshot", "Preloader", "preloadAll", "shouldPreloadLink", "preloadURL", "<PERSON><PERSON>", "clearCache", "resetCacheControl", "setCacheControl", "exemptPageFromCache", "exemptPageFromPreview", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "extendURLWithDeprecatedProperties", "defineProperties", "deprecatedLocationPropertyDescriptors", "absoluteURL", "pageObserver", "cacheObserver", "linkPrefetchObserver", "linkClickObserver", "scrollObserver", "streamObserver", "formLinkClickObserver", "frameRedirector", "streamMessageRenderer", "cache", "pageRefreshDebouncePeriod", "preloader", "debouncedRefresh", "disable", "registerAdapter", "proposeVisitIfNavigatedWithAction", "requestId", "isRecentRequest", "isCurrentUrl", "renderStreamMessage", "setProgressBarDelay", "delay", "formMode", "timeoutId", "args", "apply", "debounce", "bind", "isUnsafe", "isStream", "frame<PERSON>arget", "applicationAllowsFollowingLinkToLocation", "getActionForLink", "applicationAllowsVisitingLocation", "notifyApplicationAfterVisitingLocation", "notifyApplicationAfterPageLoad", "notifyApplicationAfterVisitingSamePageLocation", "notifyApplicationBeforeCachingSnapshot", "notifyApplicationBeforeRender", "_snapshot", "_isPreview", "notifyApplicationAfterRender", "frameLoaded", "notifyApplicationAfterFrameLoad", "frameRendered", "notifyApplicationAfterFrameRender", "ev", "notifyApplicationAfterClickingLinkToLocation", "notifyApplicationBeforeVisitingLocation", "newBody", "timing", "HashChangeEvent", "submitterIsNavigatable", "container", "withinFrame", "navigator$1", "setConfirmMethod", "setFormMode", "Turbo", "freeze", "__proto__", "TurboFrameMissingError", "getFrameElementById", "activateElement", "currentURL", "right", "StreamActions", "targetElements", "for<PERSON>ach", "templateContent", "removeDuplicateTargetChildren", "prepend", "targetElement", "StreamElement", "performAction", "beforeRenderEvent", "duplicate<PERSON><PERSON><PERSON><PERSON>", "c", "existingChildren", "flatMap", "newChildrenIds", "actionFunction", "targetElementsById", "targets", "targetElementsByQuery", "description", "newStream", "StreamSourceElement", "streamSource", "WebSocket", "EventSource", "close", "fetchResponseLoaded", "_fetchResponse", "currentFetchRequest", "resolveVisitPromise", "connected", "hasBeenLoaded", "ignoredAttributes", "shouldMorphFrame", "appearanceObserver", "loadingStyle", "loadSourceURL", "isIgnoringChangesTo", "sourceURL", "loadFrameResponse", "handleUnvisitableFrameResponse", "shouldInterceptNavigation", "navigateFrame", "currentNavigationElement", "newFrame", "_renderMethod", "_newElement", "previousFrameElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "newFrameElement", "extractForeignFrameElement", "rendererClass", "willHandleFrameMissingFromResponse", "handleFrameMissingFromResponse", "withCurrentNavigationElement", "pageSnapshot", "visitResponse", "Response", "throwFrameMissingError", "wrapped", "CSS", "escape", "formActionIsVisitable", "ignoringChangesToAttribute", "customElements", "define", "currentScript", "consumer", "getConsumer", "setConsumer", "createConsumer", "newConsumer", "subscribeTo", "channel", "mixin", "subscriptions", "create", "walk", "obj", "RegExp", "isArray", "acc", "m", "TurboCableStreamSourceElement", "subscription", "received", "dispatchMessageEvent", "subscriptionConnected", "disconnected", "subscriptionDisconnected", "unsubscribe", "MessageEvent", "signed_stream_name", "snakeize", "dataset", "isBodyInit", "formMethod", "HTMLButtonElement", "determineFormMethod", "override<PERSON><PERSON><PERSON>", "determineFetchMethod", "adapters", "logger", "log", "messages", "secondsSince", "time", "ConnectionMonitor", "connection", "visibilityDidChange", "reconnectAttempts", "isRunning", "startedAt", "stoppedAt", "startPolling", "staleThreshold", "stopPolling", "recordMessage", "pingedAt", "recordConnect", "disconnectedAt", "recordDisconnect", "poll", "pollTimeout", "reconnectIfStale", "getPollInterval", "reconnectionBackoffRate", "pow", "min", "connectionIsStale", "refreshedAt", "disconnectedRecently", "reopen", "isOpen", "ConnectionMonitor$1", "INTERNAL", "message_types", "welcome", "ping", "confirmation", "rejection", "disconnect_reasons", "unauthorized", "invalid_request", "server_restart", "remote", "default_mount_path", "protocols", "supportedProtocols", "Connection", "open", "monitor", "send", "webSocket", "JSON", "stringify", "getState", "socketProtocols", "subprotocols", "uninstallEventHandlers", "installEventHandlers", "allowReconnect", "reopenDelay", "getProtocol", "isState", "triedToReconnect", "isProtocolSupported", "states", "events", "handler", "reconnect", "parse", "reconnectAttempted", "confirmSubscription", "notify", "reconnected", "notifyAll", "willAttemptReconnect", "Connection$1", "Subscription", "object", "properties", "extend", "command", "SubscriptionGuarantor$1", "pendingSubscriptions", "guarantee", "startGuaranteeing", "forget", "stopGuaranteeing", "retrySubscribing", "retryTimeout", "subscribe", "Subscriptions", "guarantor", "SubscriptionGuarantor", "channelName", "ensureActiveConnection", "findAll", "sendCommand", "callback<PERSON><PERSON>", "Consumer", "_url", "createWebSocketURL", "addSubProtocol", "subprotocol", "a", "getConfig"], "mappings": ";;;;CA4BA,SAAWA,GAwBT,SAASC,EAAMC,EAAkBC,EAASC,GACxC,MAAM,IAAIF,EAAiB,2DAA6DC,EAAU,IAAKC,EACxG,CAzBqC,mBAA3BJ,EAAUK,gBAErBL,EAAUK,cAAgB,SAAUC,GAC9BA,IAaN,SAA2BA,EAAWC,GACpCD,aAAqBE,aAAeP,EAAMQ,UAAW,4CACnC,UAAlBH,EAAUI,MAAoBT,EAAMQ,UAAW,gDAC/CH,EAAUC,MAAQA,GAChBN,EAAMU,aAAc,0DAA2D,gBAClF,CAjBGC,CAAkBN,EAAWO,MAC7BP,EAAUQ,WAEVR,EAAYS,SAASC,cAAc,UACzBN,KAAO,SACjBJ,EAAUW,QAAS,EACnBJ,KAAKK,YAAYZ,GACjBA,EAAUQ,QACVD,KAAKM,YAAYb,GAEvB,EAYC,CA3BD,CA2BGc,gBAAgBpB,WAEnB,MAAMqB,EAAmB,IAAIC,QAQ7B,SAASC,EAAcC,GACrB,MAAMlB,EAPR,SAAsCmB,GACpC,MAAMC,EAAUD,aAAkBE,QAAUF,EAASA,aAAkBG,KAAOH,EAAOI,cAAgB,KAC/FC,EAAYJ,EAAUA,EAAQK,QAAQ,iBAAmB,KAC/D,MAA0B,UAAnBD,GAAWpB,KAAmBoB,EAAY,IACnD,CAGoBE,CAA6BR,EAAMC,QAEjDnB,GAAaA,EAAUC,MACzBc,EAAiBY,IAAI3B,EAAUC,KAAMD,EAEzC,EAEA,WACE,GAAI,cAAe4B,MAAMlC,UAAW,OAEpC,IAAIA,EAAYmC,OAAOD,MAAMlC,UAI7B,GAAI,gBAAiBmC,OAAQ,CAC3B,MAAMC,EAAyBD,OAAOE,YAAYrC,UAElD,IAAI,iBAAiBsC,KAAKC,UAAUC,SAAa,cAAeJ,EAG9D,OAFApC,EAAYoC,CAIf,CAEDK,iBAAiB,QAASlB,GAAe,GAEzCmB,OAAOC,eAAe3C,EAAW,YAAa,CAC5C,GAAA4C,GACE,GAAiB,UAAb/B,KAAKH,MAAoBG,KAAKY,kBAAkBL,gBAClD,OAAOC,EAAiBuB,IAAI/B,KAAKY,OAEpC,GAEJ,CA1BD,GA4BA,MAAMoB,EAAoB,CACxBC,MAAO,QACPC,KAAM,QAmBR,MAAMC,UAAqBxC,YACzByC,gCAA6BC,EAE7BC,OAASC,QAAQC,UAEjB,6BAAWC,GACT,MAAO,CAAC,WAAY,UAAW,MAChC,CAED,WAAAC,GACEC,QACA3C,KAAK4C,SAAW,IAAIT,EAAaU,oBAAoB7C,KACtD,CAED,iBAAA8C,GACE9C,KAAK4C,SAASG,SACf,CAED,oBAAAC,GACEhD,KAAK4C,SAASK,YACf,CAED,MAAAC,GACE,OAAOlD,KAAK4C,SAASO,mBACtB,CAED,wBAAAC,CAAyB7D,GACX,WAARA,EACFS,KAAK4C,SAASS,sBACG,OAAR9D,EACTS,KAAK4C,SAASU,mBACG,YAAR/D,GACTS,KAAK4C,SAASW,iBAEjB,CAKD,OAAIC,GACF,OAAOxD,KAAKyD,aAAa,MAC1B,CAKD,OAAID,CAAIE,GACFA,EACF1D,KAAK2D,aAAa,MAAOD,GAEzB1D,KAAK4D,gBAAgB,MAExB,CAKD,WAAIC,GACF,OAAO7D,KAAKyD,aAAa,UAC1B,CAKD,WAAII,CAAQH,GACNA,EACF1D,KAAK2D,aAAa,UAAWD,GAE7B1D,KAAK4D,gBAAgB,UAExB,CAED,yBAAIE,GACF,OAAO9D,KAAKwD,KAAwB,UAAjBxD,KAAK6D,OACzB,CAKD,WAAIE,GACF,OAoFJ,SAAqCC,GACnC,GACO,SADCA,EAAMC,cAEV,OAAOjC,EAAkBE,KAEzB,OAAOF,EAAkBC,KAE/B,CA3FWiC,CAA4BlE,KAAKyD,aAAa,YAAc,GACpE,CAKD,WAAIM,CAAQL,GACNA,EACF1D,KAAK2D,aAAa,UAAWD,GAE7B1D,KAAK4D,gBAAgB,UAExB,CAOD,YAAIO,GACF,OAAOnE,KAAKoE,aAAa,WAC1B,CAOD,YAAID,CAAST,GACPA,EACF1D,KAAK2D,aAAa,WAAY,IAE9B3D,KAAK4D,gBAAgB,WAExB,CAOD,cAAIS,GACF,OAAOrE,KAAKoE,aAAa,aAC1B,CAOD,cAAIC,CAAWX,GACTA,EACF1D,KAAK2D,aAAa,aAAc,IAEhC3D,KAAK4D,gBAAgB,aAExB,CAKD,YAAIU,GACF,OAAQtE,KAAK4C,SAAS2B,SACvB,CAOD,YAAIC,GACF,OAAOxE,KAAKyE,gBAAkBvE,WAAaF,KAAK0E,SACjD,CAOD,aAAIA,GACF,OAAO1E,KAAKyE,eAAeE,iBAAiBP,aAAa,qBAC1D,EAYH,MAAMQ,EAAQ,CACZC,SAAS,EACTC,iBAAkB,IAClBC,sBAAuB,IAAIC,IACzB,CACE,MAAO,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAC/E,QAAS,OAAQ,OAAQ,MAAO,QAAS,QAAS,OAAQ,OAAQ,QAAS,OAC3E,MAAO,QAAS,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,QAAS,OAAQ,OACzE,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,QAAS,OAAQ,OACjE,OAAQ,OAAQ,OAAQ,QAAS,OAAQ,OAAQ,QAAS,QAAS,OAAQ,OAC3E,OAAQ,QAAS,OAAQ,UAK/B,SAASC,EAAsBpE,GAC7B,GAA+C,SAA3CA,EAAQ4C,aAAa,mBACvB,OAAO5C,EACF,CACL,MAAMqE,EAAuBhF,SAASC,cAAc,UAC9CgF,EAAWC,IAOjB,OANID,IACFD,EAAqBG,MAAQF,GAE/BD,EAAqBI,YAAczE,EAAQyE,YAC3CJ,EAAqBK,OAAQ,EAMjC,SAA+BC,EAAoBC,GACjD,IAAK,MAAMlG,KAAEA,EAAImE,MAAEA,KAAW+B,EAAcC,WAC1CF,EAAmB7B,aAAapE,EAAMmE,EAE1C,CATIiC,CAAsBT,EAAsBrE,GACrCqE,CACR,CACH,CAcA,SAASU,EAASC,GAAWjF,OAAEA,EAAMkF,WAAEA,EAAUC,OAAEA,GAAW,IAC5D,MAAMpF,EAAQ,IAAIqF,YAAYH,EAAW,CACvCC,aACAG,SAAS,EACTC,UAAU,EACVH,WASF,OANInF,GAAUA,EAAOuF,YACnBvF,EAAOwF,cAAczF,GAErBT,SAASyE,gBAAgByB,cAAczF,GAGlCA,CACT,CAEA,SAAS0F,EAAY1F,GACnBA,EAAM2F,iBACN3F,EAAM4F,0BACR,CAEA,SAASC,IACP,MAAiC,WAA7BtG,SAASuG,gBACJC,IAEAC,GAEX,CAEA,SAASA,IACP,OAAO,IAAIpE,SAASC,GAAYoE,uBAAsB,IAAMpE,OAC9D,CAEA,SAASkE,IACP,OAAO,IAAInE,SAASC,GAAYqE,YAAW,IAAMrE,KAAW,IAC9D,CAMA,SAASsE,EAAkBC,EAAO,IAChC,OAAO,IAAIC,WAAYC,gBAAgBF,EAAM,YAC/C,CAEA,SAASG,EAASC,KAAYC,GAC5B,MAAMC,EAMR,SAAqBF,EAASC,GAC5B,OAAOD,EAAQG,QAAO,CAACC,EAAQC,EAAQC,IAE9BF,EAASC,GADWnF,MAAb+E,EAAOK,GAAkB,GAAKL,EAAOK,KAElD,GACL,CAXgBC,CAAYP,EAASC,GAAQO,QAAQ,MAAO,IAAIC,MAAM,MAC9DC,EAAQR,EAAM,GAAGQ,MAAM,QACvBC,EAASD,EAAQA,EAAM,GAAGE,OAAS,EACzC,OAAOV,EAAMW,KAAKC,GAASA,EAAKC,MAAMJ,KAASK,KAAK,KACtD,CASA,SAASC,IACP,OAAOC,MAAMC,KAAK,CAAEP,OAAQ,KACzBC,KAAI,CAACO,EAAGd,IACE,GAALA,GAAe,IAALA,GAAgB,IAALA,GAAgB,IAALA,EAC3B,IACO,IAALA,EACF,IACO,IAALA,GACDe,KAAKC,MAAsB,EAAhBD,KAAKE,UAAgB,GAAGC,SAAS,IAE7CH,KAAKC,MAAsB,GAAhBD,KAAKE,UAAeC,SAAS,MAGlDR,KAAK,GACV,CAEA,SAAS1E,EAAamF,KAAkBC,GACtC,IAAK,MAAMnF,KAASmF,EAASb,KAAKnH,GAAYA,GAAS4C,aAAamF,KAClE,GAAoB,iBAATlF,EAAmB,OAAOA,EAGvC,OAAO,IACT,CAMA,SAASoF,KAAcD,GACrB,IAAK,MAAMhI,KAAWgI,EACK,eAArBhI,EAAQkI,WACVlI,EAAQ8C,aAAa,OAAQ,IAE/B9C,EAAQ8C,aAAa,YAAa,OAEtC,CAEA,SAASqF,KAAkBH,GACzB,IAAK,MAAMhI,KAAWgI,EACK,eAArBhI,EAAQkI,WACVlI,EAAQ+C,gBAAgB,QAG1B/C,EAAQ+C,gBAAgB,YAE5B,CAEA,SAASqF,EAAYpI,EAASqI,EAAwB,KACpD,OAAO,IAAI3G,SAASC,IAClB,MAAM2G,EAAa,KACjBtI,EAAQuI,oBAAoB,QAASD,GACrCtI,EAAQuI,oBAAoB,OAAQD,GACpC3G,GAAS,EAGX3B,EAAQe,iBAAiB,OAAQuH,EAAY,CAAEE,MAAM,IACrDxI,EAAQe,iBAAiB,QAASuH,EAAY,CAAEE,MAAM,IACtDxC,WAAWrE,EAAS0G,EAAsB,GAE9C,CAEA,SAASI,EAA0BC,GACjC,OAAQA,GACN,IAAK,UACH,OAAOC,QAAQC,aACjB,IAAK,UACL,IAAK,UACH,OAAOD,QAAQE,UAErB,CAMA,SAASC,KAAkBd,GACzB,MAAMU,EAAS9F,EAAa,uBAAwBoF,GAEpD,OAPF,SAAkBU,GAChB,MAAiB,WAAVA,GAAiC,WAAVA,GAAiC,WAAVA,CACvD,CAKSK,CAASL,GAAUA,EAAS,IACrC,CAEA,SAASM,EAAetK,GACtB,OAAOW,SAAS4J,cAAc,cAAcvK,MAC9C,CAEA,SAASwK,EAAexK,GACtB,MAAMsB,EAAUgJ,EAAetK,GAC/B,OAAOsB,GAAWA,EAAQmJ,OAC5B,CAEA,SAAS5E,IACP,MAAMvE,EAAUgJ,EAAe,aAE/B,GAAIhJ,EAAS,CACX,MAAMwE,MAAEA,EAAK2E,QAAEA,GAAYnJ,EAC3B,MAAgB,IAATwE,EAAc2E,EAAU3E,CAChC,CACH,CAiBA,SAAS4E,EAAuBpJ,EAASqJ,GACvC,GAAIrJ,aAAmBC,QACrB,OACED,EAAQK,QAAQgJ,IAAaD,EAAuBpJ,EAAQsJ,cAAgBtJ,EAAQuJ,eAAeC,KAAMH,EAG/G,CAEA,SAASI,EAAmBzJ,GAG1B,QAASA,GAAqD,MAA1CA,EAAQK,QAFE,0EAEgE,mBAAjBL,EAAQ0J,KACvF,CAEA,SAASC,EAA0BC,GACjC,OAAOpC,MAAMC,KAAKmC,EAA0BC,iBAAiB,gBAAgBC,KAAKL,EACpF,CAcA,SAASM,EAAoBrL,GAC3B,GAAa,WAATA,EACF,OAAO,EACF,GAAIA,EAAM,CACf,IAAK,MAAMsB,KAAWX,SAAS2K,kBAAkBtL,GAC/C,GAAIsB,aAAmBiK,kBAAmB,OAAO,EAGnD,OAAO,CACX,CACI,OAAO,CAEX,CAEA,SAASC,EAAwBnK,GAC/B,OAAOqJ,EAAuBrJ,EAAQ,2CACxC,CAEA,SAASoK,EAAmBC,GAC1B,OAAOC,EAAUD,EAAKxH,aAAa,SAAW,GAChD,CAYA,MAAMhE,EAAY,CAChB,gBAAiB,CACf0L,aAAc1L,IACZA,EAAUkE,aAAa,gBAAiB,QACxClE,EAAUmC,iBAAiB,QAASyE,EAAY,EAGlD+E,YAAa3L,IACXA,EAAUmE,gBAAgB,iBAC1BnE,EAAU2J,oBAAoB,QAAS/C,EAAY,GAIvDlC,SAAY,CACVgH,aAAc1L,GAAaA,EAAU0E,UAAW,EAChDiH,YAAa3L,GAAaA,EAAU0E,UAAW,IAoBnD,MAAMkH,EAAQ,IAhBd,MACE5L,GAAa,KAEb,WAAAiD,CAAY4I,GACVzJ,OAAO0J,OAAOvL,KAAMsL,EACrB,CAED,aAAI7L,GACF,OAAOO,MAAKP,CACb,CAED,aAAIA,CAAUiE,GACZ1D,MAAKP,EAAaA,EAAUiE,IAAUA,CACvC,GAGsB,CACvB8H,KAAM,KACN/L,UAAW,aAGP6L,EAAS,CACb1G,QACAyG,SAGF,SAASH,EAAUO,GACjB,OAAO,IAAIC,IAAID,EAAU9C,WAAYzI,SAASyL,QAChD,CAEA,SAASC,EAAUC,GACjB,IAAIC,EACJ,OAAID,EAAIE,KACCF,EAAIE,KAAK7D,MAAM,IAEZ4D,EAAcD,EAAIG,KAAKnE,MAAM,WAChCiE,EAAY,QADd,CAGT,CAEA,SAASG,EAAYvM,EAAMD,GAGzB,OAAOyL,EAFQzL,GAAWgE,aAAa,eAAiB/D,EAAK+D,aAAa,WAAa/D,EAAK6J,OAG9F,CAEA,SAAS2C,EAAaL,GACpB,OA6BF,SAA8BA,GAC5B,OALF,SAA2BA,GACzB,OAAOA,EAAIM,SAASvE,MAAM,KAAKM,MAAM,EACvC,CAGSkE,CAAkBP,GAAK3D,OAAO,GAAG,EAC1C,CA/BUmE,CAAqBR,GAAKhE,MAAM,aAAe,IAAI,IAAM,EACnE,CAEA,SAASyE,EAAaC,EAASV,GAC7B,MAAMW,EA6BR,SAAmBX,GACjB,OAGwBnI,EAHAmI,EAAIY,OAASZ,EAAIM,SAIlCzI,EAAMgJ,SAAS,KAAOhJ,EAAQA,EAAQ,IAD/C,IAA0BA,CAF1B,CA/BiBiJ,CAAUd,GACzB,OAAOU,EAAQP,OAASd,EAAUsB,GAAQR,MAAQO,EAAQP,KAAKY,WAAWJ,EAC5E,CAEA,SAASK,EAAoBC,EAAUC,GACrC,OAAOT,EAAaQ,EAAUC,KAAkBzB,EAAO1G,MAAMG,sBAAsBiI,IAAId,EAAaY,GACtG,CAEA,SAASG,EAAcpB,GACrB,MAAMqB,EAAStB,EAAUC,GACzB,OAAiB,MAAVqB,EAAiBrB,EAAIG,KAAK9D,MAAM,IAAKgF,EAAOnF,OAAS,IAAM8D,EAAIG,IACxE,CAEA,SAASmB,EAAWtB,GAClB,OAAOoB,EAAcpB,EACvB,CAsBA,MAAMuB,EACJ,WAAA1K,CAAY2K,GACVrN,KAAKqN,SAAWA,CACjB,CAED,aAAIC,GACF,OAAOtN,KAAKqN,SAASE,EACtB,CAED,UAAIC,GACF,OAAQxN,KAAKsN,SACd,CAED,eAAIG,GACF,OAAOzN,KAAK0N,YAAc,KAAO1N,KAAK0N,YAAc,GACrD,CAED,eAAIC,GACF,OAAO3N,KAAK0N,YAAc,KAAO1N,KAAK0N,YAAc,GACrD,CAED,cAAIE,GACF,OAAO5N,KAAKqN,SAASO,UACtB,CAED,YAAId,GACF,OAAO5B,EAAUlL,KAAKqN,SAASxB,IAChC,CAED,UAAIgC,GACF,OAAO7N,KAAK8N,aAAe9N,KAAK8N,YAAYjG,MAAM,yDACnD,CAED,cAAI6F,GACF,OAAO1N,KAAKqN,SAASU,MACtB,CAED,eAAID,GACF,OAAO9N,KAAKgO,OAAO,eACpB,CAED,gBAAIC,GACF,OAAOjO,KAAKqN,SAASa,QAAQC,MAC9B,CAED,gBAAIC,GACF,OAAIpO,KAAK6N,OACA7N,KAAKqN,SAASa,QAAQC,OAEtB5L,QAAQC,aAAQH,EAE1B,CAED,MAAA2L,CAAOzO,GACL,OAAOS,KAAKqN,SAASgB,QAAQtM,IAAIxC,EAClC,EAGH,MAAM+O,UAAmBtJ,IACvB,WAAAtC,CAAY6L,GACV5L,QACA3C,KAAKuO,QAAUA,CAChB,CAED,GAAAC,CAAI9K,GACF,GAAI1D,KAAKyO,MAAQzO,KAAKuO,QAAS,CAC7B,MACMG,EADW1O,KAAKoH,SACOuH,OAAOjL,MACpC1D,KAAK4O,OAAOF,EACb,CACD/L,MAAM6L,IAAI9K,EACX,EAGH,MAAMmL,EAAiB,IAAIP,EAAW,IAEhCQ,EAAcxN,OAAOyN,MAE3B,SAASC,EAAsBnD,EAAKoD,EAAU,IAC5C,MAAMC,EAAkB,IAAIC,QAAQF,EAAQZ,SAAW,CAAE,GACnDe,EAAahH,IAInB,OAHAyG,EAAeL,IAAIY,GACnBF,EAAgBG,OAAO,qBAAsBD,GAEtCN,EAAYjD,EAAK,IACnBoD,EACHZ,QAASa,GAEb,CAEA,SAASI,EAAsBC,GAC7B,OAAQA,EAAOtL,eACb,IAAK,MACH,OAAOuL,EAAYzN,IACrB,IAAK,OACH,OAAOyN,EAAYC,KACrB,IAAK,MACH,OAAOD,EAAYE,IACrB,IAAK,QACH,OAAOF,EAAYG,MACrB,IAAK,SACH,OAAOH,EAAYZ,OAEzB,CAEA,MAAMY,EAAc,CAClBzN,IAAK,MACL0N,KAAM,OACNC,IAAK,MACLC,MAAO,QACPf,OAAQ,UAGV,SAASgB,EAAuBC,GAC9B,OAAQA,EAAS5L,eACf,KAAK6L,EAAaC,UAChB,OAAOD,EAAaC,UACtB,KAAKD,EAAaE,MAChB,OAAOF,EAAaE,MACtB,QACE,OAAOF,EAAaG,WAE1B,CAEA,MAAMH,EAAe,CACnBG,WAAY,oCACZF,UAAW,sBACXC,MAAO,cAGT,MAAME,EACJC,gBAAkB,IAAIC,gBACtBC,GAA0BC,IAAD,EAEzB,WAAA5N,CAAYE,EAAU2M,EAAQzC,EAAUyD,EAAc,IAAIC,gBAAmB5P,EAAS,KAAM6P,EAAUX,EAAaG,YACjH,MAAOpE,EAAK6E,GAAQC,EAAqBzF,EAAU4B,GAAWyC,EAAQgB,EAAaE,GAEnFzQ,KAAK4C,SAAWA,EAChB5C,KAAK6L,IAAMA,EACX7L,KAAKY,OAASA,EACdZ,KAAK4Q,aAAe,CAClBC,YAAa,cACbC,SAAU,SACVvB,OAAQA,EAAOwB,cACf1C,QAAS,IAAKrO,KAAKgR,gBACnBN,KAAMA,EACNO,OAAQjR,KAAKkR,YACbC,SAAUnR,KAAK4C,SAASuO,UAAUnF,MAEpChM,KAAKyQ,QAAUA,CAChB,CAED,UAAIlB,GACF,OAAOvP,KAAK4Q,aAAarB,MAC1B,CAED,UAAIA,CAAO7L,GACT,MAAM0N,EAAYpR,KAAKqR,OAASrR,KAAK6L,IAAIyF,aAAetR,KAAK4Q,aAAaF,MAAQ,IAAIa,SAChFC,EAAclC,EAAsB5L,IAAU8L,EAAYzN,IAEhE/B,KAAK6L,IAAI4F,OAAS,GAElB,MAAO5F,EAAK6E,GAAQC,EAAqB3Q,KAAK6L,IAAK2F,EAAaJ,EAAWpR,KAAKyQ,SAEhFzQ,KAAK6L,IAAMA,EACX7L,KAAK4Q,aAAaF,KAAOA,EACzB1Q,KAAK4Q,aAAarB,OAASiC,EAAYT,aACxC,CAED,WAAI1C,GACF,OAAOrO,KAAK4Q,aAAavC,OAC1B,CAED,WAAIA,CAAQ3K,GACV1D,KAAK4Q,aAAavC,QAAU3K,CAC7B,CAED,QAAIgN,GACF,OAAI1Q,KAAKqR,OACArR,KAAK6L,IAAIyF,aAETtR,KAAK4Q,aAAaF,IAE5B,CAED,QAAIA,CAAKhN,GACP1D,KAAK4Q,aAAaF,KAAOhN,CAC1B,CAED,YAAIoJ,GACF,OAAO9M,KAAK6L,GACb,CAED,UAAI6F,GACF,OAAO1R,KAAK6L,IAAIyF,YACjB,CAED,WAAIK,GACF,OAAO3R,KAAK0Q,KAAOrI,MAAMC,KAAKtI,KAAK0Q,KAAKiB,WAAa,EACtD,CAED,MAAAC,GACE5R,KAAKmQ,gBAAgB0B,OACtB,CAED,aAAMC,GACJ,MAAMlB,aAAEA,GAAiB5Q,KACzBA,KAAK4C,SAASmP,eAAe/R,MAC7B,MAAMW,QAAcX,MAAKgS,EAA6BpB,GACtD,IACE5Q,KAAK4C,SAASqP,eAAejS,MAEzBW,EAAMoF,OAAOmM,aACflS,KAAKqN,SAAW1M,EAAMoF,OAAOmM,aAAa7E,SAE1CrN,KAAKqN,SAAW2B,EAAsBhP,KAAK6L,IAAIG,KAAM4E,GAGvD,MAAMvD,QAAiBrN,KAAKqN,SAC5B,aAAarN,KAAKmS,QAAQ9E,EAC3B,CAAC,MAAO+E,GACP,GAAmB,eAAfA,EAAM7S,KAIR,MAHIS,MAAKqS,EAA2BD,IAClCpS,KAAK4C,SAAS0P,eAAetS,KAAMoS,GAE/BA,CAEd,CAAc,QACRpS,KAAK4C,SAAS2P,gBAAgBvS,KAC/B,CACF,CAED,aAAMmS,CAAQ9E,GACZ,MAAMmF,EAAgB,IAAIpF,EAAcC,GAaxC,OAZczH,EAAS,8BAA+B,CACpDE,YAAY,EACZC,OAAQ,CAAEyM,iBACV5R,OAAQZ,KAAKY,SAEL6R,iBACRzS,KAAK4C,SAAS8P,iCAAiC1S,KAAMwS,GAC5CA,EAAclF,UACvBtN,KAAK4C,SAAS+P,6BAA6B3S,KAAMwS,GAEjDxS,KAAK4C,SAASgQ,0BAA0B5S,KAAMwS,GAEzCA,CACR,CAED,kBAAIxB,GACF,MAAO,CACL6B,OAAQ,mCAEX,CAED,UAAIxB,GACF,OAAOA,EAAOrR,KAAKuP,OACpB,CAED,eAAI2B,GACF,OAAOlR,KAAKmQ,gBAAgBc,MAC7B,CAED,kBAAA6B,CAAmBC,GACjB/S,KAAKqO,QAAgB,OAAI,CAAC0E,EAAU/S,KAAKqO,QAAgB,QAAGlG,KAAK,KAClE,CAED,OAAM6J,CAA6BpB,GACjC,MAAMoC,EAAsB,IAAIzQ,SAASC,GAAaxC,MAAKqQ,EAAyB7N,IAC9E7B,EAAQiF,EAAS,6BAA8B,CACnDE,YAAY,EACZC,OAAQ,CACN6K,eACA/E,IAAK7L,KAAK6L,IACVoH,OAAQjT,MAAKqQ,GAEfzP,OAAQZ,KAAKY,SAKf,OAHAZ,KAAK6L,IAAMlL,EAAMoF,OAAO8F,IACpBlL,EAAM8R,wBAAwBO,EAE3BrS,CACR,CAED,EAAA0R,CAA2BD,GAOzB,OANcxM,EAAS,4BAA6B,CAClDhF,OAAQZ,KAAKY,OACbkF,YAAY,EACZC,OAAQ,CAAEmN,QAASlT,KAAMoS,MAAOA,KAGpBK,gBACf,EAGH,SAASpB,EAAOG,GACd,OAAOlC,EAAsBkC,IAAgBhC,EAAYzN,GAC3D,CAEA,SAAS4O,EAAqBwC,EAAU5D,EAAQgB,EAAaE,GAC3D,MAAMa,EACJjJ,MAAMC,KAAKiI,GAAaxI,OAAS,EAAI,IAAIyI,gBAAgB4C,EAAsB7C,IAAgB4C,EAAS7B,aAE1G,OAAID,EAAO9B,GACF,CAAC8D,EAAyBF,EAAU7B,GAAe,MACjDb,GAAWX,EAAaG,WAC1B,CAACkD,EAAU7B,GAEX,CAAC6B,EAAU5C,EAEtB,CAEA,SAAS6C,EAAsB7C,GAC7B,MAAMoB,EAAU,GAEhB,IAAK,MAAOpS,EAAMmE,KAAU6M,EACtB7M,aAAiB4P,MAChB3B,EAAQ4B,KAAK,CAAChU,EAAMmE,IAG3B,OAAOiO,CACT,CAEA,SAAS0B,EAAyBxH,EAAK0E,GACrC,MAAMe,EAAe,IAAId,gBAAgB4C,EAAsB7C,IAI/D,OAFA1E,EAAI4F,OAASH,EAAa3I,WAEnBkD,CACT,CAEA,MAAM2H,GACJC,SAAU,EAEV,WAAA/Q,CAAYE,EAAU/B,GACpBb,KAAK4C,SAAWA,EAChB5C,KAAKa,QAAUA,EACfb,KAAK0T,qBAAuB,IAAIC,qBAAqB3T,KAAK4T,UAC3D,CAED,KAAAC,GACO7T,KAAKyT,UACRzT,KAAKyT,SAAU,EACfzT,KAAK0T,qBAAqBI,QAAQ9T,KAAKa,SAE1C,CAED,IAAAkT,GACM/T,KAAKyT,UACPzT,KAAKyT,SAAU,EACfzT,KAAK0T,qBAAqBM,UAAUhU,KAAKa,SAE5C,CAED+S,UAAajC,IACX,MAAMsC,EAAYtC,EAAQzJ,OAAO,GAAG,GAChC+L,GAAWC,gBACblU,KAAK4C,SAASuR,0BAA0BnU,KAAKa,QAC9C,EAIL,MAAMuT,GACJhS,mBAAqB,6BAErB,WAAOiS,CAAK/U,GACV,MAAsB,iBAAXA,EACF,IAAIU,KA5sBjB,SAAgC+G,GAC9B,MAAMuN,EAAWpU,SAASC,cAAc,YAExC,OADAmU,EAASC,UAAYxN,EACduN,EAAStK,OAClB,CAwsBsBwK,CAAuBlV,IAEhCA,CAEV,CAED,WAAAoD,CAAY+R,GACVzU,KAAKyU,SAIT,SAA8BA,GAC5B,IAAK,MAAM5T,KAAW4T,EAAS/J,iBAAiB,gBAAiB,CAC/D,MAAMgK,EAAgBxU,SAASyU,WAAW9T,GAAS,GAEnD,IAAK,MAAM+T,KAAsBF,EAAcG,gBAAgB7K,QAAQU,iBAAiB,UACtFkK,EAAmBE,YAAY7P,EAAsB2P,IAGvD/T,EAAQiU,YAAYJ,EACrB,CAED,OAAOD,CACT,CAhBoBM,CAAqBN,EACtC,EAiDH,MACMO,GAAgB,IA/BtB,MACEC,GAAmB,KACnBC,GAAc,KAEd,GAAAnT,CAAI8J,GACF,GAAI7L,MAAKkV,GAAelV,MAAKkV,EAAYrJ,MAAQA,GAAO7L,MAAKkV,EAAYC,OAASC,KAAKC,MACrF,OAAOrV,MAAKkV,EAAYhC,OAE3B,CAED,QAAAoC,CAASzJ,EAAKqH,EAASqC,GACrBvV,KAAKwV,QAELxV,MAAKiV,EAAmBpO,YAAW,KACjCqM,EAAQpB,UACR9R,KAAKoB,IAAIyK,EAAKqH,EAASqC,GACvBvV,MAAKiV,EAAmB,IAAI,GAlBX,IAoBpB,CAED,GAAA7T,CAAIyK,EAAKqH,EAASqC,GAChBvV,MAAKkV,EAAc,CAAErJ,MAAKqH,UAASiC,OAAQ,IAAIC,MAAK,IAAIA,MAAOK,UAAYF,GAC5E,CAED,KAAAC,GACMxV,MAAKiV,GAAkBS,aAAa1V,MAAKiV,GAC7CjV,MAAKkV,EAAc,IACpB,GAMGS,GAAsB,CAC1BC,YAAa,cACbC,WAAY,aACZC,QAAS,UACTC,UAAW,YACXC,SAAU,WACVC,QAAS,WAGX,MAAMC,GACJC,MAAQR,GAAoBC,YAE5B,oBAAOQ,CAAc9W,GACnB,OAAOiD,QAAQC,QAAQ6T,QAAQ/W,GAChC,CAED,WAAAoD,CAAYE,EAAU0T,EAAa7W,EAAW8W,GAAe,GAC3D,MAAMhH,EA2OV,SAAmB+G,EAAa7W,GAC9B,MAAM8P,EAAS9P,GAAWgE,aAAa,eAAiB6S,EAAY7S,aAAa,WAAa,GAC9F,OAAO6L,EAAsBC,EAAOtL,gBAAkBuL,EAAYzN,GACpE,CA9OmByU,CAAUF,EAAa7W,GAChC8J,EAgOV,SAAmBkN,EAAYjF,GAC7B,MAAMjI,EAAS2B,EAAUuL,GAErBpF,EAAOG,KACTjI,EAAOkI,OAAS,IAGlB,OAAOlI,CACT,CAxOmBmN,CAsNnB,SAAuBJ,EAAa7W,GAClC,MAAMkX,EAAkD,iBAAvBL,EAAY/M,OAAsB+M,EAAY/M,OAAS,KAExF,OAAI9J,GAAW2E,aAAa,cACnB3E,EAAUgE,aAAa,eAAiB,GAExC6S,EAAY7S,aAAa,WAAakT,GAAqB,EAEtE,CA9N6BC,CAAcN,EAAa7W,GAAY8P,GAC1DmB,EA0LV,SAAuB4F,EAAa7W,GAClC,MAAMoX,EAAW,IAAItF,SAAS+E,GACxB/W,EAAOE,GAAWgE,aAAa,QAC/BC,EAAQjE,GAAWgE,aAAa,SAElClE,GACFsX,EAASxH,OAAO9P,EAAMmE,GAAS,IAGjC,OAAOmT,CACT,CApMiBC,CAAcR,EAAa7W,GAClCgR,EA6OV,SAAoB6F,EAAa7W,GAC/B,OAAOmQ,EAAuBnQ,GAAWgE,aAAa,gBAAkB6S,EAAY7F,QACtF,CA/OoBsG,CAAWT,EAAa7W,GAExCO,KAAK4C,SAAWA,EAChB5C,KAAKsW,YAAcA,EACnBtW,KAAKP,UAAYA,EACjBO,KAAKkS,aAAe,IAAIhC,EAAalQ,KAAMuP,EAAQhG,EAAQmH,EAAM4F,EAAa7F,GAC9EzQ,KAAKuW,aAAeA,CACrB,CAED,UAAIhH,GACF,OAAOvP,KAAKkS,aAAa3C,MAC1B,CAED,UAAIA,CAAO7L,GACT1D,KAAKkS,aAAa3C,OAAS7L,CAC5B,CAED,UAAI6F,GACF,OAAOvJ,KAAKkS,aAAarG,IAAIlD,UAC9B,CAED,UAAIY,CAAO7F,GACT1D,KAAKkS,aAAarG,IAAMX,EAAUxH,EACnC,CAED,QAAIgN,GACF,OAAO1Q,KAAKkS,aAAaxB,IAC1B,CAED,WAAID,GACF,OAAOzQ,KAAKkS,aAAazB,OAC1B,CAED,UAAIY,GACF,OAAOrR,KAAKkS,aAAab,MAC1B,CAED,YAAIvE,GACF,OAAO9M,KAAKkS,aAAarG,GAC1B,CAID,WAAMgI,GACJ,MAAM+B,YAAEA,EAAWC,WAAEA,GAAeF,GAC9BqB,EAAsBvT,EAAa,qBAAsBzD,KAAKP,UAAWO,KAAKsW,aAEpF,GAAmC,iBAAxBU,EAAkC,CAC3C,MAAMZ,EAAgD,mBAAzB9K,EAAOD,MAAMgL,QACxC/K,EAAOD,MAAMgL,QACbH,GAAeE,cAGjB,UADqBA,EAAcY,EAAqBhX,KAAKsW,YAAatW,KAAKP,WAE7E,MAEH,CAED,GAAIO,KAAKmW,OAASP,EAEhB,OADA5V,KAAKmW,MAAQN,EACN7V,KAAKkS,aAAaJ,SAE5B,CAED,IAAAiC,GACE,MAAMiC,SAAEA,EAAQC,QAAEA,GAAYN,GAC9B,GAAI3V,KAAKmW,OAASH,GAAYhW,KAAKmW,OAASF,EAG1C,OAFAjW,KAAKmW,MAAQH,EACbhW,KAAKkS,aAAaN,UACX,CAEV,CAID,cAAAG,CAAemB,GACb,IAAKA,EAAQ7B,OAAQ,CACnB,MAAM4F,EAwHZ,SAAwBC,GACtB,GAAkB,MAAdA,EAAoB,CACtB,MACMC,GADUjX,SAASiX,OAASjX,SAASiX,OAAOvP,MAAM,MAAQ,IACzC+C,MAAMwM,GAAWA,EAAOvK,WAAWsK,KAC1D,GAAIC,EAAQ,CACV,MAAMzT,EAAQyT,EAAOvP,MAAM,KAAKM,MAAM,GAAGC,KAAK,KAC9C,OAAOzE,EAAQ0T,mBAAmB1T,QAASrB,CAC5C,CACF,CACH,CAjIoBgV,CAAetN,EAAe,gBAAkBA,EAAe,cACzEkN,IACF/D,EAAQ7E,QAAQ,gBAAkB4I,EAErC,CAEGjX,KAAKsX,kCAAkCpE,IACzCA,EAAQJ,mBAAmBsB,GAActG,YAE5C,CAED,cAAAmE,CAAesF,GACbvX,KAAKmW,MAAQR,GAAoBG,QAC7B9V,KAAKP,WAAW6L,EAAOD,MAAM5L,UAAU0L,aAAanL,KAAKP,WAC7DO,KAAKwX,iBACL1O,EAAW9I,KAAKsW,aAChB1Q,EAAS,qBAAsB,CAC7BhF,OAAQZ,KAAKsW,YACbvQ,OAAQ,CAAE0R,eAAgBzX,QAE5BA,KAAK4C,SAAS8U,sBAAsB1X,KACrC,CAED,gCAAA0S,CAAiCQ,EAAS7F,GACxC2H,GAAcQ,QAEdxV,KAAKuH,OAAS,CAAEoQ,QAAStK,EAASC,UAAWkF,cAAenF,EAC7D,CAED,4BAAAsF,CAA6BO,EAAS7F,GACpC,GAAIA,EAASI,aAAeJ,EAASM,YACnC3N,KAAK4C,SAASgV,iCAAiC5X,KAAMqN,QAMvD,GAFA2H,GAAcQ,QAEVxV,KAAK6X,oBAAoB3E,IA8FjC,SAA0C7F,GACxC,OAA8B,KAAvBA,EAASK,aAAsBL,EAASO,UACjD,CAhG6CkK,CAAiCzK,GAAW,CACnF,MAAM+E,EAAQ,IAAI2F,MAAM,oDACxB/X,KAAK4C,SAASoV,sBAAsBhY,KAAMoS,EAChD,MACMpS,KAAKmW,MAAQR,GAAoBI,UACjC/V,KAAKuH,OAAS,CAAEoQ,SAAS,EAAMnF,cAAenF,GAC9CrN,KAAK4C,SAASqV,oCAAoCjY,KAAMqN,EAE3D,CAED,yBAAAuF,CAA0BM,EAAS7F,GACjCrN,KAAKuH,OAAS,CAAEoQ,SAAS,EAAOnF,cAAenF,GAC/CrN,KAAK4C,SAASgV,iCAAiC5X,KAAMqN,EACtD,CAED,cAAAiF,CAAeY,EAASd,GACtBpS,KAAKuH,OAAS,CAAEoQ,SAAS,EAAOvF,SAChCpS,KAAK4C,SAASoV,sBAAsBhY,KAAMoS,EAC3C,CAED,eAAAG,CAAgBgF,GACdvX,KAAKmW,MAAQR,GAAoBM,QAC7BjW,KAAKP,WAAW6L,EAAOD,MAAM5L,UAAU2L,YAAYpL,KAAKP,WAC5DO,KAAKkY,qBACLlP,EAAehJ,KAAKsW,aACpB1Q,EAAS,mBAAoB,CAC3BhF,OAAQZ,KAAKsW,YACbvQ,OAAQ,CAAE0R,eAAgBzX,QAASA,KAAKuH,UAE1CvH,KAAK4C,SAASuV,uBAAuBnY,KACtC,CAID,cAAAwX,GACE,GAAKxX,KAAKP,WAAcO,KAAKoY,YAE7B,GAAIpY,KAAKP,UAAU4Y,QAAQ,UACzBrY,KAAKsY,mBAAqBtY,KAAKP,UAAU8U,UACzCvU,KAAKP,UAAU8U,UAAYvU,KAAKoY,iBAC3B,GAAIpY,KAAKP,UAAU4Y,QAAQ,SAAU,CAC1C,MAAME,EAAQvY,KAAKP,UACnBO,KAAKsY,mBAAqBC,EAAM7U,MAChC6U,EAAM7U,MAAQ1D,KAAKoY,WACpB,CACF,CAED,kBAAAF,GACE,GAAKlY,KAAKP,WAAcO,KAAKsY,mBAE7B,GAAItY,KAAKP,UAAU4Y,QAAQ,UACzBrY,KAAKP,UAAU8U,UAAYvU,KAAKsY,wBAC3B,GAAItY,KAAKP,UAAU4Y,QAAQ,SAAU,CAC5BrY,KAAKP,UACbiE,MAAQ1D,KAAKsY,kBACpB,CACF,CAED,mBAAAT,CAAoB3E,GAClB,OAAQA,EAAQ7B,QAAUrR,KAAKuW,YAChC,CAED,iCAAAe,CAAkCpE,GAChC,OAAQA,EAAQ7B,QAn3BpB,SAAsBzI,KAAkBC,GACtC,OAAOA,EAAS2P,MAAM3X,GAAYA,GAAWA,EAAQuD,aAAawE,IACpE,CAi3B8BxE,CAAa,oBAAqBpE,KAAKP,UAAWO,KAAKsW,YAClF,CAED,eAAI8B,GACF,OAAOpY,KAAKP,WAAWgE,aAAa,0BACrC,EA2DH,MAAMgV,GACJ,WAAA/V,CAAY7B,GACVb,KAAKa,QAAUA,CAChB,CAED,iBAAI6X,GACF,OAAO1Y,KAAKa,QAAQ4D,cAAciU,aACnC,CAED,YAAIC,GACF,MAAO,IAAI3Y,KAAKa,QAAQ8X,SACzB,CAED,SAAAC,CAAU1L,GACR,OAA2C,MAApClN,KAAK6Y,oBAAoB3L,EACjC,CAED,mBAAA2L,CAAoB3L,GAClB,OAAOA,EAASlN,KAAKa,QAAQiJ,cAAc,QAAQoD,gBAAqBA,OAAc,IACvF,CAED,eAAI/G,GACF,OAAOnG,KAAKa,QAAQsF,WACrB,CAED,6BAAI2S,GACF,OAAOtO,EAA0BxK,KAAKa,QACvC,CAED,qBAAIkY,GACF,OAAOC,GAA0BhZ,KAAKa,QACvC,CAED,uBAAAoY,CAAwBC,GACtB,OAAOD,GAAwBjZ,KAAKa,QAASqY,EAC9C,CAED,iCAAAC,CAAkCC,GAChC,MAAMC,EAAsB,CAAA,EAE5B,IAAK,MAAMC,KAA2BtZ,KAAK+Y,kBAAmB,CAC5D,MAAMG,GAAEA,GAAOI,EACTC,EAAsBH,EAASH,wBAAwBC,GACzDK,IACFF,EAAoBH,GAAM,CAACI,EAAyBC,GAEvD,CAED,OAAOF,CACR,EAGH,SAASJ,GAAwBO,EAAMN,GACrC,OAAOM,EAAK1P,cAAc,IAAIoP,0BAChC,CAEA,SAASF,GAA0BQ,GACjC,OAAOA,EAAK9O,iBAAiB,6BAC/B,CAEA,MAAM+O,GACJhG,SAAU,EAEV,WAAA/Q,CAAYE,EAAU8W,GACpB1Z,KAAK4C,SAAWA,EAChB5C,KAAK0Z,YAAcA,CACpB,CAED,KAAA7F,GACO7T,KAAKyT,UACRzT,KAAK0Z,YAAY9X,iBAAiB,SAAU5B,KAAK2Z,gBAAgB,GACjE3Z,KAAKyT,SAAU,EAElB,CAED,IAAAM,GACM/T,KAAKyT,UACPzT,KAAK0Z,YAAYtQ,oBAAoB,SAAUpJ,KAAK2Z,gBAAgB,GACpE3Z,KAAKyT,SAAU,EAElB,CAEDkG,eAAiB,KACf3Z,KAAK0Z,YAAYtQ,oBAAoB,SAAUpJ,KAAK4Z,eAAe,GACnE5Z,KAAK0Z,YAAY9X,iBAAiB,SAAU5B,KAAK4Z,eAAe,EAAM,EAGxEA,cAAiBjZ,IACf,IAAKA,EAAM8R,iBAAkB,CAC3B,MAAM/S,EAAOiB,EAAMC,kBAAkBL,gBAAkBI,EAAMC,YAASyB,EAChE5C,EAAYkB,EAAMlB,gBAAa4C,EAGnC3C,GAaR,SAAwCA,EAAMD,GAC5C,MAAM8P,EAAS9P,GAAWgE,aAAa,eAAiB/D,EAAK+D,aAAa,UAE1E,MAAiB,UAAV8L,CACT,CAhBQsK,CAA+Bna,EAAMD,IAkB7C,SAAuCC,EAAMD,GAC3C,MAAMmB,EAASnB,GAAWgE,aAAa,eAAiB/D,EAAK+D,aAAa,UAE1E,OAAOmH,EAAoBhK,EAC7B,CArBQkZ,CAA8Bpa,EAAMD,IACpCO,KAAK4C,SAASmX,eAAera,EAAMD,KAEnCkB,EAAM2F,iBACN3F,EAAM4F,2BACNvG,KAAK4C,SAASoX,cAActa,EAAMD,GAErC,GAgBL,MAAMwa,GACJC,GAAyB5J,IAAD,EACxB6J,GAA+B7J,IAAD,EAE9B,WAAA5N,CAAYE,EAAU/B,GACpBb,KAAK4C,SAAWA,EAChB5C,KAAKa,QAAUA,CAChB,CAID,cAAAuZ,CAAelN,GACb,MAAMrM,EAAUb,KAAKoZ,SAASP,oBAAoB3L,GAC9CrM,GACFb,KAAKqa,gBAAgBxZ,GACrBb,KAAKsa,aAAazZ,IAElBb,KAAKua,iBAAiB,CAAEC,EAAG,EAAGC,EAAG,GAEpC,CAED,0BAAAC,CAA2B5N,GACzB9M,KAAKoa,eAAexO,EAAUkB,GAC/B,CAED,eAAAuN,CAAgBxZ,GACdA,EAAQ8Z,gBACT,CAED,YAAAL,CAAazZ,GACPA,aAAmBlB,cACjBkB,EAAQuD,aAAa,YACvBvD,EAAQ0J,SAER1J,EAAQ8C,aAAa,WAAY,MACjC9C,EAAQ0J,QACR1J,EAAQ+C,gBAAgB,aAG7B,CAED,gBAAA2W,EAAiBC,EAAEA,EAACC,EAAEA,IACpBza,KAAK4a,WAAWC,SAASL,EAAGC,EAC7B,CAED,WAAAK,GACE9a,KAAKua,iBAAiB,CAAEC,EAAG,EAAGC,EAAG,GAClC,CAED,cAAIG,GACF,OAAOtZ,MACR,CAID,YAAMyZ,CAAOC,GACX,MAAMtW,UAAEA,EAASuW,aAAEA,EAAYC,WAAEA,EAAYC,YAAa/B,GAAa4B,EAIjEI,EAAmBF,EAEzB,GAAID,EACF,IACEjb,KAAKqb,cAAgB,IAAI9Y,SAASC,GAAaxC,MAAKka,EAAwB1X,IAC5ExC,KAAKgb,SAAWA,QACVhb,KAAKsb,wBAAwBN,GAEnC,MAAMO,EAAqB,IAAIhZ,SAASC,GAAaxC,MAAKma,EAA8B3X,IAClFyM,EAAU,CAAEgE,OAAQjT,MAAKma,EAA6BY,OAAQ/a,KAAKgb,SAASQ,cAAeC,aAAczb,KAAKgb,SAASS,cACrGzb,KAAK4C,SAAS8Y,sBAAsBtC,EAAUnK,UAC1CsM,QAEtBvb,KAAK2b,eAAeX,GAC1Bhb,KAAK4C,SAASgZ,qBAAqBxC,EAAU1U,EAAW1E,KAAKgb,SAASS,cACtEzb,KAAK4C,SAASiZ,0BAA0B7b,KAAKa,SAC7Cb,KAAK8b,wBAAwBd,EACrC,CAAgB,eACDhb,KAAKgb,SACZhb,MAAKka,OAAsB7X,UACpBrC,KAAKqb,aACb,MACQD,GACTpb,KAAK+b,WAAWf,EAASgB,aAE5B,CAED,UAAAD,CAAWE,GACTjc,KAAK4C,SAASsZ,gBAAgBD,EAC/B,CAED,6BAAMX,CAAwBN,GAC5Bhb,KAAKmc,cAAcnB,EAAStW,iBACtBsW,EAASoB,iBAChB,CAED,aAAAD,CAAczX,GACRA,EACF1E,KAAKa,QAAQ8C,aAAa,qBAAsB,IAEhD3D,KAAKa,QAAQ+C,gBAAgB,qBAEhC,CAED,kBAAAyY,CAAmBC,GACjBtc,KAAKa,QAAQ8C,aAAa,6BAA8B2Y,EACzD,CAED,oBAAAC,GACEvc,KAAKa,QAAQ+C,gBAAgB,6BAC9B,CAED,oBAAM+X,CAAeX,SACbA,EAASD,QAChB,CAED,uBAAAe,CAAwBd,GACtBA,EAASwB,iBACV,EAGH,MAAMC,WAAkBxC,GACtB,OAAAyC,GACE1c,KAAKa,QAAQ0T,UAAY,4DAC1B,CAED,YAAI6E,GACF,OAAO,IAAIX,GAASzY,KAAKa,QAC1B,EAGH,MAAM8b,GACJ,WAAAja,CAAYE,EAAU/B,GACpBb,KAAK4C,SAAWA,EAChB5C,KAAKa,QAAUA,CAChB,CAED,KAAAgT,GACE7T,KAAKa,QAAQe,iBAAiB,QAAS5B,KAAK4c,cAC5C1c,SAAS0B,iBAAiB,cAAe5B,KAAK6c,aAC9C3c,SAAS0B,iBAAiB,qBAAsB5B,KAAK8c,UACtD,CAED,IAAA/I,GACE/T,KAAKa,QAAQuI,oBAAoB,QAASpJ,KAAK4c,cAC/C1c,SAASkJ,oBAAoB,cAAepJ,KAAK6c,aACjD3c,SAASkJ,oBAAoB,qBAAsBpJ,KAAK8c,UACzD,CAEDF,aAAgBjc,IACVX,KAAK+c,wBAAwBpc,GAC/BX,KAAKgd,WAAarc,SAEXX,KAAKgd,UACb,EAGHH,YAAelc,IACTX,KAAKgd,YAAchd,KAAK+c,wBAAwBpc,IAC9CX,KAAK4C,SAASqa,yBAAyBtc,EAAMC,OAAQD,EAAMoF,OAAO8F,IAAKlL,EAAMoF,OAAOmX,iBACtFld,KAAKgd,WAAW1W,iBAChB3F,EAAM2F,iBACNtG,KAAK4C,SAASua,qBAAqBxc,EAAMC,OAAQD,EAAMoF,OAAO8F,IAAKlL,EAAMoF,OAAOmX,uBAG7Eld,KAAKgd,UAAU,EAGxBF,UAAaM,WACJpd,KAAKgd,UAAU,EAGxB,uBAAAD,CAAwBpc,GACtB,MAAMC,EAASD,EAAMuF,SAAWvF,EAAMC,QAAQI,cAAgBL,EAAMC,OAC9DC,EAAUkK,EAAwBnK,IAAWA,EAEnD,OAAOC,aAAmBC,SAAWD,EAAQK,QAAQ,sBAAwBlB,KAAKa,OACnF,EAGH,MAAMwc,GACJ5J,SAAU,EAEV,WAAA/Q,CAAYE,EAAU8W,GACpB1Z,KAAK4C,SAAWA,EAChB5C,KAAK0Z,YAAcA,CACpB,CAED,KAAA7F,GACO7T,KAAKyT,UACRzT,KAAK0Z,YAAY9X,iBAAiB,QAAS5B,KAAKU,eAAe,GAC/DV,KAAKyT,SAAU,EAElB,CAED,IAAAM,GACM/T,KAAKyT,UACPzT,KAAK0Z,YAAYtQ,oBAAoB,QAASpJ,KAAKU,eAAe,GAClEV,KAAKyT,SAAU,EAElB,CAED/S,cAAgB,KACdV,KAAK0Z,YAAYtQ,oBAAoB,QAASpJ,KAAK4c,cAAc,GACjE5c,KAAK0Z,YAAY9X,iBAAiB,QAAS5B,KAAK4c,cAAc,EAAM,EAGtEA,aAAgBjc,IACd,GAAIA,aAAiB2c,YAActd,KAAK+c,wBAAwBpc,GAAQ,CACtE,MACMsK,EAAOF,EADGpK,EAAM4c,cAAgB5c,EAAM4c,eAAe,IAAO5c,EAAMC,QAExE,GAAIqK,GAAQL,EAAoBK,EAAKrK,QAAS,CAC5C,MAAMkM,EAAW9B,EAAmBC,GAChCjL,KAAK4C,SAAS4a,yBAAyBvS,EAAM6B,EAAUnM,KACzDA,EAAM2F,iBACNtG,KAAK4C,SAAS6a,uBAAuBxS,EAAM6B,GAE9C,CACF,GAGH,uBAAAiQ,CAAwBpc,GACtB,QACGA,EAAMC,QAAUD,EAAMC,OAAO8c,mBAC9B/c,EAAM8R,kBACN9R,EAAMgd,MAAQ,GACdhd,EAAMid,QACNjd,EAAMkd,SACNld,EAAMmd,SACNnd,EAAMod,SAET,EAGH,MAAMC,GACJ,WAAAtb,CAAYE,EAAU/B,GACpBb,KAAK4C,SAAWA,EAChB5C,KAAKie,gBAAkB,IAAIZ,GAAkBrd,KAAMa,EACpD,CAED,KAAAgT,GACE7T,KAAKie,gBAAgBpK,OACtB,CAED,IAAAE,GACE/T,KAAKie,gBAAgBlK,MACtB,CAID,4BAAAmK,CAA6BjT,EAAM6B,GACjC,OAAO,CACR,CAED,iCAAAqR,CAAkClT,EAAM6B,GAEvC,CAID,wBAAA0Q,CAAyBvS,EAAM6B,EAAUoQ,GACvC,OACEld,KAAK4C,SAASwb,6BAA6BnT,EAAM6B,EAAUoQ,KAC1DjS,EAAK7G,aAAa,sBAAwB6G,EAAK7G,aAAa,qBAEhE,CAED,sBAAAqZ,CAAuBxS,EAAM6B,GAC3B,MAAMpN,EAAOQ,SAASC,cAAc,QAGpC,IAAK,MAAOZ,EAAMmE,KAAUoJ,EAASwE,aACnC5R,EAAK2P,OAAOxN,OAAO0J,OAAOrL,SAASC,cAAc,SAAU,CAAEN,KAFlD,SAEwDN,OAAMmE,WAG3E,MAAM6F,EAAS1H,OAAO0J,OAAOuB,EAAU,CAAE2E,OAAQ,KACjD/R,EAAKiE,aAAa,aAAc,QAChCjE,EAAKiE,aAAa,SAAU4F,EAAOyC,MACnCtM,EAAKiE,aAAa,SAAU,IAE5B,MAAM4L,EAAStE,EAAKxH,aAAa,qBAC7B8L,GAAQ7P,EAAKiE,aAAa,SAAU4L,GAExC,MAAM8O,EAAapT,EAAKxH,aAAa,oBACjC4a,GAAY3e,EAAKiE,aAAa,mBAAoB0a,GAEtD,MAAMC,EAAc3U,EAAesB,GAC/BqT,GAAa5e,EAAKiE,aAAa,oBAAqB2a,GAExD,MAAMC,EAAetT,EAAKxH,aAAa,sBACnC8a,GAAc7e,EAAKiE,aAAa,qBAAsB4a,GAEtCtT,EAAK7G,aAAa,sBACrB1E,EAAKiE,aAAa,oBAAqB,IAExD3D,KAAK4C,SAAS4b,4BAA4BvT,EAAM6B,EAAUpN,GAE1DQ,SAASwQ,KAAKrQ,YAAYX,GAC1BA,EAAKkC,iBAAiB,oBAAoB,IAAMlC,EAAK+e,UAAU,CAAEpV,MAAM,IACvEzC,uBAAsB,IAAMlH,EAAKF,iBAClC,EAGH,MAAMkf,GACJ,wCAAaC,CAA4B/b,EAAUyW,EAAqBuF,GACtE,MAAMC,EAAQ,IAAI7e,KAAK4C,EAAUyW,GACjCwF,EAAMC,cACAF,IACNC,EAAME,OACP,CAED,WAAArc,CAAYE,EAAUyW,GACpBrZ,KAAK4C,SAAWA,EAChB5C,KAAKqZ,oBAAsBA,CAC5B,CAED,KAAAyF,GACE,IAAK,MAAM5F,KAAMlZ,KAAKqZ,oBAAqB,CACzC,MAAOC,EAAyBC,GAAuBvZ,KAAKqZ,oBAAoBH,GAChFlZ,KAAK4C,SAASoc,cAAc1F,EAAyBC,GACrDvZ,KAAKif,0CAA0C1F,EAChD,CACF,CAED,KAAAwF,GACE,IAAK,MAAM7F,KAAMlZ,KAAKqZ,oBAAqB,CACzC,MAAOC,GAA2BtZ,KAAKqZ,oBAAoBH,GAC3DlZ,KAAKkf,wCAAwC5F,GAC7CtZ,KAAKmf,uCAAuC7F,GAC5CtZ,KAAK4C,SAASwc,aAAa9F,EAC5B,CACF,CAED,yCAAA2F,CAA0CI,GACxC,MAAMC,EAuBV,SAA8CD,GAC5C,MAAMxe,EAAUX,SAASC,cAAc,QAGvC,OAFAU,EAAQ8C,aAAa,OAAQ,+BAC7B9C,EAAQ8C,aAAa,UAAW0b,EAAiBnG,IAC1CrY,CACT,CA5BwB0e,CAAqCF,GACzDA,EAAiBvK,YAAYwK,EAC9B,CAED,uCAAAJ,CAAwCG,GACtC,MAAMnR,EAAQmR,EAAiBG,WAAU,GACzCH,EAAiBvK,YAAY5G,EAC9B,CAED,sCAAAiR,CAAuCE,GACrC,MAAMC,EAActf,KAAKyf,mBAAmBJ,EAAiBnG,IAC7DoG,GAAaxK,YAAYuK,EAC1B,CAED,kBAAAI,CAAmBvG,GACjB,OAAOlZ,KAAK0f,aAAa/U,MAAM9J,GAAYA,EAAQmJ,SAAWkP,GAC/D,CAED,gBAAIwG,GACF,MAAO,IAAIxf,SAASwK,iBAAiB,mDACtC,EAUH,MAAMiV,GACJjH,GAAiB,KAEjB,oBAAO8C,CAAcoE,EAAgBC,GAEpC,CAED,WAAAnd,CAAYod,EAAiB3E,EAAazW,EAAWwW,GAAa,GAChElb,KAAK8f,gBAAkBA,EACvB9f,KAAKmb,YAAcA,EACnBnb,KAAK0E,UAAYA,EACjB1E,KAAKkb,WAAaA,EAClBlb,KAAKwb,cAAgBxb,KAAK0C,YAAY8Y,cACtCxb,KAAK+f,QAAU,IAAIxd,SAAQ,CAACC,EAASwd,IAAYhgB,KAAKigB,mBAAqB,CAAEzd,UAASwd,WACvF,CAED,gBAAI/E,GACF,OAAO,CACR,CAED,mBAAIiF,GACF,OAAO,CACR,CAED,gBAAIlE,GAEH,CAED,eAAAI,GAEC,CAED,MAAArB,GAEC,CAED,eAAAyB,GACMxc,KAAKigB,qBACPjgB,KAAKigB,mBAAmBzd,iBACjBxC,KAAKigB,mBAEf,CAED,iCAAMtB,CAA4BC,SAC1BF,GAAMC,4BAA4B3e,KAAMA,KAAKqZ,oBAAqBuF,EACzE,CAED,8BAAAuB,GACE,GAAIngB,KAAKkgB,gBAAiB,CACxB,MAAMrf,EAAUb,KAAKogB,kBAAkBtH,0BACnCjY,GACFA,EAAQ0J,OAEX,CACF,CAID,aAAAyU,CAAc1F,GACRtZ,MAAK0Y,GAELY,EAAwB+G,SAASrgB,KAAK8f,gBAAgBpH,iBACxD1Y,MAAK0Y,EAAiB1Y,KAAK8f,gBAAgBpH,cAE9C,CAED,YAAA0G,CAAa9F,GACPA,EAAwB+G,SAASrgB,MAAK0Y,IAAmB1Y,MAAK0Y,aAA0B/Y,cAC1FK,MAAK0Y,EAAenO,QAEpBvK,MAAK0Y,EAAiB,KAEzB,CAED,qBAAI0H,GACF,OAAOpgB,KAAKmb,YAAYhV,YAAcnG,KAAKmb,YAAcnb,KAAK8f,eAC/D,CAED,kBAAIF,GACF,OAAO5f,KAAK8f,gBAAgBjf,OAC7B,CAED,cAAIgf,GACF,OAAO7f,KAAKmb,YAAYta,OACzB,CAED,uBAAIwY,GACF,OAAOrZ,KAAK8f,gBAAgB3G,kCAAkCnZ,KAAKmb,YACpE,CAED,gBAAIM,GACF,MAAO,SACR,EAGH,MAAM6E,WAAsBX,GAC1B,oBAAOnE,CAAcoE,EAAgBC,GACnC,MAAMU,EAAmBrgB,SAASsgB,cAClCD,EAAiBE,mBAAmBb,GACpCW,EAAiBG,iBAEjB,MAAMC,EAAed,EACfe,EAAcD,EAAalc,eAAe+b,cAC5CI,IACFA,EAAYH,mBAAmBE,GAC/Bf,EAAevf,YAAYugB,EAAYC,mBAE1C,CAED,WAAAne,CAAYE,EAAUkd,EAAiB3E,EAAaK,EAAe9W,EAAWwW,GAAa,GACzFvY,MAAMmd,EAAiB3E,EAAaK,EAAe9W,EAAWwW,GAC9Dlb,KAAK4C,SAAWA,CACjB,CAED,gBAAIqY,GACF,OAAO,CACR,CAED,YAAMF,SACEvU,IACNxG,KAAK2e,6BAA4B,KAC/B3e,KAAK8gB,kBAAkB,IAEzB9gB,KAAK+gB,4BACCva,IACNxG,KAAKmgB,uCACC3Z,IACNxG,KAAKghB,wBACN,CAED,gBAAAF,GACE9gB,KAAK4C,SAASqe,gBAAgBjhB,KAAK4f,eAAgB5f,KAAK6f,YACxD7f,KAAKwb,cAAcxb,KAAK4f,eAAgB5f,KAAK6f,WAC9C,CAED,mBAAAkB,GACE,GAAI/gB,KAAK4f,eAAevb,YAAcrE,KAAK6f,WAAWxb,WAAY,CAChE,MAAMxD,EAAUb,KAAK4f,eAAesB,kBAC9BC,GAuBuBzd,EAvBW1D,KAAK4f,eAAenc,aAAa,yBAuBrC2d,EAvB+D,MAwB1F,OAAT1d,GAA2B,SAATA,GAA6B,UAATA,GAA8B,WAATA,EACtDA,EAEA0d,GA1BCC,EA8BZ,SAA4B3d,EAAO0d,GACjC,MAAa,QAAT1d,GAA4B,UAATA,EACdA,EAEA0d,CAEX,CApCuBE,CAAmBthB,KAAK4f,eAAenc,aAAa,4BAA6B,QAElG,GAAI5C,EAEF,OADAA,EAAQ8Z,eAAe,CAAEwG,QAAOE,cACzB,CAEV,CAgBL,IAAmC3d,EAAO0d,EAftC,OAAO,CACR,CAED,sBAAAJ,GACE,IAAK,MAAMpM,KAAsB5U,KAAKuhB,kBAAmB,CACvD,MAAMC,EAAyBvc,EAAsB2P,GACrDA,EAAmBE,YAAY0M,EAChC,CACF,CAED,qBAAID,GACF,OAAOvhB,KAAK4f,eAAelV,iBAAiB,SAC7C,EAmHH,IAAI+W,GAAY,WAuBd,MAAMC,EAAO,OAKPC,EAAW,CACfC,WAAY,YACZC,UAAW,CACTC,gBAAiBJ,EACjBK,eAAgBL,EAChBM,kBAAmBN,EACnBO,iBAAkBP,EAClBQ,kBAAmBR,EACnBS,iBAAkBT,EAClBU,uBAAwBV,GAE1BW,KAAM,CACJre,MAAO,QACPse,eAAiBC,GAA4C,SAApCA,EAAI9e,aAAa,eAC1C+e,eAAiBD,GAA6C,SAArCA,EAAI9e,aAAa,gBAC1Cgf,aAAcf,EACdgB,iBAAkBhB,GAEpBiB,cAAc,GAyGhB,MAAMC,EAAgB,WAgHpB,SAASC,EAAWC,EAAWC,EAAUC,EAAgBC,GACvD,IAAgD,IAA5CA,EAAIpB,UAAUC,gBAAgBiB,GAAqB,OAAO,KAC9D,GAAIE,EAAIC,MAAMlW,IAAI+V,GAAW,CAE3B,MAAMI,EAAgBjjB,SAASC,cACN,EAAWijB,SAKpC,OAHAN,EAAUO,aAAaF,EAAeH,GACtCM,EAAUH,EAAeJ,EAAUE,GACnCA,EAAIpB,UAAUE,eAAeoB,GACtBA,CACf,CAAa,CAEL,MAAMI,EAAiBrjB,SAASyU,WAAWoO,GAAU,GAGrD,OAFAD,EAAUO,aAAaE,EAAgBP,GACvCC,EAAIpB,UAAUE,eAAewB,GACtBA,CACR,CACF,CAKD,MAAMC,EAAgB,WAoEpB,SAASC,EAAaR,EAAKS,EAASC,GAClC,IAAIC,EAASX,EAAIC,MAAMnhB,IAAI2hB,GACvBG,EAASZ,EAAIC,MAAMnhB,IAAI4hB,GAE3B,IAAKE,IAAWD,EAAQ,OAAO,EAE/B,IAAK,MAAM1K,KAAM0K,EAKf,GAAIC,EAAO7W,IAAIkM,GACb,OAAO,EAGX,OAAO,CACR,CAQD,SAAS4K,EAAYJ,EAASC,GAE5B,MAAMI,EAAiC,EACjCC,EAAiC,EAEvC,OACED,EAAOE,WAAaD,EAAOC,UAC3BF,EAAOX,UAAYY,EAAOZ,WAIxBW,EAAO7K,IAAM6K,EAAO7K,KAAO8K,EAAO9K,GAEvC,CAED,OAhGA,SAAuB+J,EAAKzJ,EAAM0K,EAAYC,GAC5C,IAAIC,EAAY,KACZC,EAAc7K,EAAK6K,YACnBC,EAAwB,EAExBC,EAASL,EACb,KAAOK,GAAUA,GAAUJ,GAAU,CAEnC,GAAIL,EAAYS,EAAQ/K,GAAO,CAC7B,GAAIiK,EAAaR,EAAKsB,EAAQ/K,GAC5B,OAAO+K,EAIS,OAAdH,IAEGnB,EAAIC,MAAMlW,IAAIuX,KAEjBH,EAAYG,GAGjB,CAqBD,GAnBgB,OAAdH,GACAC,GACAP,EAAYS,EAAQF,KAIpBC,IACAD,EAAcA,EAAYA,YAKtBC,GAAyB,IAC3BF,OAAY/hB,IAMZkiB,EAAOlE,SAASngB,SAASwY,eAAgB,MAE7C6L,EAASA,EAAOF,WACjB,CAED,OAAOD,GAAa,IACrB,CAiDF,CA5GqB,GAyHtB,SAASI,EAAWvB,EAAKzJ,GAEvB,GAAIyJ,EAAIC,MAAMlW,IAAIwM,GAEhBiL,EAAWxB,EAAIyB,OAAQlL,EAAM,UACxB,CAEL,IAA8C,IAA1CyJ,EAAIpB,UAAUK,kBAAkB1I,GAAiB,OACrDA,EAAKmL,YAAYrkB,YAAYkZ,GAC7ByJ,EAAIpB,UAAUM,iBAAiB3I,EAChC,CACF,CASD,SAASoL,EAAmB3B,EAAK4B,EAAgBC,GAE/C,IAAIP,EAASM,EAEb,KAAON,GAAUA,IAAWO,GAAc,CACxC,IAAIC,EAAgC,EACpCR,EAASA,EAAOF,YAChBG,EAAWvB,EAAK8B,EACjB,CACD,OAAOR,CACR,CAYD,SAASS,EAAeL,EAAYzL,EAAI+L,EAAOhC,GAC7C,MAAMriB,EAGFqiB,EAAIriB,OAAOkJ,cAAc,IAAIoP,MAC3B+J,EAAIyB,OAAO5a,cAAc,IAAIoP,KAInC,OAWF,SAA0CrY,EAASoiB,GACjD,MAAM/J,EAAKrY,EAAQqY,GAEnB,KAAQrY,EAAUA,EAAQ8jB,YAAa,CACrC,IAAIO,EAAQjC,EAAIC,MAAMnhB,IAAIlB,GACtBqkB,IACFA,EAAMtW,OAAOsK,GACRgM,EAAMzW,MACTwU,EAAIC,MAAMtU,OAAO/N,GAGtB,CACF,CAzBCskB,CAAiCvkB,EAAQqiB,GACzCwB,EAAWE,EAAY/jB,EAAQqkB,GACxBrkB,CACR,CAkCD,SAAS6jB,EAAWE,EAAY9jB,EAASokB,GAEvC,GAAIN,EAAWF,WACb,IAEEE,EAAWF,WAAW5jB,EAASokB,EAChC,CAAC,MAAOG,GAEPT,EAAWtB,aAAaxiB,EAASokB,EAClC,MAEDN,EAAWtB,aAAaxiB,EAASokB,EAEpC,CAED,OA1UA,SACEhC,EACAH,EACAuC,EACArC,EAAiB,KACjBmB,EAAW,MAITrB,aAAqBwC,qBACrBD,aAAqBC,sBAGrBxC,EAAYA,EAAU9Y,QAEtBqb,EAAYA,EAAUrb,SAExBgZ,IAAmBF,EAAUyC,WAG7B,IAAK,MAAMxC,KAAYsC,EAAUG,WAAY,CAE3C,GAAIxC,GAAkBA,GAAkBmB,EAAU,CAChD,MAAMsB,EAAYjC,EAChBP,EACAF,EACAC,EACAmB,GAEF,GAAIsB,EAAW,CAETA,IAAczC,GAChB4B,EAAmB3B,EAAKD,EAAgByC,GAE1CnC,EAAUmC,EAAW1C,EAAUE,GAC/BD,EAAiByC,EAAUpB,YAC3B,QACD,CACF,CAGD,GAAItB,aAAoBjiB,SAAWmiB,EAAIyC,cAAc1Y,IAAI+V,EAAS7J,IAAK,CAErE,MAAMyM,EAAaX,EACjBlC,EACAC,EAAS7J,GACT8J,EACAC,GAEFK,EAAUqC,EAAY5C,EAAUE,GAChCD,EAAiB2C,EAAWtB,YAC5B,QACD,CAGD,MAAMuB,EAAe/C,EACnBC,EACAC,EACAC,EACAC,GAGE2C,IACF5C,EAAiB4C,EAAavB,YAEjC,CAGD,KAAOrB,GAAkBA,GAAkBmB,GAAU,CACnD,MAAMY,EAAW/B,EACjBA,EAAiBA,EAAeqB,YAChCG,EAAWvB,EAAK8B,EACjB,CACF,CAkQF,CAtWqB,GA2WhBzB,EAAY,WAkKhB,SAASuC,EAAqBC,EAAYjG,EAAYjX,EAAeqa,GAEnE,MAAM8C,EAAelG,EAAWjX,GAGhC,GAAImd,IADaD,EAAWld,GACO,CACjC,MAAMod,EAAeC,EACnBrd,EACAkd,EACA,SACA7C,GAEG+C,IAGHF,EAAWld,GAAiBiX,EAAWjX,IAErCmd,EACGC,GAGHF,EAAWniB,aAAaiF,EAAe,IAGpCqd,EAAgBrd,EAAekd,EAAY,SAAU7C,IACxD6C,EAAWliB,gBAAgBgF,EAGhC,CACF,CASD,SAASqd,EAAgBC,EAAMrlB,EAASslB,EAAYlD,GAClD,QACW,UAATiD,IACAjD,EAAImD,mBACJvlB,IAAYX,SAASwY,iBAMrB,IADAuK,EAAIpB,UAAUO,uBAAuB8D,EAAMrlB,EAASslB,EAGvD,CAOD,SAASE,EAA2BC,EAAuBrD,GACzD,QACIA,EAAImD,mBACNE,IAA0BpmB,SAASwY,eACnC4N,IAA0BpmB,SAASwQ,IAEtC,CAED,OA5NA,SAAmBgT,EAAS6C,EAAYtD,GACtC,OAAIA,EAAIuD,cAAgB9C,IAAYxjB,SAASwY,cAEpC,OAGoD,IAAzDuK,EAAIpB,UAAUG,kBAAkB0B,EAAS6C,KAIzC7C,aAAmB+C,iBAAmBxD,EAAIZ,KAAKqE,SACjDhD,aAAmB+C,iBACA,UAAnBxD,EAAIZ,KAAKre,MAGT2iB,EACEjD,EACV,EACUT,KAqBN,SAAyBS,EAASC,EAASV,GACzC,IAAIpjB,EAAO8jB,EAAQM,SAInB,GAAa,IAATpkB,EAA+B,CACjC,MAAMkkB,EAAiC,EACjCC,EAAiC,EAEjC4C,EAAgB7C,EAAOre,WACvBmhB,EAAgB7C,EAAOte,WAC7B,IAAK,MAAMohB,KAAgBD,EACrBZ,EAAgBa,EAAavnB,KAAMwkB,EAAQ,SAAUd,IAGrDc,EAAOtgB,aAAaqjB,EAAavnB,QAAUunB,EAAapjB,OAC1DqgB,EAAOpgB,aAAamjB,EAAavnB,KAAMunB,EAAapjB,OAIxD,IAAK,IAAI+D,EAAImf,EAAc7e,OAAS,EAAG,GAAKN,EAAGA,IAAK,CAClD,MAAMsf,EAAeH,EAAcnf,GAInC,GAAKsf,IAEA/C,EAAO5f,aAAa2iB,EAAaxnB,MAAO,CAC3C,GAAI0mB,EAAgBc,EAAaxnB,KAAMwkB,EAAQ,SAAUd,GACvD,SAEFc,EAAOngB,gBAAgBmjB,EAAaxnB,KACrC,CACF,CAEI8mB,EAA2BtC,EAAQd,IAuB5C,SAAwB6C,EAAYjG,EAAYoD,GAC9C,GACE6C,aAAsBkB,kBACtBnH,aAAsBmH,kBACF,SAApBnH,EAAWhgB,KACX,CACA,IAAIonB,EAAWpH,EAAWnc,MACtBwjB,EAAWpB,EAAWpiB,MAG1BmiB,EAAqBC,EAAYjG,EAAY,UAAWoD,GACxD4C,EAAqBC,EAAYjG,EAAY,WAAYoD,GAEpDpD,EAAWzb,aAAa,SAKlB8iB,IAAaD,IACjBhB,EAAgB,QAASH,EAAY,SAAU7C,KAClD6C,EAAWniB,aAAa,QAASsjB,GACjCnB,EAAWpiB,MAAQujB,IAPhBhB,EAAgB,QAASH,EAAY,SAAU7C,KAClD6C,EAAWpiB,MAAQ,GACnBoiB,EAAWliB,gBAAgB,SAUvC,MAAa,GACLkiB,aAAsBqB,mBACtBtH,aAAsBsH,kBAEtBtB,EAAqBC,EAAYjG,EAAY,WAAYoD,QACpD,GACL6C,aAAsBsB,qBACtBvH,aAAsBuH,oBACtB,CACA,IAAIH,EAAWpH,EAAWnc,MACtBwjB,EAAWpB,EAAWpiB,MAC1B,GAAIuiB,EAAgB,QAASH,EAAY,SAAU7C,GACjD,OAEEgE,IAAaC,IACfpB,EAAWpiB,MAAQujB,GAGnBnB,EAAWP,YACXO,EAAWP,WAAW8B,YAAcJ,IAEpCnB,EAAWP,WAAW8B,UAAYJ,EAErC,CACF,CAxEKK,CAAevD,EAAQC,EAAQf,EAElC,CAGY,IAATpjB,GAAqC,IAATA,GAC1B6jB,EAAQ2D,YAAc1D,EAAQ0D,YAChC3D,EAAQ2D,UAAY1D,EAAQ0D,UAGjC,CAhEGE,CAAgB7D,EAAS6C,EAAYtD,GAChCoD,EAA2B3C,EAAST,IAEvCL,EAAcK,EAAKS,EAAS6C,KAGhCtD,EAAIpB,UAAUI,iBAAiByB,EAAS6C,IApB/B7C,EAsBV,CAgMF,CApOiB,GA8QlB,SAASiD,EAAkBa,EAASC,EAASxE,GAC3C,IAAIyE,EAAQ,GACRC,EAAU,GACVC,EAAY,GACZC,EAAgB,GAGhBC,EAAoB,IAAIC,IAC5B,IAAK,MAAMC,KAAgBP,EAAQ9O,SACjCmP,EAAkB1mB,IAAI4mB,EAAaC,UAAWD,GAIhD,IAAK,MAAME,KAAkBV,EAAQ7O,SAAU,CAE7C,IAAIwP,EAAeL,EAAkB9a,IAAIkb,EAAeD,WACpDG,EAAenF,EAAIZ,KAAKG,eAAe0F,GACvCG,EAAcpF,EAAIZ,KAAKC,eAAe4F,GACtCC,GAAgBE,EACdD,EAEFT,EAAQpU,KAAK2U,IAIbJ,EAAkBlZ,OAAOsZ,EAAeD,WACxCL,EAAUrU,KAAK2U,IAGM,WAAnBjF,EAAIZ,KAAKre,MAGPokB,IACFT,EAAQpU,KAAK2U,GACbL,EAActU,KAAK2U,KAIyB,IAA1CjF,EAAIZ,KAAKI,aAAayF,IACxBP,EAAQpU,KAAK2U,EAIpB,CAIDL,EAActU,QAAQuU,EAAkB1gB,UAExC,IAAIkhB,EAAW,GACf,IAAK,MAAM3E,KAAWkE,EAAe,CAEnC,IAAI7D,EACF9jB,SAASsgB,cAAc+H,yBAAyB5E,EAAQsE,WAEhE,WACM,IAA8C,IAA1ChF,EAAIpB,UAAUC,gBAAgBkC,GAAmB,CACnD,GACG,SAAUA,GAAUA,EAAOhY,MAC3B,QAASgY,GAAUA,EAAOxgB,IAC3B,CACsC,IAAIhB,EACtCud,EAAU,IAAIxd,SAAQ,SAAUimB,GAClChmB,EAAUgmB,CACtB,IACUxE,EAAOpiB,iBAAiB,QAAQ,WAC9BY,GACZ,IACU8lB,EAAS/U,KAAKwM,EACf,CACDyH,EAAQnnB,YAAY2jB,GACpBf,EAAIpB,UAAUE,eAAeiC,GAC7B0D,EAAMnU,KAAKyQ,EACZ,CACF,CAID,IAAK,MAAMyE,KAAkBd,GAC6B,IAApD1E,EAAIpB,UAAUK,kBAAkBuG,KAClCjB,EAAQlnB,YAAYmoB,GACpBxF,EAAIpB,UAAUM,iBAAiBsG,IASnC,OALAxF,EAAIZ,KAAKK,iBAAiB8E,EAAS,CACjCE,MAAOA,EACPgB,KAAMd,EACND,QAASA,IAEJW,CACR,CAKD,MAAMK,EAAqB,WA6DzB,SAASC,IACP,MAAMlE,EAASxkB,SAASC,cAAc,OAGtC,OAFAukB,EAAOtkB,QAAS,EAChBF,SAASwQ,KAAKmY,sBAAsB,WAAYnE,GACzCA,CACR,CAQD,SAASoE,EAAeC,GACtB,IAAIlgB,EAAWR,MAAMC,KAAKygB,EAAKre,iBAAiB,SAIhD,OAHIqe,EAAK7P,IACPrQ,EAAS0K,KAAKwV,GAETlgB,CACR,CAaD,SAASmgB,EAAsB9F,EAAOwC,EAAeqD,EAAMlgB,GACzD,IAAK,MAAM0Z,KAAO1Z,EAChB,GAAI6c,EAAc1Y,IAAIuV,EAAIrJ,IAAK,CAE7B,IAAI+P,EAAU1G,EAGd,KAAO0G,GAAS,CACd,IAAI/D,EAAQhC,EAAMnhB,IAAIknB,GAQtB,GANa,MAAT/D,IACFA,EAAQ,IAAIlgB,IACZke,EAAM9hB,IAAI6nB,EAAS/D,IAErBA,EAAM1W,IAAI+T,EAAIrJ,IAEV+P,IAAYF,EAAM,MACtBE,EAAUA,EAAQjoB,aACnB,CACF,CAEJ,CAiED,OA3KA,SAA4B0iB,EAAS6C,EAAYjb,GAC/C,MAAMoa,cAAEA,EAAaxC,MAAEA,GAqHzB,SAAsBgG,EAAY3C,GAChC,MAAM4C,EAAgBL,EAAeI,GAC/BE,EAAgBN,EAAevC,GAE/Bb,EAoBR,SAA6ByD,EAAeC,GAC1C,IAAIC,EAAe,IAAIrkB,IAGnBskB,EAAkB,IAAIvB,IAC1B,IAAK,MAAM7O,GAAEA,EAAEkK,QAAEA,KAAa+F,EACxBG,EAAgBtc,IAAIkM,GACtBmQ,EAAa7a,IAAI0K,GAEjBoQ,EAAgBloB,IAAI8X,EAAIkK,GAI5B,IAAIsC,EAAgB,IAAI1gB,IACxB,IAAK,MAAMkU,GAAEA,EAAEkK,QAAEA,KAAagG,EACxB1D,EAAc1Y,IAAIkM,GACpBmQ,EAAa7a,IAAI0K,GACRoQ,EAAgBvnB,IAAImX,KAAQkK,GACrCsC,EAAclX,IAAI0K,GAKtB,IAAK,MAAMA,KAAMmQ,EACf3D,EAAc9W,OAAOsK,GAEvB,OAAOwM,CACR,CA/CuB6D,CAAoBJ,EAAeC,GAGzD,IAAIlG,EAAQ,IAAI6E,IAChBiB,EAAsB9F,EAAOwC,EAAewD,EAAYC,GAGxD,MAAMK,EAAUjD,EAAWkD,iBAAmBlD,EAG9C,OAFAyC,EAAsB9F,EAAOwC,EAAe8D,EAASJ,GAE9C,CAAE1D,gBAAexC,QACzB,CApIkCwG,CAAahG,EAAS6C,GAEjDoD,EA4BR,SAAuBre,GACrB,IAAIse,EAAc/nB,OAAO0J,OAAO,CAAE,EAAEoW,GAepC,OAZA9f,OAAO0J,OAAOqe,EAAate,GAG3Bse,EAAY/H,UAAYhgB,OAAO0J,OAC7B,CAAE,EACFoW,EAASE,UACTvW,EAAOuW,WAIT+H,EAAYvH,KAAOxgB,OAAO0J,OAAO,CAAA,EAAIoW,EAASU,KAAM/W,EAAO+W,MAEpDuH,CACR,CA7CsBC,CAAcve,GAC7BsW,EAAa+H,EAAa/H,YAAc,YAC9C,IAAK,CAAC,YAAa,aAAakI,SAASlI,GACvC,KAAM,wCAAwCA,IAGhD,MAAO,CACLhhB,OAAQ8iB,EACR6C,WAAYA,EACZjb,OAAQqe,EACR/H,WAAYA,EACZ4E,aAAcmD,EAAanD,aAC3BJ,kBAAmBuD,EAAavD,kBAChCzD,aAAcgH,EAAahH,aAC3BO,MAAOA,EACPwC,cAAeA,EACfhB,OAAQkE,IACR/G,UAAW8H,EAAa9H,UACxBQ,KAAMsH,EAAatH,KAEtB,CAqJF,CApL0B,IAyLrB0H,iBAAEA,EAAgBC,gBAAEA,GAAoB,WAE5C,MAAMC,EAAuB,IAAIC,QAmIjC,MAAO,CAAEH,iBA5HT,SAA0B/f,GACxB,OAAIA,aAAmBmgB,SACdngB,EAAQrF,gBAERqF,CAEV,EAsH0BggB,gBA/G3B,SAASA,EAAgBzD,GACvB,GAAkB,MAAdA,EACF,OAAOrmB,SAASC,cAAc,OACzB,GAA0B,iBAAfomB,EAChB,OAAOyD,EAgEX,SAAsBzD,GACpB,IAAI6D,EAAS,IAAIpjB,UAGbqjB,EAAyB9D,EAAW5e,QACtC,uCACA,IAIF,GACE0iB,EAAuBxiB,MAAM,aAC7BwiB,EAAuBxiB,MAAM,aAC7BwiB,EAAuBxiB,MAAM,YAC7B,CACA,IAAImC,EAAUogB,EAAOnjB,gBAAgBsf,EAAY,aAEjD,GAAI8D,EAAuBxiB,MAAM,YAE/B,OADAoiB,EAAqBzb,IAAIxE,GAClBA,EACF,CAEL,IAAIsgB,EAActgB,EAAQub,WAI1B,OAHI+E,GACFL,EAAqBzb,IAAI8b,GAEpBA,CACR,CACT,CAAa,CAGL,IAIItgB,EAJcogB,EAAOnjB,gBACvB,mBAAqBsf,EAAa,qBAClC,aAGY7V,KAAK5G,cAAc,YAC/BE,QAEF,OADAigB,EAAqBzb,IAAIxE,GAClBA,CACR,CACF,CAzG0BugB,CAAahE,IAC/B,GACL0D,EAAqBjd,IAAG,GAGxB,OAAA,EACK,GAAIuZ,aAAsBxlB,KAAM,CACrC,GAAIwlB,EAAW5B,WAIb,OAyBN,SAA+B4B,GAC7B,MAAA,CAEIf,WAAY,CAACe,GAEb7b,iBAAmB8f,IAEjB,MAAM3hB,EAAW0d,EAAW7b,iBAAiB8f,GAE7C,OAAOjE,EAAWlO,QAAQmS,GAAK,CAACjE,KAAe1d,GAAYA,CAAQ,EAGrEwa,aAAc,CAACoH,EAAGC,IAAMnE,EAAW5B,WAAWtB,aAAaoH,EAAGC,GAE9DjG,WAAY,CAACgG,EAAGC,IAAMnE,EAAW5B,WAAWF,WAAWgG,EAAGC,GAE1D,mBAAIjB,GACF,OAAOlD,CACR,EAGN,CA9CYoE,CAAsBpE,GACxB,CAEL,MAAMqE,EAAc1qB,SAASC,cAAc,OAE3C,OADAyqB,EAAYvb,OAAOkX,GACZqE,CACR,CACT,CAAa,CAGL,MAAMA,EAAc1qB,SAASC,cAAc,OAC3C,IAAK,MAAMoiB,IAAO,IAAIgE,GACpBqE,EAAYvb,OAAOkT,GAErB,OAAOqI,CACR,CACF,EAiFF,CAtI6C,GA2I9C,MAAO,CACLC,MA5nCF,SAAenH,EAAS6C,EAAYjb,EAAS,CAAA,GAC3CoY,EAAUqG,EAAiBrG,GAC3B,MAAMC,EAAUqG,EAAgBzD,GAC1BtD,EAAM0F,EAAmBjF,EAASC,EAASrY,GAE3Cwf,EAyDR,SAA6B7H,EAAK8H,GAChC,IAAK9H,EAAI3X,OAAOqX,aAAc,OAAOoI,IACrC,IAAIrS,EAEAxY,SACR,cAGI,KAEIwY,aAAyBsO,kBACzBtO,aAAyB0O,qBAG3B,OAAO2D,IAGT,MAAQ7R,GAAI8R,EAAeC,eAAEA,EAAcC,aAAEA,GAAiBxS,EAExDyS,EAAUJ,IAEZC,GAAmBA,IAAoB9qB,SAASwY,eAAeQ,KACjER,EAAgBuK,EAAIriB,OAAOkJ,cAAc,IAAIkhB,KAC7CtS,GAAenO,SAEbmO,IAAkBA,EAAcwS,cAAgBA,GAClDxS,EAAc0S,kBAAkBH,EAAgBC,GAGlD,OAAOC,CACR,CAvFsBE,CAAoBpI,GAAK,IAorBhD,SAA0BA,EAAKS,EAASC,EAAS/E,GAC/C,GAAIqE,EAAIZ,KAAKlB,MAAO,CAClB,MAAMqG,EAAU9D,EAAQ5Z,cAAc,QAChC2d,EAAU9D,EAAQ7Z,cAAc,QACtC,GAAI0d,GAAWC,EAAS,CACtB,MAAMa,EAAW3B,EAAkBa,EAASC,EAASxE,GAErD,OAAO1gB,QAAQ+oB,IAAIhD,GAAUiD,MAAK,KAChC,MAAMC,EAAS3pB,OAAO0J,OAAO0X,EAAK,CAChCZ,KAAM,CACJlB,OAAO,EACPuF,QAAQ,KAGZ,OAAO9H,EAAS4M,EAAO,GAE1B,CACF,CAED,OAAO5M,EAASqE,EACjB,CAvsBUwI,CACLxI,EACAS,EACAC,GACkCV,GACT,cAAnBA,EAAIrB,YACNgB,EAAcK,EAAKS,EAASC,GACrBtb,MAAMC,KAAKob,EAAQ8B,aAoBpC,SAAwBvC,EAAKS,EAASC,GACpC,MAAMb,EAAYkH,EAAgBtG,GAIlC,IAAI8B,EAAand,MAAMC,KAAKwa,EAAU0C,YACtC,MAAMkG,EAAQlG,EAAWmG,QAAQjI,GAE3BkI,EAAcpG,EAAWzd,QAAU2jB,EAAQ,GAajD,OAXA9I,EACEK,EACAH,EACAa,EAEAD,EACAA,EAAQW,aAIVmB,EAAand,MAAMC,KAAKwa,EAAU0C,YAC3BA,EAAWtd,MAAMwjB,EAAOlG,EAAWzd,OAAS6jB,EACpD,CAxCgBC,CAAe5I,EAAKS,EAASC,OAO5C,OADAV,EAAIyB,OAAOjG,SACJqM,CACR,EAsmCCnJ,WAEH,CAxrCe,GA0rChB,SAASmK,GAAclM,EAAgBC,GAAYgC,UAAEA,KAAc5S,GAAY,IAC7EwS,GAAUoJ,MAAMjL,EAAgBC,EAAY,IACvC5Q,EACH4S,UAAW,IAAIkK,GAA0BlK,IAE7C,CAEA,SAASe,GAAchD,EAAgBC,GACrCiM,GAAclM,EAAgBC,EAAW2F,WAAY,CACnD5D,WAAY,aAEhB,CAEA,MAAMmK,GACJ/J,GAEA,WAAAtf,EAAYsf,kBAAEA,GAAsB,IAClChiB,MAAKgiB,EAAqBA,GAAsB,MAAM,EACvD,CAEDF,gBAAmBtI,KACRA,EAAKN,IAAMM,EAAKpV,aAAa,yBAA2BlE,SAAS8rB,eAAexS,EAAKN,KAGhG8I,kBAAoB,CAACpC,EAAgBC,KACnC,GAAID,aAA0B9e,QAAS,CACrC,IAAK8e,EAAexb,aAAa,yBAA2BpE,MAAKgiB,EAAmBpC,EAAgBC,GAAa,CAO/G,OANcja,EAAS,6BAA8B,CACnDE,YAAY,EACZlF,OAAQgf,EACR7Z,OAAQ,CAAE6Z,iBAAgBC,gBAGdpN,gBACtB,CACQ,OAAO,CAEV,GAGH2P,uBAAyB,CAACxZ,EAAehI,EAAQqrB,KACjCrmB,EAAS,+BAAgC,CACrDE,YAAY,EACZlF,SACAmF,OAAQ,CAAE6C,gBAAeqjB,kBAGbxZ,iBAGhByP,kBAAqB1I,GACZxZ,KAAKgiB,kBAAkBxI,GAGhCyI,iBAAmB,CAACrC,EAAgBC,KAC9BD,aAA0B9e,SAC5B8E,EAAS,sBAAuB,CAC9BhF,OAAQgf,EACR7Z,OAAQ,CAAE6Z,iBAAgBC,eAE7B,EAIL,MAAMqM,WAA8B5L,GAClC,oBAAO9E,CAAcoE,EAAgBC,GACnCja,EAAS,2BAA4B,CACnChF,OAAQgf,EACR7Z,OAAQ,CAAE6Z,iBAAgBC,gBAG5B+C,GAAchD,EAAgBC,EAC/B,CAED,iCAAMlB,CAA4BC,GAChC,aAAaA,GACd,EAGH,MAAMuN,GACJ/pB,yBAA2B,IAE3B,qBAAWgqB,GACT,OAAOllB,CAAQ;;;;;;;;;;kBAUDilB,GAAYE;oBACVF,GAAYE,kBAAoB,OAAOF,GAAYE,kBAAoB;;;KAIxF,CAEDC,QAAS,EACT5oB,MAAQ,EACR6oB,SAAU,EAEV,WAAA7pB,GACE1C,KAAKwsB,kBAAoBxsB,KAAKysB,0BAC9BzsB,KAAK0sB,gBAAkB1sB,KAAK2sB,wBAC5B3sB,KAAK4sB,2BACL5sB,KAAK6sB,SAAS,EACf,CAED,IAAAC,GACO9sB,KAAKusB,UACRvsB,KAAKusB,SAAU,EACfvsB,KAAK+sB,yBACL/sB,KAAKgtB,iBAER,CAED,IAAAC,GACMjtB,KAAKusB,UAAYvsB,KAAKssB,SACxBtsB,KAAKssB,QAAS,EACdtsB,KAAKktB,qBAAoB,KACvBltB,KAAKmtB,2BACLntB,KAAKotB,gBACLptB,KAAKusB,SAAU,EACfvsB,KAAKssB,QAAS,CAAK,IAGxB,CAED,QAAAO,CAASnpB,GACP1D,KAAK0D,MAAQA,EACb1D,KAAK6D,SACN,CAID,wBAAA+oB,GACE1sB,SAASmiB,KAAKgB,aAAarjB,KAAKwsB,kBAAmBtsB,SAASmiB,KAAKkD,WAClE,CAED,sBAAAwH,GACE/sB,KAAK0sB,gBAAgB1oB,MAAMqpB,MAAQ,IACnCrtB,KAAK0sB,gBAAgB1oB,MAAMspB,QAAU,IACrCptB,SAASyE,gBAAgB0e,aAAarjB,KAAK0sB,gBAAiBxsB,SAASwQ,MACrE1Q,KAAK6D,SACN,CAED,mBAAAqpB,CAAoBtO,GAClB5e,KAAK0sB,gBAAgB1oB,MAAMspB,QAAU,IACrCzmB,WAAW+X,EAA0C,IAAhCuN,GAAYE,kBAClC,CAED,wBAAAc,GACMntB,KAAK0sB,gBAAgB/H,YACvBzkB,SAASyE,gBAAgBrE,YAAYN,KAAK0sB,gBAE7C,CAED,cAAAM,GACOhtB,KAAKutB,kBACRvtB,KAAKutB,gBAAkBjsB,OAAOksB,YAAYxtB,KAAKytB,QAAStB,GAAYE,mBAEvE,CAED,aAAAe,GACE9rB,OAAOosB,cAAc1tB,KAAKutB,wBACnBvtB,KAAKutB,eACb,CAEDE,QAAU,KACRztB,KAAK6sB,SAAS7sB,KAAK0D,MAAQ8E,KAAKE,SAAW,IAAI,EAGjD,OAAA7E,GACE+C,uBAAsB,KACpB5G,KAAK0sB,gBAAgB1oB,MAAMqpB,MAAW,GAAkB,GAAbrtB,KAAK0D,MAAb,GAA0B,GAEhE,CAED,uBAAA+oB,GACE,MAAM5rB,EAAUX,SAASC,cAAc,SACvCU,EAAQhB,KAAO,WACfgB,EAAQyE,YAAc6mB,GAAYC,WAClC,MAAMjnB,EAAWC,IAIjB,OAHID,IACFtE,EAAQwE,MAAQF,GAEXtE,CACR,CAED,qBAAA8rB,GACE,MAAM9rB,EAAUX,SAASC,cAAc,OAEvC,OADAU,EAAQ8sB,UAAY,qBACb9sB,CACR,EAGH,MAAM+sB,WAAqBnV,GACzBoV,mBAAqB7tB,KAAK2Y,SACvBmV,QAAQjtB,IAwFb,SAA2BA,GACzB,MAAMuiB,EAAUviB,EAAQkI,UACxB,MAAkB,YAAXqa,CACT,CA3F0B2K,CAAkBltB,KACvCmH,KAAKnH,GAsGV,SAA6BA,GACvBA,EAAQuD,aAAa,UACvBvD,EAAQ8C,aAAa,QAAS,IAGhC,OAAO9C,CACT,CA5GsBmtB,CAAoBntB,KACrCyG,QAAO,CAACC,EAAQ1G,KACf,MAAMonB,UAAEA,GAAcpnB,EAChBotB,EACJhG,KAAa1gB,EACTA,EAAO0gB,GACP,CACEpoB,KAAMquB,GAAYrtB,GAClBstB,QAASC,GAAiBvtB,GAC1BgI,SAAU,IAElB,MAAO,IACFtB,EACH0gB,CAACA,GAAY,IACRgG,EACHplB,SAAU,IAAIolB,EAAQplB,SAAUhI,IAEnC,GACA,IAEL,2BAAIwtB,GACF,OAAOxsB,OAAOysB,KAAKtuB,KAAK6tB,oBACrBC,QAAQ7F,GAAcjoB,KAAK6tB,mBAAmB5F,GAAWkG,UACzDhmB,KAAK,GACT,CAED,8BAAAomB,CAA+BnV,GAC7B,OAAOpZ,KAAKwuB,qCAAqC,SAAUpV,EAC5D,CAED,kCAAAqV,CAAmCrV,GACjC,OAAOpZ,KAAKwuB,qCAAqC,aAAcpV,EAChE,CAED,oCAAAoV,CAAqCE,EAAatV,GAChD,OAAOvX,OAAOysB,KAAKtuB,KAAK6tB,oBACrBC,QAAQ7F,KAAgBA,KAAa7O,EAASyU,sBAC9C7lB,KAAKigB,GAAcjoB,KAAK6tB,mBAAmB5F,KAC3C6F,QAAO,EAAGjuB,UAAWA,GAAQ6uB,IAC7B1mB,KAAI,EAAGa,UAAWhI,MAAeA,GACrC,CAED,uBAAI8tB,GACF,OAAO9sB,OAAOysB,KAAKtuB,KAAK6tB,oBAAoBvmB,QAAO,CAACC,EAAQ0gB,KAC1D,MAAMpoB,KAAEA,EAAIsuB,QAAEA,EAAOtlB,SAAEA,GAAa7I,KAAK6tB,mBAAmB5F,GAC5D,OAAY,MAARpoB,GAAiBsuB,EAEVtlB,EAASd,OAAS,EACpB,IAAIR,KAAWsB,EAASX,MAAM,IAE9BX,EAJA,IAAIA,KAAWsB,EAKvB,GACA,GACJ,CAED,YAAA+lB,CAAarvB,GACX,MAAMsB,EAAUb,KAAK6uB,sBAAsBtvB,GAC3C,OAAOsB,EAAUA,EAAQ4C,aAAa,WAAa,IACpD,CAED,qBAAAorB,CAAsBtvB,GACpB,OAAOsC,OAAOysB,KAAKtuB,KAAK6tB,oBAAoBvmB,QAAO,CAACC,EAAQ0gB,KAC1D,MACEpf,UAAWhI,IACTb,KAAK6tB,mBAAmB5F,GAC5B,OAgCN,SAAsCpnB,EAAStB,GAC7C,MAAM6jB,EAAUviB,EAAQkI,UACxB,MAAkB,QAAXqa,GAAqBviB,EAAQ4C,aAAa,SAAWlE,CAC9D,CAnCauvB,CAA6BjuB,EAAStB,GAAQsB,EAAU0G,IAC9DlF,EACJ,EAGH,SAAS6rB,GAAYrtB,GACnB,OAWF,SAAyBA,GACvB,MAAMuiB,EAAUviB,EAAQkI,UACxB,MAAkB,UAAXqa,CACT,CAdM2L,CAAgBluB,GACX,SAoBX,SAA6BA,GAC3B,MAAMuiB,EAAUviB,EAAQkI,UACxB,MAAkB,SAAXqa,GAAkC,QAAXA,GAAoD,cAA/BviB,EAAQ4C,aAAa,MAC1E,CAtBaurB,CAAoBnuB,GACtB,kBADF,CAGT,CAEA,SAASutB,GAAiBvtB,GACxB,MAAmD,UAA5CA,EAAQ4C,aAAa,mBAC9B,CA8BA,MAAMwrB,WAAqBxW,GACzB,qBAAOyW,CAAenoB,EAAO,IAC3B,OAAO/G,KAAKmvB,aAAaroB,EAAkBC,GAC5C,CAED,kBAAOqoB,CAAYvuB,GACjB,OAAOb,KAAKmvB,aAAatuB,EAAQ4D,cAClC,CAED,mBAAO0qB,EAAaxqB,gBAAEA,EAAe+L,KAAEA,EAAI2R,KAAEA,IAC3C,OAAO,IAAIriB,KAAK2E,EAAiB+L,EAAM,IAAIkd,GAAavL,GACzD,CAED,WAAA3f,CAAYiC,EAAiB+L,EAAM2e,GACjC1sB,MAAM+N,GACN1Q,KAAK2E,gBAAkBA,EACvB3E,KAAKqvB,aAAeA,CACrB,CAED,KAAAnhB,GACE,MAAMohB,EAAgBtvB,KAAKa,QAAQ2e,WAAU,GAEvC+P,EAAiBvvB,KAAKa,QAAQ6J,iBAAiB,UAC/C8kB,EAAuBF,EAAc5kB,iBAAiB,UAE5D,IAAK,MAAOghB,EAAO+D,KAAWF,EAAe5d,UAAW,CACtD,MAAMzD,EAAQshB,EAAqB9D,GACnC,IAAK,MAAMgE,KAAUxhB,EAAMyhB,gBAAiBD,EAAOE,UAAW,EAC9D,IAAK,MAAMF,KAAUD,EAAOE,gBAAiBzhB,EAAMe,QAAQygB,EAAOhE,OAAOkE,UAAW,CACrF,CAED,IAAK,MAAMC,KAAuBP,EAAc5kB,iBAAiB,0BAC/DmlB,EAAoBnsB,MAAQ,GAG9B,OAAO,IAAIurB,GAAajvB,KAAK2E,gBAAiB2qB,EAAetvB,KAAKqvB,aACnE,CAED,QAAIS,GACF,OAAO9vB,KAAK2E,gBAAgBlB,aAAa,OAC1C,CAED,eAAIssB,GACF,OAAO/vB,KAAKqvB,aAAaxuB,OAC1B,CAED,gBAAIkM,GAEF,OAAO7B,EADMlL,KAAKgwB,WAAW,SAAW,IAEzC,CAED,qBAAIC,GACF,OAAOjwB,KAAKgwB,WAAW,gBACxB,CAED,iBAAIE,GACF,MAAiC,cAA1BlwB,KAAKiwB,iBACb,CAED,eAAIE,GACF,MAAiC,YAA1BnwB,KAAKiwB,iBACb,CAED,eAAIG,GACF,MAA2C,UAApCpwB,KAAKgwB,WAAW,gBACxB,CAED,0BAAIK,GACF,MAA6D,gBAAtDrwB,KAAKqvB,aAAaT,aAAa,kBACvC,CAED,mBAAI0B,GACF,MAA6C,UAAtCtwB,KAAKgwB,WAAW,iBACxB,CAED,gCAAIO,GACF,MAA6C,aAAtCvwB,KAAKgwB,WAAW,iBACxB,CAID,UAAAA,CAAWzwB,GACT,OAAOS,KAAKqvB,aAAaT,aAAa,SAASrvB,IAChD,EAGH,MAAMixB,GACJC,IAAyB,EACzBC,GAAiBnuB,QAAQC,UAEzB,YAAAmuB,CAAaC,EAAmB7V,GAU9B,OATI6V,GAAqB5wB,KAAK6wB,2BAA6B7wB,MAAKywB,GAC9DzwB,MAAKywB,GAAyB,EAC9BzwB,MAAK0wB,EAAiB1wB,MAAK0wB,EAAenF,MAAKhmB,gBACvCrF,SAAS4wB,oBAAoB/V,GAAQgW,QAAQ,KAGrD/wB,MAAK0wB,EAAiB1wB,MAAK0wB,EAAenF,KAAKxQ,GAG1C/a,MAAK0wB,CACb,CAED,4BAAIG,GACF,OAAO3wB,SAAS4wB,mBACjB,EAGH,MAAME,GAAiB,CACrBznB,OAAQ,UACR0nB,gBAAgB,EAChBC,oBAAqB,OACrBhW,YAAY,EACZiW,eAAe,EACfC,qBAAqB,EACrBC,uBAAuB,GAGnBC,GACQ,aADRA,GAEU,eAFVA,GAGQ,aAHRA,GAIM,WAGNC,GACS,cADTA,GAEK,UAFLA,GAGM,WAHNA,GAII,SAJJA,GAKO,YAGPC,GACY,EADZA,IAEa,EAFbA,IAGkB,EAGlBC,GAAY,CAChBC,QAAS,UACTC,QAAS,OACThqB,QAAS,QAGX,MAAMiqB,GACJC,WAAazpB,IACb0pB,cAAgB,CAAE,EAElBC,kBAAmB,EACnBd,gBAAiB,EACjBe,UAAW,EACXZ,qBAAsB,EACtBC,uBAAwB,EACxBY,gBAAiB,EACjB9b,MAAQob,GACRW,iBAAmB,IAAI1B,GAEvB,WAAA9tB,CAAYE,EAAUkK,EAAUqlB,EAAuBljB,EAAU,CAAA,GAC/DjP,KAAK4C,SAAWA,EAChB5C,KAAK8M,SAAWA,EAChB9M,KAAKmyB,sBAAwBA,GAAyB/pB,IAEtD,MAAMmB,OACJA,EAAM0nB,eACNA,EAAc9f,SACdA,EAAQiI,SACRA,EAAQgZ,aACRA,EAAY/kB,SACZA,EAAQ6jB,oBACRA,EAAmBhW,WACnBA,EAAUiW,cACVA,EAAaC,oBACbA,EAAmBC,sBACnBA,EAAqB/U,UACrBA,GACE,IACC0U,MACA/hB,GAELjP,KAAKuJ,OAASA,EACdvJ,KAAKixB,eAAiBA,EACtBjxB,KAAKmR,SAAWA,EAChBnR,KAAKoZ,SAAWA,EAChBpZ,KAAKoyB,aAAeA,EACpBpyB,KAAKqN,SAAWA,EAChBrN,KAAKqyB,WAAaryB,KAAK4C,SAAS0vB,6BAA6BtyB,KAAK8M,SAAU9M,KAAKuJ,QACjFvJ,KAAKuyB,cAAgBvyB,KAAKwyB,KAAKD,cAAcvyB,MAC7CA,KAAKkxB,oBAAsBA,EAC3BlxB,KAAKkb,WAAaA,EAClBlb,KAAKmxB,cAAgBA,EACrBnxB,KAAKgyB,UAAY9W,EACjBlb,KAAKoxB,oBAAsBA,EAC3BpxB,KAAKqxB,sBAAwBA,EAC7BrxB,KAAKsc,UAAYA,GAAamV,GAAUloB,EACzC,CAED,WAAIkpB,GACF,OAAOzyB,KAAK4C,SAAS6vB,OACtB,CAED,QAAID,GACF,OAAOxyB,KAAK4C,SAAS4vB,IACtB,CAED,WAAIhpB,GACF,OAAOxJ,KAAK4C,SAAS4G,OACtB,CAED,mBAAIkpB,GACF,OAAO1yB,KAAKwJ,QAAQmpB,gCAAgC3yB,KAAKmyB,sBAC1D,CAED,UAAIS,GACF,OAAO5yB,KAAKqyB,UACb,CAED,KAAAxe,GACM7T,KAAKmW,OAASob,KAChBvxB,KAAK6yB,mBAAmBvB,IACxBtxB,KAAKmW,MAAQob,GACbvxB,KAAKyyB,QAAQK,aAAa9yB,MAC1BA,KAAK4C,SAASkwB,aAAa9yB,MAE9B,CAED,MAAA4R,GACM5R,KAAKmW,OAASob,KACZvxB,KAAKkT,SACPlT,KAAKkT,QAAQtB,SAEf5R,KAAK+yB,eACL/yB,KAAKmW,MAAQob,GAEhB,CAED,QAAAjtB,GACMtE,KAAKmW,OAASob,KAChBvxB,KAAK6yB,mBAAmBvB,IACxBtxB,KAAKyyB,QAAQO,eAAehzB,MAC5BA,KAAKmW,MAAQob,GACbvxB,KAAKizB,iBAEAjzB,KAAK+xB,kBACR/xB,KAAK4C,SAASowB,eAAehzB,MAGlC,CAED,IAAAkzB,GACMlzB,KAAKmW,OAASob,KAChBvxB,KAAKmW,MAAQob,GACbvxB,KAAKyyB,QAAQU,YAAYnzB,MACzBA,KAAK4C,SAASowB,eAAehzB,MAEhC,CAED,aAAAozB,GACE,IAAKpzB,KAAKixB,gBAAkBjxB,KAAKmxB,cAAe,CAC9C,MACM5hB,EAASjG,EADUtJ,KAAK8M,SAASd,OAAShM,KAAKmR,UAAUnF,KAAO,UAAYhM,KAAKuJ,QAEvFvJ,KAAKwJ,QAAQ6pB,OAAO9jB,EAAQvP,KAAK8M,SAAU9M,KAAKmyB,uBAChDnyB,KAAKixB,gBAAiB,CACvB,CACF,CAED,YAAAqC,GACMtzB,KAAKuzB,uBACPvzB,KAAKwzB,kBACIxzB,KAAKyzB,uBAAyBzzB,KAAKkT,UAC5ClT,KAAKkT,QAAU,IAAIhD,EAAalQ,KAAMwP,EAAYzN,IAAK/B,KAAK8M,UAC5D9M,KAAKkT,QAAQpB,UAEhB,CAED,eAAA0hB,GACMxzB,KAAKqN,WACPrN,KAAK0zB,eACL1zB,KAAK2zB,iBACL3zB,KAAK4zB,gBAER,CAED,YAAAF,GACE1zB,KAAK6yB,mBAAmBvB,IACxBtxB,KAAKyyB,QAAQoB,oBAAoB7zB,KAClC,CAED,cAAA2zB,CAAetmB,EAAWrN,KAAKqN,UAE7B,GADArN,KAAKqN,SAAWA,EACZA,EAAU,CACZ,MAAMK,WAAEA,GAAeL,EACnBymB,GAAapmB,GACf1N,KAAKyyB,QAAQsB,sBAAsB/zB,MAEnCA,KAAKyyB,QAAQuB,iCAAiCh0B,KAAM0N,EAEvD,CACF,CAED,aAAAkmB,GACE5zB,KAAK6yB,mBAAmBvB,IACxBtxB,KAAKyyB,QAAQwB,qBAAqBj0B,KACnC,CAED,YAAAk0B,GACE,GAAIl0B,KAAKqN,SAAU,CACjB,MAAMK,WAAEA,EAAUU,aAAEA,GAAiBpO,KAAKqN,SAC1CrN,KAAK+a,QAAOxV,UAIV,GAHIvF,KAAKoxB,qBAAqBpxB,KAAKm0B,gBAC/Bn0B,KAAKwyB,KAAKnX,qBAAqBrb,KAAKwyB,KAAKnX,cAEzCyY,GAAapmB,IAA+B,MAAhBU,EAAsB,CACpD,MAAMgL,EAAW6V,GAAaC,eAAe9gB,SACvCpO,KAAKo0B,mBAAmBhb,GAAU,GAExCpZ,KAAKyyB,QAAQ4B,cAAcr0B,MAC3BA,KAAKsE,UACf,YACgBtE,KAAKwyB,KAAK8B,YAAYrF,GAAaC,eAAe9gB,GAAepO,MACvEA,KAAKyyB,QAAQ4B,cAAcr0B,MAC3BA,KAAKkzB,MACN,GAEJ,CACF,CAED,iBAAAqB,GACE,MAAMnb,EAAWpZ,KAAKwyB,KAAKgC,6BAA6Bx0B,KAAK8M,WAAa9M,KAAKy0B,uBAE/E,GAAIrb,KAAcxN,EAAU5L,KAAK8M,WAAasM,EAASR,UAAUhN,EAAU5L,KAAK8M,cAC3D,WAAf9M,KAAKuJ,QAAuB6P,EAAS8W,eACvC,OAAO9W,CAGZ,CAED,oBAAAqb,GACE,GAAIz0B,KAAKoyB,aACP,OAAOnD,GAAaC,eAAelvB,KAAKoyB,aAE3C,CAED,iBAAAsC,GACE,OAAmC,MAA5B10B,KAAKu0B,mBACb,CAED,kBAAAI,GACE,MAAMvb,EAAWpZ,KAAKu0B,oBACtB,GAAInb,EAAU,CACZ,MAAM1U,EAAY1E,KAAKyzB,qBACvBzzB,KAAK+a,QAAOxV,UACVvF,KAAKm0B,gBACDn0B,KAAKqyB,YAAcryB,KAAKuyB,cAC1BvyB,KAAKyyB,QAAQ4B,cAAcr0B,OAEvBA,KAAKwyB,KAAKnX,qBAAqBrb,KAAKwyB,KAAKnX,oBAEvCrb,KAAKo0B,mBAAmBhb,EAAU1U,GAExC1E,KAAKyyB,QAAQ4B,cAAcr0B,MACtB0E,GACH1E,KAAKsE,WAER,GAEJ,CACF,CAED,cAAA2uB,GACMjzB,KAAK40B,uBAAyB50B,KAAK+xB,kBAAoB/xB,KAAKqN,UAAUO,aACxE5N,KAAKyyB,QAAQoC,wBAAwB70B,KAAK40B,qBAAsB,CAC9DrrB,OAAQ,UACR8D,SAAUrN,KAAKqN,SACf+jB,qBAAqB,EACrBlW,YAAY,IAEdlb,KAAK+xB,kBAAmB,EAE3B,CAED,kBAAA+C,GACM90B,KAAKqyB,YACPryB,KAAK+a,QAAOxV,UACVvF,KAAKm0B,gBACLn0B,KAAK+0B,gBACL/0B,KAAKozB,gBACLpzB,KAAKyyB,QAAQ4B,cAAcr0B,KAAK,GAGrC,CAID,cAAA+R,CAAemB,GACTlT,KAAKqxB,uBACPne,EAAQJ,mBAAmBsB,GAActG,YAE5C,CAED,cAAAmE,GACEjS,KAAK0zB,cACN,CAED,gCAAAhhB,CAAiC6E,EAAUyd,GAAa,CAExD,kCAAMriB,CAA6BO,EAAS7F,GAC1C,MAAMe,QAAqBf,EAASe,cAC9BR,WAAEA,EAAUF,WAAEA,GAAeL,EACfhL,MAAhB+L,EACFpO,KAAK2zB,eAAe,CAClBjmB,WAAY8jB,GACZ5jB,gBAGF5N,KAAK40B,qBAAuBvnB,EAASO,WAAaP,EAASP,cAAWzK,EACtErC,KAAK2zB,eAAe,CAAEjmB,WAAYA,EAAYU,eAAcR,eAE/D,CAED,+BAAMgF,CAA0BM,EAAS7F,GACvC,MAAMe,QAAqBf,EAASe,cAC9BR,WAAEA,EAAUF,WAAEA,GAAeL,EACfhL,MAAhB+L,EACFpO,KAAK2zB,eAAe,CAClBjmB,WAAY8jB,GACZ5jB,eAGF5N,KAAK2zB,eAAe,CAAEjmB,WAAYA,EAAYU,eAAcR,cAE/D,CAED,cAAA0E,CAAeiF,EAAU0d,GACvBj1B,KAAK2zB,eAAe,CAClBjmB,WAAY8jB,GACZ5jB,YAAY,GAEf,CAED,eAAA2E,GACEvS,KAAK4zB,eACN,CAID,aAAAmB,GACO/0B,KAAKgyB,UAAahyB,KAAKwyB,KAAK0C,eAAkBl1B,KAAKwyB,KAAKjC,6BAA6BvwB,QACrE,WAAfA,KAAKuJ,OACPvJ,KAAKm1B,4BAA8Bn1B,KAAKoa,kBAAoBpa,KAAKwyB,KAAK1X,cAEtE9a,KAAKoa,kBAAoBpa,KAAKwyB,KAAK1X,cAEjC9a,KAAKqyB,YACPryB,KAAK4C,SAASwyB,gCAAgCp1B,KAAKwyB,KAAK6C,qBAAsBr1B,KAAK8M,UAGrF9M,KAAKgyB,UAAW,EAEnB,CAED,wBAAAmD,GACE,MAAMG,eAAEA,GAAmBt1B,KAAK0yB,gBAChC,GAAI4C,EAEF,OADAt1B,KAAKwyB,KAAKjY,iBAAiB+a,IACpB,CAEV,CAED,cAAAlb,GACE,MAAMlN,EAAStB,EAAU5L,KAAK8M,UAC9B,GAAc,MAAVI,EAEF,OADAlN,KAAKwyB,KAAKpY,eAAelN,IAClB,CAEV,CAID,kBAAA2lB,CAAmB0C,GACjBv1B,KAAK8xB,cAAcyD,IAAU,IAAIngB,MAAOK,SACzC,CAED,gBAAA+f,GACE,MAAO,IAAKx1B,KAAK8xB,cAClB,CAID,oBAAAyB,GACE,MAA+B,iBAAjBvzB,KAAKqN,QACpB,CAED,kBAAAomB,GACE,OAAIzzB,KAAKqyB,aAEiB,WAAfryB,KAAKuJ,QACNvJ,KAAK00B,oBAEN10B,KAAKkb,WAEf,CAED,aAAAiZ,GACOn0B,KAAKiyB,iBACRjyB,KAAKwyB,KAAK2B,cAAcn0B,KAAKoZ,UAAUmS,MAAMnS,GAAaA,GAAYpZ,KAAKkxB,oBAAoB9X,KAC/FpZ,KAAKiyB,gBAAiB,EAEzB,CAED,YAAMlX,CAAO6D,GACX5e,KAAK+yB,qBACC,IAAIxwB,SAASC,IACjBxC,KAAKy1B,MAC0B,WAA7Bv1B,SAASuG,gBAA+BI,YAAW,IAAMrE,KAAW,GAAKoE,uBAAsB,IAAMpE,KAAU,UAE7Goc,WACC5e,KAAKy1B,KACb,CAED,wBAAMrB,CAAmBhb,EAAU1U,SAC3B1E,KAAKkyB,iBAAiBvB,aAAa3wB,KAAKwyB,KAAKkD,mBAAmBtc,IAAW7T,gBACzEvF,KAAKwyB,KAAKmD,WAAWvc,EAAU1U,EAAW1E,KAAKkb,WAAYlb,MACjEA,KAAK+0B,eAAe,GAEvB,CAED,YAAAhC,GACM/yB,KAAKy1B,QACPG,qBAAqB51B,KAAKy1B,cACnBz1B,KAAKy1B,MAEf,EAGH,SAAS3B,GAAapmB,GACpB,OAAOA,GAAc,KAAOA,EAAa,GAC3C,CAEA,MAAMmoB,GACJC,YAAc,IAAI3J,GAElB,WAAAzpB,CAAYqzB,GACV/1B,KAAK+1B,QAAUA,CAChB,CAED,uBAAAlB,CAAwB/nB,EAAUmC,GAC5BpC,EAAoBC,EAAU9M,KAAK0B,UAAUqL,cAC/C/M,KAAK0B,UAAUs0B,WAAWlpB,EAAUmC,GAASkjB,uBAAyB/pB,IAAQ6G,GAE9E3N,OAAOwL,SAASd,KAAOc,EAASnE,UAEnC,CAED,YAAAmqB,CAAamD,GACXj2B,KAAK8M,SAAWmpB,EAAMnpB,SACtBmpB,EAAMtB,qBACNsB,EAAM3C,eACN2C,EAAMnB,oBACP,CAED,mBAAAjB,CAAoBoC,GAClBj2B,KAAK81B,YAAYjJ,SAAS,GACtBoJ,EAAMvB,qBAAuC,WAAhBuB,EAAM1sB,OACrCvJ,KAAKk2B,iCAELl2B,KAAKm2B,iBAER,CAED,qBAAApC,CAAsBkC,GACpBA,EAAM/B,cACP,CAED,gCAAAF,CAAiCiC,EAAOvoB,GACtC,OAAQA,GACN,KAAK8jB,GACL,KAAKA,GACL,KAAKA,GACH,OAAOxxB,KAAKkD,OAAO,CACjB+Y,OAAQ,iBACRma,QAAS,CACP1oB,gBAGN,QACE,OAAOuoB,EAAM/B,eAElB,CAED,oBAAAD,CAAqBoC,GAAU,CAE/B,cAAArD,CAAeqD,GACbr2B,KAAK81B,YAAYjJ,SAAS,GAC1B7sB,KAAKs2B,sBACN,CAED,eAAAC,CAAgBta,GACdjc,KAAKkD,OAAO+Y,EACb,CAED,WAAAkX,CAAYkD,GACVr2B,KAAK81B,YAAYjJ,SAAS,GAC1B7sB,KAAKs2B,sBACN,CAED,aAAAjC,CAAcgC,GAAU,CAIxB,mCAAAG,CAAoC1pB,GAClC,OAAO,CACR,CAID,qBAAA4K,CAAsB+e,GACpBz2B,KAAK81B,YAAYjJ,SAAS,GAC1B7sB,KAAK02B,+BACN,CAED,sBAAAve,CAAuBse,GACrBz2B,KAAK81B,YAAYjJ,SAAS,GAC1B7sB,KAAK22B,qBACN,CAID,8BAAAT,GACEl2B,KAAK42B,wBAA0Bt1B,OAAOuF,WAAW7G,KAAKm2B,gBAAiBn2B,KAAK+1B,QAAQjxB,iBACrF,CAED,oBAAAwxB,GACEt2B,KAAK81B,YAAY7I,OACmB,MAAhCjtB,KAAK42B,0BACPt1B,OAAOoU,aAAa1V,KAAK42B,gCAClB52B,KAAK42B,wBAEf,CAED,6BAAAF,GACqC,MAA/B12B,KAAK62B,yBACP72B,KAAK62B,uBAAyBv1B,OAAOuF,WAAW7G,KAAKm2B,gBAAiBn2B,KAAK+1B,QAAQjxB,kBAEtF,CAED,mBAAA6xB,GACE32B,KAAK81B,YAAY7I,OACkB,MAA/BjtB,KAAK62B,yBACPv1B,OAAOoU,aAAa1V,KAAK62B,+BAClB72B,KAAK62B,uBAEf,CAEDV,gBAAkB,KAChBn2B,KAAK81B,YAAYhJ,MAAM,EAGzB,MAAA5pB,CAAO+Y,GACLrW,EAAS,eAAgB,CAAEG,OAAQkW,IAEnC3a,OAAOwL,SAASd,KAAOhM,KAAK8M,UAAUnE,YAAcrH,OAAOwL,SAASd,IACrE,CAED,aAAItK,GACF,OAAO1B,KAAK+1B,QAAQr0B,SACrB,EAGH,MAAMo1B,GACJ5sB,SAAW,yBACX6sB,mBAAqB,2BAErBtjB,SAAU,EAEV,KAAAI,GACO7T,KAAKyT,UACRzT,KAAKyT,SAAU,EACf7R,iBAAiB,qBAAsB5B,KAAKg3B,yBAAyB,GAExE,CAED,IAAAjjB,GACM/T,KAAKyT,UACPzT,KAAKyT,SAAU,EACfrK,oBAAoB,qBAAsBpJ,KAAKg3B,yBAAyB,GAE3E,CAEDA,wBAA2B5Z,IACzB,IAAK,MAAMvc,KAAWb,KAAKi3B,kBACzBp2B,EAAQ4d,QACT,EAGH,qBAAIwY,GACF,MAAO,IAAI/2B,SAASwK,iBAAiB1K,KAAKkK,aAAclK,KAAKk3B,iCAC9D,CAED,oCAAIA,GACF,MAAMruB,EAAW3I,SAASwK,iBAAiB1K,KAAK+2B,oBAQhD,OANIluB,EAASd,QACXovB,QAAQC,KACN,OAAOp3B,KAAK+2B,0FAA0F/2B,KAAKkK,qBAIxG,IAAIrB,EACZ,EAGH,MAAMwuB,GACJ,WAAA30B,CAAYqzB,EAASl1B,GACnBb,KAAK+1B,QAAUA,EACf/1B,KAAKa,QAAUA,EACfb,KAAKie,gBAAkB,IAAItB,GAAgB3c,KAAMa,GACjDb,KAAKs3B,mBAAqB,IAAI7d,GAAmBzZ,KAAMa,EACxD,CAED,KAAAgT,GACE7T,KAAKie,gBAAgBpK,QACrB7T,KAAKs3B,mBAAmBzjB,OACzB,CAED,IAAAE,GACE/T,KAAKie,gBAAgBlK,OACrB/T,KAAKs3B,mBAAmBvjB,MACzB,CAID,wBAAAkJ,CAAyBpc,EAAS02B,EAAWna,GAC3C,OAAOpd,MAAKw3B,EAAgB32B,EAC7B,CAED,oBAAAsc,CAAqBtc,EAASgL,EAAKlL,GACjC,MAAM80B,EAAQz1B,MAAKy3B,EAAkB52B,GACjC40B,GACFA,EAAM7yB,SAASua,qBAAqBtc,EAASgL,EAAKlL,EAErD,CAID,cAAAoZ,CAAelZ,EAASpB,GACtB,OACoC,MAAlCoB,EAAQK,QAAQ,gBAChBlB,MAAK03B,EAAc72B,EAASpB,IAC5BO,MAAKw3B,EAAgB32B,EAASpB,EAEjC,CAED,aAAAua,CAAcnZ,EAASpB,GACrB,MAAMg2B,EAAQz1B,MAAKy3B,EAAkB52B,EAASpB,GAC1Cg2B,GACFA,EAAM7yB,SAASoX,cAAcnZ,EAASpB,EAEzC,CAED,EAAAi4B,CAAch4B,EAAMD,GAClB,MAAM8J,EAAS0C,EAAYvM,EAAMD,GAC3Bk4B,EAAO33B,KAAKa,QAAQ4D,cAAcqF,cAAc,2BAChDiD,EAAe7B,EAAUysB,GAAM3tB,SAAW,KAEhD,OAAOhK,MAAKw3B,EAAgB93B,EAAMD,IAAcoN,EAAoBtD,EAAQwD,EAC7E,CAED,EAAAyqB,CAAgB32B,EAASpB,GAMvB,GAJEoB,aAAmBN,gBACfP,KAAK+1B,QAAQ6B,wBAAwB/2B,EAASpB,GAC9CO,KAAK+1B,QAAQ8B,qBAAqBh3B,GAErB,CACjB,MAAM40B,EAAQz1B,MAAKy3B,EAAkB52B,EAASpB,GAC9C,QAAOg2B,GAAQA,GAAS50B,EAAQK,QAAQ,cAC9C,CACM,OAAO,CAEV,CAED,EAAAu2B,CAAkB52B,EAASpB,GACzB,MAAMyZ,EAAKzZ,GAAWgE,aAAa,qBAAuB5C,EAAQ4C,aAAa,oBAC/E,GAAIyV,GAAY,QAANA,EAAc,CACtB,MAAMuc,EAAQz1B,KAAKa,QAAQiJ,cAAc,IAAIoP,qBAC7C,GAAIuc,aAAiBtzB,EACnB,OAAOszB,CAEV,CACF,EAGH,MAAMqC,GACJhrB,SACAqlB,sBAAwB/pB,IACxBsqB,gBAAkB,CAAE,EACpBjf,SAAU,EACVskB,YAAa,EACbC,aAAe,EAEf,WAAAt1B,CAAYE,GACV5C,KAAK4C,SAAWA,CACjB,CAED,KAAAiR,GACO7T,KAAKyT,UACR7R,iBAAiB,WAAY5B,KAAKi4B,YAAY,GAC9Cr2B,iBAAiB,OAAQ5B,KAAKk4B,YAAY,GAC1Cl4B,KAAKg4B,aAAexuB,QAAQ2M,OAAOgiB,OAAOC,kBAAoB,EAC9Dp4B,KAAKyT,SAAU,EACfzT,KAAK2H,QAAQ,IAAI+D,IAAIpK,OAAOwL,SAASd,OAExC,CAED,IAAA+H,GACM/T,KAAKyT,UACPrK,oBAAoB,WAAYpJ,KAAKi4B,YAAY,GACjD7uB,oBAAoB,OAAQpJ,KAAKk4B,YAAY,GAC7Cl4B,KAAKyT,SAAU,EAElB,CAED,IAAAF,CAAKzG,EAAUqlB,GACbnyB,KAAKqzB,OAAO7pB,QAAQE,UAAWoD,EAAUqlB,EAC1C,CAED,OAAAxqB,CAAQmF,EAAUqlB,GAChBnyB,KAAKqzB,OAAO7pB,QAAQC,aAAcqD,EAAUqlB,EAC7C,CAED,MAAAkB,CAAO9jB,EAAQzC,EAAUqlB,EAAwB/pB,KAC3CmH,IAAW/F,QAAQE,aAAa1J,KAAKg4B,aAEzC,MAAM7hB,EAAQ,CAAEgiB,MAAO,CAAEhG,wBAAuBiG,iBAAkBp4B,KAAKg4B,eACvEzoB,EAAO8oB,KAAK7uB,QAAS2M,EAAO,GAAIrJ,EAASd,MACzChM,KAAK8M,SAAWA,EAChB9M,KAAKmyB,sBAAwBA,CAC9B,CAID,+BAAAQ,CAAgCR,GAC9B,OAAOnyB,KAAK0yB,gBAAgBP,IAA0B,CAAE,CACzD,CAED,qBAAAmG,CAAsBC,GACpB,MAAMpG,sBAAEA,GAA0BnyB,KAC5B0yB,EAAkB1yB,KAAK0yB,gBAAgBP,GAC7CnyB,KAAK0yB,gBAAgBP,GAAyB,IACzCO,KACA6F,EAEN,CAID,gCAAAC,GACOx4B,KAAKy4B,4BACRz4B,KAAKy4B,0BAA4BjvB,QAAQkvB,mBAAqB,OAC9DlvB,QAAQkvB,kBAAoB,SAE/B,CAED,oCAAAC,GACM34B,KAAKy4B,4BACPjvB,QAAQkvB,kBAAoB14B,KAAKy4B,iCAC1Bz4B,KAAKy4B,0BAEf,CAIDR,WAAct3B,IACZ,GAAIX,KAAK44B,uBAAwB,CAC/B,MAAMT,MAAEA,GAAUx3B,EAAMwV,OAAS,CAAA,EACjC,GAAIgiB,EAAO,CACTn4B,KAAK8M,SAAW,IAAIpB,IAAIpK,OAAOwL,SAASd,MACxC,MAAMmmB,sBAAEA,EAAqBiG,iBAAEA,GAAqBD,EACpDn4B,KAAKmyB,sBAAwBA,EAC7B,MAAM7V,EAAY8b,EAAmBp4B,KAAKg4B,aAAe,UAAY,OACrEh4B,KAAK4C,SAASi2B,6DAA6D74B,KAAK8M,SAAUqlB,EAAuB7V,GACjHtc,KAAKg4B,aAAeI,CACrB,CACF,GAGHF,WAAa3yB,MAAO6X,UA5jIb7a,QAAQC,UA8jIbxC,KAAK+3B,YAAa,CAAI,EAKxB,oBAAAa,GAEE,OAAO54B,KAAK84B,cACb,CAED,YAAAA,GACE,OAAO94B,KAAK+3B,YAAqC,YAAvB73B,SAAS64B,UACpC,EAGH,MAAMC,GACJvlB,SAAU,EACVwlB,GAAkB,KAElB,WAAAv2B,CAAYE,EAAU8W,GACpB1Z,KAAK4C,SAAWA,EAChB5C,KAAK0Z,YAAcA,CACpB,CAED,KAAA7F,GACM7T,KAAKyT,UAE2B,YAAhCzT,KAAK0Z,YAAYqf,WACnB/4B,KAAK0Z,YAAY9X,iBAAiB,mBAAoB5B,MAAKk5B,EAAS,CAAE7vB,MAAM,IAE5ErJ,MAAKk5B,IAER,CAED,IAAAnlB,GACO/T,KAAKyT,UAEVzT,KAAK0Z,YAAYtQ,oBAAoB,aAAcpJ,MAAKm5B,EAAuB,CAC7EC,SAAS,EACTC,SAAS,IAEXr5B,KAAK0Z,YAAYtQ,oBAAoB,aAAcpJ,MAAKs5B,EAA0B,CAChFF,SAAS,EACTC,SAAS,IAGXr5B,KAAK0Z,YAAYtQ,oBAAoB,6BAA8BpJ,MAAKu5B,GAA4B,GACpGv5B,KAAKyT,SAAU,EAChB,CAEDylB,GAAU,KACRl5B,KAAK0Z,YAAY9X,iBAAiB,aAAc5B,MAAKm5B,EAAuB,CAC1EC,SAAS,EACTC,SAAS,IAEXr5B,KAAK0Z,YAAY9X,iBAAiB,aAAc5B,MAAKs5B,EAA0B,CAC7EF,SAAS,EACTC,SAAS,IAGXr5B,KAAK0Z,YAAY9X,iBAAiB,6BAA8B5B,MAAKu5B,GAA4B,GACjGv5B,KAAKyT,SAAU,CAAI,EAGrB0lB,GAAyBx4B,IACvB,GAAyC,UAArCoJ,EAAe,kBAA+B,OAElD,MAAMnJ,EAASD,EAAMC,OAGrB,GAFeA,EAAOyX,SAAWzX,EAAOyX,QAAQ,6CAElCrY,MAAKw5B,EAAgB54B,GAAS,CAC1C,MAAMqK,EAAOrK,EACPkM,EAAW9B,EAAmBC,GAEpC,GAAIjL,KAAK4C,SAASsb,6BAA6BjT,EAAM6B,GAAW,CAC9D9M,MAAKi5B,EAAkBhuB,EAEvB,MAAMiH,EAAe,IAAIhC,EACvBlQ,KACAwP,EAAYzN,IACZ+K,EACA,IAAI0D,gBACJ5P,GAGFoU,GAAcM,SAASxI,EAASnE,WAAYuJ,EAAclS,MAAKy5B,EAChE,CACF,GAGHH,GAA4B34B,IACtBA,EAAMC,SAAWZ,MAAKi5B,GAAiBj5B,MAAK05B,GAAwB,EAG1EA,GAAyB,KACvB1kB,GAAcQ,QACdxV,MAAKi5B,EAAkB,IAAI,EAG7BM,GAA8B54B,IAC5B,GAA6B,SAAzBA,EAAMC,OAAOwiB,SAA2D,QAArCziB,EAAMoF,OAAO6K,aAAarB,OAAkB,CACjF,MAAMoqB,EAAS3kB,GAAcjT,IAAIpB,EAAMoF,OAAO8F,IAAIlD,YAE9CgxB,IAEFh5B,EAAMoF,OAAOmM,aAAeynB,GAG9B3kB,GAAcQ,OACf,GAGH,cAAAzD,CAAemB,GACb,MAAMjI,EAAOiI,EAAQtS,OAErBsS,EAAQ7E,QAAQ,iBAAmB,WAEnC,MAAMgQ,EAAapT,EAAK/J,QAAQ,eAC1B04B,EAAmB3uB,EAAKxH,aAAa,qBAAuB4a,GAAY5a,aAAa,WAAa4a,GAAYnF,GAEhH0gB,GAAyC,SAArBA,IACtB1mB,EAAQ7E,QAAQ,eAAiBurB,EAEpC,CAID,4BAAAjnB,GAAiC,CAEjC,cAAAV,CAAeC,GAAgB,CAE/B,cAAAI,CAAeJ,GAAgB,CAE/B,eAAAK,CAAgBL,GAAgB,CAEhC,gCAAAQ,CAAiCR,EAAcM,GAAiB,CAEhE,yBAAAI,CAA0BV,EAAcM,GAAiB,CAEzD,KAAIinB,GACF,OAAOI,OAAO9vB,EAAe,+BAl/GhB,GAm/Gd,CAED,EAAAyvB,CAAgBvuB,GAGd,QAFaA,EAAKxH,aAAa,WAI3Bq2B,GAAgB7uB,MAChB8uB,GAAkB9uB,MAClB+uB,GAAY/uB,MACZgvB,GAAYhvB,KACZivB,GAAejvB,MAGpB,EAGH,MAAM6uB,GAAmB7uB,GAChBA,EAAKwB,SAAWvM,SAAS4M,SAASL,SAAW,CAAC,QAAS,UAAUqd,SAAS7e,EAAKkvB,WAAalvB,EAAK7G,aAAa,UAGjH21B,GAAqB9uB,GACjBA,EAAKkB,SAAWlB,EAAKwG,SAAWvR,SAAS4M,SAASX,SAAWjM,SAAS4M,SAAS2E,QAAWxG,EAAKe,KAAKY,WAAW,KAGnHotB,GAAe/uB,IACnB,GAAiD,UAA7CA,EAAKxH,aAAa,uBAAoC,OAAO,EACjE,GAAwC,UAApCwH,EAAKxH,aAAa,cAA2B,OAAO,EAExD,MAAM22B,EAAsBnwB,EAAuBgB,EAAM,yBACzD,SAAImvB,GAAmF,UAA5DA,EAAoB32B,aAAa,uBAErD,EAGHw2B,GAAehvB,IACnB,MAAMovB,EAAcpvB,EAAKxH,aAAa,qBACtC,SAAI42B,GAA6C,QAA9BA,EAAYp2B,mBAE3Bq2B,GAAMrvB,OACNA,EAAK7G,aAAa,yBAClB6G,EAAK7G,aAAa,sBAEf,EAGHk2B,GAASrvB,GACNA,EAAK7G,aAAa,gBAAkB6G,EAAK7G,aAAa,kBAAoB6G,EAAK7G,aAAa,iBAAmB6G,EAAK7G,aAAa,eAGpI81B,GAAkBjvB,GACRrF,EAAS,wBAAyB,CAAEhF,OAAQqK,EAAMnF,YAAY,IAC/D2M,iBAGf,MAAM8nB,GACJ,WAAA73B,CAAYE,GACV5C,KAAK4C,SAAWA,CACjB,CAED,YAAA43B,CAAa1tB,EAAUmC,EAAU,IAC3BjP,KAAK4C,SAAS63B,iCAAiC3tB,EAAUmC,EAAQ1F,SACnEvJ,KAAK4C,SAASiyB,wBAAwB/nB,EAAUmC,EAEnD,CAED,UAAA+mB,CAAWvqB,EAAW0mB,EAAuBljB,EAAU,CAAA,GACrDjP,KAAK+T,OACL/T,KAAK06B,aAAe,IAAI9I,GAAM5xB,KAAMkL,EAAUO,GAAY0mB,EAAuB,CAC/EhhB,SAAUnR,KAAK8M,YACZmC,IAELjP,KAAK06B,aAAa7mB,OACnB,CAED,UAAA8mB,CAAWj7B,EAAMD,GACfO,KAAK+T,OACL/T,KAAKyX,eAAiB,IAAIvB,GAAelW,KAAMN,EAAMD,GAAW,GAEhEO,KAAKyX,eAAe5D,OACrB,CAED,IAAAE,GACM/T,KAAKyX,iBACPzX,KAAKyX,eAAe1D,cACb/T,KAAKyX,gBAGVzX,KAAK06B,eACP16B,KAAK06B,aAAa9oB,gBACX5R,KAAK06B,aAEf,CAED,WAAIjI,GACF,OAAOzyB,KAAK4C,SAAS6vB,OACtB,CAED,QAAID,GACF,OAAOxyB,KAAK4C,SAAS4vB,IACtB,CAED,gBAAIzlB,GACF,OAAO/M,KAAKwyB,KAAKpZ,SAASrM,YAC3B,CAED,WAAIvD,GACF,OAAOxJ,KAAK4C,SAAS4G,OACtB,CAID,qBAAAkO,CAAsBD,GAE8B,mBAAvCzX,KAAKyyB,QAAQ/a,uBACtB1X,KAAKyyB,QAAQ/a,sBAAsBD,EAEtC,CAED,yCAAMQ,CAAoCR,EAAgBjF,GACxD,GAAIiF,GAAkBzX,KAAKyX,eAAgB,CACzC,MAAMrJ,QAAqBoE,EAAcpE,aACzC,GAAIA,EAAc,CAChB,MAAMgjB,EAAsB3Z,EAAepG,OACtC+f,GACHpxB,KAAKwyB,KAAKoI,qBAGZ,MAAMltB,WAAEA,EAAUE,WAAEA,GAAe4E,EAE7BqoB,EAAe,CACnBtxB,OAFavJ,MAAK86B,EAA4BrjB,EAAgBjF,GAG9D4e,sBACA/jB,SAAU,CAAEK,aAAYU,eAAcR,eAExC5N,KAAKw6B,aAAahoB,EAAc1F,SAAU+tB,EAC3C,CACF,CACF,CAED,sCAAMjjB,CAAiCH,EAAgBjF,GACrD,MAAMpE,QAAqBoE,EAAcpE,aAEzC,GAAIA,EAAc,CAChB,MAAMgL,EAAW6V,GAAaC,eAAe9gB,GACzCoE,EAAc7E,kBACV3N,KAAKwyB,KAAK8B,YAAYlb,EAAUpZ,KAAK06B,oBAErC16B,KAAKwyB,KAAKmD,WAAWvc,GAAU,GAAO,EAAMpZ,KAAK06B,cAErDthB,EAASmX,8BACXvwB,KAAKwyB,KAAK1X,cAEZ9a,KAAKwyB,KAAKoI,oBACX,CACF,CAED,qBAAA5iB,CAAsBP,EAAgBrF,GACpC+kB,QAAQ/kB,MAAMA,EACf,CAED,sBAAA+F,CAAuBV,GAE8B,mBAAxCzX,KAAKyyB,QAAQta,wBACtBnY,KAAKyyB,QAAQta,uBAAuBV,EAEvC,CAID,mCAAA+e,CAAoC1pB,GAElC,MAAgE,mBAArD9M,KAAKyyB,QAAQ+D,qCACfx2B,KAAKyyB,QAAQ+D,oCAAoC1pB,EAI3D,CAID,YAAAgmB,CAAamD,GACXj2B,KAAK4C,SAASkwB,aAAamD,EAC5B,CAED,cAAAjD,CAAeiD,GACbj2B,KAAK4C,SAASowB,eAAeiD,UACtBj2B,KAAK06B,YACb,CAED,4BAAApI,CAA6BxlB,EAAUvD,GACrC,MAAM2D,EAAStB,EAAUkB,GACnBiuB,EAAgBnvB,EAAU5L,KAAKwyB,KAAK6C,sBACpC2F,EAAgC,YAAXzxB,QAA0C,IAAX2D,EAE1D,MACa,YAAX3D,GACA0D,EAAcH,KAAcG,EAAcjN,KAAKwyB,KAAK6C,wBACnD2F,GAAiC,MAAV9tB,GAAkBA,IAAW6tB,EAExD,CAED,+BAAA3F,CAAgC6F,EAAQC,GACtCl7B,KAAK4C,SAASwyB,gCAAgC6F,EAAQC,EACvD,CAID,YAAIpuB,GACF,OAAO9M,KAAKwJ,QAAQsD,QACrB,CAED,yBAAIqlB,GACF,OAAOnyB,KAAKwJ,QAAQ2oB,qBACrB,CAED,EAAA2I,CAA4BrjB,EAAgBjF,GAC1C,MAAM/S,UAAEA,EAAS6W,YAAEA,GAAgBmB,EACnC,OAAO9N,EAAelK,EAAW6W,IAAgBtW,MAAKm7B,EAAkB3oB,EACzE,CAED,EAAA2oB,CAAkB3oB,GAEhB,OAD6BA,EAAc5E,YAAc4E,EAAc1F,SAASd,OAAShM,KAAK8M,UAAUd,KAC1E,UAAY,SAC3C,EAGH,MAAMovB,GACK,EADLA,GAEK,EAFLA,GAGS,EAHTA,GAIM,EAGZ,MAAMC,GACJC,MAAQF,GACR3nB,SAAU,EAEV,WAAA/Q,CAAYE,GACV5C,KAAK4C,SAAWA,CACjB,CAED,KAAAiR,GACO7T,KAAKyT,UACJzT,KAAKs7B,OAASF,KAChBp7B,KAAKs7B,MAAQF,IAEfl7B,SAAS0B,iBAAiB,mBAAoB5B,KAAKu7B,qBAAqB,GACxE35B,iBAAiB,WAAY5B,KAAKw7B,gBAAgB,GAClDx7B,KAAKyT,SAAU,EAElB,CAED,IAAAM,GACM/T,KAAKyT,UACPvT,SAASkJ,oBAAoB,mBAAoBpJ,KAAKu7B,qBAAqB,GAC3EnyB,oBAAoB,WAAYpJ,KAAKw7B,gBAAgB,GACrDx7B,KAAKyT,SAAU,EAElB,CAED8nB,oBAAsB,KACpB,MAAMxC,WAAEA,GAAe/4B,KACL,eAAd+4B,EACF/4B,KAAKy7B,oBACkB,YAAd1C,GACT/4B,KAAK07B,gBACN,EAGH,iBAAAD,GACMz7B,KAAKs7B,OAASF,KAChBp7B,KAAKs7B,MAAQF,GACbp7B,KAAK4C,SAAS+4B,wBAEjB,CAED,cAAAD,GACE17B,KAAKy7B,oBACDz7B,KAAKs7B,OAASF,KAChBp7B,KAAKs7B,MAAQF,GACbp7B,KAAK4C,SAASm1B,aAEjB,CAEDyD,eAAiB,KACfx7B,KAAK4C,SAAS44B,gBAAgB,EAGhC,cAAIzC,GACF,OAAO74B,SAAS64B,UACjB,EAGH,MAAM6C,GACJnoB,SAAU,EAEV,WAAA/Q,CAAYE,GACV5C,KAAK4C,SAAWA,CACjB,CAED,KAAAiR,GACO7T,KAAKyT,UACR7R,iBAAiB,SAAU5B,KAAK67B,UAAU,GAC1C77B,KAAK67B,WACL77B,KAAKyT,SAAU,EAElB,CAED,IAAAM,GACM/T,KAAKyT,UACPrK,oBAAoB,SAAUpJ,KAAK67B,UAAU,GAC7C77B,KAAKyT,SAAU,EAElB,CAEDooB,SAAW,KACT77B,KAAK87B,eAAe,CAAEthB,EAAGlZ,OAAOy6B,YAAathB,EAAGnZ,OAAO06B,aAAc,EAKvE,cAAAF,CAAeG,GACbj8B,KAAK4C,SAASs5B,sBAAsBD,EACrC,EAGH,MAAME,GACJ,MAAAphB,EAAOtG,SAAEA,IACPiK,GAAMC,4BAA4B3e,KAkBtC,SAA2CyU,GACzC,MAAM2nB,EAA8BpjB,GAA0B9Y,SAASyE,iBACjE0U,EAAsB,CAAA,EAC5B,IAAK,MAAMgjB,KAA8BD,EAA6B,CACpE,MAAMljB,GAAEA,GAAOmjB,EAEf,IAAK,MAAM3nB,KAAiBD,EAAS/J,iBAAiB,gBAAiB,CACrE,MAAM4xB,EAAkBrjB,GAAwBvE,EAAcG,gBAAgB7K,QAASkP,GAEnFojB,IACFjjB,EAAoBH,GAAM,CAACmjB,EAA4BC,GAE1D,CACF,CAED,OAAOjjB,CACT,CAlC4CkjB,CAAkC9nB,IAAW,MAoCzFlP,eAAyCkP,EAAUmK,GACjD,MAAM4d,EAAc,0BAA0Bp0B,MACxCq0B,EAAehoB,EAAS/J,iBAAiB,gBACzCgyB,EA4CR,SAA4CC,GAC1C,IAAK,MAAMjoB,KAAiBioB,EAA0B,CACpD,MAAMD,EAAuBlyB,EAA0BkK,EAAcG,gBAAgB7K,SAErF,GAAI0yB,EAAsB,OAAOA,CAClC,CAED,OAAO,IACT,CApD+BE,CAAmCH,GAChE,IAAII,EAAkB,KAElBH,IAEAG,EADEH,EAAqBxjB,GACLwjB,EAAqBxjB,GAErBsjB,EAGpBE,EAAqBxjB,GAAK2jB,GAG5Bje,UACMpY,IAIN,IAFqD,MAA1BtG,SAASwY,eAAyBxY,SAASwY,eAAiBxY,SAASwQ,OAEtEmsB,EAAiB,CACzC,MAAMC,EAAqB58B,SAAS8rB,eAAe6Q,GAE/CvyB,EAAmBwyB,IACrBA,EAAmBvyB,QAEjBuyB,GAAsBA,EAAmB5jB,IAAMsjB,GACjDM,EAAmBl5B,gBAAgB,KAEtC,CACH,CAlEMm5B,CAA0BtoB,GAAU,MAoE1ClP,eAAkCqZ,GAChC,MAAOoe,EAA2BC,SAj8IpC13B,eAAsBqZ,EAAUse,GAC9B,MAAMC,EAASD,IAQf,OANAte,UAEMjY,IAIC,CAACw2B,EAFMD,IAGhB,CAu7IsEE,CAAOxe,GAAU,IAAM1e,SAASwY,gBAE9F2kB,EAAiBL,GAA6BA,EAA0B9jB,GAE9E,GAAImkB,EAAgB,CAClB,MAAMC,EAAiBp9B,SAAS8rB,eAAeqR,GAE3C/yB,EAAmBgzB,IAAmBA,GAAkBL,GAC1DK,EAAe/yB,OAElB,CACH,CA/EQgzB,EAAmB,KACjBr9B,SAASyE,gBAAgBtE,YAAYoU,EAAS,GAC9C,GACF,GAEL,CAID,aAAAuK,CAAc1F,EAAyBC,GACrCA,EAAoBzE,YAAYwE,EAAwBkG,WAAU,GACnE,CAED,YAAAJ,GAAiB,EA8EnB,MAAMoe,GACJC,QAAU,IAAIz4B,IACdyO,IAAW,EAEX,WAAA/Q,CAAYE,GACV5C,KAAK4C,SAAWA,CACjB,CAED,KAAAiR,GACO7T,MAAKyT,IACRzT,MAAKyT,GAAW,EAChB7R,iBAAiB,8BAA+B5B,KAAK09B,sBAAsB,GAE9E,CAED,IAAA3pB,GACM/T,MAAKyT,IACPzT,MAAKyT,GAAW,EAChBrK,oBAAoB,8BAA+BpJ,KAAK09B,sBAAsB,GAEjF,CAED,mBAAAC,CAAoBlO,GACbzvB,KAAK49B,wBAAwBnO,KAChCzvB,KAAKy9B,QAAQjvB,IAAIihB,GACjBA,EAAO7tB,iBAAiB,UAAW5B,KAAK69B,qBAAqB,GAEhE,CAED,sBAAAC,CAAuBrO,GACjBzvB,KAAK49B,wBAAwBnO,KAC/BzvB,KAAKy9B,QAAQ7uB,OAAO6gB,GACpBA,EAAOrmB,oBAAoB,UAAWpJ,KAAK69B,qBAAqB,GAEnE,CAED,uBAAAD,CAAwBnO,GACtB,OAAOzvB,KAAKy9B,QAAQzwB,IAAIyiB,EACzB,CAEDiO,qBAAwB/8B,IACtB,MAAM0M,EAyBV,SAAgC1M,GAC9B,MAAM6R,EAAgB7R,EAAMoF,QAAQyM,cACpC,GAAIA,aAAyBpF,EAC3B,OAAOoF,CAEX,CA9BqBurB,CAAuBp9B,GACpC0M,GA+BR,SAA+BA,GAC7B,MAAMS,EAAcT,EAASS,aAAe,GAC5C,OAAOA,EAAYlB,WAAWwH,GAActG,YAC9C,CAlCoBkwB,CAAsB3wB,KACpC1M,EAAM2F,iBACNtG,KAAKi+B,uBAAuB5wB,GAC7B,EAGHwwB,oBAAuBl9B,IACjBX,MAAKyT,GAAiC,iBAAd9S,EAAMu9B,MAChCl+B,KAAKm+B,mBAAmBx9B,EAAMu9B,KAC/B,EAGH,4BAAMD,CAAuB5wB,GAC3B,MAAMtG,QAAasG,EAASe,aACxBrH,GACF/G,KAAKm+B,mBAAmBp3B,EAE3B,CAED,kBAAAo3B,CAAmBp3B,GACjB/G,KAAK4C,SAASw7B,0BAA0BhqB,GAAcC,KAAKtN,GAC5D,EAeH,MAAMs3B,WAAsB1e,GAC1B,oBAAOnE,CAAcoE,EAAgBC,GACnC,MAAMlb,gBAAEA,EAAe+L,KAAEA,GAASxQ,SAElCyE,EAAgB25B,aAAaze,EAAYnP,EAC1C,CAED,YAAMqK,GACJ/a,KAAKu+B,qBACLv+B,KAAKghB,wBACN,CAED,kBAAAud,GACE,MAAM55B,gBAAEA,EAAe0d,KAAEA,GAASniB,SAClCyE,EAAgB25B,aAAat+B,KAAKynB,QAASpF,GAC3CriB,KAAKwb,cAAcxb,KAAK4f,eAAgB5f,KAAK6f,WAC9C,CAED,sBAAAmB,GACE,IAAK,MAAMwd,KAAsBx+B,KAAKy+B,eAAgB,CACpD,MAAM9Z,EAAa6Z,EAAmB7Z,WACtC,GAAIA,EAAY,CACd,MAAM9jB,EAAUoE,EAAsBu5B,GACtC7Z,EAAW2Z,aAAaz9B,EAAS29B,EAClC,CACF,CACF,CAED,WAAI/W,GACF,OAAOznB,KAAKmb,YAAYkU,aAAaxuB,OACtC,CAED,kBAAI49B,GACF,OAAOv+B,SAASyE,gBAAgB+F,iBAAiB,SAClD,EAGH,MAAMg0B,WAAqB/e,GACzB,oBAAOnE,CAAcoE,EAAgBC,GAC/B3f,SAASwQ,MAAQmP,aAAsB8e,gBACzCz+B,SAASwQ,KAAKoE,YAAY+K,GAE1B3f,SAASyE,gBAAgBtE,YAAYwf,EAExC,CAED,gBAAI5E,GACF,OAAOjb,KAAKmb,YAAYiV,aAAepwB,KAAK4+B,2BAC7C,CAED,gBAAI5iB,GACF,OAAKhc,KAAKmb,YAAYiV,YAMjBpwB,KAAK4+B,iCAAV,EACS,CACL3iB,OAAQ,4BAPH,CACLA,OAAQ,gCASb,CAED,qBAAMG,GACJpc,MAAK6+B,UACC7+B,KAAK8+B,WACZ,CAED,YAAM/jB,GACA/a,KAAKkb,kBACDlb,KAAK++B,aAEd,CAED,eAAAviB,GACE7Z,MAAM6Z,kBACDxc,KAAK0E,WACR1E,KAAKmgB,gCAER,CAED,uBAAI6e,GACF,OAAOh/B,KAAK8f,gBAAgBuP,YAC7B,CAED,mBAAI4P,GACF,OAAOj/B,KAAKmb,YAAYkU,YACzB,CAED,cAAIxP,GACF,OAAO7f,KAAKmb,YAAYta,OACzB,CAED,EAAAg+B,GACE,MAAMl6B,gBAAEA,GAAoB3E,KAAK8f,iBAC3BgQ,KAAEA,GAAS9vB,KAAKmb,YAElB2U,EACFnrB,EAAgBhB,aAAa,OAAQmsB,GAErCnrB,EAAgBf,gBAAgB,OAEnC,CAED,eAAMk7B,GACJ,MAAMI,EAAqBl/B,KAAKm/B,2BAC1BC,EAAwBp/B,KAAKq/B,gCACnCr/B,KAAKs/B,kCAECJ,QACAE,EAEFp/B,KAAKkb,YACPlb,KAAKu/B,uCAER,CAED,iBAAMR,SACE/+B,KAAK2e,6BAA4BpZ,UACrCvF,KAAKw/B,wBACCx/B,KAAKy/B,eAAe,GAE7B,CAED,+BAAIb,GACF,OAAO5+B,KAAKg/B,oBAAoB3Q,yBAA2BruB,KAAKi/B,gBAAgB5Q,uBACjF,CAED,mCAAMgR,GACJ,MAAMK,EAAkB,GAExB,IAAK,MAAM7+B,KAAWb,KAAK2/B,0BACzBD,EAAgBnsB,KAAKtK,EAAYpI,IAEjCX,SAASmiB,KAAKhiB,YAAYQ,SAGtB0B,QAAQ+oB,IAAIoU,EACnB,CAED,yBAAAJ,GACE,IAAK,MAAMz+B,KAAWb,KAAK4/B,sBACzB1/B,SAASmiB,KAAKhiB,YAAY4E,EAAsBpE,GAEnD,CAED,qCAAA0+B,GACE,IAAK,MAAM1+B,KAAWb,KAAK6/B,gCACzB3/B,SAASmiB,KAAK/hB,YAAYO,EAE7B,CAED,8BAAMs+B,GACJ,MAAMW,EAAkB,IAAI9/B,KAAK+/B,4BAEjC,IAAK,MAAMl/B,KAAWb,KAAKggC,+BACpBhgC,KAAKigC,8BAA8Bp/B,EAASi/B,IAC/C5/B,SAASmiB,KAAK/hB,YAAYO,GAI9B,IAAK,MAAMA,KAAWi/B,EACpB5/B,SAASmiB,KAAKhiB,YAAYQ,EAE7B,CAED,6BAAAo/B,CAA8Bp/B,EAASq/B,GACrC,IAAK,MAAOxU,EAAO7L,KAAeqgB,EAAYvuB,UAAW,CAEvD,GAAuB,SAAnB9Q,EAAQuiB,QAAoB,CAC9B,GAA0B,SAAtBvD,EAAWuD,QACb,SAEF,GAAIviB,EAAQ0T,WAAasL,EAAWtL,UAElC,OADA2rB,EAAYC,OAAOzU,EAAO,IACnB,CAEV,CAGD,GAAI7L,EAAWugB,YAAYv/B,GAEzB,OADAq/B,EAAYC,OAAOzU,EAAO,IACnB,CAEV,CAED,OAAO,CACR,CAED,oCAAA2U,GACE,IAAK,MAAMx/B,KAAWb,KAAKggC,+BACzB9/B,SAASmiB,KAAK/hB,YAAYO,EAE7B,CAED,8BAAAy/B,GACE,IAAK,MAAMz/B,KAAWb,KAAK+/B,2BACzB7/B,SAASmiB,KAAKhiB,YAAYQ,EAE7B,CAED,eAAA2+B,GACEt/B,SAASqgC,UAAUvgC,KAAK6f,YACxB7f,KAAKwgC,+BACN,CAED,6BAAAA,GACE,IAAK,MAAM5rB,KAAsB5U,KAAKygC,sBAAuB,CAC3D,MAAMjf,EAAyBvc,EAAsB2P,GACrDA,EAAmBE,YAAY0M,EAChC,CACF,CAED,mBAAMie,SACEz/B,KAAKwb,cAAcxb,KAAK4f,eAAgB5f,KAAK6f,WACpD,CAED,mCAAIggB,GACF,OAAO7/B,KAAK0gC,0BAA0B5S,QAAQjtB,GACQ,YAA7CA,EAAQ4C,aAAa,qBAE/B,CAED,6BAAIi9B,GACF,OAAO1gC,KAAKg/B,oBAAoBvQ,mCAAmCzuB,KAAKi/B,gBACzE,CAED,6BAAIU,GACF,OAAO3/B,KAAKi/B,gBAAgBxQ,mCAAmCzuB,KAAKg/B,oBACrE,CAED,yBAAIY,GACF,OAAO5/B,KAAKi/B,gBAAgB1Q,+BAA+BvuB,KAAKg/B,oBACjE,CAED,kCAAIgB,GACF,OAAOhgC,KAAKg/B,oBAAoBrQ,mBACjC,CAED,8BAAIoR,GACF,OAAO//B,KAAKi/B,gBAAgBtQ,mBAC7B,CAED,yBAAI8R,GACF,OAAOzgC,KAAK6f,WAAWnV,iBAAiB,SACzC,EAGH,MAAMi2B,WAA6BjC,GACjC,oBAAOljB,CAAcoE,EAAgBC,GACnCiM,GAAclM,EAAgBC,EAAY,CACxCgC,UAAW,CACTG,kBAAmBnhB,IAAY+/B,GAAgB//B,MAInD,IAAK,MAAM40B,KAAS7V,EAAelV,iBAAiB,eAC9Ck2B,GAAgBnL,IAAQA,EAAMvyB,SAGpC0C,EAAS,cAAe,CAAEG,OAAQ,CAAE6Z,iBAAgBC,eACrD,CAED,iCAAMlB,CAA4BC,GAChC,aAAaA,GACd,CAED,gBAAInD,GACF,MAAO,OACR,CAED,mBAAIyE,GACF,OAAO,CACR,EAGH,SAAS0gB,GAAgBnL,GACvB,OAAOA,aAAiBtzB,GACtBszB,EAAMjyB,KACY,UAAlBiyB,EAAM5xB,UACL4xB,EAAMv0B,QAAQ,yBACnB,CAEA,MAAM2/B,GACJvS,KAAO,GACPwS,UAAY,CAAE,EAEd,WAAAp+B,CAAY+L,GACVzO,KAAKyO,KAAOA,CACb,CAED,GAAAzB,CAAIF,GACF,OAAOK,EAAWL,KAAa9M,KAAK8gC,SACrC,CAED,GAAA/+B,CAAI+K,GACF,GAAI9M,KAAKgN,IAAIF,GAAW,CACtB,MAAMsM,EAAWpZ,KAAK+gC,KAAKj0B,GAE3B,OADA9M,KAAKghC,MAAMl0B,GACJsM,CACR,CACF,CAED,GAAA1J,CAAI5C,EAAUsM,GAGZ,OAFApZ,KAAKihC,MAAMn0B,EAAUsM,GACrBpZ,KAAKghC,MAAMl0B,GACJsM,CACR,CAED,KAAA5D,GACExV,KAAK8gC,UAAY,EAClB,CAID,IAAAC,CAAKj0B,GACH,OAAO9M,KAAK8gC,UAAU3zB,EAAWL,GAClC,CAED,KAAAm0B,CAAMn0B,EAAUsM,GACdpZ,KAAK8gC,UAAU3zB,EAAWL,IAAasM,CACxC,CAED,KAAA4nB,CAAMl0B,GACJ,MAAMo0B,EAAM/zB,EAAWL,GACjB4e,EAAQ1rB,KAAKsuB,KAAK3C,QAAQuV,GAC5BxV,GAAS,GAAG1rB,KAAKsuB,KAAK6R,OAAOzU,EAAO,GACxC1rB,KAAKsuB,KAAK6S,QAAQD,GAClBlhC,KAAKohC,MACN,CAED,IAAAA,GACE,IAAK,MAAMF,KAAOlhC,KAAKsuB,KAAK6R,OAAOngC,KAAKyO,aAC/BzO,KAAK8gC,UAAUI,EAEzB,EAGH,MAAMG,WAAiBpnB,GACrBqnB,cAAgB,IAAIT,GAAc,IAClCxL,qBAAuB,IAAI3pB,IAAIoB,SAASd,MACxCkpB,eAAgB,EAEhB,kBAAAQ,CAAmBva,GACjB,OAAOnb,KAAKoZ,SAASiX,wBAA0BlV,EAAYkV,sBAC5D,CAED,UAAAsF,CAAWvc,EAAU1U,GAAY,EAAOwW,GAAa,EAAM+a,GACzD,MAGMjb,EAAW,IAHOhb,KAAKuyB,cAAc0D,IAAUj2B,KAAKoZ,SAASkX,gBAC3BqQ,GAAuBjC,IAE5B1+B,KAAKoZ,SAAUA,EAAU1U,EAAWwW,GAQvE,OANKF,EAASC,aAGZgb,GAAO7C,gBAFPpzB,KAAKk1B,eAAgB,EAKhBl1B,KAAK+a,OAAOC,EACpB,CAED,WAAAsZ,CAAYlb,EAAU6c,GACpBA,GAAO7C,gBACP,MAAMpY,EAAW,IAAIqjB,GAAcr+B,KAAKoZ,SAAUA,GAAU,GAC5D,OAAOpZ,KAAK+a,OAAOC,EACpB,CAED,kBAAA4f,GACE56B,KAAKshC,cAAc9rB,OACpB,CAED,mBAAM2e,CAAc/a,EAAWpZ,KAAKoZ,UAClC,GAAIA,EAAS+W,YAAa,CACxBnwB,KAAK4C,SAAS2+B,wBACd,MAAQlM,qBAAsBvoB,GAAa9M,WACrC0G,IACN,MAAM86B,EAAiBpoB,EAASlL,QAEhC,OADAlO,KAAKshC,cAAc5xB,IAAI5C,EAAU00B,GAC1BA,CACR,CACF,CAED,4BAAAhN,CAA6B1nB,GAC3B,OAAO9M,KAAKshC,cAAcv/B,IAAI+K,EAC/B,CAED,aAAAylB,CAAc0D,GACZ,OAAQA,GAAUj2B,KAAKq1B,qBAAqBlpB,WAAa8pB,EAAMnpB,SAASX,UAA6B,YAAjB8pB,EAAM1sB,MAC3F,CAED,4BAAAgnB,CAA6B0F,GAC3B,OAAOj2B,KAAKuyB,cAAc0D,IAAUj2B,KAAKoZ,SAASmX,4BACnD,CAED,YAAInX,GACF,OAAO6V,GAAaG,YAAYpvB,KAAKa,QACtC,EAGH,MAAM4gC,GACJv3B,SAAW,wBAEX,WAAAxH,CAAYE,EAAU0+B,GACpBthC,KAAK4C,SAAWA,EAChB5C,KAAKshC,cAAgBA,CACtB,CAED,KAAAztB,GAC8B,YAAxB3T,SAAS64B,WACX74B,SAAS0B,iBAAiB,mBAAoB5B,MAAK0hC,GAEnD1hC,KAAK6b,0BAA0B3b,SAASwQ,KAE3C,CAED,IAAAqD,GACE7T,SAASkJ,oBAAoB,mBAAoBpJ,MAAK0hC,EACvD,CAED,yBAAA7lB,CAA0Bhb,GACxB,IAAK,MAAMoK,KAAQpK,EAAQ6J,iBAAiB1K,KAAKkK,UAC3ClK,KAAK4C,SAAS++B,kBAAkB12B,IAClCjL,KAAK4hC,WAAW32B,EAGrB,CAED,gBAAM22B,CAAW32B,GACf,MAAM6B,EAAW,IAAIpB,IAAIT,EAAKe,MAE9B,GAAIhM,KAAKshC,cAAct0B,IAAIF,GACzB,OAGF,MAAMoF,EAAe,IAAIhC,EAAalQ,KAAMwP,EAAYzN,IAAK+K,EAAU,IAAI0D,gBAAmBvF,SACxFiH,EAAaJ,SACpB,CAID,cAAAC,CAAeG,GACbA,EAAa7D,QAAQ,iBAAmB,UACzC,CAED,kCAAMsE,CAA6BT,EAAcM,GAC/C,IACE,MAAMpE,QAAqBoE,EAAcpE,aACnCgL,EAAW6V,GAAaC,eAAe9gB,GAE7CpO,KAAKshC,cAAc5xB,IAAIwC,EAAarG,IAAKuN,EAC1C,CAAC,MAAO7Q,GAER,CACF,CAED,cAAA0J,CAAeC,GAAgB,CAE/B,cAAAI,CAAeJ,GAAgB,CAE/B,eAAAK,CAAgBL,GAAgB,CAEhC,gCAAAQ,CAAiCR,EAAcM,GAAiB,CAEhE,yBAAAI,CAA0BV,EAAcM,GAAiB,CAEzDkvB,GAAc,KACZ1hC,KAAK6b,0BAA0B3b,SAASwQ,KAAK,EAIjD,MAAMmxB,GACJ,WAAAn/B,CAAYqzB,GACV/1B,KAAK+1B,QAAUA,CAChB,CAED,KAAAvgB,GACExV,KAAK+1B,QAAQ+L,YACd,CAED,iBAAAC,GACE/hC,MAAKgiC,EAAiB,GACvB,CAED,mBAAAC,GACEjiC,MAAKgiC,EAAiB,WACvB,CAED,qBAAAE,GACEliC,MAAKgiC,EAAiB,aACvB,CAED,EAAAA,CAAiBt+B,IApjKnB,SAAwBnE,EAAMyK,GAC5B,IAAInJ,EAAUgJ,EAAetK,GAExBsB,IACHA,EAAUX,SAASC,cAAc,QACjCU,EAAQ8C,aAAa,OAAQpE,GAE7BW,SAASmiB,KAAKhiB,YAAYQ,IAG5BA,EAAQ8C,aAAa,UAAWqG,EAGlC,CAwiKIm4B,CAAe,sBAAuBz+B,EACvC,EAyeH,SAAS0+B,GAAkCv2B,GACzChK,OAAOwgC,iBAAiBx2B,EAAKy2B,GAC/B,CAEA,MAAMA,GAAwC,CAC5CC,YAAa,CACX,GAAAxgC,GACE,OAAO/B,KAAK2I,UACb,IAICotB,GAAU,IAlfhB,MACEr0B,UAAY,IAAI64B,GAAUv6B,MAC1BwJ,QAAU,IAAIsuB,GAAQ93B,MACtBwyB,KAAO,IAAI6O,GAASrhC,KAAME,SAASyE,iBACnC8tB,QAAU,IAAIoD,GAAe71B,MAE7BwiC,aAAe,IAAInH,GAAar7B,MAChCyiC,cAAgB,IAAI3L,GACpB4L,qBAAuB,IAAI1J,GAAqBh5B,KAAME,UACtDyiC,kBAAoB,IAAItlB,GAAkBrd,KAAMsB,QAChDg2B,mBAAqB,IAAI7d,GAAmBzZ,KAAME,UAClD0iC,eAAiB,IAAIhH,GAAe57B,MACpC6iC,eAAiB,IAAIrF,GAAex9B,MACpC8iC,sBAAwB,IAAI9kB,GAAsBhe,KAAME,SAASyE,iBACjEo+B,gBAAkB,IAAI1L,GAAgBr3B,KAAME,SAASyE,iBACrDq+B,sBAAwB,IAAI7G,GAC5B8G,MAAQ,IAAIpB,GAAM7hC,MAElB6E,SAAU,EACV4O,SAAU,EACVyvB,GAA6B,IAE7B,WAAAxgC,CAAYmM,GACV7O,KAAK6O,eAAiBA,EACtB7O,KAAKmjC,UAAY,IAAI1B,GAAUzhC,KAAMA,KAAKwyB,KAAK8O,eAC/CthC,KAAKojC,iBAAmBpjC,KAAK6D,QAC7B7D,KAAKkjC,0BAA4BljC,KAAKkjC,yBACvC,CAED,KAAArvB,GACO7T,KAAKyT,UACRzT,KAAKwiC,aAAa3uB,QAClB7T,KAAKyiC,cAAc5uB,QACnB7T,KAAK0iC,qBAAqB7uB,QAC1B7T,KAAK8iC,sBAAsBjvB,QAC3B7T,KAAK2iC,kBAAkB9uB,QACvB7T,KAAKs3B,mBAAmBzjB,QACxB7T,KAAK4iC,eAAe/uB,QACpB7T,KAAK6iC,eAAehvB,QACpB7T,KAAK+iC,gBAAgBlvB,QACrB7T,KAAKwJ,QAAQqK,QACb7T,KAAKmjC,UAAUtvB,QACf7T,KAAKyT,SAAU,EACfzT,KAAK6E,SAAU,EAElB,CAED,OAAAw+B,GACErjC,KAAK6E,SAAU,CAChB,CAED,IAAAkP,GACM/T,KAAKyT,UACPzT,KAAKwiC,aAAazuB,OAClB/T,KAAKyiC,cAAc1uB,OACnB/T,KAAK0iC,qBAAqB3uB,OAC1B/T,KAAK8iC,sBAAsB/uB,OAC3B/T,KAAK2iC,kBAAkB5uB,OACvB/T,KAAKs3B,mBAAmBvjB,OACxB/T,KAAK4iC,eAAe7uB,OACpB/T,KAAK6iC,eAAe9uB,OACpB/T,KAAK+iC,gBAAgBhvB,OACrB/T,KAAKwJ,QAAQuK,OACb/T,KAAKmjC,UAAUpvB,OACf/T,KAAKyT,SAAU,EAElB,CAED,eAAA6vB,CAAgB7Q,GACdzyB,KAAKyyB,QAAUA,CAChB,CAED,KAAAwD,CAAMnpB,EAAUmC,EAAU,IACxB,MAAM0R,EAAe1R,EAAQwmB,MAAQv1B,SAAS8rB,eAAe/c,EAAQwmB,OAAS,KAE9E,GAAI9U,aAAwBxe,EAAc,CACxC,MAAMoH,EAAS0F,EAAQ1F,QAAUI,EAAegX,GAEhDA,EAAa/d,SAAS2gC,kCAAkC5iB,EAAcpX,GACtEoX,EAAand,IAAMsJ,EAASnE,UAClC,MACM3I,KAAK0B,UAAU84B,aAAatvB,EAAU4B,GAAWmC,EAEpD,CAED,OAAApL,CAAQgI,EAAK23B,GACX,MAAMC,EAAkBD,GAAaxjC,KAAK6O,eAAe7B,IAAIw2B,GACvDE,EAAe73B,IAAQ3L,SAASyL,QACjC83B,GAAoBzjC,KAAK0B,UAAUg5B,eAAgBgJ,GACtD1jC,KAAKi2B,MAAMpqB,EAAK,CAAEtC,OAAQ,UAAW6nB,qBAAqB,GAE7D,CAED,mBAAAuM,CAAoBlO,GAClBzvB,KAAK6iC,eAAelF,oBAAoBlO,EACzC,CAED,sBAAAqO,CAAuBrO,GACrBzvB,KAAK6iC,eAAe/E,uBAAuBrO,EAC5C,CAED,mBAAAkU,CAAoBrkC,GAClBU,KAAKgjC,sBAAsBjoB,OAAO3G,GAAcC,KAAK/U,GACtD,CAED,UAAAwiC,GACE9hC,KAAKwyB,KAAKoI,oBACX,CAED,mBAAAgJ,CAAoBC,GAClB1M,QAAQC,KACN,8KAGFp3B,KAAK8E,iBAAmB++B,CACzB,CAED,oBAAI/+B,CAAiB++B,GACnBv4B,EAAO1G,MAAME,iBAAmB++B,CACjC,CAED,oBAAI/+B,GACF,OAAOwG,EAAO1G,MAAME,gBACrB,CAED,SAAIF,CAAMlB,GACR4H,EAAO1G,MAAMC,QAAUnB,CACxB,CAED,SAAIkB,GACF,OAAO0G,EAAO1G,MAAMC,OACrB,CAED,YAAIi/B,CAASpgC,GACX4H,EAAOD,MAAMG,KAAO9H,CACrB,CAED,YAAIogC,GACF,OAAOx4B,EAAOD,MAAMG,IACrB,CAED,YAAIsB,GACF,OAAO9M,KAAKwJ,QAAQsD,QACrB,CAED,yBAAIqlB,GACF,OAAOnyB,KAAKwJ,QAAQ2oB,qBACrB,CAED,6BAAI+Q,GACF,OAAOljC,MAAKkjC,CACb,CAED,6BAAIA,CAA0Bx/B,GAC5B1D,KAAK6D,QAhpKT,SAAkBknB,EAAI8Y,GACpB,IAAIE,EAAY,KAEhB,MAAO,IAAIC,KAETtuB,aAAaquB,GACbA,EAAYl9B,YAFK,IAAMkkB,EAAGkZ,MAAMjkC,KAAMgkC,IAELH,EAAM,CAE3C,CAwoKmBK,CAASlkC,KAAKojC,iBAAiBe,KAAKnkC,MAAO0D,GAC1D1D,MAAKkjC,EAA6Bx/B,CACnC,CAID,iBAAAi+B,CAAkB9gC,GAChB,MAAMujC,EAAWvjC,EAAQuD,aAAa,qBAChCigC,EAAWxjC,EAAQuD,aAAa,qBAChCkgC,EAAczjC,EAAQ4C,aAAa,oBACnCgyB,EAAuB,QAAf6O,EACZ,KACApkC,SAAS8rB,eAAesY,IAAgBr6B,EAAuBpJ,EAAS,+BAE1E,GAAIujC,GAAYC,GAAY5O,aAAiBtzB,EAC3C,OAAO,EACF,CACL,MAAM2K,EAAW,IAAIpB,IAAI7K,EAAQmL,MAEjC,OAAOhM,KAAK63B,qBAAqBh3B,IAAYgM,EAAoBC,EAAU9M,KAAKoZ,SAASrM,aAC1F,CACF,CAID,4DAAA8rB,CAA6D/rB,EAAUqlB,EAAuB7V,GACxFtc,KAAK6E,QACP7E,KAAK0B,UAAUs0B,WAAWlpB,EAAUqlB,EAAuB,CACzD5oB,OAAQ,UACR0nB,gBAAgB,EAChB3U,cAGFtc,KAAKyyB,QAAQ8D,gBAAgB,CAC3Bta,OAAQ,kBAGb,CAID,qBAAAigB,CAAsBD,GACpBj8B,KAAKwJ,QAAQ8uB,sBAAsB,CAAEhD,eAAgB2G,GACtD,CAID,4BAAA7d,CAA6BnT,EAAM6B,GACjC,OAAO9M,KAAK63B,qBAAqB5sB,IAAS4B,EAAoBC,EAAU9M,KAAKoZ,SAASrM,aACvF,CAED,2BAAAyR,GAAgC,CAIhC,4BAAAN,CAA6BjT,EAAM6B,GACjC,OACE9M,KAAK63B,qBAAqB5sB,IAC1B4B,EAAoBC,EAAU9M,KAAKoZ,SAASrM,eAC5C/M,KAAK0B,UAAU80B,oCAAoC1pB,EAEtD,CAID,wBAAA0Q,CAAyBvS,EAAM6B,EAAUnM,GACvC,OACEX,KAAK63B,qBAAqB5sB,IAC1B4B,EAAoBC,EAAU9M,KAAKoZ,SAASrM,eAC5C/M,KAAKukC,yCAAyCt5B,EAAM6B,EAAUnM,EAEjE,CAED,sBAAA8c,CAAuBxS,EAAM6B,GAC3B,MAAMvD,EAASvJ,KAAKwkC,iBAAiBv5B,GAC/BomB,EAAwBpmB,EAAK7G,aAAa,qBAEhDpE,KAAKi2B,MAAMnpB,EAASd,KAAM,CAAEzC,SAAQ8nB,yBACrC,CAID,gCAAAoJ,CAAiC3tB,EAAUvD,GACzC,OAAOvJ,KAAKsyB,6BAA6BxlB,EAAUvD,IAAWvJ,KAAKykC,kCAAkC33B,EACtG,CAED,uBAAA+nB,CAAwB/nB,EAAUmC,GAChCmzB,GAAkCt1B,GAClC9M,KAAKyyB,QAAQoC,wBAAwB/nB,EAAUmC,EAChD,CAID,YAAA6jB,CAAamD,GACNA,EAAM5E,wBACTvoB,EAAW5I,SAASyE,iBACpB3E,KAAKwyB,KAAKnW,mBAAmB4Z,EAAM3Z,YAErC8lB,GAAkCnM,EAAMnpB,UACnCmpB,EAAMrD,QACT5yB,KAAK0kC,uCAAuCzO,EAAMnpB,SAAUmpB,EAAM1sB,OAErE,CAED,cAAAypB,CAAeiD,GACbj2B,KAAKwyB,KAAKjW,uBACVvT,EAAe9I,SAASyE,iBACxB3E,KAAK2kC,+BAA+B1O,EAAMT,mBAC3C,CAED,4BAAAlD,CAA6BxlB,EAAUvD,GACrC,OAAOvJ,KAAK0B,UAAU4wB,6BAA6BxlB,EAAUvD,EAC9D,CAED,+BAAA6rB,CAAgC6F,EAAQC,GACtCl7B,KAAK4kC,+CAA+C3J,EAAQC,EAC7D,CAID,cAAAnhB,CAAera,EAAMD,GACnB,MAAM8J,EAAS0C,EAAYvM,EAAMD,GAEjC,OACEO,KAAK43B,wBAAwBl4B,EAAMD,IACnCoN,EAAoB3B,EAAU3B,GAASvJ,KAAKoZ,SAASrM,aAExD,CAED,aAAAiN,CAActa,EAAMD,GAClBO,KAAK0B,UAAUi5B,WAAWj7B,EAAMD,EACjC,CAID,qBAAAk8B,GACE37B,KAAKwyB,KAAK6C,qBAAuBr1B,KAAK8M,SACtC9M,KAAK2kC,gCACN,CAED,UAAA5M,GACE/3B,KAAKwJ,QAAQgvB,kCACd,CAED,cAAAgD,GACEx7B,KAAKwJ,QAAQmvB,sCACd,CAID,yBAAAyF,CAA0B9+B,GACxBU,KAAK2jC,oBAAoBrkC,EAC1B,CAID,qBAAAiiC,GACOvhC,KAAK0B,UAAUg5B,cAAc9H,QAChC5yB,KAAK6kC,wCAER,CAED,qBAAAnpB,EAAsB7a,QAAEA,GAAWoO,GACjC,MAAMtO,EAAQX,KAAK8kC,8BAA8BjkC,EAASoO,IACpDwD,iBACJA,EACA1M,QAAQgV,OAAEA,IACRpa,EAMJ,OAJIX,KAAKwyB,KAAKxX,UAAYD,IACxB/a,KAAKwyB,KAAKxX,SAASQ,cAAgBT,IAG7BtI,CACT,CAED,oBAAAmJ,CAAqBmpB,EAAWC,EAAYvpB,GAC1Czb,KAAKwyB,KAAK6C,qBAAuBr1B,KAAKwJ,QAAQsD,SAC9C9M,KAAKilC,6BAA6BxpB,EACnC,CAED,yBAAAI,CAA0Bhb,GACxBb,KAAKmjC,UAAUtnB,0BAA0Bhb,EAC1C,CAED,eAAAqb,CAAgBD,GACdjc,KAAKyyB,QAAQ8D,gBAAgBta,EAC9B,CAID,WAAAipB,CAAYzP,GACVz1B,KAAKmlC,gCAAgC1P,EACtC,CAED,aAAA2P,CAAc5yB,EAAeijB,GAC3Bz1B,KAAKqlC,kCAAkC7yB,EAAeijB,EACvD,CAID,wCAAA8O,CAAyCt5B,EAAM6B,EAAUw4B,GAEvD,OADctlC,KAAKulC,6CAA6Ct6B,EAAM6B,EAAUw4B,GAClE7yB,gBACf,CAED,iCAAAgyB,CAAkC33B,GAEhC,OADc9M,KAAKwlC,wCAAwC14B,GAC7C2F,gBACf,CAED,4CAAA8yB,CAA6Ct6B,EAAM6B,EAAUnM,GAC3D,OAAOiF,EAAS,cAAe,CAC7BhF,OAAQqK,EACRlF,OAAQ,CAAE8F,IAAKiB,EAASd,KAAMkR,cAAevc,GAC7CmF,YAAY,GAEf,CAED,uCAAA0/B,CAAwC14B,GACtC,OAAOlH,EAAS,qBAAsB,CACpCG,OAAQ,CAAE8F,IAAKiB,EAASd,MACxBlG,YAAY,GAEf,CAED,sCAAA4+B,CAAuC53B,EAAUvD,GAC/C,OAAO3D,EAAS,cAAe,CAAEG,OAAQ,CAAE8F,IAAKiB,EAASd,KAAMzC,WAChE,CAED,sCAAAs7B,GACE,OAAOj/B,EAAS,qBACjB,CAED,6BAAAk/B,CAA8BW,EAASx2B,GACrC,OAAOrJ,EAAS,sBAAuB,CACrCG,OAAQ,CAAE0/B,aAAYx2B,GACtBnJ,YAAY,GAEf,CAED,4BAAAm/B,CAA6BxpB,GAC3B,OAAO7V,EAAS,eAAgB,CAAEG,OAAQ,CAAE0V,iBAC7C,CAED,8BAAAkpB,CAA+Be,EAAS,IACtC,OAAO9/B,EAAS,aAAc,CAC5BG,OAAQ,CAAE8F,IAAK7L,KAAK8M,SAASd,KAAM05B,WAEtC,CAED,8CAAAd,CAA+C3J,EAAQC,GACrD90B,cACE,IAAIu/B,gBAAgB,aAAc,CAChC1K,OAAQA,EAAOtyB,WACfuyB,OAAQA,EAAOvyB,aAGpB,CAED,+BAAAw8B,CAAgC1P,GAC9B,OAAO7vB,EAAS,mBAAoB,CAAEhF,OAAQ60B,GAC/C,CAED,iCAAA4P,CAAkC7yB,EAAeijB,GAC/C,OAAO7vB,EAAS,qBAAsB,CACpCG,OAAQ,CAAEyM,iBACV5R,OAAQ60B,EACR3vB,YAAY,GAEf,CAID,uBAAA8xB,CAAwBl4B,EAAMD,GAC5B,GAAyB,OAArB6L,EAAOD,MAAMG,KACf,OAAO,EACF,CACL,MAAMo6B,GAAyBnmC,GAAYO,KAAK63B,qBAAqBp4B,GAErE,MAAyB,SAArB6L,EAAOD,MAAMG,KACRo6B,GAAiE,MAAvClmC,EAAKwB,QAAQ,uBAEvC0kC,GAA0B5lC,KAAK63B,qBAAqBn4B,EAE9D,CACF,CAED,oBAAAm4B,CAAqBh3B,GACnB,MAAMglC,EAAY57B,EAAuBpJ,EAAS,gBAC5CilC,EAAc77B,EAAuBpJ,EAAS,eAGpD,OAAIyK,EAAO1G,MAAMC,SAAWihC,GAEtBD,GAC6C,SAAxCA,EAAUpiC,aAAa,gBAM5BoiC,GAC6C,QAAxCA,EAAUpiC,aAAa,aAKnC,CAID,gBAAA+gC,CAAiBv5B,GACf,OAAOtB,EAAesB,IAAS,SAChC,CAED,YAAImO,GACF,OAAOpZ,KAAKwyB,KAAKpZ,QAClB,GA0ByBvK,IACtBo0B,MAAEA,GAAOvhC,UAAWqkC,IAAgBhQ,GAO1C,SAASliB,KACPkiB,GAAQliB,OACV,CAOA,SAASyvB,GAAgB7Q,GACvBsD,GAAQuN,gBAAgB7Q,EAC1B,CAgBA,SAASwD,GAAMnpB,EAAUmC,GACvB8mB,GAAQE,MAAMnpB,EAAUmC,EAC1B,CAOA,SAAS0uB,GAAoBlO,GAC3BsG,GAAQ4H,oBAAoBlO,EAC9B,CAOA,SAASqO,GAAuBrO,GAC9BsG,GAAQ+H,uBAAuBrO,EACjC,CAQA,SAASkU,GAAoBrkC,GAC3By2B,GAAQ4N,oBAAoBrkC,EAC9B,CAQA,SAASwiC,KACP3K,QAAQC,KACN,2JAEFrB,GAAQ+L,YACV,CAYA,SAAS8B,GAAoBC,GAC3B1M,QAAQC,KACN,iMAEF9rB,EAAO1G,MAAME,iBAAmB++B,CAClC,CAEA,SAASmC,GAAiB5vB,GACxB+gB,QAAQC,KACN,qMAEF9rB,EAAOD,MAAMgL,QAAUD,CACzB,CAEA,SAAS6vB,GAAYz6B,GACnB2rB,QAAQC,KACN,2KAEF9rB,EAAOD,MAAMG,KAAOA,CACtB,CAEA,IAAI06B,GAAqBrkC,OAAOskC,OAAO,CACrCC,UAAW,KACX1kC,UAAWqkC,GACXhQ,QAASA,GACTkN,MAAOA,GACPvE,aAAcA,GACdzP,aAAcA,GACd3O,cAAeA,GACfvR,MAAOC,EACP1D,OAAQA,EACRuI,MAAOA,GACPyvB,gBAAiBA,GACjBrN,MAAOA,GACP0H,oBAAqBA,GACrBG,uBAAwBA,GACxB6F,oBAAqBA,GACrB7B,WAAYA,GACZ8B,oBAAqBA,GACrBoC,iBAAkBA,GAClBC,YAAaA,KAGf,MAAMI,WAA+BtuB,OA0hBrC,SAASuuB,GAAoBptB,GAC3B,GAAU,MAANA,EAAY,CACd,MAAMrY,EAAUX,SAAS8rB,eAAe9S,GACxC,GAAIrY,aAAmBsB,EACrB,OAAOtB,CAEV,CACH,CAEA,SAAS0lC,GAAgB1lC,EAAS2lC,GAChC,GAAI3lC,EAAS,CACX,MAAM2C,EAAM3C,EAAQ4C,aAAa,OACjC,GAAW,MAAPD,GAA6B,MAAdgjC,IAljMKC,EAkjMmCD,EAjjMtDt7B,EAijMiD1H,GAjjMjCwI,MAAQd,EAAUu7B,GAAOz6B,MAkjM5C,MAAM,IAAI+L,MAAM,6BAA6BlX,EAAQqY,yDAMvD,GAJIrY,EAAQ4D,gBAAkBvE,WAC5BW,EAAUX,SAASyU,WAAW9T,GAAS,IAGrCA,aAAmBsB,EAGrB,OAFAtB,EAAQiC,oBACRjC,EAAQmC,uBACDnC,CAEV,CA9jMH,IAA4B4lC,CA+jM5B,CAEA,MAAMC,GAAgB,CACpB,KAAAzhB,GACEjlB,KAAK2mC,eAAeC,SAASxhB,GAAMA,EAAEpkB,eAAeqiB,aAAarjB,KAAK6mC,gBAAiBzhB,EAAEf,cAC1F,EAED,MAAAhV,GACErP,KAAK8mC,gCACL9mC,KAAK2mC,eAAeC,SAASxhB,GAAMA,EAAE/V,OAAOrP,KAAK6mC,kBAClD,EAED,MAAA1J,GACEn9B,KAAK2mC,eAAeC,SAASxhB,GAAMA,EAAEpkB,eAAeqiB,aAAarjB,KAAK6mC,gBAAiBzhB,IACxF,EAED,OAAA2hB,GACE/mC,KAAK8mC,gCACL9mC,KAAK2mC,eAAeC,SAASxhB,GAAMA,EAAE2hB,QAAQ/mC,KAAK6mC,kBACnD,EAED,MAAApoB,GACEze,KAAK2mC,eAAeC,SAASxhB,GAAMA,EAAE3G,UACtC,EAED,OAAA9W,GACE,MAAM4H,EAASvP,KAAKyD,aAAa,UAEjCzD,KAAK2mC,eAAeC,SAASI,IACZ,UAAXz3B,EACFuc,GAAckb,EAAehnC,KAAK6mC,iBAElCG,EAAclyB,YAAY9U,KAAK6mC,gBAChC,GAEJ,EAED,MAAAxT,GACE,MAAM9jB,EAASvP,KAAKyD,aAAa,UAEjCzD,KAAK2mC,eAAeC,SAASI,IACZ,UAAXz3B,EACFqT,GAAcokB,EAAehnC,KAAK6mC,kBAElCG,EAAczyB,UAAY,GAC1ByyB,EAAc33B,OAAOrP,KAAK6mC,iBAC3B,GAEJ,EAED,OAAAhjC,GACEkyB,GAAQlyB,QAAQ7D,KAAK2L,QAAS3L,KAAKwjC,UACpC,GA2BH,MAAMyD,WAAsBtnC,YAC1B,0BAAa6b,CAAcqE,SACnBA,EAAWqnB,eAClB,CAED,uBAAMpkC,GACJ,UACQ9C,KAAK+a,QACZ,CAAC,MAAO3I,GACP+kB,QAAQ/kB,MAAMA,EACpB,CAAc,QACRpS,KAAKiD,YACN,CACF,CAED,YAAM8X,GACJ,OAAQ/a,KAAKqb,gBAAkB,WAC7B,MAAM1a,EAAQX,KAAKmnC,kBAEfnnC,KAAKoG,cAAczF,WACf6F,UACA7F,EAAMoF,OAAOgV,OAAO/a,MAE7B,EAP8B,EAQhC,CAED,UAAAiD,GACE,IACEjD,KAAKye,QAEN,CAAC,MAAQ,CACX,CAKD,6BAAAqoB,GACE9mC,KAAKonC,kBAAkBR,SAASS,GAAMA,EAAE5oB,UACzC,CAKD,qBAAI2oB,GACF,MAAME,EAAmBtnC,KAAK2mC,eAAeY,SAASniB,GAAM,IAAIA,EAAEzM,YAAWmV,QAAQuZ,KAAQA,EAAE5jC,aAAa,QACtG+jC,EAAiB,IAAKxnC,KAAK6mC,iBAAiBluB,UAAY,IAAKmV,QAAQuZ,KAAQA,EAAE5jC,aAAa,QAAOuE,KAAKq/B,GAAMA,EAAE5jC,aAAa,QAEnI,OAAO6jC,EAAiBxZ,QAAQuZ,GAAMG,EAAe1d,SAASud,EAAE5jC,aAAa,QAC9E,CAKD,iBAAIyjC,GACF,GAAIlnC,KAAKuJ,OAAQ,CACf,MAAMk+B,EAAiBf,GAAc1mC,KAAKuJ,QAC1C,GAAIk+B,EACF,OAAOA,EAETznC,MAAKZ,EAAO,iBACb,CACDY,MAAKZ,EAAO,8BACb,CAKD,kBAAIunC,GACF,OAAI3mC,KAAKY,OACAZ,KAAK0nC,mBACH1nC,KAAK2nC,QACP3nC,KAAK4nC,2BAEZ5nC,MAAKZ,EAAO,yCAEf,CAKD,mBAAIynC,GACF,OAAO7mC,KAAK6U,gBAAgB7K,QAAQwV,WAAU,EAC/C,CAKD,mBAAI3K,GACF,GAA+B,OAA3B7U,KAAKkhB,kBAA4B,CACnC,MAAM5M,EAAWtU,KAAKyE,cAActE,cAAc,YAElD,OADAH,KAAKK,YAAYiU,GACVA,CACb,CAAW,GAAItU,KAAKkhB,6BAA6BoE,oBAC3C,OAAOtlB,KAAKkhB,kBAEdlhB,MAAKZ,EAAO,mDACb,CAKD,UAAImK,GACF,OAAOvJ,KAAKyD,aAAa,SAC1B,CAMD,UAAI7C,GACF,OAAOZ,KAAKyD,aAAa,SAC1B,CAKD,WAAIkkC,GACF,OAAO3nC,KAAKyD,aAAa,UAC1B,CAKD,aAAI+/B,GACF,OAAOxjC,KAAKyD,aAAa,aAC1B,CAED,EAAArE,CAAOE,GACL,MAAM,IAAIyY,MAAM,GAAG/X,KAAK6nC,gBAAgBvoC,IACzC,CAED,eAAIuoC,GACF,OAAQ7nC,KAAKioB,UAAUpgB,MAAM,YAAc,IAAI,IAAM,gBACtD,CAED,qBAAIs/B,GACF,OAAO,IAAInhC,YAAY,6BAA8B,CACnDC,SAAS,EACTH,YAAY,EACZC,OAAQ,CAAE+hC,UAAW9nC,KAAM+a,OAAQksB,GAAczrB,gBAEpD,CAED,sBAAIksB,GACF,MAAM7mC,EAAUb,KAAKyE,eAAeunB,eAAehsB,KAAKY,QAExD,OAAgB,OAAZC,EACK,CAACA,GAED,EAEV,CAED,yBAAI+mC,GACF,MAAM/+B,EAAW7I,KAAKyE,eAAeiG,iBAAiB1K,KAAK2nC,SAE3D,OAAwB,IAApB9+B,EAASd,OACJM,MAAMlJ,UAAU+I,MAAMmwB,KAAKxvB,GAE3B,EAEV,EAGH,MAAMk/B,WAA4BpoC,YAChCqoC,aAAe,KAEf,iBAAAllC,GACE9C,KAAKgoC,aAAehoC,KAAKwD,IAAIqE,MAAM,aAAe,IAAIogC,UAAUjoC,KAAKwD,KAAO,IAAI0kC,YAAYloC,KAAKwD,KAEjGm6B,GAAoB39B,KAAKgoC,aAC1B,CAED,oBAAAhlC,GACMhD,KAAKgoC,eACPhoC,KAAKgoC,aAAaG,QAElBrK,GAAuB99B,KAAKgoC,cAE/B,CAED,OAAIxkC,GACF,OAAOxD,KAAKyD,aAAa,QAAU,EACpC,EAGHtB,EAAaU,oBA1zBb,MACEulC,oBAAuBC,GAAmB9lC,QAAQC,UAClD8lC,GAAuB,KACvBC,GAAuB,OACvBC,IAAa,EACbC,IAAiB,EACjBC,GAAqB,IAAI1jC,IACzB2jC,IAAoB,EACpBp/B,OAAS,KAET,WAAA7G,CAAY7B,GACVb,KAAKa,QAAUA,EACfb,KAAKwyB,KAAO,IAAI/V,GAAUzc,KAAMA,KAAKa,SACrCb,KAAK4oC,mBAAqB,IAAIp1B,GAAmBxT,KAAMA,KAAKa,SAC5Db,KAAK8iC,sBAAwB,IAAI9kB,GAAsBhe,KAAMA,KAAKa,SAClEb,KAAKie,gBAAkB,IAAItB,GAAgB3c,KAAMA,KAAKa,SACtDb,KAAKmyB,sBAAwB/pB,IAC7BpI,KAAKs3B,mBAAqB,IAAI7d,GAAmBzZ,KAAMA,KAAKa,QAC7D,CAID,OAAAkC,GACO/C,MAAKwoC,IACRxoC,MAAKwoC,GAAa,EACdxoC,KAAK6oC,cAAgB7mC,EAAkBE,KACzClC,KAAK4oC,mBAAmB/0B,QAExB7T,MAAK8oC,IAEP9oC,KAAK8iC,sBAAsBjvB,QAC3B7T,KAAKie,gBAAgBpK,QACrB7T,KAAKs3B,mBAAmBzjB,QAE3B,CAED,UAAA5Q,GACMjD,MAAKwoC,IACPxoC,MAAKwoC,GAAa,EAClBxoC,KAAK4oC,mBAAmB70B,OACxB/T,KAAK8iC,sBAAsB/uB,OAC3B/T,KAAKie,gBAAgBlK,OACrB/T,KAAKs3B,mBAAmBvjB,OAE3B,CAED,eAAAxQ,GACMvD,KAAK6oC,cAAgB7mC,EAAkBC,OACzCjC,MAAK8oC,GAER,CAED,gBAAAxlC,GACMtD,MAAK+oC,EAAqB,SAE1B/oC,KAAKa,QAAQsF,cACfnG,KAAKsE,UAAW,IAGdtE,KAAK6oC,cAAgB7mC,EAAkBC,OAASjC,MAAKyoC,IACvDzoC,MAAK8oC,IAER,CAED,iBAAA3lC,GACE,MAAMU,QAAEA,EAAOL,IAAEA,GAAQxD,KAAKa,QAO9B,OALAb,MAAK2oC,EAAoBnlC,GAAmB,UAAZK,EAEhC7D,KAAKa,QAAQ+C,gBAAgB,YAC7B5D,KAAKa,QAAQ2C,IAAM,KACnBxD,KAAKa,QAAQ2C,IAAMA,EACZxD,KAAKa,QAAQyB,MACrB,CAED,mBAAAe,GACMrD,KAAK6oC,cAAgB7mC,EAAkBE,KACzClC,KAAK4oC,mBAAmB/0B,SAExB7T,KAAK4oC,mBAAmB70B,OACxB/T,MAAK8oC,IAER,CAED,OAAMA,GACA9oC,KAAK6E,SAAW7E,KAAKwE,WAAaxE,KAAKsE,UAAYtE,KAAKgpC,YAC1DhpC,KAAKa,QAAQyB,OAAStC,MAAKi2B,EAAO/qB,EAAUlL,KAAKgpC,YACjDhpC,KAAK4oC,mBAAmB70B,aAClB/T,KAAKa,QAAQyB,OACnBtC,MAAKyoC,GAAiB,EAEzB,CAED,kBAAMvU,CAAa1hB,IACbA,EAAc5E,YAAe4E,EAAclF,WAAakF,EAAc3E,UACxE7N,KAAKgpC,UAAYx2B,EAAcnF,SAASxB,KAG1C,IACE,MAAM9E,QAAayL,EAAcpE,aACjC,GAAIrH,EAAM,CACR,MAAM7G,EAAW4G,EAAkBC,GACdkoB,GAAaE,aAAajvB,GAE9BkwB,kBACTpwB,MAAKipC,EAAmBz2B,EAAetS,SAEvCF,MAAKkpC,EAAgC12B,EAE9C,CACP,CAAc,QACRxS,MAAK2oC,GAAoB,EACzB3oC,KAAKooC,oBAAsB,IAAM7lC,QAAQC,SAC1C,CACF,CAID,yBAAA2R,CAA0BtT,GACxBb,KAAKujC,kCAAkC1iC,EAAS8I,EAAe9I,IAC/Db,MAAK8oC,GACN,CAID,4BAAA1qB,CAA6BnT,GAC3B,OAAOjL,MAAKmpC,EAA2Bl+B,EACxC,CAED,2BAAAuT,CAA4BvT,EAAMssB,EAAW73B,GAC3C,MAAM+1B,EAAQz1B,MAAKy3B,EAAkBxsB,GACjCwqB,GAAO/1B,EAAKiE,aAAa,mBAAoB8xB,EAAMvc,GACxD,CAID,wBAAA+D,CAAyBpc,EAAS02B,EAAWna,GAC3C,OAAOpd,MAAKmpC,EAA2BtoC,EACxC,CAED,oBAAAsc,CAAqBtc,EAASiM,GAC5B9M,MAAKopC,EAAevoC,EAASiM,EAC9B,CAID,cAAAiN,CAAelZ,EAASpB,GACtB,OAAOoB,EAAQK,QAAQ,gBAAkBlB,KAAKa,SAAWb,MAAKmpC,EAA2BtoC,EAASpB,EACnG,CAED,aAAAua,CAAcnZ,EAASpB,GACjBO,KAAKyX,gBACPzX,KAAKyX,eAAe1D,OAGtB/T,KAAKyX,eAAiB,IAAIvB,GAAelW,KAAMa,EAASpB,GACxD,MAAMyS,aAAEA,GAAiBlS,KAAKyX,eAC9BzX,KAAK+R,eAAeG,GACpBlS,KAAKyX,eAAe5D,OACrB,CAID,cAAA9B,CAAemB,GACbA,EAAQ7E,QAAQ,eAAiBrO,KAAKkZ,GAElClZ,KAAKqpC,0BAA0BjlC,aAAa,sBAC9C8O,EAAQJ,mBAAmBsB,GAActG,YAE5C,CAED,cAAAmE,CAAesF,GACbzO,EAAW9I,KAAKa,QACjB,CAED,gCAAA6R,CAAiC6E,EAAUyd,GACzCh1B,MAAKuoC,GACN,CAED,kCAAM51B,CAA6BO,EAAS7F,SACpCrN,KAAKk0B,aAAa7mB,GACxBrN,MAAKuoC,GACN,CAED,+BAAM31B,CAA0BM,EAAS7F,SACjCrN,KAAKk0B,aAAa7mB,GACxBrN,MAAKuoC,GACN,CAED,cAAAj2B,CAAeY,EAASd,GACtB+kB,QAAQ/kB,MAAMA,GACdpS,MAAKuoC,GACN,CAED,eAAAh2B,CAAgBgF,GACdvO,EAAehJ,KAAKa,QACrB,CAID,qBAAA6W,EAAsBpB,YAAEA,IACtBxN,EAAWwN,EAAatW,MAAKy3B,EAAkBnhB,GAChD,CAED,mCAAA2B,CAAoCR,EAAgBpK,GAClD,MAAMooB,EAAQz1B,MAAKy3B,EAAkBhgB,EAAenB,YAAamB,EAAehY,WAEhFg2B,EAAM7yB,SAAS2gC,kCAAkC9N,EAAO9rB,EAAe8N,EAAehY,UAAWgY,EAAenB,YAAamf,IAC7HA,EAAM7yB,SAASsxB,aAAa7mB,GAEvBoK,EAAepG,QAClB0kB,GAAQ+L,YAEX,CAED,gCAAAlqB,CAAiCH,EAAgBjF,GAC/CxS,KAAKa,QAAQ+B,SAASsxB,aAAa1hB,GACnCujB,GAAQ+L,YACT,CAED,qBAAA9pB,CAAsBP,EAAgBrF,GACpC+kB,QAAQ/kB,MAAMA,EACf,CAED,sBAAA+F,EAAuB7B,YAAEA,IACvBtN,EAAesN,EAAatW,MAAKy3B,EAAkBnhB,GACpD,CAID,qBAAAoF,EAAwB7a,QAASyoC,GAAYr6B,GAC3C,MAAMtO,EAAQiF,EAAS,4BAA6B,CAClDhF,OAAQZ,KAAKa,QACbkF,OAAQ,CAAEujC,cAAar6B,GACvBnJ,YAAY,KAGR2M,iBACJA,EACA1M,QAAQgV,OAAEA,IACRpa,EAMJ,OAJIX,KAAKwyB,KAAKxX,UAAYD,IACxB/a,KAAKwyB,KAAKxX,SAASQ,cAAgBT,IAG7BtI,CACT,CAED,oBAAAmJ,CAAqBmpB,EAAWC,EAAYuE,GAAiB,CAE7D,yBAAA1tB,CAA0Bhb,GACxBk1B,GAAQla,0BAA0Bhb,EACnC,CAED,eAAAqb,GAAoB,CAIpB,eAAA+E,CAAgBrB,EAAgB4pB,GAC9BxpC,KAAKypC,qBAAuB7pB,EAAeJ,WAAU,EACtD,CAED0R,oBAAsB,EAAGrwB,cACvB,MAAM40B,EAAQ50B,EAAQiJ,cAAc,IAAM9J,KAAKa,QAAQqY,IAEnDuc,GAASz1B,KAAKypC,sBAChBhU,EAAMiU,mBAAmB1pC,KAAKypC,qBAAqB9wB,iBAG9C3Y,KAAKypC,oBAAoB,EAKlC,OAAMR,CAAmBz2B,EAAetS,GACtC,MAAMypC,QAAwB3pC,KAAK4pC,2BAA2B1pC,EAASwQ,MACjEm5B,EAAgB7pC,MAAK2oC,EAAoBzc,GAAwB5L,GAEvE,GAAIqpB,EAAiB,CACnB,MAAMvwB,EAAW,IAAIX,GAASkxB,GACxB3uB,EAAW,IAAI6uB,EAAc7pC,KAAMA,KAAKwyB,KAAKpZ,SAAUA,GAAU,GAAO,GAC1EpZ,KAAKwyB,KAAKnX,qBAAqBrb,KAAKwyB,KAAKnX,cAC7Crb,KAAKozB,sBAECpzB,KAAKwyB,KAAKzX,OAAOC,GACvBhb,KAAKsE,UAAW,EAChByxB,GAAQqP,cAAc5yB,EAAexS,KAAKa,SAC1Ck1B,GAAQmP,YAAYllC,KAAKa,eACnBb,KAAKooC,oBAAoB51B,EAChC,MAAUxS,MAAK8pC,EAAoCt3B,IAClDxS,MAAK+pC,EAAgCv3B,EAExC,CAED,OAAMyjB,CAAOpqB,GACX,MAAMqH,EAAU,IAAIhD,EAAalQ,KAAMwP,EAAYzN,IAAK8J,EAAK,IAAI2E,gBAAmBxQ,KAAKa,SAKzF,OAHAb,MAAKsoC,GAAsB12B,SAC3B5R,MAAKsoC,EAAuBp1B,EAErB,IAAI3Q,SAASC,IAClBxC,MAAKuoC,EAAuB,KAC1BvoC,MAAKuoC,EAAuB,OAC5BvoC,MAAKsoC,EAAuB,KAC5B9lC,GAAS,EAEX0Q,EAAQpB,SAAS,GAEpB,CAED,EAAAs3B,CAAevoC,EAASgL,EAAKpM,GAC3B,MAAMg2B,EAAQz1B,MAAKy3B,EAAkB52B,EAASpB,GAE9Cg2B,EAAM7yB,SAAS2gC,kCAAkC9N,EAAO9rB,EAAelK,EAAWoB,EAAS40B,IAE3Fz1B,MAAKgqC,EAA8BnpC,GAAS,KAC1C40B,EAAMjyB,IAAMqI,CAAG,GAElB,CAED,iCAAA03B,CAAkC9N,EAAOlsB,EAAS,MAGhD,GAFAvJ,KAAKuJ,OAASA,EAEVvJ,KAAKuJ,OAAQ,CACf,MAAM0gC,EAAehb,GAAaG,YAAYqG,GAAOvnB,SAC/CgjB,oBAAEA,GAAwBuE,EAAM7yB,SAEtC6yB,EAAM7yB,SAASwlC,oBAAsB7iC,MAAOiN,IAC1C,GAAIijB,EAAMjyB,IAAK,CACb,MAAMkK,WAAEA,EAAUE,WAAEA,GAAe4E,EAG7BvD,EAAU,CACd5B,SAFe,CAAEK,aAAYE,aAAYQ,mBADhBoE,EAAcpE,cAIvC8iB,sBACAhW,YAAY,EACZiW,eAAe,EACfgB,sBAAuBnyB,KAAKmyB,sBAC5B/Y,SAAU6wB,GAGRjqC,KAAKuJ,SAAQ0F,EAAQ1F,OAASvJ,KAAKuJ,QAEvCwsB,GAAQE,MAAMR,EAAMjyB,IAAKyL,EAC1B,EAEJ,CACF,CAED,aAAAmkB,GACE,GAAIpzB,KAAKuJ,OAAQ,CACf,MAAMgG,EAASjG,EAA0BtJ,KAAKuJ,QAC9CwsB,GAAQvsB,QAAQ6pB,OAAO9jB,EAAQrE,EAAUlL,KAAKa,QAAQ2C,KAAO,IAAKxD,KAAKmyB,sBACxE,CACF,CAED,OAAM+W,CAAgC12B,GACpC2kB,QAAQC,KACN,iBAAiB5kB,EAAc9E,qCAAqC1N,KAAKa,QAAQqY,0EAG7ElZ,MAAKkqC,EAAe13B,EAAcnF,SACzC,CAED,EAAAy8B,CAAoCt3B,GAClCxS,KAAKa,QAAQ8C,aAAa,WAAY,IAEtC,MAAM0J,EAAWmF,EAAcnF,SAe/B,OANczH,EAAS,sBAAuB,CAC5ChF,OAAQZ,KAAKa,QACbkF,OAAQ,CAAEsH,WAAU4oB,MAVR1wB,MAAOsG,EAAKoD,KACpBpD,aAAes+B,SACjBnqC,MAAKkqC,EAAer+B,GAEpBkqB,GAAQE,MAAMpqB,EAAKoD,EACpB,GAMDnJ,YAAY,IAGA2M,gBACf,CAED,EAAAs3B,CAAgCv3B,GAC9BxS,KAAKwyB,KAAK9V,UACV1c,MAAKoqC,EAAwB53B,EAC9B,CAED,EAAA43B,CAAwB53B,GACtB,MAAMlT,EAAU,iBAAiBkT,EAAc9E,6DAA6D1N,KAAKa,QAAQqY,qGACzH,MAAM,IAAImtB,GAAuB/mC,EAClC,CAED,OAAM4qC,CAAe78B,GACnB,MAAMg9B,EAAU,IAAIj9B,EAAcC,GAC5Be,QAAqBi8B,EAAQj8B,cAC7BtB,SAAEA,EAAQc,WAAEA,EAAUF,WAAEA,GAAe28B,EAE7C,OAAOtU,GAAQE,MAAMnpB,EAAU,CAAEO,SAAU,CAAEO,aAAYF,aAAYU,iBACtE,CAED,EAAAqpB,CAAkB52B,EAASpB,GAEzB,OAAO6mC,GADI7iC,EAAa,mBAAoBhE,EAAWoB,IAAYb,KAAKa,QAAQ4C,aAAa,YAC3DzD,KAAKa,OACxC,CAED,gCAAM+oC,CAA2B/D,GAC/B,IAAIhlC,EACJ,MAAMqY,EAAKoxB,IAAIC,OAAOvqC,KAAKkZ,IAE3B,IAEE,GADArY,EAAU0lC,GAAgBV,EAAU/7B,cAAc,eAAeoP,KAAOlZ,KAAKgpC,WACzEnoC,EACF,OAAOA,EAIT,GADAA,EAAU0lC,GAAgBV,EAAU/7B,cAAc,6BAA6BoP,MAAQlZ,KAAKgpC,WACxFnoC,EAEF,aADMA,EAAQyB,aACDtC,KAAK4pC,2BAA2B/oC,EAEhD,CAAC,MAAOuR,GAEP,OADA+kB,QAAQ/kB,MAAMA,GACP,IAAIjQ,CACZ,CAED,OAAO,IACR,CAED,EAAAqoC,CAAuB9qC,EAAMD,GAG3B,OAAOoN,EAAoB3B,EAFZe,EAAYvM,EAAMD,IAEaO,KAAK+M,aACpD,CAED,EAAAo8B,CAA2BtoC,EAASpB,GAClC,MAAMyZ,EAAKzV,EAAa,mBAAoBhE,EAAWoB,IAAYb,KAAKa,QAAQ4C,aAAa,UAE7F,GAAI5C,aAAmBN,kBAAoBP,MAAKwqC,EAAuB3pC,EAASpB,GAC9E,OAAO,EAGT,IAAKO,KAAK6E,SAAiB,QAANqU,EACnB,OAAO,EAGT,GAAIA,EAAI,CACN,MAAMyH,EAAe2lB,GAAoBptB,GACzC,GAAIyH,EACF,OAAQA,EAAaxc,QAExB,CAED,QAAK4xB,GAAQ8B,qBAAqBh3B,MAI9BpB,IAAcs2B,GAAQ8B,qBAAqBp4B,GAKhD,CAID,MAAIyZ,GACF,OAAOlZ,KAAKa,QAAQqY,EACrB,CAED,WAAIrU,GACF,OAAQ7E,KAAKa,QAAQsD,QACtB,CAED,aAAI6kC,GACF,GAAIhpC,KAAKa,QAAQ2C,IACf,OAAOxD,KAAKa,QAAQ2C,GAEvB,CAED,aAAIwlC,CAAUA,GACZhpC,MAAKyqC,EAA4B,OAAO,KACtCzqC,KAAKa,QAAQ2C,IAAMwlC,GAAa,IAAI,GAEvC,CAED,gBAAIH,GACF,OAAO7oC,KAAKa,QAAQkD,OACrB,CAED,aAAIQ,GACF,YAA+BlC,IAAxBrC,KAAKyX,qBAAgEpV,IAAhCrC,MAAKuoC,GAClD,CAED,YAAIjkC,GACF,OAAOtE,KAAKa,QAAQuD,aAAa,WAClC,CAED,YAAIE,CAASZ,GACPA,EACF1D,KAAKa,QAAQ8C,aAAa,WAAY,IAEtC3D,KAAKa,QAAQ+C,gBAAgB,WAEhC,CAED,YAAIY,GACF,OAAOxE,KAAKa,QAAQ2D,UAAYxE,MAAKwoC,CACtC,CAED,gBAAIz7B,GACF,MAAM4qB,EAAO33B,KAAKa,QAAQ4D,cAAcqF,cAAc,2BAEtD,OAAOoB,EADMysB,GAAM3tB,SAAW,IAE/B,CAED,EAAA++B,CAAqBngC,GACnB,OAAO5I,MAAK0oC,EAAmB17B,IAAIpE,EACpC,CAED,EAAA6hC,CAA4B7hC,EAAegW,GACzC5e,MAAK0oC,EAAmBl6B,IAAI5F,GAC5BgW,IACA5e,MAAK0oC,EAAmB95B,OAAOhG,EAChC,CAED,EAAAohC,CAA8BnpC,EAAS+d,GACrC5e,KAAKqpC,yBAA2BxoC,EAChC+d,WACO5e,KAAKqpC,wBACb,QAuSuChnC,IAAtCqoC,eAAe3oC,IAAI,gBACrB2oC,eAAeC,OAAO,cAAexoC,QAGIE,IAAvCqoC,eAAe3oC,IAAI,iBACrB2oC,eAAeC,OAAO,eAAgB1D,SAGU5kC,IAA9CqoC,eAAe3oC,IAAI,wBACrB2oC,eAAeC,OAAO,sBAAuB5C,IAG/C,MACE,IAAIlnC,EAAUX,SAAS0qC,cACvB,GAAK/pC,IACDA,EAAQuD,aAAa,+BAGzB,IADAvD,EAAUA,EAAQG,cACXH,GAAS,CACd,GAAIA,GAAWX,SAASwQ,KACtB,OAAOymB,QAAQC,KACblwB,CAAQ;;;;;;;;;QAURrG,EAAQonB,WAIZpnB,EAAUA,EAAQG,aACnB,CACF,EAzBD,GA2BAM,OAAO4kC,MAAQ,IAAKA,GAAOQ,kBAC3B7yB,shBCvgOA,IAAIg3B,GAEGtlC,eAAeulC,KACpB,OAAOD,IAAYE,GAAYC,KAAiBzf,KAAKwf,IACvD,CAEO,SAASA,GAAYE,GAC1B,OAAOJ,GAAWI,CACpB,CAEO1lC,eAAeylC,KACpB,MAAMA,eAAEA,SAAyBzoC,QAAsEC,UAAA+oB,MAAA,WAAA,OAAAG,EAAA,IACvG,OAAOsf,GACT,CAEOzlC,eAAe2lC,GAAYC,EAASC,GACzC,MAAMC,cAAEA,SAAwBP,KAChC,OAAOO,EAAcC,OAAOH,EAASC,EACvC,uGCCe,SAASG,GAAMC,GAC1B,OAAKA,GAAsB,iBAARA,EACfA,aAAep2B,MAAQo2B,aAAeC,OAAeD,EACrDnjC,MAAMqjC,QAAQF,GAAaA,EAAIxjC,IAAIujC,IAChC1pC,OAAOysB,KAAKkd,GAAKlkC,QAAO,SAAUqkC,EAAKzK,GAK1C,OADAyK,EAHYzK,EAAI,GAAGj9B,cAAgBi9B,EAAIh5B,MAAM,GAAGP,QAAQ,aAAa,SAAUikC,EAAGpxB,GAC9E,MAAO,IAAMA,EAAEvW,aAC3B,KACqBsnC,GAAKC,EAAItK,IACfyK,CACV,GAAE,CAAE,GATuCH,CAUhD,CC1BA,MAAMK,WAAsClsC,YAC1CyC,0BAA4B,CAAC,UAAW,sBAExC,uBAAMU,GACJ66B,GAAoB39B,MACpBA,KAAK8rC,mBAAqBZ,GAAYlrC,KAAKmrC,QAAS,CAClDY,SAAU/rC,KAAKgsC,qBAAqB7H,KAAKnkC,MACzCwoC,UAAWxoC,KAAKisC,sBAAsB9H,KAAKnkC,MAC3CksC,aAAclsC,KAAKmsC,yBAAyBhI,KAAKnkC,OAEpD,CAED,oBAAAgD,GACE86B,GAAuB99B,MACnBA,KAAK8rC,cAAc9rC,KAAK8rC,aAAaM,cACzCpsC,KAAKmsC,0BACN,CAED,wBAAA/oC,GACMpD,KAAK8rC,eACP9rC,KAAKgD,uBACLhD,KAAK8C,oBAER,CAED,oBAAAkpC,CAAqB9N,GACnB,MAAMv9B,EAAQ,IAAI0rC,aAAa,UAAW,CAAEnO,SAC5C,OAAOl+B,KAAKoG,cAAczF,EAC3B,CAED,qBAAAsrC,GACEjsC,KAAK2D,aAAa,YAAa,GAChC,CAED,wBAAAwoC,GACEnsC,KAAK4D,gBAAgB,YACtB,CAED,WAAIunC,GAGF,MAAO,CAAEA,QAFOnrC,KAAKyD,aAAa,WAEhB6oC,mBADStsC,KAAKyD,aAAa,yBACJ8oC,GAAS,IAAKvsC,KAAKwsC,UAC7D,OAIqDnqC,IAApDqoC,eAAe3oC,IAAI,8BACrB2oC,eAAeC,OAAO,4BAA6BkB,ICzCrDvqC,OAAO4kC,MAAQA,GAEftkC,iBAAiB,8BCZV,SAAqCjB,GAC1C,GAAIA,EAAMC,kBAAkBL,gBAAiB,CAC3C,MAAQK,OAAQlB,EAAMqG,QAAQ6K,aAAEA,IAAmBjQ,EAEnDjB,EAAKkC,iBAAiB,sBAAsB,EAAGmE,QAAU0R,gBAAkBhY,kBACzE,MAAMiR,EAmDZ,SAAoBA,GAClB,OAAOA,aAAgBa,UAAYb,aAAgBF,eACrD,CArDmBi8B,CAAW77B,EAAaF,MAAQE,EAAaF,KAAO,IAAIF,gBAC/DjB,EAeZ,SAA8B9P,EAAWiR,EAAMhR,GAC7C,MAAMgtC,EAaR,SAA6BjtC,GAC3B,OAAIA,aAAqBktC,mBAAqBltC,aAAqBunB,iBAQ1C,YAAnBvnB,EAAUF,KACLE,EAAUiE,MACRjE,EAAU2E,aAAa,cACzB3E,EAAUitC,WAEV,KAGF,IAEX,CAhCqBE,CAAoBntC,GACjCotC,EAAiBn8B,EAAK3O,IAAI,WAC1BwN,EAAS7P,EAAK+D,aAAa,WAAa,MAE9C,MAAyB,iBAAdipC,EACFA,EAC2B,iBAAlBG,EACTA,EAEAt9B,CAEX,CA3BqBu9B,CAAqBrtC,EAAWiR,EAAMhR,GAEhD,OAAO+B,KAAK8N,KACX,QAAQ9N,KAAK8N,GACfmB,EAAK9B,OAAO,WAEZ8B,EAAKtP,IAAI,UAAWmO,GAGtBqB,EAAarB,OAAS,OACvB,GACA,CAAElG,MAAM,GACZ,CACH,ICnBA,IAAe0jC,GAAA,CACbC,OAA2B,oBAAZ7V,QAA0BA,aAAU90B,EACnD4lC,UAAgC,oBAAdA,UAA4BA,eAAY5lC,GCY7C2qC,GAAA,CACb,GAAAC,IAAOC,GACDltC,KAAK6E,UACPqoC,EAAS35B,KAAK6B,KAAKC,OACnB03B,GAASC,OAAOC,IAAI,mBAAoBC,GAE3C,GCfH,MAAM73B,GAAM,KAAM,IAAID,MAAOK,UAEvB03B,GAAeC,IAAS/3B,KAAQ+3B,GAAQ,IAE9C,MAAMC,GACJ,WAAA3qC,CAAY4qC,GACVttC,KAAKutC,oBAAsBvtC,KAAKutC,oBAAoBpJ,KAAKnkC,MACzDA,KAAKstC,WAAaA,EAClBttC,KAAKwtC,kBAAoB,CAC1B,CAED,KAAA35B,GACO7T,KAAKytC,cACRztC,KAAK0tC,UAAYr4B,YACVrV,KAAK2tC,UACZ3tC,KAAK4tC,eACLhsC,iBAAiB,mBAAoB5B,KAAKutC,qBAC1CP,GAAOC,IAAI,gDAAgDjtC,KAAK0C,YAAYmrC,oBAE/E,CAED,IAAA95B,GACM/T,KAAKytC,cACPztC,KAAK2tC,UAAYt4B,KACjBrV,KAAK8tC,cACL1kC,oBAAoB,mBAAoBpJ,KAAKutC,qBAC7CP,GAAOC,IAAI,6BAEd,CAED,SAAAQ,GACE,OAAOztC,KAAK0tC,YAAc1tC,KAAK2tC,SAChC,CAED,aAAAI,GACE/tC,KAAKguC,SAAW34B,IACjB,CAED,aAAA44B,GACEjuC,KAAKwtC,kBAAoB,SAClBxtC,KAAKkuC,eACZlB,GAAOC,IAAI,qCACZ,CAED,gBAAAkB,GACEnuC,KAAKkuC,eAAiB74B,KACtB23B,GAAOC,IAAI,wCACZ,CAID,YAAAW,GACE5tC,KAAK8tC,cACL9tC,KAAKouC,MACN,CAED,WAAAN,GACEp4B,aAAa1V,KAAKquC,YACnB,CAED,IAAAD,GACEpuC,KAAKquC,YAAcxnC,YAAW,KAC5B7G,KAAKsuC,mBACLtuC,KAAKouC,MAAM,GAEXpuC,KAAKuuC,kBACR,CAED,eAAAA,GACE,MAAMV,eAAEA,EAAcW,wBAAEA,GAA4BxuC,KAAK0C,YAIzD,OAAwB,IAAjBmrC,EAHSrlC,KAAKimC,IAAI,EAAID,EAAyBhmC,KAAKkmC,IAAI1uC,KAAKwtC,kBAAmB,MAG7C,GAFG,IAA3BxtC,KAAKwtC,kBAA0B,EAAMgB,GAC5BhmC,KAAKE,SAEjC,CAED,gBAAA4lC,GACMtuC,KAAK2uC,sBACP3B,GAAOC,IAAI,oEAAoEjtC,KAAKwtC,mCAAmCL,GAAantC,KAAK4uC,qCAAqC5uC,KAAK0C,YAAYmrC,oBAC/L7tC,KAAKwtC,oBACDxtC,KAAK6uC,uBACP7B,GAAOC,IAAI,+EAA+EE,GAAantC,KAAKkuC,sBAE5GlB,GAAOC,IAAI,+BACXjtC,KAAKstC,WAAWwB,UAGrB,CAED,eAAIF,GACF,OAAO5uC,KAAKguC,SAAWhuC,KAAKguC,SAAWhuC,KAAK0tC,SAC7C,CAED,iBAAAiB,GACE,OAAOxB,GAAantC,KAAK4uC,aAAe5uC,KAAK0C,YAAYmrC,cAC1D,CAED,oBAAAgB,GACE,OAAO7uC,KAAKkuC,gBAAmBf,GAAantC,KAAKkuC,gBAAkBluC,KAAK0C,YAAYmrC,cACrF,CAED,mBAAAN,GACmC,YAA7BrtC,SAASuG,iBACXI,YAAW,MACL7G,KAAK2uC,qBAAwB3uC,KAAKstC,WAAWyB,WAC/C/B,GAAOC,IAAI,uFAAuF/sC,SAASuG,mBAC3GzG,KAAKstC,WAAWwB,SACjB,GAED,IAEL,EAIHzB,GAAkBQ,eAAiB,EACnCR,GAAkBmB,wBAA0B,IAE5C,IAAeQ,GAAA3B,GC3HA4B,GAAA,CACbC,cAAiB,CACfC,QAAW,UACXlsC,WAAc,aACdmsC,KAAQ,OACRC,aAAgB,uBAChBC,UAAa,uBAEfC,mBAAsB,CACpBC,aAAgB,eAChBC,gBAAmB,kBACnBC,eAAkB,iBAClBC,OAAU,UAEZC,mBAAsB,SACtBC,UAAa,CACX,sBACA,4BCVJ,MAAMX,cAACA,GAAaW,UAAEA,IAAaZ,GAC7Ba,GAAqBD,GAAU3nC,MAAM,EAAG2nC,GAAU9nC,OAAS,GAE3D4jB,GAAU,GAAGA,QAEnB,MAAMokB,GACJ,WAAArtC,CAAYmoC,GACV7qC,KAAKgwC,KAAOhwC,KAAKgwC,KAAK7L,KAAKnkC,MAC3BA,KAAK6qC,SAAWA,EAChB7qC,KAAKqrC,cAAgBrrC,KAAK6qC,SAASQ,cACnCrrC,KAAKiwC,QAAU,IAAI5C,GAAkBrtC,MACrCA,KAAKksC,cAAe,CACrB,CAED,IAAAgE,CAAKhS,GACH,QAAIl+B,KAAK+uC,WACP/uC,KAAKmwC,UAAUD,KAAKE,KAAKC,UAAUnS,KAC5B,EAIV,CAED,IAAA8R,GACE,GAAIhwC,KAAKwE,WAEP,OADAwoC,GAAOC,IAAI,uDAAuDjtC,KAAKswC,eAChE,EACF,CACL,MAAMC,EAAkB,IAAIV,MAAc7vC,KAAK6qC,SAAS2F,cAAgB,IAMxE,OALAxD,GAAOC,IAAI,uCAAuCjtC,KAAKswC,6BAA6BC,KAChFvwC,KAAKmwC,WAAanwC,KAAKywC,yBAC3BzwC,KAAKmwC,UAAY,IAAIpD,GAAS9E,UAAUjoC,KAAK6qC,SAASh/B,IAAK0kC,GAC3DvwC,KAAK0wC,uBACL1wC,KAAKiwC,QAAQp8B,SACN,CACR,CACF,CAED,KAAAs0B,EAAMwI,eAACA,GAAkB,CAACA,gBAAgB,IAGxC,GAFKA,GAAkB3wC,KAAKiwC,QAAQl8B,OAEhC/T,KAAK+uC,SACP,OAAO/uC,KAAKmwC,UAAUhI,OAEzB,CAED,MAAA2G,GAEE,GADA9B,GAAOC,IAAI,yCAAyCjtC,KAAKswC,eACrDtwC,KAAKwE,WAWP,OAAOxE,KAAKgwC,OAVZ,IACE,OAAOhwC,KAAKmoC,OACb,CAAC,MAAO/1B,GACP46B,GAAOC,IAAI,6BAA8B76B,EAC1C,CACO,QACN46B,GAAOC,IAAI,0BAA0BjtC,KAAK0C,YAAYkuC,iBACtD/pC,WAAW7G,KAAKgwC,KAAMhwC,KAAK0C,YAAYkuC,YACxC,CAIJ,CAED,WAAAC,GACE,GAAI7wC,KAAKmwC,UACP,OAAOnwC,KAAKmwC,UAAUhW,QAEzB,CAED,MAAA4U,GACE,OAAO/uC,KAAK8wC,QAAQ,OACrB,CAED,QAAAtsC,GACE,OAAOxE,KAAK8wC,QAAQ,OAAQ,aAC7B,CAED,gBAAAC,GACE,OAAO/wC,KAAKiwC,QAAQzC,kBAAoB,CACzC,CAID,mBAAAwD,GACE,OAAOrlB,GAAQ0M,KAAKyX,GAAoB9vC,KAAK6wC,gBAAkB,CAChE,CAED,OAAAC,IAAWG,GACT,OAAOtlB,GAAQ0M,KAAK4Y,EAAQjxC,KAAKswC,aAAe,CACjD,CAED,QAAAA,GACE,GAAItwC,KAAKmwC,UACP,IAAK,IAAIh6B,KAAS42B,GAAS9E,UACzB,GAAI8E,GAAS9E,UAAU9xB,KAAWnW,KAAKmwC,UAAUpX,WAC/C,OAAO5iB,EAAMlS,cAInB,OAAO,IACR,CAED,oBAAAysC,GACE,IAAK,IAAI7qC,KAAa7F,KAAKkxC,OAAQ,CACjC,MAAMC,EAAUnxC,KAAKkxC,OAAOrrC,GAAWs+B,KAAKnkC,MAC5CA,KAAKmwC,UAAU,KAAKtqC,KAAesrC,CACpC,CACF,CAED,sBAAAV,GACE,IAAK,IAAI5qC,KAAa7F,KAAKkxC,OACzBlxC,KAAKmwC,UAAU,KAAKtqC,KAAe,WAAa,CAEnD,EAIHkqC,GAAWa,YAAc,IAEzBb,GAAW5wC,UAAU+xC,OAAS,CAC5B,OAAA5xC,CAAQqB,GACN,IAAKX,KAAKgxC,sBAAyB,OACnC,MAAMnf,WAACA,EAAUvyB,QAAEA,EAAO2c,OAAEA,EAAMm1B,UAAEA,EAASvxC,KAAEA,GAAQuwC,KAAKiB,MAAM1wC,EAAMu9B,MAExE,OADAl+B,KAAKiwC,QAAQlC,gBACLluC,GACN,KAAKqvC,GAAcC,QAKjB,OAJInvC,KAAK+wC,qBACP/wC,KAAKsxC,oBAAqB,GAE5BtxC,KAAKiwC,QAAQhC,gBACNjuC,KAAKqrC,cAAcnoC,SAC5B,KAAKgsC,GAAcjsC,WAEjB,OADA+pC,GAAOC,IAAI,0BAA0BhxB,KAC9Bjc,KAAKmoC,MAAM,CAACwI,eAAgBS,IACrC,KAAKlC,GAAcE,KACjB,OAAO,KACT,KAAKF,GAAcG,aAEjB,OADArvC,KAAKqrC,cAAckG,oBAAoB1f,GACnC7xB,KAAKsxC,oBACPtxC,KAAKsxC,oBAAqB,EACnBtxC,KAAKqrC,cAAcmG,OAAO3f,EAAY,YAAa,CAAC4f,aAAa,KAEjEzxC,KAAKqrC,cAAcmG,OAAO3f,EAAY,YAAa,CAAC4f,aAAa,IAE5E,KAAKvC,GAAcI,UACjB,OAAOtvC,KAAKqrC,cAAcrrB,OAAO6R,GACnC,QACE,OAAO7xB,KAAKqrC,cAAcmG,OAAO3f,EAAY,WAAYvyB,GAE9D,EAED,IAAA0wC,GAGE,GAFAhD,GAAOC,IAAI,kCAAkCjtC,KAAK6wC,8BAClD7wC,KAAKksC,cAAe,GACflsC,KAAKgxC,sBAER,OADAhE,GAAOC,IAAI,gEACJjtC,KAAKmoC,MAAM,CAACwI,gBAAgB,GAEtC,EAED,KAAAxI,CAAMxnC,GAEJ,GADAqsC,GAAOC,IAAI,4BACPjtC,KAAKksC,aAGT,OAFAlsC,KAAKksC,cAAe,EACpBlsC,KAAKiwC,QAAQ9B,mBACNnuC,KAAKqrC,cAAcqG,UAAU,eAAgB,CAACC,qBAAsB3xC,KAAKiwC,QAAQxC,aACzF,EAED,KAAAr7B,GACE46B,GAAOC,IAAI,0BACZ,GAGH,IAAe2E,GAAA7B,GChHA,MAAM8B,GACnB,WAAAnvC,CAAYmoC,EAAUn5B,EAAS,CAAA,EAAI05B,GACjCprC,KAAK6qC,SAAWA,EAChB7qC,KAAK6xB,WAAaue,KAAKC,UAAU3+B,GAbtB,SAASogC,EAAQC,GAC9B,GAAkB,MAAdA,EACF,IAAK,IAAI7Q,KAAO6Q,EAAY,CAC1B,MAAMruC,EAAQquC,EAAW7Q,GACzB4Q,EAAO5Q,GAAOx9B,CACf,CAGL,CAMIsuC,CAAOhyC,KAAMorC,EACd,CAGD,OAAAt5B,CAAQvI,EAAQ20B,EAAO,IAErB,OADAA,EAAK30B,OAASA,EACPvJ,KAAKkwC,KAAKhS,EAClB,CAED,IAAAgS,CAAKhS,GACH,OAAOl+B,KAAK6qC,SAASqF,KAAK,CAAC+B,QAAS,UAAWpgB,WAAY7xB,KAAK6xB,WAAYqM,KAAMkS,KAAKC,UAAUnS,IAClG,CAED,WAAAkO,GACE,OAAOpsC,KAAK6qC,SAASQ,cAAc5sB,OAAOze,KAC3C,ECtCH,IAAekyC,GA5Cf,MACE,WAAAxvC,CAAY2oC,GACVrrC,KAAKqrC,cAAgBA,EACrBrrC,KAAKmyC,qBAAuB,EAC7B,CAED,SAAAC,CAAUtG,IAC+C,GAApD9rC,KAAKmyC,qBAAqBxmB,QAAQmgB,IACnCkB,GAAOC,IAAI,sCAAsCnB,EAAaja,cAC9D7xB,KAAKmyC,qBAAqB5+B,KAAKu4B,IAG/BkB,GAAOC,IAAI,8CAA8CnB,EAAaja,cAExE7xB,KAAKqyC,mBACN,CAED,MAAAC,CAAOxG,GACLkB,GAAOC,IAAI,oCAAoCnB,EAAaja,cAC5D7xB,KAAKmyC,qBAAwBnyC,KAAKmyC,qBAAqBrkB,QAAQtD,GAAMA,IAAMshB,GAC5E,CAED,iBAAAuG,GACEryC,KAAKuyC,mBACLvyC,KAAKwyC,kBACN,CAED,gBAAAD,GACE78B,aAAa1V,KAAKyyC,aACnB,CAED,gBAAAD,GACExyC,KAAKyyC,aAAe5rC,YAAW,KACzB7G,KAAKqrC,eAA0D,mBAAlCrrC,KAAKqrC,cAAuB,WAC3DrrC,KAAKmyC,qBAAqBnqC,KAAK8jC,IAC7BkB,GAAOC,IAAI,uCAAuCnB,EAAaja,cAC/D7xB,KAAKqrC,cAAcqH,UAAU5G,EAAa,GAE7C,GAED,IACH,GChCY,MAAM6G,GACnB,WAAAjwC,CAAYmoC,GACV7qC,KAAK6qC,SAAWA,EAChB7qC,KAAK4yC,UAAY,IAAIC,GAAsB7yC,MAC3CA,KAAKqrC,cAAgB,EACtB,CAED,MAAAC,CAAOwH,EAAa1H,GAClB,MACM15B,EAA4B,iBADlBohC,IACuC,CAAC3H,QADxC2H,GAEVhH,EAAe,IAAI+F,GAAa7xC,KAAK6qC,SAAUn5B,EAAQ05B,GAC7D,OAAOprC,KAAKwO,IAAIs9B,EACjB,CAID,GAAAt9B,CAAIs9B,GAKF,OAJA9rC,KAAKqrC,cAAc93B,KAAKu4B,GACxB9rC,KAAK6qC,SAASkI,yBACd/yC,KAAKwxC,OAAO1F,EAAc,eAC1B9rC,KAAK0yC,UAAU5G,GACRA,CACR,CAED,MAAArtB,CAAOqtB,GAKL,OAJA9rC,KAAKsyC,OAAOxG,GACP9rC,KAAKgzC,QAAQlH,EAAaja,YAAY9pB,QACzC/H,KAAKizC,YAAYnH,EAAc,eAE1BA,CACR,CAED,MAAA9rB,CAAO6R,GACL,OAAO7xB,KAAKgzC,QAAQnhB,GAAY7pB,KAAK8jC,IACnC9rC,KAAKsyC,OAAOxG,GACZ9rC,KAAKwxC,OAAO1F,EAAc,YACnBA,IAEV,CAED,MAAAwG,CAAOxG,GAGL,OAFA9rC,KAAK4yC,UAAUN,OAAOxG,GACtB9rC,KAAKqrC,cAAiBrrC,KAAKqrC,cAAcvd,QAAQtD,GAAMA,IAAMshB,IACtDA,CACR,CAED,OAAAkH,CAAQnhB,GACN,OAAO7xB,KAAKqrC,cAAcvd,QAAQtD,GAAMA,EAAEqH,aAAeA,GAC1D,CAED,MAAA3uB,GACE,OAAOlD,KAAKqrC,cAAcrjC,KAAK8jC,GAC7B9rC,KAAK0yC,UAAU5G,IAClB,CAED,SAAA4F,CAAUwB,KAAiBlP,GACzB,OAAOhkC,KAAKqrC,cAAcrjC,KAAK8jC,GAC7B9rC,KAAKwxC,OAAO1F,EAAcoH,KAAiBlP,IAC9C,CAED,MAAAwN,CAAO1F,EAAcoH,KAAiBlP,GACpC,IAAIqH,EAOJ,OALEA,EAD0B,iBAAjBS,EACO9rC,KAAKgzC,QAAQlH,GAEb,CAACA,GAGZT,EAAcrjC,KAAK8jC,GACe,mBAA/BA,EAAaoH,GAA+BpH,EAAaoH,MAAiBlP,QAAQ3hC,GAC7F,CAED,SAAAqwC,CAAU5G,GACJ9rC,KAAKizC,YAAYnH,EAAc,cACjC9rC,KAAK4yC,UAAUR,UAAUtG,EAE5B,CAED,mBAAAyF,CAAoB1f,GAClBmb,GAAOC,IAAI,0BAA0Bpb,KACrC7xB,KAAKgzC,QAAQnhB,GAAY7pB,KAAK8jC,GAC5B9rC,KAAK4yC,UAAUN,OAAOxG,IACzB,CAED,WAAAmH,CAAYnH,EAAcmG,GACxB,MAAMpgB,WAACA,GAAcia,EACrB,OAAO9rC,KAAK6qC,SAASqF,KAAK,CAAC+B,UAASpgB,cACrC,ECxEY,MAAMshB,GACnB,WAAAzwC,CAAYmJ,GACV7L,KAAKozC,KAAOvnC,EACZ7L,KAAKqrC,cAAgB,IAAIsH,GAAc3yC,MACvCA,KAAKstC,WAAa,IAAIyC,GAAW/vC,MACjCA,KAAKwwC,aAAe,EACrB,CAED,OAAI3kC,GACF,OAAOwnC,GAAmBrzC,KAAKozC,KAChC,CAED,IAAAlD,CAAKhS,GACH,OAAOl+B,KAAKstC,WAAW4C,KAAKhS,EAC7B,CAED,OAAAn7B,GACE,OAAO/C,KAAKstC,WAAW0C,MACxB,CAED,UAAA/sC,GACE,OAAOjD,KAAKstC,WAAWnF,MAAM,CAACwI,gBAAgB,GAC/C,CAED,sBAAAoC,GACE,IAAK/yC,KAAKstC,WAAW9oC,WACnB,OAAOxE,KAAKstC,WAAW0C,MAE1B,CAED,cAAAsD,CAAeC,GACbvzC,KAAKwwC,aAAe,IAAIxwC,KAAKwwC,aAAc+C,EAC5C,EAGI,SAASF,GAAmBxnC,GAKjC,GAJmB,mBAARA,IACTA,EAAMA,KAGJA,IAAQ,UAAUpK,KAAKoK,GAAM,CAC/B,MAAM2nC,EAAItzC,SAASC,cAAc,KAKjC,OAJAqzC,EAAExnC,KAAOH,EAET2nC,EAAExnC,KAAOwnC,EAAExnC,KACXwnC,EAAErZ,SAAWqZ,EAAErZ,SAASxyB,QAAQ,OAAQ,MACjC6rC,EAAExnC,IACb,CACI,OAAOH,CAEX,CCpDO,SAAS4nC,GAAUl0C,GACxB,MAAMsB,EAAUX,SAASmiB,KAAKvY,cAAc,2BAA2BvK,OACvE,GAAIsB,EACF,OAAOA,EAAQ4C,aAAa,UAEhC,sNATO,SAAwBoI,EAAM4nC,GAAU,QAAUxE,GAASW,oBAChE,OAAO,IAAIuD,GAAStnC,EACtB"}