/* AI Query Interface Styles */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Smooth transitions for AI query interface */
.ai-query-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Gradient background for AI branding */
.ai-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Pulse animation for loading states */
@keyframes ai-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.ai-pulse {
  animation: ai-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Custom scrollbar for suggestions dropdown */
.ai-suggestions::-webkit-scrollbar {
  width: 4px;
}

.ai-suggestions::-webkit-scrollbar-track {
  background: transparent;
}

.ai-suggestions::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 2px;
}

.ai-suggestions::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* Enhanced focus states */
.ai-input:focus {
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  border-color: rgb(99, 102, 241);
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .ai-suggestions::-webkit-scrollbar-thumb {
    background: rgba(75, 85, 99, 0.5);
  }
  
  .ai-suggestions::-webkit-scrollbar-thumb:hover {
    background: rgba(75, 85, 99, 0.7);
  }
}

/* Results animation */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.ai-results-enter {
  animation: slideInUp 0.4s ease-out;
}

/* Toast animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.toast-enter {
  animation: slideInRight 0.3s ease-out;
}

.toast-exit {
  animation: slideOutRight 0.3s ease-in;
}

/* Card hover effects */
.ai-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Loading skeleton */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@media (prefers-color-scheme: dark) {
  .skeleton {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200% 100%;
  }
}

/* Interactive elements */
.ai-interactive:hover {
  transform: scale(1.02);
}

.ai-interactive:active {
  transform: scale(0.98);
}

/* Responsive improvements */
@media (max-width: 640px) {
  .ai-query-input {
    font-size: 16px; /* Prevent zoom on iOS */
  }
}
