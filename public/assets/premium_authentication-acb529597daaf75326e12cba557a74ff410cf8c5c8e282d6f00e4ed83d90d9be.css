/* Premium Authentication Styles with Glassmorphism */

/* Glassmorphic containers */
.auth-glassmorphic {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.auth-glassmorphic-dark {
  background: rgba(17, 24, 39, 0.85);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

/* Animated gradient backgrounds */
@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.auth-gradient-animated {
  background: linear-gradient(-45deg, #6366F1, #8B5CF6, #EC4899, #10B981);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

/* Floating elements animation */
@keyframes float {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(10deg); }
}

@keyframes floatReverse {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(-10deg); }
}

.auth-float {
  animation: float 6s ease-in-out infinite;
}

.auth-float-reverse {
  animation: floatReverse 8s ease-in-out infinite;
}

/* Pulse glow effect */
@keyframes pulseGlow {
  0%, 100% { 
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.5),
                0 0 40px rgba(99, 102, 241, 0.3),
                0 0 60px rgba(99, 102, 241, 0.1);
  }
  50% {
    box-shadow: 0 0 30px rgba(99, 102, 241, 0.8),
                0 0 60px rgba(99, 102, 241, 0.6),
                0 0 90px rgba(99, 102, 241, 0.4);
  }
}

.auth-glow-pulse {
  animation: pulseGlow 3s ease-in-out infinite;
}

/* Form input styles */
.auth-input {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(209, 213, 219, 0.5);
  transition: all 0.3s ease;
  font-size: 1rem;
  padding: 0.875rem 1rem;
}

.auth-input:focus {
  background: rgba(255, 255, 255, 1);
  border-color: rgba(99, 102, 241, 0.5);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1),
              0 4px 12px rgba(99, 102, 241, 0.1);
  transform: translateY(-1px);
}

@media (min-width: 1024px) {
  .auth-input {
    font-size: 1.125rem;
    padding: 1rem 1.25rem;
  }
}

/* Premium button styles */
.auth-button-premium {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%);
  transition: all 0.3s ease;
  transform-style: preserve-3d;
}

.auth-button-premium::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.auth-button-premium:hover::before {
  left: 100%;
}

.auth-button-premium:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(99, 102, 241, 0.3),
              0 5px 15px rgba(139, 92, 246, 0.3);
}

.auth-button-premium:active {
  transform: translateY(0);
}

/* Decorative orbs */
.auth-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  opacity: 0.7;
  animation: float 20s ease-in-out infinite;
}

.auth-orb-1 {
  width: 300px;
  height: 300px;
  background: linear-gradient(135deg, #6366F1, #8B5CF6);
  top: -150px;
  right: -150px;
  animation-delay: 0s;
}

.auth-orb-2 {
  width: 250px;
  height: 250px;
  background: linear-gradient(135deg, #EC4899, #F59E0B);
  bottom: -125px;
  left: -125px;
  animation-delay: 5s;
}

.auth-orb-3 {
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg, #10B981, #3B82F6);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: 10s;
}

/* Loading states */
.auth-loading {
  position: relative;
  color: transparent;
}

.auth-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Success animation */
@keyframes successPulse {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.auth-success-icon {
  animation: successPulse 0.6s ease-out;
}

/* Error shake animation */
@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
  20%, 40%, 60%, 80% { transform: translateX(10px); }
}

.auth-error-shake {
  animation: errorShake 0.6s ease-out;
}

/* Particle effects */
.auth-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.auth-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(99, 102, 241, 0.5);
  border-radius: 50%;
  animation: particleFloat 10s linear infinite;
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) rotate(720deg);
    opacity: 0;
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .auth-orb {
    filter: blur(60px);
  }
  
  .auth-orb-1 {
    width: 200px;
    height: 200px;
    top: -100px;
    right: -100px;
  }
  
  .auth-orb-2 {
    width: 150px;
    height: 150px;
    bottom: -75px;
    left: -75px;
  }
}

/* Link hover effects */
.auth-link-premium {
  position: relative;
  transition: color 0.3s ease;
}

.auth-link-premium::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #6366F1, #8B5CF6);
  transition: width 0.3s ease;
}

.auth-link-premium:hover::after {
  width: 100%;
}

/* Checkbox custom styling */
.auth-checkbox {
  appearance: none;
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(209, 213, 219, 0.5);
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
}

.auth-checkbox:checked {
  background: linear-gradient(135deg, #6366F1, #8B5CF6);
  border-color: transparent;
}

.auth-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.auth-checkbox:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}
