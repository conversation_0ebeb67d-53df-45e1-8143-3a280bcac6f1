{"version": 3, "file": "stimulus.min.js", "sources": ["../src/core/event_listener.ts", "../src/core/dispatcher.ts", "../src/core/action_descriptor.ts", "../src/core/string_helpers.ts", "../src/core/utils.ts", "../src/core/action.ts", "../src/core/binding.ts", "../src/mutation-observers/element_observer.ts", "../src/mutation-observers/attribute_observer.ts", "../src/multimap/set_operations.ts", "../src/multimap/multimap.ts", "../src/multimap/indexed_multimap.ts", "../src/mutation-observers/selector_observer.ts", "../src/mutation-observers/string_map_observer.ts", "../src/mutation-observers/token_list_observer.ts", "../src/mutation-observers/value_list_observer.ts", "../src/core/binding_observer.ts", "../src/core/value_observer.ts", "../src/core/target_observer.ts", "../src/core/inheritable_statics.ts", "../src/core/outlet_observer.ts", "../src/core/context.ts", "../src/core/blessing.ts", "../src/core/module.ts", "../src/core/definition.ts", "../src/core/class_map.ts", "../src/core/data_map.ts", "../src/core/guide.ts", "../src/core/selectors.ts", "../src/core/target_set.ts", "../src/core/outlet_set.ts", "../src/core/scope.ts", "../src/core/scope_observer.ts", "../src/core/router.ts", "../src/core/schema.ts", "../src/core/application.ts", "../src/core/outlet_properties.ts", "../src/core/value_properties.ts", "../src/core/controller.ts", "../src/core/class_properties.ts", "../src/core/target_properties.ts"], "sourcesContent": ["import { Binding } from \"./binding\"\n\nexport class <PERSON><PERSON><PERSON><PERSON> implements EventListenerObject {\n  readonly eventTarget: EventTarget\n  readonly eventName: string\n  readonly eventOptions: AddEventListenerOptions\n  private unorderedBindings: Set<Binding>\n\n  constructor(eventTarget: EventTarget, eventName: string, eventOptions: AddEventListenerOptions) {\n    this.eventTarget = eventTarget\n    this.eventName = eventName\n    this.eventOptions = eventOptions\n    this.unorderedBindings = new Set()\n  }\n\n  connect() {\n    this.eventTarget.addEventListener(this.eventName, this, this.eventOptions)\n  }\n\n  disconnect() {\n    this.eventTarget.removeEventListener(this.eventName, this, this.eventOptions)\n  }\n\n  // Binding observer delegate\n\n  bindingConnected(binding: Binding) {\n    this.unorderedBindings.add(binding)\n  }\n\n  bindingDisconnected(binding: Binding) {\n    this.unorderedBindings.delete(binding)\n  }\n\n  handleEvent(event: Event) {\n    // FIXME: Determine why TS won't recognize that the extended event has immediatePropagationStopped\n    const extendedEvent = extendEvent(event) as any\n    for (const binding of this.bindings) {\n      if (extendedEvent.immediatePropagationStopped) {\n        break\n      } else {\n        binding.handleEvent(extendedEvent)\n      }\n    }\n  }\n\n  hasBindings() {\n    return this.unorderedBindings.size > 0\n  }\n\n  get bindings(): Binding[] {\n    return Array.from(this.unorderedBindings).sort((left, right) => {\n      const leftIndex = left.index,\n        rightIndex = right.index\n      return leftIndex < rightIndex ? -1 : leftIndex > rightIndex ? 1 : 0\n    })\n  }\n}\n\nfunction extendEvent(event: Event) {\n  if (\"immediatePropagationStopped\" in event) {\n    return event\n  } else {\n    const { stopImmediatePropagation } = event\n    return Object.assign(event, {\n      immediatePropagationStopped: false,\n      stopImmediatePropagation() {\n        this.immediatePropagationStopped = true\n        stopImmediatePropagation.call(this)\n      },\n    })\n  }\n}\n", "import { Application } from \"./application\"\nimport { Binding } from \"./binding\"\nimport { BindingObserverDelegate } from \"./binding_observer\"\nimport { EventListener } from \"./event_listener\"\n\nexport class Dispatcher implements BindingObserverDelegate {\n  readonly application: Application\n  private eventListenerMaps: Map<EventTarget, Map<string, EventListener>>\n  private started: boolean\n\n  constructor(application: Application) {\n    this.application = application\n    this.eventListenerMaps = new Map()\n    this.started = false\n  }\n\n  start() {\n    if (!this.started) {\n      this.started = true\n      this.eventListeners.forEach((eventListener) => eventListener.connect())\n    }\n  }\n\n  stop() {\n    if (this.started) {\n      this.started = false\n      this.eventListeners.forEach((eventListener) => eventListener.disconnect())\n    }\n  }\n\n  get eventListeners(): EventListener[] {\n    return Array.from(this.eventListenerMaps.values()).reduce(\n      (listeners, map) => listeners.concat(Array.from(map.values())),\n      [] as EventListener[]\n    )\n  }\n\n  // Binding observer delegate\n\n  bindingConnected(binding: Binding) {\n    this.fetchEventListenerForBinding(binding).bindingConnected(binding)\n  }\n\n  bindingDisconnected(binding: Binding, clearEventListeners = false) {\n    this.fetchEventListenerForBinding(binding).bindingDisconnected(binding)\n    if (clearEventListeners) this.clearEventListenersForBinding(binding)\n  }\n\n  // Error handling\n\n  handleError(error: Error, message: string, detail: object = {}) {\n    this.application.handleError(error, `Error ${message}`, detail)\n  }\n\n  private clearEventListenersForBinding(binding: Binding) {\n    const eventListener = this.fetchEventListenerForBinding(binding)\n    if (!eventListener.hasBindings()) {\n      eventListener.disconnect()\n      this.removeMappedEventListenerFor(binding)\n    }\n  }\n\n  private removeMappedEventListenerFor(binding: Binding) {\n    const { eventTarget, eventName, eventOptions } = binding\n    const eventListenerMap = this.fetchEventListenerMapForEventTarget(eventTarget)\n    const cacheKey = this.cacheKey(eventName, eventOptions)\n\n    eventListenerMap.delete(cacheKey)\n    if (eventListenerMap.size == 0) this.eventListenerMaps.delete(eventTarget)\n  }\n\n  private fetchEventListenerForBinding(binding: Binding): EventListener {\n    const { eventTarget, eventName, eventOptions } = binding\n    return this.fetchEventListener(eventTarget, eventName, eventOptions)\n  }\n\n  private fetchEventListener(\n    eventTarget: EventTarget,\n    eventName: string,\n    eventOptions: AddEventListenerOptions\n  ): EventListener {\n    const eventListenerMap = this.fetchEventListenerMapForEventTarget(eventTarget)\n    const cacheKey = this.cacheKey(eventName, eventOptions)\n    let eventListener = eventListenerMap.get(cacheKey)\n    if (!eventListener) {\n      eventListener = this.createEventListener(eventTarget, eventName, eventOptions)\n      eventListenerMap.set(cacheKey, eventListener)\n    }\n    return eventListener\n  }\n\n  private createEventListener(\n    eventTarget: EventTarget,\n    eventName: string,\n    eventOptions: AddEventListenerOptions\n  ): EventListener {\n    const eventListener = new EventListener(eventTarget, eventName, eventOptions)\n    if (this.started) {\n      eventListener.connect()\n    }\n    return eventListener\n  }\n\n  private fetchEventListenerMapForEventTarget(eventTarget: EventTarget): Map<string, EventListener> {\n    let eventListenerMap = this.eventListenerMaps.get(eventTarget)\n    if (!eventListenerMap) {\n      eventListenerMap = new Map()\n      this.eventListenerMaps.set(eventTarget, eventListenerMap)\n    }\n    return eventListenerMap\n  }\n\n  private cacheKey(eventName: string, eventOptions: any): string {\n    const parts = [eventName]\n    Object.keys(eventOptions)\n      .sort()\n      .forEach((key) => {\n        parts.push(`${eventOptions[key] ? \"\" : \"!\"}${key}`)\n      })\n    return parts.join(\":\")\n  }\n}\n", "import type { Controller } from \"./controller\"\n\nexport type ActionDescriptorFilters = Record<string, ActionDescriptorFilter>\nexport type ActionDescriptorFilter = (options: ActionDescriptorFilterOptions) => boolean\ntype ActionDescriptorFilterOptions = {\n  name: string\n  value: boolean\n  event: Event\n  element: Element\n  controller: Controller<Element>\n}\n\nexport const defaultActionDescriptorFilters: ActionDescriptorFilters = {\n  stop({ event, value }) {\n    if (value) event.stopPropagation()\n\n    return true\n  },\n\n  prevent({ event, value }) {\n    if (value) event.preventDefault()\n\n    return true\n  },\n\n  self({ event, value, element }) {\n    if (value) {\n      return element === event.target\n    } else {\n      return true\n    }\n  },\n}\n\nexport interface ActionDescriptor {\n  eventTarget: EventTarget\n  eventOptions: AddEventListenerOptions\n  eventName: string\n  identifier: string\n  methodName: string\n  keyFilter: string\n}\n\n// capture nos.:                  1      1    2   2     3   3      4               4      5   5    6      6     7  7\nconst descriptorPattern = /^(?:(?:([^.]+?)\\+)?(.+?)(?:\\.(.+?))?(?:@(window|document))?->)?(.+?)(?:#([^:]+?))(?::(.+))?$/\n\nexport function parseActionDescriptorString(descriptorString: string): Partial<ActionDescriptor> {\n  const source = descriptorString.trim()\n  const matches = source.match(descriptorPattern) || []\n  let eventName = matches[2]\n  let keyFilter = matches[3]\n\n  if (keyFilter && ![\"keydown\", \"keyup\", \"keypress\"].includes(eventName)) {\n    eventName += `.${keyFilter}`\n    keyFilter = \"\"\n  }\n\n  return {\n    eventTarget: parseEventTarget(matches[4]),\n    eventName,\n    eventOptions: matches[7] ? parseEventOptions(matches[7]) : {},\n    identifier: matches[5],\n    methodName: matches[6],\n    keyFilter: matches[1] || keyFilter,\n  }\n}\n\nfunction parseEventTarget(eventTargetName: string): EventTarget | undefined {\n  if (eventTargetName == \"window\") {\n    return window\n  } else if (eventTargetName == \"document\") {\n    return document\n  }\n}\n\nfunction parseEventOptions(eventOptions: string): AddEventListenerOptions {\n  return eventOptions\n    .split(\":\")\n    .reduce((options, token) => Object.assign(options, { [token.replace(/^!/, \"\")]: !/^!/.test(token) }), {})\n}\n\nexport function stringifyEventTarget(eventTarget: EventTarget) {\n  if (eventTarget == window) {\n    return \"window\"\n  } else if (eventTarget == document) {\n    return \"document\"\n  }\n}\n", "export function camelize(value: string) {\n  return value.replace(/(?:[_-])([a-z0-9])/g, (_, char) => char.toUpperCase())\n}\n\nexport function namespaceCamelize(value: string) {\n  return camelize(value.replace(/--/g, \"-\").replace(/__/g, \"_\"))\n}\n\nexport function capitalize(value: string) {\n  return value.charAt(0).toUpperCase() + value.slice(1)\n}\n\nexport function dasherize(value: string) {\n  return value.replace(/([A-Z])/g, (_, char) => `-${char.toLowerCase()}`)\n}\n\nexport function tokenize(value: string) {\n  return value.match(/[^\\s]+/g) || []\n}\n", "export function isSomething(object: any): boolean {\n  return object !== null && object !== undefined\n}\n\nexport function hasProperty(object: any, property: string): boolean {\n  return Object.prototype.hasOwnProperty.call(object, property)\n}\n", "import { ActionDescriptor, parseActionDescriptorString, stringifyEventTarget } from \"./action_descriptor\"\nimport { Token } from \"../mutation-observers\"\nimport { Schema } from \"./schema\"\nimport { camelize } from \"./string_helpers\"\nimport { hasProperty } from \"./utils\"\n\nconst allModifiers = [\"meta\", \"ctrl\", \"alt\", \"shift\"]\n\nexport class Action {\n  readonly element: Element\n  readonly index: number\n  readonly eventTarget: EventTarget\n  readonly eventName: string\n  readonly eventOptions: AddEventListenerOptions\n  readonly identifier: string\n  readonly methodName: string\n  readonly keyFilter: string\n  readonly schema: Schema\n\n  static forToken(token: Token, schema: Schema) {\n    return new this(token.element, token.index, parseActionDescriptorString(token.content), schema)\n  }\n\n  constructor(element: Element, index: number, descriptor: Partial<ActionDescriptor>, schema: Schema) {\n    this.element = element\n    this.index = index\n    this.eventTarget = descriptor.eventTarget || element\n    this.eventName = descriptor.eventName || getDefaultEventNameForElement(element) || error(\"missing event name\")\n    this.eventOptions = descriptor.eventOptions || {}\n    this.identifier = descriptor.identifier || error(\"missing identifier\")\n    this.methodName = descriptor.methodName || error(\"missing method name\")\n    this.keyFilter = descriptor.keyFilter || \"\"\n    this.schema = schema\n  }\n\n  toString() {\n    const eventFilter = this.keyFilter ? `.${this.keyFilter}` : \"\"\n    const eventTarget = this.eventTargetName ? `@${this.eventTargetName}` : \"\"\n    return `${this.eventName}${eventFilter}${eventTarget}->${this.identifier}#${this.methodName}`\n  }\n\n  shouldIgnoreKeyboardEvent(event: KeyboardEvent): boolean {\n    if (!this.keyFilter) {\n      return false\n    }\n\n    const filters = this.keyFilter.split(\"+\")\n    if (this.keyFilterDissatisfied(event, filters)) {\n      return true\n    }\n\n    const standardFilter = filters.filter((key) => !allModifiers.includes(key))[0]\n    if (!standardFilter) {\n      // missing non modifier key\n      return false\n    }\n\n    if (!hasProperty(this.keyMappings, standardFilter)) {\n      error(`contains unknown key filter: ${this.keyFilter}`)\n    }\n\n    return this.keyMappings[standardFilter].toLowerCase() !== event.key.toLowerCase()\n  }\n\n  shouldIgnoreMouseEvent(event: MouseEvent): boolean {\n    if (!this.keyFilter) {\n      return false\n    }\n\n    const filters = [this.keyFilter]\n    if (this.keyFilterDissatisfied(event, filters)) {\n      return true\n    }\n\n    return false\n  }\n\n  get params() {\n    const params: { [key: string]: any } = {}\n    const pattern = new RegExp(`^data-${this.identifier}-(.+)-param$`, \"i\")\n\n    for (const { name, value } of Array.from(this.element.attributes)) {\n      const match = name.match(pattern)\n      const key = match && match[1]\n      if (key) {\n        params[camelize(key)] = typecast(value)\n      }\n    }\n    return params\n  }\n\n  private get eventTargetName() {\n    return stringifyEventTarget(this.eventTarget)\n  }\n\n  private get keyMappings() {\n    return this.schema.keyMappings\n  }\n\n  private keyFilterDissatisfied(event: KeyboardEvent | MouseEvent, filters: Array<string>): boolean {\n    const [meta, ctrl, alt, shift] = allModifiers.map((modifier) => filters.includes(modifier))\n\n    return event.metaKey !== meta || event.ctrlKey !== ctrl || event.altKey !== alt || event.shiftKey !== shift\n  }\n}\n\nconst defaultEventNames: { [tagName: string]: (element: Element) => string } = {\n  a: () => \"click\",\n  button: () => \"click\",\n  form: () => \"submit\",\n  details: () => \"toggle\",\n  input: (e) => (e.getAttribute(\"type\") == \"submit\" ? \"click\" : \"input\"),\n  select: () => \"change\",\n  textarea: () => \"input\",\n}\n\nexport function getDefaultEventNameForElement(element: Element): string | undefined {\n  const tagName = element.tagName.toLowerCase()\n  if (tagName in defaultEventNames) {\n    return defaultEventNames[tagName](element)\n  }\n}\n\nfunction error(message: string): never {\n  throw new Error(message)\n}\n\nfunction typecast(value: any): any {\n  try {\n    return JSON.parse(value)\n  } catch (o_O) {\n    return value\n  }\n}\n", "import { Action } from \"./action\"\nimport { ActionEvent } from \"./action_event\"\nimport { Context } from \"./context\"\nimport { Controller } from \"./controller\"\nimport { Scope } from \"./scope\"\nexport class Binding {\n  readonly context: Context\n  readonly action: Action\n\n  constructor(context: Context, action: Action) {\n    this.context = context\n    this.action = action\n  }\n\n  get index(): number {\n    return this.action.index\n  }\n\n  get eventTarget(): EventTarget {\n    return this.action.eventTarget\n  }\n\n  get eventOptions(): AddEventListenerOptions {\n    return this.action.eventOptions\n  }\n\n  get identifier(): string {\n    return this.context.identifier\n  }\n\n  handleEvent(event: Event) {\n    const actionEvent = this.prepareActionEvent(event)\n    if (this.willBeInvokedByEvent(event) && this.applyEventModifiers(actionEvent)) {\n      this.invokeWithEvent(actionEvent)\n    }\n  }\n\n  get eventName(): string {\n    return this.action.eventName\n  }\n\n  get method(): Function {\n    const method = (this.controller as any)[this.methodName]\n    if (typeof method == \"function\") {\n      return method\n    }\n    throw new Error(`Action \"${this.action}\" references undefined method \"${this.methodName}\"`)\n  }\n\n  private applyEventModifiers(event: Event): boolean {\n    const { element } = this.action\n    const { actionDescriptorFilters } = this.context.application\n    const { controller } = this.context\n\n    let passes = true\n\n    for (const [name, value] of Object.entries(this.eventOptions)) {\n      if (name in actionDescriptorFilters) {\n        const filter = actionDescriptorFilters[name]\n\n        passes = passes && filter({ name, value, event, element, controller })\n      } else {\n        continue\n      }\n    }\n\n    return passes\n  }\n\n  private prepareActionEvent(event: Event): ActionEvent {\n    return Object.assign(event, { params: this.action.params })\n  }\n\n  private invokeWithEvent(event: ActionEvent) {\n    const { target, currentTarget } = event\n    try {\n      this.method.call(this.controller, event)\n      this.context.logDebugActivity(this.methodName, { event, target, currentTarget, action: this.methodName })\n    } catch (error: any) {\n      const { identifier, controller, element, index } = this\n      const detail = { identifier, controller, element, index, event }\n      this.context.handleError(error, `invoking action \"${this.action}\"`, detail)\n    }\n  }\n\n  private willBeInvokedByEvent(event: Event): boolean {\n    const eventTarget = event.target\n\n    if (event instanceof KeyboardEvent && this.action.shouldIgnoreKeyboardEvent(event)) {\n      return false\n    }\n\n    if (event instanceof MouseEvent && this.action.shouldIgnoreMouseEvent(event)) {\n      return false\n    }\n\n    if (this.element === eventTarget) {\n      return true\n    } else if (eventTarget instanceof Element && this.element.contains(eventTarget)) {\n      return this.scope.containsElement(eventTarget)\n    } else {\n      return this.scope.containsElement(this.action.element)\n    }\n  }\n\n  private get controller(): Controller {\n    return this.context.controller\n  }\n\n  private get methodName(): string {\n    return this.action.methodName\n  }\n\n  private get element(): Element {\n    return this.scope.element\n  }\n\n  private get scope(): Scope {\n    return this.context.scope\n  }\n}\n", "export interface ElementObserverDelegate {\n  matchElement(element: Element): boolean\n  matchElementsInTree(tree: Element): Element[]\n\n  elementMatched?(element: Element): void\n  elementUnmatched?(element: Element): void\n  elementAttributeChanged?(element: Element, attributeName: string): void\n}\n\nexport class ElementObserver {\n  element: Element\n  started: boolean\n  private delegate: ElementObserverDelegate\n\n  private elements: Set<Element>\n  private mutationObserver: MutationObserver\n  private mutationObserverInit: MutationObserverInit = { attributes: true, childList: true, subtree: true }\n\n  constructor(element: Element, delegate: ElementObserverDelegate) {\n    this.element = element\n    this.started = false\n    this.delegate = delegate\n\n    this.elements = new Set()\n    this.mutationObserver = new MutationObserver((mutations) => this.processMutations(mutations))\n  }\n\n  start() {\n    if (!this.started) {\n      this.started = true\n      this.mutationObserver.observe(this.element, this.mutationObserverInit)\n      this.refresh()\n    }\n  }\n\n  pause(callback: () => void) {\n    if (this.started) {\n      this.mutationObserver.disconnect()\n      this.started = false\n    }\n\n    callback()\n\n    if (!this.started) {\n      this.mutationObserver.observe(this.element, this.mutationObserverInit)\n      this.started = true\n    }\n  }\n\n  stop() {\n    if (this.started) {\n      this.mutationObserver.takeRecords()\n      this.mutationObserver.disconnect()\n      this.started = false\n    }\n  }\n\n  refresh() {\n    if (this.started) {\n      const matches = new Set(this.matchElementsInTree())\n\n      for (const element of Array.from(this.elements)) {\n        if (!matches.has(element)) {\n          this.removeElement(element)\n        }\n      }\n\n      for (const element of Array.from(matches)) {\n        this.addElement(element)\n      }\n    }\n  }\n\n  // Mutation record processing\n\n  private processMutations(mutations: MutationRecord[]) {\n    if (this.started) {\n      for (const mutation of mutations) {\n        this.processMutation(mutation)\n      }\n    }\n  }\n\n  private processMutation(mutation: MutationRecord) {\n    if (mutation.type == \"attributes\") {\n      this.processAttributeChange(mutation.target as Element, mutation.attributeName!)\n    } else if (mutation.type == \"childList\") {\n      this.processRemovedNodes(mutation.removedNodes)\n      this.processAddedNodes(mutation.addedNodes)\n    }\n  }\n\n  private processAttributeChange(element: Element, attributeName: string) {\n    if (this.elements.has(element)) {\n      if (this.delegate.elementAttributeChanged && this.matchElement(element)) {\n        this.delegate.elementAttributeChanged(element, attributeName)\n      } else {\n        this.removeElement(element)\n      }\n    } else if (this.matchElement(element)) {\n      this.addElement(element)\n    }\n  }\n\n  private processRemovedNodes(nodes: NodeList) {\n    for (const node of Array.from(nodes)) {\n      const element = this.elementFromNode(node)\n      if (element) {\n        this.processTree(element, this.removeElement)\n      }\n    }\n  }\n\n  private processAddedNodes(nodes: NodeList) {\n    for (const node of Array.from(nodes)) {\n      const element = this.elementFromNode(node)\n      if (element && this.elementIsActive(element)) {\n        this.processTree(element, this.addElement)\n      }\n    }\n  }\n\n  // Element matching\n\n  private matchElement(element: Element): boolean {\n    return this.delegate.matchElement(element)\n  }\n\n  private matchElementsInTree(tree: Element = this.element): Element[] {\n    return this.delegate.matchElementsInTree(tree)\n  }\n\n  private processTree(tree: Element, processor: (element: Element) => void) {\n    for (const element of this.matchElementsInTree(tree)) {\n      processor.call(this, element)\n    }\n  }\n\n  private elementFromNode(node: Node): Element | undefined {\n    if (node.nodeType == Node.ELEMENT_NODE) {\n      return node as Element\n    }\n  }\n\n  private elementIsActive(element: Element): boolean {\n    if (element.isConnected != this.element.isConnected) {\n      return false\n    } else {\n      return this.element.contains(element)\n    }\n  }\n\n  // Element tracking\n\n  private addElement(element: Element) {\n    if (!this.elements.has(element)) {\n      if (this.elementIsActive(element)) {\n        this.elements.add(element)\n        if (this.delegate.elementMatched) {\n          this.delegate.elementMatched(element)\n        }\n      }\n    }\n  }\n\n  private removeElement(element: Element) {\n    if (this.elements.has(element)) {\n      this.elements.delete(element)\n      if (this.delegate.elementUnmatched) {\n        this.delegate.elementUnmatched(element)\n      }\n    }\n  }\n}\n", "import { ElementObserver, ElementObserverDelegate } from \"./element_observer\"\n\nexport interface AttributeObserverDelegate {\n  elementMatchedAttribute?(element: Element, attributeName: string): void\n  elementAttributeValueChanged?(element: Element, attributeName: string): void\n  elementUnmatchedAttribute?(element: Element, attributeName: string): void\n}\n\nexport class AttributeObserver implements ElementObserverDelegate {\n  attributeName: string\n  private delegate: AttributeObserverDelegate\n\n  private elementObserver: ElementObserver\n\n  constructor(element: Element, attributeName: string, delegate: AttributeObserverDelegate) {\n    this.attributeName = attributeName\n    this.delegate = delegate\n\n    this.elementObserver = new ElementObserver(element, this)\n  }\n\n  get element(): Element {\n    return this.elementObserver.element\n  }\n\n  get selector(): string {\n    return `[${this.attributeName}]`\n  }\n\n  start() {\n    this.elementObserver.start()\n  }\n\n  pause(callback: () => void) {\n    this.elementObserver.pause(callback)\n  }\n\n  stop() {\n    this.elementObserver.stop()\n  }\n\n  refresh() {\n    this.elementObserver.refresh()\n  }\n\n  get started(): boolean {\n    return this.elementObserver.started\n  }\n\n  // Element observer delegate\n\n  matchElement(element: Element): boolean {\n    return element.hasAttribute(this.attributeName)\n  }\n\n  matchElementsInTree(tree: Element): Element[] {\n    const match = this.matchElement(tree) ? [tree] : []\n    const matches = Array.from(tree.querySelectorAll(this.selector))\n    return match.concat(matches)\n  }\n\n  elementMatched(element: Element) {\n    if (this.delegate.elementMatchedAttribute) {\n      this.delegate.elementMatchedAttribute(element, this.attributeName)\n    }\n  }\n\n  elementUnmatched(element: Element) {\n    if (this.delegate.elementUnmatchedAttribute) {\n      this.delegate.elementUnmatchedAttribute(element, this.attributeName)\n    }\n  }\n\n  elementAttributeChanged(element: Element, attributeName: string) {\n    if (this.delegate.elementAttributeValueChanged && this.attributeName == attributeName) {\n      this.delegate.elementAttributeValueChanged(element, attributeName)\n    }\n  }\n}\n", "export function add<K, V>(map: Map<K, Set<V>>, key: K, value: V) {\n  fetch(map, key).add(value)\n}\n\nexport function del<K, V>(map: Map<K, Set<V>>, key: K, value: V) {\n  fetch(map, key).delete(value)\n  prune(map, key)\n}\n\nexport function fetch<K, V>(map: Map<K, Set<V>>, key: K): Set<V> {\n  let values = map.get(key)\n  if (!values) {\n    values = new Set()\n    map.set(key, values)\n  }\n  return values\n}\n\nexport function prune<K, V>(map: Map<K, Set<V>>, key: K) {\n  const values = map.get(key)\n  if (values != null && values.size == 0) {\n    map.delete(key)\n  }\n}\n", "import { add, del } from \"./set_operations\"\n\nexport class Multimap<K, V> {\n  private valuesByKey: Map<K, Set<V>>\n\n  constructor() {\n    this.valuesByKey = new Map<K, Set<V>>()\n  }\n\n  get keys() {\n    return Array.from(this.valuesByKey.keys())\n  }\n\n  get values(): V[] {\n    const sets = Array.from(this.valuesByKey.values())\n    return sets.reduce((values, set) => values.concat(Array.from(set)), <V[]>[])\n  }\n\n  get size(): number {\n    const sets = Array.from(this.valuesByKey.values())\n    return sets.reduce((size, set) => size + set.size, 0)\n  }\n\n  add(key: K, value: V) {\n    add(this.valuesByKey, key, value)\n  }\n\n  delete(key: K, value: V) {\n    del(this.valuesByKey, key, value)\n  }\n\n  has(key: K, value: V): boolean {\n    const values = this.valuesByKey.get(key)\n    return values != null && values.has(value)\n  }\n\n  hasKey(key: K): boolean {\n    return this.valuesByKey.has(key)\n  }\n\n  hasValue(value: V): boolean {\n    const sets = Array.from(this.valuesByKey.values())\n    return sets.some((set) => set.has(value))\n  }\n\n  getValuesForKey(key: K): V[] {\n    const values = this.valuesByKey.get(key)\n    return values ? Array.from(values) : []\n  }\n\n  getKeysForValue(value: V): K[] {\n    return Array.from(this.valuesByKey)\n      .filter(([_key, values]) => values.has(value))\n      .map(([key, _values]) => key)\n  }\n}\n", "import { Multimap } from \"./multimap\"\nimport { add, del } from \"./set_operations\"\n\nexport class IndexedMultimap<K, V> extends Multimap<K, V> {\n  private keysByValue: Map<V, Set<K>>\n\n  constructor() {\n    super()\n    this.keysByValue = new Map()\n  }\n\n  get values(): V[] {\n    return Array.from(this.keysByValue.keys())\n  }\n\n  add(key: K, value: V) {\n    super.add(key, value)\n    add(this.keysByValue, value, key)\n  }\n\n  delete(key: K, value: V) {\n    super.delete(key, value)\n    del(this.keysByValue, value, key)\n  }\n\n  hasValue(value: V): boolean {\n    return this.keysByValue.has(value)\n  }\n\n  getKeysForValue(value: V): K[] {\n    const set = this.keysByValue.get(value)\n    return set ? Array.from(set) : []\n  }\n}\n", "import { ElementObserver, ElementObserverDelegate } from \"./element_observer\"\nimport { Multimap } from \"../multimap\"\n\nexport interface SelectorObserverDelegate {\n  selectorMatched(element: Element, selector: string, details: object): void\n  selectorUnmatched(element: Element, selector: string, details: object): void\n  selectorMatchElement?(element: Element, details: object): boolean\n}\n\nexport class SelectorObserver implements ElementObserverDelegate {\n  private readonly elementObserver: ElementObserver\n  private readonly delegate: SelectorObserverDelegate\n  private readonly matchesByElement: Multimap<string, Element>\n  private readonly details: object\n  _selector: string | null\n\n  constructor(element: Element, selector: string, delegate: SelectorObserverDelegate, details: object) {\n    this._selector = selector\n    this.details = details\n    this.elementObserver = new ElementObserver(element, this)\n    this.delegate = delegate\n    this.matchesByElement = new Multimap()\n  }\n\n  get started(): boolean {\n    return this.elementObserver.started\n  }\n\n  get selector() {\n    return this._selector\n  }\n\n  set selector(selector: string | null) {\n    this._selector = selector\n    this.refresh()\n  }\n\n  start() {\n    this.elementObserver.start()\n  }\n\n  pause(callback: () => void) {\n    this.elementObserver.pause(callback)\n  }\n\n  stop() {\n    this.elementObserver.stop()\n  }\n\n  refresh() {\n    this.elementObserver.refresh()\n  }\n\n  get element(): Element {\n    return this.elementObserver.element\n  }\n\n  // Element observer delegate\n\n  matchElement(element: Element): boolean {\n    const { selector } = this\n\n    if (selector) {\n      const matches = element.matches(selector)\n\n      if (this.delegate.selectorMatchElement) {\n        return matches && this.delegate.selectorMatchElement(element, this.details)\n      }\n\n      return matches\n    } else {\n      return false\n    }\n  }\n\n  matchElementsInTree(tree: Element): Element[] {\n    const { selector } = this\n\n    if (selector) {\n      const match = this.matchElement(tree) ? [tree] : []\n      const matches = Array.from(tree.querySelectorAll(selector)).filter((match) => this.matchElement(match))\n      return match.concat(matches)\n    } else {\n      return []\n    }\n  }\n\n  elementMatched(element: Element) {\n    const { selector } = this\n\n    if (selector) {\n      this.selectorMatched(element, selector)\n    }\n  }\n\n  elementUnmatched(element: Element) {\n    const selectors = this.matchesByElement.getKeysForValue(element)\n\n    for (const selector of selectors) {\n      this.selectorUnmatched(element, selector)\n    }\n  }\n\n  elementAttributeChanged(element: Element, _attributeName: string) {\n    const { selector } = this\n\n    if (selector) {\n      const matches = this.matchElement(element)\n      const matchedBefore = this.matchesByElement.has(selector, element)\n\n      if (matches && !matchedBefore) {\n        this.selectorMatched(element, selector)\n      } else if (!matches && matchedBefore) {\n        this.selectorUnmatched(element, selector)\n      }\n    }\n  }\n\n  // Selector management\n\n  private selectorMatched(element: Element, selector: string) {\n    this.delegate.selectorMatched(element, selector, this.details)\n    this.matchesByElement.add(selector, element)\n  }\n\n  private selectorUnmatched(element: Element, selector: string) {\n    this.delegate.selectorUnmatched(element, selector, this.details)\n    this.matchesByElement.delete(selector, element)\n  }\n}\n", "export interface StringMapObserverDelegate {\n  getStringMapKeyForAttribute(attributeName: string): string | undefined\n  stringMapKeyAdded?(key: string, attributeName: string): void\n  stringMapValueChanged?(value: string | null, key: string, oldValue: string | null): void\n  stringMapKeyRemoved?(key: string, attributeName: string, oldValue: string | null): void\n}\n\nexport class StringMapObserver {\n  readonly element: Element\n  readonly delegate: StringMapObserverDelegate\n  private started: boolean\n  private stringMap: Map<string, string>\n  private mutationObserver: MutationObserver\n\n  constructor(element: Element, delegate: StringMapObserverDelegate) {\n    this.element = element\n    this.delegate = delegate\n    this.started = false\n    this.stringMap = new Map()\n    this.mutationObserver = new MutationObserver((mutations) => this.processMutations(mutations))\n  }\n\n  start() {\n    if (!this.started) {\n      this.started = true\n      this.mutationObserver.observe(this.element, { attributes: true, attributeOldValue: true })\n      this.refresh()\n    }\n  }\n\n  stop() {\n    if (this.started) {\n      this.mutationObserver.takeRecords()\n      this.mutationObserver.disconnect()\n      this.started = false\n    }\n  }\n\n  refresh() {\n    if (this.started) {\n      for (const attributeName of this.knownAttributeNames) {\n        this.refreshAttribute(attributeName, null)\n      }\n    }\n  }\n\n  // Mutation record processing\n\n  private processMutations(mutations: MutationRecord[]) {\n    if (this.started) {\n      for (const mutation of mutations) {\n        this.processMutation(mutation)\n      }\n    }\n  }\n\n  private processMutation(mutation: MutationRecord) {\n    const attributeName = mutation.attributeName\n    if (attributeName) {\n      this.refreshAttribute(attributeName, mutation.oldValue)\n    }\n  }\n\n  // State tracking\n\n  private refreshAttribute(attributeName: string, oldValue: string | null) {\n    const key = this.delegate.getStringMapKeyForAttribute(attributeName)\n    if (key != null) {\n      if (!this.stringMap.has(attributeName)) {\n        this.stringMapKeyAdded(key, attributeName)\n      }\n\n      const value = this.element.getAttribute(attributeName)\n      if (this.stringMap.get(attributeName) != value) {\n        this.stringMapValueChanged(value, key, oldValue)\n      }\n\n      if (value == null) {\n        const oldValue = this.stringMap.get(attributeName)\n        this.stringMap.delete(attributeName)\n        if (oldValue) this.stringMapKeyRemoved(key, attributeName, oldValue)\n      } else {\n        this.stringMap.set(attributeName, value)\n      }\n    }\n  }\n\n  private stringMapKeyAdded(key: string, attributeName: string) {\n    if (this.delegate.stringMapKeyAdded) {\n      this.delegate.stringMapKeyAdded(key, attributeName)\n    }\n  }\n\n  private stringMapValueChanged(value: string | null, key: string, oldValue: string | null) {\n    if (this.delegate.stringMapValueChanged) {\n      this.delegate.stringMapValueChanged(value, key, oldValue)\n    }\n  }\n\n  private stringMapKeyRemoved(key: string, attributeName: string, oldValue: string | null) {\n    if (this.delegate.stringMapKeyRemoved) {\n      this.delegate.stringMapKeyRemoved(key, attributeName, oldValue)\n    }\n  }\n\n  private get knownAttributeNames() {\n    return Array.from(new Set(this.currentAttributeNames.concat(this.recordedAttributeNames)))\n  }\n\n  private get currentAttributeNames() {\n    return Array.from(this.element.attributes).map((attribute) => attribute.name)\n  }\n\n  private get recordedAttributeNames() {\n    return Array.from(this.stringMap.keys())\n  }\n}\n", "import { AttributeObserver, AttributeObserverDelegate } from \"./attribute_observer\"\nimport { Multimap } from \"../multimap\"\n\nexport interface Token {\n  element: Element\n  attributeName: string\n  index: number\n  content: string\n}\n\nexport interface TokenListObserverDelegate {\n  tokenMatched(token: Token): void\n  tokenUnmatched(token: Token): void\n}\n\nexport class TokenListObserver implements AttributeObserverDelegate {\n  private attributeObserver: AttributeObserver\n  private delegate: TokenListObserverDelegate\n  private tokensByElement: Multimap<Element, Token>\n\n  constructor(element: Element, attributeName: string, delegate: TokenListObserverDelegate) {\n    this.attributeObserver = new AttributeObserver(element, attributeName, this)\n    this.delegate = delegate\n    this.tokensByElement = new Multimap()\n  }\n\n  get started(): boolean {\n    return this.attributeObserver.started\n  }\n\n  start() {\n    this.attributeObserver.start()\n  }\n\n  pause(callback: () => void) {\n    this.attributeObserver.pause(callback)\n  }\n\n  stop() {\n    this.attributeObserver.stop()\n  }\n\n  refresh() {\n    this.attributeObserver.refresh()\n  }\n\n  get element(): Element {\n    return this.attributeObserver.element\n  }\n\n  get attributeName(): string {\n    return this.attributeObserver.attributeName\n  }\n\n  // Attribute observer delegate\n\n  elementMatchedAttribute(element: Element) {\n    this.tokensMatched(this.readTokensForElement(element))\n  }\n\n  elementAttributeValueChanged(element: Element) {\n    const [unmatchedTokens, matchedTokens] = this.refreshTokensForElement(element)\n    this.tokensUnmatched(unmatchedTokens)\n    this.tokensMatched(matchedTokens)\n  }\n\n  elementUnmatchedAttribute(element: Element) {\n    this.tokensUnmatched(this.tokensByElement.getValuesForKey(element))\n  }\n\n  private tokensMatched(tokens: Token[]) {\n    tokens.forEach((token) => this.tokenMatched(token))\n  }\n\n  private tokensUnmatched(tokens: Token[]) {\n    tokens.forEach((token) => this.tokenUnmatched(token))\n  }\n\n  private tokenMatched(token: Token) {\n    this.delegate.tokenMatched(token)\n    this.tokensByElement.add(token.element, token)\n  }\n\n  private tokenUnmatched(token: Token) {\n    this.delegate.tokenUnmatched(token)\n    this.tokensByElement.delete(token.element, token)\n  }\n\n  private refreshTokensForElement(element: Element): [Token[], Token[]] {\n    const previousTokens = this.tokensByElement.getValuesForKey(element)\n    const currentTokens = this.readTokensForElement(element)\n    const firstDifferingIndex = zip(previousTokens, currentTokens).findIndex(\n      ([previousToken, currentToken]) => !tokensAreEqual(previousToken, currentToken)\n    )\n\n    if (firstDifferingIndex == -1) {\n      return [[], []]\n    } else {\n      return [previousTokens.slice(firstDifferingIndex), currentTokens.slice(firstDifferingIndex)]\n    }\n  }\n\n  private readTokensForElement(element: Element): Token[] {\n    const attributeName = this.attributeName\n    const tokenString = element.getAttribute(attributeName) || \"\"\n    return parseTokenString(tokenString, element, attributeName)\n  }\n}\n\nfunction parseTokenString(tokenString: string, element: Element, attributeName: string): Token[] {\n  return tokenString\n    .trim()\n    .split(/\\s+/)\n    .filter((content) => content.length)\n    .map((content, index) => ({ element, attributeName, content, index }))\n}\n\nfunction zip<L, R>(left: L[], right: R[]): [L | undefined, R | undefined][] {\n  const length = Math.max(left.length, right.length)\n  return Array.from({ length }, (_, index) => [left[index], right[index]] as [L, R])\n}\n\nfunction tokensAreEqual(left?: Token, right?: Token) {\n  return left && right && left.index == right.index && left.content == right.content\n}\n", "import { Token, TokenListObserver, TokenListObserverDelegate } from \"./token_list_observer\"\n\nexport interface ValueListObserverDelegate<T> {\n  parseValueForToken(token: Token): T | undefined\n  elementMatchedValue(element: Element, value: T): void\n  elementUnmatchedValue(element: Element, value: T): void\n}\n\ninterface ParseResult<T> {\n  value?: T\n  error?: Error\n}\n\nexport class ValueListObserver<T> implements TokenListObserverDelegate {\n  private tokenListObserver: TokenListObserver\n  private delegate: ValueListObserverDelegate<T>\n  private parseResultsByToken: WeakMap<Token, ParseResult<T>>\n  private valuesByTokenByElement: WeakMap<Element, Map<Token, T>>\n\n  constructor(element: Element, attributeName: string, delegate: ValueListObserverDelegate<T>) {\n    this.tokenListObserver = new TokenListObserver(element, attributeName, this)\n    this.delegate = delegate\n    this.parseResultsByToken = new WeakMap()\n    this.valuesByTokenByElement = new WeakMap()\n  }\n\n  get started(): boolean {\n    return this.tokenListObserver.started\n  }\n\n  start() {\n    this.tokenListObserver.start()\n  }\n\n  stop() {\n    this.tokenListObserver.stop()\n  }\n\n  refresh() {\n    this.tokenListObserver.refresh()\n  }\n\n  get element(): Element {\n    return this.tokenListObserver.element\n  }\n\n  get attributeName(): string {\n    return this.tokenListObserver.attributeName\n  }\n\n  tokenMatched(token: Token) {\n    const { element } = token\n    const { value } = this.fetchParseResultForToken(token)\n    if (value) {\n      this.fetchValuesByTokenForElement(element).set(token, value)\n      this.delegate.elementMatchedValue(element, value)\n    }\n  }\n\n  tokenUnmatched(token: Token) {\n    const { element } = token\n    const { value } = this.fetchParseResultForToken(token)\n    if (value) {\n      this.fetchValuesByTokenForElement(element).delete(token)\n      this.delegate.elementUnmatchedValue(element, value)\n    }\n  }\n\n  private fetchParseResultForToken(token: Token) {\n    let parseResult = this.parseResultsByToken.get(token)\n    if (!parseResult) {\n      parseResult = this.parseToken(token)\n      this.parseResultsByToken.set(token, parseResult)\n    }\n    return parseResult\n  }\n\n  private fetchValuesByTokenForElement(element: Element) {\n    let valuesByToken = this.valuesByTokenByElement.get(element)\n    if (!valuesByToken) {\n      valuesByToken = new Map()\n      this.valuesByTokenByElement.set(element, valuesByToken)\n    }\n    return valuesByToken\n  }\n\n  private parseToken(token: Token): ParseResult<T> {\n    try {\n      const value = this.delegate.parseValueForToken(token)\n      return { value }\n    } catch (error: any) {\n      return { error }\n    }\n  }\n}\n", "import { Action } from \"./action\"\nimport { Binding } from \"./binding\"\nimport { Context } from \"./context\"\nimport { <PERSON><PERSON>r<PERSON><PERSON><PERSON> } from \"./error_handler\"\nimport { Schema } from \"./schema\"\nimport { Token, ValueListObserver, ValueListObserverDelegate } from \"../mutation-observers\"\n\nexport interface BindingObserverDelegate extends <PERSON>rrorHandler {\n  bindingConnected(binding: Binding): void\n  bindingDisconnected(binding: Binding, clearEventListeners?: boolean): void\n}\n\nexport class BindingObserver implements ValueListObserverDelegate<Action> {\n  readonly context: Context\n  private delegate: BindingObserverDelegate\n  private valueListObserver?: ValueListObserver<Action>\n  private bindingsByAction: Map<Action, Binding>\n\n  constructor(context: Context, delegate: BindingObserverDelegate) {\n    this.context = context\n    this.delegate = delegate\n    this.bindingsByAction = new Map()\n  }\n\n  start() {\n    if (!this.valueListObserver) {\n      this.valueListObserver = new ValueListObserver(this.element, this.actionAttribute, this)\n      this.valueListObserver.start()\n    }\n  }\n\n  stop() {\n    if (this.valueListObserver) {\n      this.valueListObserver.stop()\n      delete this.valueListObserver\n      this.disconnectAllActions()\n    }\n  }\n\n  get element() {\n    return this.context.element\n  }\n\n  get identifier() {\n    return this.context.identifier\n  }\n\n  get actionAttribute() {\n    return this.schema.actionAttribute\n  }\n\n  get schema(): Schema {\n    return this.context.schema\n  }\n\n  get bindings(): Binding[] {\n    return Array.from(this.bindingsByAction.values())\n  }\n\n  private connectAction(action: Action) {\n    const binding = new Binding(this.context, action)\n    this.bindingsByAction.set(action, binding)\n    this.delegate.bindingConnected(binding)\n  }\n\n  private disconnectAction(action: Action) {\n    const binding = this.bindingsByAction.get(action)\n    if (binding) {\n      this.bindingsByAction.delete(action)\n      this.delegate.bindingDisconnected(binding)\n    }\n  }\n\n  private disconnectAllActions() {\n    this.bindings.forEach((binding) => this.delegate.bindingDisconnected(binding, true))\n    this.bindingsByAction.clear()\n  }\n\n  // Value observer delegate\n\n  parseValueForToken(token: Token): Action | undefined {\n    const action = Action.forToken(token, this.schema)\n    if (action.identifier == this.identifier) {\n      return action\n    }\n  }\n\n  elementMatchedValue(element: Element, action: Action) {\n    this.connectAction(action)\n  }\n\n  elementUnmatchedValue(element: Element, action: Action) {\n    this.disconnectAction(action)\n  }\n}\n", "import { Context } from \"./context\"\nimport { StringMapObserver, StringMapObserverDelegate } from \"../mutation-observers\"\nimport { ValueDescriptor } from \"./value_properties\"\nimport { capitalize } from \"./string_helpers\"\n\nexport class ValueObserver implements StringMapObserverDelegate {\n  readonly context: Context\n  readonly receiver: any\n  private stringMapObserver: StringMapObserver\n  private valueDescriptorMap: { [attributeName: string]: ValueDescriptor }\n\n  constructor(context: Context, receiver: any) {\n    this.context = context\n    this.receiver = receiver\n    this.stringMapObserver = new StringMapObserver(this.element, this)\n    this.valueDescriptorMap = (this.controller as any).valueDescriptorMap\n  }\n\n  start() {\n    this.stringMapObserver.start()\n    this.invokeChangedCallbacksForDefaultValues()\n  }\n\n  stop() {\n    this.stringMapObserver.stop()\n  }\n\n  get element() {\n    return this.context.element\n  }\n\n  get controller() {\n    return this.context.controller\n  }\n\n  // String map observer delegate\n\n  getStringMapKeyForAttribute(attributeName: string) {\n    if (attributeName in this.valueDescriptorMap) {\n      return this.valueDescriptorMap[attributeName].name\n    }\n  }\n\n  stringMapKeyAdded(key: string, attributeName: string) {\n    const descriptor = this.valueDescriptorMap[attributeName]\n\n    if (!this.hasValue(key)) {\n      this.invokeChangedCallback(key, descriptor.writer(this.receiver[key]), descriptor.writer(descriptor.defaultValue))\n    }\n  }\n\n  stringMapValueChanged(value: string, name: string, oldValue: string) {\n    const descriptor = this.valueDescriptorNameMap[name]\n\n    if (value === null) return\n\n    if (oldValue === null) {\n      oldValue = descriptor.writer(descriptor.defaultValue)\n    }\n\n    this.invokeChangedCallback(name, value, oldValue)\n  }\n\n  stringMapKeyRemoved(key: string, attributeName: string, oldValue: string) {\n    const descriptor = this.valueDescriptorNameMap[key]\n\n    if (this.hasValue(key)) {\n      this.invokeChangedCallback(key, descriptor.writer(this.receiver[key]), oldValue)\n    } else {\n      this.invokeChangedCallback(key, descriptor.writer(descriptor.defaultValue), oldValue)\n    }\n  }\n\n  private invokeChangedCallbacksForDefaultValues() {\n    for (const { key, name, defaultValue, writer } of this.valueDescriptors) {\n      if (defaultValue != undefined && !this.controller.data.has(key)) {\n        this.invokeChangedCallback(name, writer(defaultValue), undefined)\n      }\n    }\n  }\n\n  private invokeChangedCallback(name: string, rawValue: string, rawOldValue: string | undefined) {\n    const changedMethodName = `${name}Changed`\n    const changedMethod = this.receiver[changedMethodName]\n\n    if (typeof changedMethod == \"function\") {\n      const descriptor = this.valueDescriptorNameMap[name]\n\n      try {\n        const value = descriptor.reader(rawValue)\n        let oldValue = rawOldValue\n\n        if (rawOldValue) {\n          oldValue = descriptor.reader(rawOldValue)\n        }\n\n        changedMethod.call(this.receiver, value, oldValue)\n      } catch (error) {\n        if (error instanceof TypeError) {\n          error.message = `Stimulus Value \"${this.context.identifier}.${descriptor.name}\" - ${error.message}`\n        }\n\n        throw error\n      }\n    }\n  }\n\n  private get valueDescriptors() {\n    const { valueDescriptorMap } = this\n    return Object.keys(valueDescriptorMap).map((key) => valueDescriptorMap[key])\n  }\n\n  private get valueDescriptorNameMap() {\n    const descriptors: { [type: string]: ValueDescriptor } = {}\n\n    Object.keys(this.valueDescriptorMap).forEach((key) => {\n      const descriptor = this.valueDescriptorMap[key]\n      descriptors[descriptor.name] = descriptor\n    })\n\n    return descriptors\n  }\n\n  private hasValue(attributeName: string) {\n    const descriptor = this.valueDescriptorNameMap[attributeName]\n    const hasMethodName = `has${capitalize(descriptor.name)}`\n\n    return this.receiver[hasMethodName]\n  }\n}\n", "import { Multimap } from \"../multimap\"\nimport { Token, TokenListObserver, TokenListObserverDelegate } from \"../mutation-observers\"\nimport { Context } from \"./context\"\n\nexport interface TargetObserverDelegate {\n  targetConnected(element: Element, name: string): void\n  targetDisconnected(element: Element, name: string): void\n}\n\nexport class TargetObserver implements TokenListObserverDelegate {\n  readonly context: Context\n  readonly delegate: TargetObserverDelegate\n  readonly targetsByName: Multimap<string, Element>\n  private tokenListObserver?: TokenListObserver\n\n  constructor(context: Context, delegate: TargetObserverDelegate) {\n    this.context = context\n    this.delegate = delegate\n    this.targetsByName = new Multimap()\n  }\n\n  start() {\n    if (!this.tokenListObserver) {\n      this.tokenListObserver = new TokenListObserver(this.element, this.attributeName, this)\n      this.tokenListObserver.start()\n    }\n  }\n\n  stop() {\n    if (this.tokenListObserver) {\n      this.disconnectAllTargets()\n      this.tokenListObserver.stop()\n      delete this.tokenListObserver\n    }\n  }\n\n  // Token list observer delegate\n\n  tokenMatched({ element, content: name }: Token) {\n    if (this.scope.containsElement(element)) {\n      this.connectTarget(element, name)\n    }\n  }\n\n  tokenUnmatched({ element, content: name }: Token) {\n    this.disconnectTarget(element, name)\n  }\n\n  // Target management\n\n  connectTarget(element: Element, name: string) {\n    if (!this.targetsByName.has(name, element)) {\n      this.targetsByName.add(name, element)\n      this.tokenListObserver?.pause(() => this.delegate.targetConnected(element, name))\n    }\n  }\n\n  disconnectTarget(element: Element, name: string) {\n    if (this.targetsByName.has(name, element)) {\n      this.targetsByName.delete(name, element)\n      this.tokenListObserver?.pause(() => this.delegate.targetDisconnected(element, name))\n    }\n  }\n\n  disconnectAllTargets() {\n    for (const name of this.targetsByName.keys) {\n      for (const element of this.targetsByName.getValuesForKey(name)) {\n        this.disconnectTarget(element, name)\n      }\n    }\n  }\n\n  // Private\n\n  private get attributeName() {\n    return `data-${this.context.identifier}-target`\n  }\n\n  private get element() {\n    return this.context.element\n  }\n\n  private get scope() {\n    return this.context.scope\n  }\n}\n", "import { Constructor } from \"./constructor\"\n\nexport function readInheritableStaticArrayValues<T, U = string>(constructor: Constructor<T>, propertyName: string) {\n  const ancestors = getAncestorsForConstructor(constructor)\n  return Array.from(\n    ancestors.reduce((values, constructor) => {\n      getOwnStaticArrayValues(constructor, propertyName).forEach((name) => values.add(name))\n      return values\n    }, new Set() as Set<U>)\n  )\n}\n\nexport function readInheritableStaticObjectPairs<T, U>(constructor: Constructor<T>, propertyName: string) {\n  const ancestors = getAncestorsForConstructor(constructor)\n  return ancestors.reduce((pairs, constructor) => {\n    pairs.push(...(getOwnStaticObjectPairs(constructor, propertyName) as any))\n    return pairs\n  }, [] as [string, U][])\n}\n\nfunction getAncestorsForConstructor<T>(constructor: Constructor<T>) {\n  const ancestors: Constructor<any>[] = []\n  while (constructor) {\n    ancestors.push(constructor)\n    constructor = Object.getPrototypeOf(constructor)\n  }\n  return ancestors.reverse()\n}\n\nfunction getOwnStaticArrayValues<T>(constructor: Constructor<T>, propertyName: string) {\n  const definition = (constructor as any)[propertyName]\n  return Array.isArray(definition) ? definition : []\n}\n\nfunction getOwnStaticObjectPairs<T, U>(constructor: Constructor<T>, propertyName: string) {\n  const definition = (constructor as any)[propertyName]\n  return definition ? Object.keys(definition).map((key) => [key, definition[key]] as [string, U]) : []\n}\n", "import { Multimap } from \"../multimap\"\nimport { AttributeObserver, AttributeObserverDelegate } from \"../mutation-observers\"\nimport { SelectorObserver, SelectorObserverDelegate } from \"../mutation-observers\"\nimport { Context } from \"./context\"\nimport { Controller } from \"./controller\"\n\nimport { readInheritableStaticArrayValues } from \"./inheritable_statics\"\n\ntype OutletObserverDetails = { outletName: string }\n\nexport interface OutletObserverDelegate {\n  outletConnected(outlet: Controller, element: Element, outletName: string): void\n  outletDisconnected(outlet: Controller, element: Element, outletName: string): void\n}\n\nexport class OutletObserver implements AttributeObserverDelegate, SelectorObserverDelegate {\n  started: boolean\n  readonly context: Context\n  readonly delegate: OutletObserverDelegate\n  readonly outletsByName: Multimap<string, Controller>\n  readonly outletElementsByName: Multimap<string, Element>\n  private selectorObserverMap: Map<string, SelectorObserver>\n  private attributeObserverMap: Map<string, AttributeObserver>\n\n  constructor(context: Context, delegate: OutletObserverDelegate) {\n    this.started = false\n    this.context = context\n    this.delegate = delegate\n    this.outletsByName = new Multimap()\n    this.outletElementsByName = new Multimap()\n    this.selectorObserverMap = new Map()\n    this.attributeObserverMap = new Map()\n  }\n\n  start() {\n    if (!this.started) {\n      this.outletDefinitions.forEach((outletName) => {\n        this.setupSelectorObserverForOutlet(outletName)\n        this.setupAttributeObserverForOutlet(outletName)\n      })\n      this.started = true\n      this.dependentContexts.forEach((context) => context.refresh())\n    }\n  }\n\n  refresh() {\n    this.selectorObserverMap.forEach((observer) => observer.refresh())\n    this.attributeObserverMap.forEach((observer) => observer.refresh())\n  }\n\n  stop() {\n    if (this.started) {\n      this.started = false\n      this.disconnectAllOutlets()\n      this.stopSelectorObservers()\n      this.stopAttributeObservers()\n    }\n  }\n\n  stopSelectorObservers() {\n    if (this.selectorObserverMap.size > 0) {\n      this.selectorObserverMap.forEach((observer) => observer.stop())\n      this.selectorObserverMap.clear()\n    }\n  }\n\n  stopAttributeObservers() {\n    if (this.attributeObserverMap.size > 0) {\n      this.attributeObserverMap.forEach((observer) => observer.stop())\n      this.attributeObserverMap.clear()\n    }\n  }\n\n  // Selector observer delegate\n\n  selectorMatched(element: Element, _selector: string, { outletName }: OutletObserverDetails) {\n    const outlet = this.getOutlet(element, outletName)\n\n    if (outlet) {\n      this.connectOutlet(outlet, element, outletName)\n    }\n  }\n\n  selectorUnmatched(element: Element, _selector: string, { outletName }: OutletObserverDetails) {\n    const outlet = this.getOutletFromMap(element, outletName)\n\n    if (outlet) {\n      this.disconnectOutlet(outlet, element, outletName)\n    }\n  }\n\n  selectorMatchElement(element: Element, { outletName }: OutletObserverDetails) {\n    const selector = this.selector(outletName)\n    const hasOutlet = this.hasOutlet(element, outletName)\n    const hasOutletController = element.matches(`[${this.schema.controllerAttribute}~=${outletName}]`)\n\n    if (selector) {\n      return hasOutlet && hasOutletController && element.matches(selector)\n    } else {\n      return false\n    }\n  }\n\n  // Attribute observer delegate\n\n  elementMatchedAttribute(_element: Element, attributeName: string) {\n    const outletName = this.getOutletNameFromOutletAttributeName(attributeName)\n\n    if (outletName) {\n      this.updateSelectorObserverForOutlet(outletName)\n    }\n  }\n\n  elementAttributeValueChanged(_element: Element, attributeName: string) {\n    const outletName = this.getOutletNameFromOutletAttributeName(attributeName)\n\n    if (outletName) {\n      this.updateSelectorObserverForOutlet(outletName)\n    }\n  }\n\n  elementUnmatchedAttribute(_element: Element, attributeName: string) {\n    const outletName = this.getOutletNameFromOutletAttributeName(attributeName)\n\n    if (outletName) {\n      this.updateSelectorObserverForOutlet(outletName)\n    }\n  }\n\n  // Outlet management\n\n  connectOutlet(outlet: Controller, element: Element, outletName: string) {\n    if (!this.outletElementsByName.has(outletName, element)) {\n      this.outletsByName.add(outletName, outlet)\n      this.outletElementsByName.add(outletName, element)\n      this.selectorObserverMap.get(outletName)?.pause(() => this.delegate.outletConnected(outlet, element, outletName))\n    }\n  }\n\n  disconnectOutlet(outlet: Controller, element: Element, outletName: string) {\n    if (this.outletElementsByName.has(outletName, element)) {\n      this.outletsByName.delete(outletName, outlet)\n      this.outletElementsByName.delete(outletName, element)\n      this.selectorObserverMap\n        .get(outletName)\n        ?.pause(() => this.delegate.outletDisconnected(outlet, element, outletName))\n    }\n  }\n\n  disconnectAllOutlets() {\n    for (const outletName of this.outletElementsByName.keys) {\n      for (const element of this.outletElementsByName.getValuesForKey(outletName)) {\n        for (const outlet of this.outletsByName.getValuesForKey(outletName)) {\n          this.disconnectOutlet(outlet, element, outletName)\n        }\n      }\n    }\n  }\n\n  // Observer management\n\n  private updateSelectorObserverForOutlet(outletName: string) {\n    const observer = this.selectorObserverMap.get(outletName)\n\n    if (observer) {\n      observer.selector = this.selector(outletName)\n    }\n  }\n\n  private setupSelectorObserverForOutlet(outletName: string) {\n    const selector = this.selector(outletName)\n    const selectorObserver = new SelectorObserver(document.body, selector!, this, { outletName })\n\n    this.selectorObserverMap.set(outletName, selectorObserver)\n\n    selectorObserver.start()\n  }\n\n  private setupAttributeObserverForOutlet(outletName: string) {\n    const attributeName = this.attributeNameForOutletName(outletName)\n    const attributeObserver = new AttributeObserver(this.scope.element, attributeName, this)\n\n    this.attributeObserverMap.set(outletName, attributeObserver)\n\n    attributeObserver.start()\n  }\n\n  // Private\n\n  private selector(outletName: string) {\n    return this.scope.outlets.getSelectorForOutletName(outletName)\n  }\n\n  private attributeNameForOutletName(outletName: string) {\n    return this.scope.schema.outletAttributeForScope(this.identifier, outletName)\n  }\n\n  private getOutletNameFromOutletAttributeName(attributeName: string) {\n    return this.outletDefinitions.find((outletName) => this.attributeNameForOutletName(outletName) === attributeName)\n  }\n\n  private get outletDependencies() {\n    const dependencies = new Multimap<string, string>()\n\n    this.router.modules.forEach((module) => {\n      const constructor = module.definition.controllerConstructor\n      const outlets = readInheritableStaticArrayValues(constructor, \"outlets\")\n\n      outlets.forEach((outlet) => dependencies.add(outlet, module.identifier))\n    })\n\n    return dependencies\n  }\n\n  private get outletDefinitions() {\n    return this.outletDependencies.getKeysForValue(this.identifier)\n  }\n\n  private get dependentControllerIdentifiers() {\n    return this.outletDependencies.getValuesForKey(this.identifier)\n  }\n\n  private get dependentContexts() {\n    const identifiers = this.dependentControllerIdentifiers\n    return this.router.contexts.filter((context) => identifiers.includes(context.identifier))\n  }\n\n  private hasOutlet(element: Element, outletName: string) {\n    return !!this.getOutlet(element, outletName) || !!this.getOutletFromMap(element, outletName)\n  }\n\n  private getOutlet(element: Element, outletName: string) {\n    return this.application.getControllerForElementAndIdentifier(element, outletName)\n  }\n\n  private getOutletFromMap(element: Element, outletName: string) {\n    return this.outletsByName.getValuesForKey(outletName).find((outlet) => outlet.element === element)\n  }\n\n  private get scope() {\n    return this.context.scope\n  }\n\n  private get schema() {\n    return this.context.schema\n  }\n\n  private get identifier() {\n    return this.context.identifier\n  }\n\n  private get application() {\n    return this.context.application\n  }\n\n  private get router() {\n    return this.application.router\n  }\n}\n", "import { Application } from \"./application\"\nimport { BindingObserver } from \"./binding_observer\"\nimport { Controller } from \"./controller\"\nimport { Dispatcher } from \"./dispatcher\"\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from \"./error_handler\"\nimport { <PERSON><PERSON><PERSON> } from \"./module\"\nimport { Schema } from \"./schema\"\nimport { Scope } from \"./scope\"\nimport { ValueObserver } from \"./value_observer\"\nimport { TargetObserver, TargetObserverDelegate } from \"./target_observer\"\nimport { OutletObserver, OutletObserverDelegate } from \"./outlet_observer\"\nimport { namespaceCamelize } from \"./string_helpers\"\n\nexport class Context implements <PERSON><PERSON>r<PERSON>and<PERSON>, TargetObserverDelegate, OutletObserverDelegate {\n  readonly module: Module\n  readonly scope: Scope\n  readonly controller: Controller\n  private bindingObserver: BindingObserver\n  private valueObserver: ValueObserver\n  private targetObserver: TargetObserver\n  private outletObserver: OutletObserver\n\n  constructor(module: Module, scope: Scope) {\n    this.module = module\n    this.scope = scope\n    this.controller = new module.controllerConstructor(this)\n    this.bindingObserver = new BindingObserver(this, this.dispatcher)\n    this.valueObserver = new ValueObserver(this, this.controller)\n    this.targetObserver = new TargetObserver(this, this)\n    this.outletObserver = new OutletObserver(this, this)\n\n    try {\n      this.controller.initialize()\n      this.logDebugActivity(\"initialize\")\n    } catch (error: any) {\n      this.handleError(error, \"initializing controller\")\n    }\n  }\n\n  connect() {\n    this.bindingObserver.start()\n    this.valueObserver.start()\n    this.targetObserver.start()\n    this.outletObserver.start()\n\n    try {\n      this.controller.connect()\n      this.logDebugActivity(\"connect\")\n    } catch (error: any) {\n      this.handleError(error, \"connecting controller\")\n    }\n  }\n\n  refresh() {\n    this.outletObserver.refresh()\n  }\n\n  disconnect() {\n    try {\n      this.controller.disconnect()\n      this.logDebugActivity(\"disconnect\")\n    } catch (error: any) {\n      this.handleError(error, \"disconnecting controller\")\n    }\n\n    this.outletObserver.stop()\n    this.targetObserver.stop()\n    this.valueObserver.stop()\n    this.bindingObserver.stop()\n  }\n\n  get application(): Application {\n    return this.module.application\n  }\n\n  get identifier(): string {\n    return this.module.identifier\n  }\n\n  get schema(): Schema {\n    return this.application.schema\n  }\n\n  get dispatcher(): Dispatcher {\n    return this.application.dispatcher\n  }\n\n  get element(): Element {\n    return this.scope.element\n  }\n\n  get parentElement(): Element | null {\n    return this.element.parentElement\n  }\n\n  // Error handling\n\n  handleError(error: Error, message: string, detail: object = {}) {\n    const { identifier, controller, element } = this\n    detail = Object.assign({ identifier, controller, element }, detail)\n    this.application.handleError(error, `Error ${message}`, detail)\n  }\n\n  // Debug logging\n\n  logDebugActivity = (functionName: string, detail: object = {}): void => {\n    const { identifier, controller, element } = this\n    detail = Object.assign({ identifier, controller, element }, detail)\n    this.application.logDebugActivity(this.identifier, functionName, detail)\n  }\n\n  // Target observer delegate\n\n  targetConnected(element: Element, name: string) {\n    this.invokeControllerMethod(`${name}TargetConnected`, element)\n  }\n\n  targetDisconnected(element: Element, name: string) {\n    this.invokeControllerMethod(`${name}TargetDisconnected`, element)\n  }\n\n  // Outlet observer delegate\n\n  outletConnected(outlet: Controller, element: Element, name: string) {\n    this.invokeControllerMethod(`${namespaceCamelize(name)}OutletConnected`, outlet, element)\n  }\n\n  outletDisconnected(outlet: Controller, element: Element, name: string) {\n    this.invokeControllerMethod(`${namespaceCamelize(name)}OutletDisconnected`, outlet, element)\n  }\n\n  // Private\n\n  invokeControllerMethod(methodName: string, ...args: any[]) {\n    const controller: any = this.controller\n    if (typeof controller[methodName] == \"function\") {\n      controller[methodName](...args)\n    }\n  }\n}\n", "import { Constructor } from \"./constructor\"\nimport { readInheritableStaticArrayValues } from \"./inheritable_statics\"\n\nexport type Blessing<T> = (constructor: Constructor<T>) => PropertyDescriptorMap\n\nexport interface Blessable<T> extends Constructor<T> {\n  readonly blessings?: Blessing<T>[]\n}\n\nexport function bless<T>(constructor: Blessable<T>): Constructor<T> {\n  return shadow(constructor, getBlessedProperties(constructor))\n}\n\nfunction shadow<T>(constructor: Constructor<T>, properties: PropertyDescriptorMap) {\n  const shadowConstructor = extend(constructor)\n  const shadowProperties = getShadowProperties(constructor.prototype, properties)\n  Object.defineProperties(shadowConstructor.prototype, shadowProperties)\n  return shadowConstructor\n}\n\nfunction getBlessedProperties<T>(constructor: Constructor<T>) {\n  const blessings = readInheritableStaticArrayValues(constructor, \"blessings\") as Blessing<T>[]\n  return blessings.reduce((blessedProperties, blessing) => {\n    const properties = blessing(constructor)\n    for (const key in properties) {\n      const descriptor = blessedProperties[key] || ({} as PropertyDescriptor)\n      blessedProperties[key] = Object.assign(descriptor, properties[key])\n    }\n    return blessedProperties\n  }, {} as PropertyDescriptorMap)\n}\n\nfunction getShadowProperties(prototype: any, properties: PropertyDescriptorMap) {\n  return getOwnKeys(properties).reduce((shadowProperties, key) => {\n    const descriptor = getShadowedDescriptor(prototype, properties, key)\n    if (descriptor) {\n      Object.assign(shadowProperties, { [key]: descriptor })\n    }\n    return shadowProperties\n  }, {} as PropertyDescriptorMap)\n}\n\nfunction getShadowedDescriptor(prototype: any, properties: PropertyDescriptorMap, key: string | symbol) {\n  const shadowingDescriptor = Object.getOwnPropertyDescriptor(prototype, key)\n  const shadowedByValue = shadowingDescriptor && \"value\" in shadowingDescriptor\n  if (!shadowedByValue) {\n    const descriptor = Object.getOwnPropertyDescriptor(properties, key)!.value\n    if (shadowingDescriptor) {\n      descriptor.get = shadowingDescriptor.get || descriptor.get\n      descriptor.set = shadowingDescriptor.set || descriptor.set\n    }\n    return descriptor\n  }\n}\n\nconst getOwnKeys = (() => {\n  if (typeof Object.getOwnPropertySymbols == \"function\") {\n    return (object: any) => [...Object.getOwnPropertyNames(object), ...Object.getOwnPropertySymbols(object)]\n  } else {\n    return Object.getOwnPropertyNames\n  }\n})()\n\nconst extend = (() => {\n  function extendWithReflect<T extends Constructor<any>>(constructor: T): T {\n    function extended() {\n      return Reflect.construct(constructor, arguments, new.target)\n    }\n\n    extended.prototype = Object.create(constructor.prototype, {\n      constructor: { value: extended },\n    })\n\n    Reflect.setPrototypeOf(extended, constructor)\n    return extended as any\n  }\n\n  function testReflectExtension() {\n    const a = function (this: any) {\n      this.a.call(this)\n    } as any\n    const b = extendWithReflect(a)\n    b.prototype.a = function () {}\n    return new b()\n  }\n\n  try {\n    testReflectExtension()\n    return extendWithReflect\n  } catch (error: any) {\n    return <T extends Constructor<any>>(constructor: T) => class extended extends constructor {}\n  }\n})()\n", "import { Application } from \"./application\"\nimport { Context } from \"./context\"\nimport { ControllerConstructor } from \"./controller\"\nimport { Definition, blessDefinition } from \"./definition\"\nimport { Scope } from \"./scope\"\n\nexport class Module {\n  readonly application: Application\n  readonly definition: Definition\n  private contextsByScope: WeakMap<Scope, Context>\n  private connectedContexts: Set<Context>\n\n  constructor(application: Application, definition: Definition) {\n    this.application = application\n    this.definition = blessDefinition(definition)\n    this.contextsByScope = new WeakMap()\n    this.connectedContexts = new Set()\n  }\n\n  get identifier(): string {\n    return this.definition.identifier\n  }\n\n  get controllerConstructor(): ControllerConstructor {\n    return this.definition.controllerConstructor\n  }\n\n  get contexts(): Context[] {\n    return Array.from(this.connectedContexts)\n  }\n\n  connectContextForScope(scope: Scope) {\n    const context = this.fetchContextForScope(scope)\n    this.connectedContexts.add(context)\n    context.connect()\n  }\n\n  disconnectContextForScope(scope: Scope) {\n    const context = this.contextsByScope.get(scope)\n    if (context) {\n      this.connectedContexts.delete(context)\n      context.disconnect()\n    }\n  }\n\n  private fetchContextForScope(scope: Scope): Context {\n    let context = this.contextsByScope.get(scope)\n    if (!context) {\n      context = new Context(this, scope)\n      this.contextsByScope.set(scope, context)\n    }\n    return context\n  }\n}\n", "import { bless } from \"./blessing\"\nimport { ControllerConstructor } from \"./controller\"\n\nexport interface Definition {\n  identifier: string\n  controllerConstructor: ControllerConstructor\n}\n\nexport function blessDefinition(definition: Definition): Definition {\n  return {\n    identifier: definition.identifier,\n    controllerConstructor: bless(definition.controllerConstructor),\n  }\n}\n", "import { Scope } from \"./scope\"\nimport { tokenize } from \"./string_helpers\"\n\nexport class ClassMap {\n  readonly scope: Scope\n\n  constructor(scope: Scope) {\n    this.scope = scope\n  }\n\n  has(name: string) {\n    return this.data.has(this.getData<PERSON><PERSON>(name))\n  }\n\n  get(name: string): string | undefined {\n    return this.getAll(name)[0]\n  }\n\n  getAll(name: string) {\n    const tokenString = this.data.get(this.getDataKey(name)) || \"\"\n    return tokenize(tokenString)\n  }\n\n  getAttributeName(name: string) {\n    return this.data.getAttributeNameForKey(this.getDataKey(name))\n  }\n\n  getDataKey(name: string) {\n    return `${name}-class`\n  }\n\n  get data() {\n    return this.scope.data\n  }\n}\n", "import { Scope } from \"./scope\"\nimport { dasherize } from \"./string_helpers\"\n\nexport class DataMap {\n  readonly scope: Scope\n\n  constructor(scope: Scope) {\n    this.scope = scope\n  }\n\n  get element(): Element {\n    return this.scope.element\n  }\n\n  get identifier(): string {\n    return this.scope.identifier\n  }\n\n  get(key: string): string | null {\n    const name = this.getAttributeNameFor<PERSON>ey(key)\n    return this.element.getAttribute(name)\n  }\n\n  set(key: string, value: string): string | null {\n    const name = this.getAttributeNameForKey(key)\n    this.element.setAttribute(name, value)\n    return this.get(key)\n  }\n\n  has(key: string): boolean {\n    const name = this.getAttributeNameForKey(key)\n    return this.element.hasAttribute(name)\n  }\n\n  delete(key: string): boolean {\n    if (this.has(key)) {\n      const name = this.getAttributeNameFor<PERSON>ey(key)\n      this.element.removeAttribute(name)\n      return true\n    } else {\n      return false\n    }\n  }\n\n  getAttributeNameFor<PERSON>ey(key: string): string {\n    return `data-${this.identifier}-${dasherize(key)}`\n  }\n}\n", "import { Logger } from \"./logger\"\n\nexport class Guide {\n  readonly logger: Logger\n  readonly warnedKeysByObject: WeakMap<any, Set<string>> = new WeakMap()\n\n  constructor(logger: Logger) {\n    this.logger = logger\n  }\n\n  warn(object: any, key: string, message: string) {\n    let warnedKeys: Set<string> | undefined = this.warnedKeysByObject.get(object)\n\n    if (!warnedKeys) {\n      warnedKeys = new Set()\n      this.warnedKeysByObject.set(object, warnedKeys)\n    }\n\n    if (!warnedKeys.has(key)) {\n      warnedKeys.add(key)\n      this.logger.warn(message, object)\n    }\n  }\n}\n", "export function attributeValueContainsToken(attributeName: string, token: string) {\n  return `[${attributeName}~=\"${token}\"]`\n}\n", "import { Scope } from \"./scope\"\nimport { attributeValueContainsToken } from \"./selectors\"\n\nexport class TargetSet {\n  readonly scope: Scope\n\n  constructor(scope: Scope) {\n    this.scope = scope\n  }\n\n  get element() {\n    return this.scope.element\n  }\n\n  get identifier() {\n    return this.scope.identifier\n  }\n\n  get schema() {\n    return this.scope.schema\n  }\n\n  has(targetName: string) {\n    return this.find(targetName) != null\n  }\n\n  find(...targetNames: string[]) {\n    return targetNames.reduce(\n      (target, targetName) => target || this.findTarget(targetName) || this.findLegacyTarget(targetName),\n      undefined as Element | undefined\n    )\n  }\n\n  findAll(...targetNames: string[]) {\n    return targetNames.reduce(\n      (targets, targetName) => [\n        ...targets,\n        ...this.findAllTargets(targetName),\n        ...this.findAllLegacyTargets(targetName),\n      ],\n      [] as Element[]\n    )\n  }\n\n  private findTarget(targetName: string) {\n    const selector = this.getSelectorForTargetName(targetName)\n    return this.scope.findElement(selector)\n  }\n\n  private findAllTargets(targetName: string) {\n    const selector = this.getSelectorForTargetName(targetName)\n    return this.scope.findAllElements(selector)\n  }\n\n  private getSelectorForTargetName(targetName: string) {\n    const attributeName = this.schema.targetAttributeForScope(this.identifier)\n    return attributeValueContainsToken(attributeName, targetName)\n  }\n\n  private findLegacyTarget(targetName: string) {\n    const selector = this.getLegacySelectorForTargetName(targetName)\n    return this.deprecate(this.scope.findElement(selector), targetName)\n  }\n\n  private findAllLegacyTargets(targetName: string) {\n    const selector = this.getLegacySelectorForTargetName(targetName)\n    return this.scope.findAllElements(selector).map((element) => this.deprecate(element, targetName))\n  }\n\n  private getLegacySelectorForTargetName(targetName: string) {\n    const targetDescriptor = `${this.identifier}.${targetName}`\n    return attributeValueContainsToken(this.schema.targetAttribute, targetDescriptor)\n  }\n\n  private deprecate<T>(element: T, targetName: string) {\n    if (element) {\n      const { identifier } = this\n      const attributeName = this.schema.targetAttribute\n      const revisedAttributeName = this.schema.targetAttributeForScope(identifier)\n      this.guide.warn(\n        element,\n        `target:${targetName}`,\n        `Please replace ${attributeName}=\"${identifier}.${targetName}\" with ${revisedAttributeName}=\"${targetName}\". ` +\n          `The ${attributeName} attribute is deprecated and will be removed in a future version of Stimulus.`\n      )\n    }\n    return element\n  }\n\n  private get guide() {\n    return this.scope.guide\n  }\n}\n", "import { <PERSON>ope } from \"./scope\"\n\nexport class OutletSet {\n  readonly scope: Scope\n  readonly controllerElement: Element\n\n  constructor(scope: Scope, controllerElement: Element) {\n    this.scope = scope\n    this.controllerElement = controllerElement\n  }\n\n  get element() {\n    return this.scope.element\n  }\n\n  get identifier() {\n    return this.scope.identifier\n  }\n\n  get schema() {\n    return this.scope.schema\n  }\n\n  has(outletName: string) {\n    return this.find(outletName) != null\n  }\n\n  find(...outletNames: string[]) {\n    return outletNames.reduce(\n      (outlet, outletName) => outlet || this.findOutlet(outletName),\n      undefined as Element | undefined\n    )\n  }\n\n  findAll(...outletNames: string[]) {\n    return outletNames.reduce(\n      (outlets, outletName) => [...outlets, ...this.findAllOutlets(outletName)],\n      [] as Element[]\n    )\n  }\n\n  getSelectorForOutletName(outletName: string) {\n    const attributeName = this.schema.outletAttributeForScope(this.identifier, outletName)\n    return this.controllerElement.getAttribute(attributeName)\n  }\n\n  private findOutlet(outletName: string) {\n    const selector = this.getSelectorForOutletName(outletName)\n    if (selector) return this.findElement(selector, outletName)\n  }\n\n  private findAllOutlets(outletName: string) {\n    const selector = this.getSelectorForOutletName(outletName)\n    return selector ? this.findAllElements(selector, outletName) : []\n  }\n\n  private findElement(selector: string, outletName: string): Element | undefined {\n    const elements = this.scope.queryElements(selector)\n    return elements.filter((element) => this.matchesElement(element, selector, outletName))[0]\n  }\n\n  private findAllElements(selector: string, outletName: string): Element[] {\n    const elements = this.scope.queryElements(selector)\n    return elements.filter((element) => this.matchesElement(element, selector, outletName))\n  }\n\n  private matchesElement(element: Element, selector: string, outletName: string): boolean {\n    const controllerAttribute = element.getAttribute(this.scope.schema.controllerAttribute) || \"\"\n    return element.matches(selector) && controllerAttribute.split(\" \").includes(outletName)\n  }\n}\n", "import { ClassMap } from \"./class_map\"\nimport { DataMap } from \"./data_map\"\nimport { Guide } from \"./guide\"\nimport { Logger } from \"./logger\"\nimport { Schema } from \"./schema\"\nimport { attributeValueContainsToken } from \"./selectors\"\nimport { TargetSet } from \"./target_set\"\nimport { OutletSet } from \"./outlet_set\"\n\nexport class Scope {\n  readonly schema: Schema\n  readonly element: Element\n  readonly identifier: string\n  readonly guide: Guide\n  readonly outlets: OutletSet\n  readonly targets = new TargetSet(this)\n  readonly classes = new ClassMap(this)\n  readonly data = new DataMap(this)\n\n  constructor(schema: Schema, element: Element, identifier: string, logger: Logger) {\n    this.schema = schema\n    this.element = element\n    this.identifier = identifier\n    this.guide = new Guide(logger)\n    this.outlets = new OutletSet(this.documentScope, element)\n  }\n\n  findElement(selector: string): Element | undefined {\n    return this.element.matches(selector) ? this.element : this.queryElements(selector).find(this.containsElement)\n  }\n\n  findAllElements(selector: string): Element[] {\n    return [\n      ...(this.element.matches(selector) ? [this.element] : []),\n      ...this.queryElements(selector).filter(this.containsElement),\n    ]\n  }\n\n  containsElement = (element: Element): boolean => {\n    return element.closest(this.controllerSelector) === this.element\n  }\n\n  queryElements(selector: string): Element[] {\n    return Array.from(this.element.querySelectorAll(selector))\n  }\n\n  private get controllerSelector(): string {\n    return attributeValueContainsToken(this.schema.controllerAttribute, this.identifier)\n  }\n\n  private get isDocumentScope() {\n    return this.element === document.documentElement\n  }\n\n  private get documentScope(): Scope {\n    return this.isDocumentScope\n      ? this\n      : new Scope(this.schema, document.documentElement, this.identifier, this.guide.logger)\n  }\n}\n", "import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from \"./error_handler\"\nimport { Schema } from \"./schema\"\nimport { Scope } from \"./scope\"\nimport { Token, ValueListObserver, ValueListObserverDelegate } from \"../mutation-observers\"\n\nexport interface ScopeObserverDelegate extends E<PERSON>r<PERSON><PERSON>ler {\n  createScopeForElementAndIdentifier(element: Element, identifier: string): Scope\n  scopeConnected(scope: Scope): void\n  scopeDisconnected(scope: Scope): void\n}\n\nexport class ScopeObserver implements ValueListObserverDelegate<Scope> {\n  readonly element: Element\n  readonly schema: Schema\n  private delegate: ScopeObserverDelegate\n  private valueListObserver: ValueListObserver<Scope>\n  private scopesByIdentifierByElement: WeakMap<Element, Map<string, Scope>>\n  private scopeReferenceCounts: WeakMap<Scope, number>\n\n  constructor(element: Element, schema: Schema, delegate: ScopeObserverDelegate) {\n    this.element = element\n    this.schema = schema\n    this.delegate = delegate\n    this.valueListObserver = new ValueListObserver(this.element, this.controllerAttribute, this)\n    this.scopesByIdentifierByElement = new WeakMap()\n    this.scopeReferenceCounts = new WeakMap()\n  }\n\n  start() {\n    this.valueListObserver.start()\n  }\n\n  stop() {\n    this.valueListObserver.stop()\n  }\n\n  get controllerAttribute() {\n    return this.schema.controllerAttribute\n  }\n\n  // Value observer delegate\n\n  parseValueForToken(token: Token): Scope | undefined {\n    const { element, content: identifier } = token\n    return this.parseValueForElementAndIdentifier(element, identifier)\n  }\n\n  parseValueForElementAndIdentifier(element: Element, identifier: string): Scope | undefined {\n    const scopesByIdentifier = this.fetchScopesByIdentifierForElement(element)\n\n    let scope = scopesByIdentifier.get(identifier)\n    if (!scope) {\n      scope = this.delegate.createScopeForElementAndIdentifier(element, identifier)\n      scopesByIdentifier.set(identifier, scope)\n    }\n\n    return scope\n  }\n\n  elementMatchedValue(element: Element, value: Scope) {\n    const referenceCount = (this.scopeReferenceCounts.get(value) || 0) + 1\n    this.scopeReferenceCounts.set(value, referenceCount)\n    if (referenceCount == 1) {\n      this.delegate.scopeConnected(value)\n    }\n  }\n\n  elementUnmatchedValue(element: Element, value: Scope) {\n    const referenceCount = this.scopeReferenceCounts.get(value)\n    if (referenceCount) {\n      this.scopeReferenceCounts.set(value, referenceCount - 1)\n      if (referenceCount == 1) {\n        this.delegate.scopeDisconnected(value)\n      }\n    }\n  }\n\n  private fetchScopesByIdentifierForElement(element: Element) {\n    let scopesByIdentifier = this.scopesByIdentifierByElement.get(element)\n    if (!scopesByIdentifier) {\n      scopesByIdentifier = new Map()\n      this.scopesByIdentifierByElement.set(element, scopesByIdentifier)\n    }\n    return scopesByIdentifier\n  }\n}\n", "import { Application } from \"./application\"\nimport { Context } from \"./context\"\nimport { Definition } from \"./definition\"\nimport { <PERSON><PERSON><PERSON> } from \"./module\"\nimport { Multimap } from \"../multimap\"\nimport { Scope } from \"./scope\"\nimport { ScopeObserver, ScopeObserverDelegate } from \"./scope_observer\"\n\nexport class Router implements ScopeObserverDelegate {\n  readonly application: Application\n  private scopeObserver: ScopeObserver\n  private scopesByIdentifier: Multimap<string, Scope>\n  private modulesByIdentifier: Map<string, Module>\n\n  constructor(application: Application) {\n    this.application = application\n    this.scopeObserver = new ScopeObserver(this.element, this.schema, this)\n    this.scopesByIdentifier = new Multimap()\n    this.modulesByIdentifier = new Map()\n  }\n\n  get element() {\n    return this.application.element\n  }\n\n  get schema() {\n    return this.application.schema\n  }\n\n  get logger() {\n    return this.application.logger\n  }\n\n  get controllerAttribute(): string {\n    return this.schema.controllerAttribute\n  }\n\n  get modules() {\n    return Array.from(this.modulesByIdentifier.values())\n  }\n\n  get contexts() {\n    return this.modules.reduce((contexts, module) => contexts.concat(module.contexts), [] as Context[])\n  }\n\n  start() {\n    this.scopeObserver.start()\n  }\n\n  stop() {\n    this.scopeObserver.stop()\n  }\n\n  loadDefinition(definition: Definition) {\n    this.unloadIdentifier(definition.identifier)\n    const module = new Module(this.application, definition)\n    this.connectModule(module)\n    const afterLoad = (definition.controllerConstructor as any).afterLoad\n    if (afterLoad) {\n      afterLoad.call(definition.controllerConstructor, definition.identifier, this.application)\n    }\n  }\n\n  unloadIdentifier(identifier: string) {\n    const module = this.modulesByIdentifier.get(identifier)\n    if (module) {\n      this.disconnectModule(module)\n    }\n  }\n\n  getContextForElementAndIdentifier(element: Element, identifier: string) {\n    const module = this.modulesByIdentifier.get(identifier)\n    if (module) {\n      return module.contexts.find((context) => context.element == element)\n    }\n  }\n\n  proposeToConnectScopeForElementAndIdentifier(element: Element, identifier: string) {\n    const scope = this.scopeObserver.parseValueForElementAndIdentifier(element, identifier)\n\n    if (scope) {\n      this.scopeObserver.elementMatchedValue(scope.element, scope)\n    } else {\n      console.error(`Couldn't find or create scope for identifier: \"${identifier}\" and element:`, element)\n    }\n  }\n\n  // Error handler delegate\n\n  handleError(error: Error, message: string, detail: any) {\n    this.application.handleError(error, message, detail)\n  }\n\n  // Scope observer delegate\n\n  createScopeForElementAndIdentifier(element: Element, identifier: string) {\n    return new Scope(this.schema, element, identifier, this.logger)\n  }\n\n  scopeConnected(scope: Scope) {\n    this.scopesByIdentifier.add(scope.identifier, scope)\n    const module = this.modulesByIdentifier.get(scope.identifier)\n    if (module) {\n      module.connectContextForScope(scope)\n    }\n  }\n\n  scopeDisconnected(scope: Scope) {\n    this.scopesByIdentifier.delete(scope.identifier, scope)\n    const module = this.modulesByIdentifier.get(scope.identifier)\n    if (module) {\n      module.disconnectContextForScope(scope)\n    }\n  }\n\n  // Modules\n\n  private connectModule(module: Module) {\n    this.modulesByIdentifier.set(module.identifier, module)\n    const scopes = this.scopesByIdentifier.getValuesForKey(module.identifier)\n    scopes.forEach((scope) => module.connectContextForScope(scope))\n  }\n\n  private disconnectModule(module: Module) {\n    this.modulesByIdentifier.delete(module.identifier)\n    const scopes = this.scopesByIdentifier.getValuesForKey(module.identifier)\n    scopes.forEach((scope) => module.disconnectContextForScope(scope))\n  }\n}\n", "export interface Schema {\n  controllerAttribute: string\n  actionAttribute: string\n  targetAttribute: string\n  targetAttributeForScope(identifier: string): string\n  outletAttributeForScope(identifier: string, outlet: string): string\n  keyMappings: { [key: string]: string }\n}\n\nexport const defaultSchema: Schema = {\n  controllerAttribute: \"data-controller\",\n  actionAttribute: \"data-action\",\n  targetAttribute: \"data-target\",\n  targetAttributeForScope: (identifier) => `data-${identifier}-target`,\n  outletAttributeForScope: (identifier, outlet) => `data-${identifier}-${outlet}-outlet`,\n  keyMappings: {\n    enter: \"Enter\",\n    tab: \"Tab\",\n    esc: \"Escape\",\n    space: \" \",\n    up: \"ArrowUp\",\n    down: \"ArrowDown\",\n    left: \"ArrowLeft\",\n    right: \"ArrowRight\",\n    home: \"Home\",\n    end: \"End\",\n    page_up: \"PageUp\",\n    page_down: \"PageDown\",\n    // [a-z]\n    ...objectFromEntries(\"abcdefghijklmnopqrstuvwxyz\".split(\"\").map((c) => [c, c])),\n    // [0-9]\n    ...objectFromEntries(\"0123456789\".split(\"\").map((n) => [n, n])),\n  },\n}\n\nfunction objectFromEntries(array: [string, any][]): object {\n  // polyfill\n  return array.reduce((memo, [k, v]) => ({ ...memo, [k]: v }), {})\n}\n", "import { Controller, ControllerConstructor } from \"./controller\"\nimport { Definition } from \"./definition\"\nimport { Dispatcher } from \"./dispatcher\"\nimport { <PERSON><PERSON>r<PERSON><PERSON><PERSON> } from \"./error_handler\"\nimport { Logger } from \"./logger\"\nimport { Router } from \"./router\"\nimport { Schema, defaultSchema } from \"./schema\"\nimport { ActionDescriptorFilter, ActionDescriptorFilters, defaultActionDescriptorFilters } from \"./action_descriptor\"\n\nexport class Application implements ErrorHandler {\n  readonly element: Element\n  readonly schema: Schema\n  readonly dispatcher: Dispatcher\n  readonly router: Router\n  readonly actionDescriptorFilters: ActionDescriptorFilters\n  logger: Logger = console\n  debug = false\n\n  static start(element?: Element, schema?: Schema): Application {\n    const application = new this(element, schema)\n    application.start()\n    return application\n  }\n\n  constructor(element: Element = document.documentElement, schema: Schema = defaultSchema) {\n    this.element = element\n    this.schema = schema\n    this.dispatcher = new Dispatcher(this)\n    this.router = new Router(this)\n    this.actionDescriptorFilters = { ...defaultActionDescriptorFilters }\n  }\n\n  async start() {\n    await domReady()\n    this.logDebugActivity(\"application\", \"starting\")\n    this.dispatcher.start()\n    this.router.start()\n    this.logDebugActivity(\"application\", \"start\")\n  }\n\n  stop() {\n    this.logDebugActivity(\"application\", \"stopping\")\n    this.dispatcher.stop()\n    this.router.stop()\n    this.logDebugActivity(\"application\", \"stop\")\n  }\n\n  register(identifier: string, controllerConstructor: ControllerConstructor) {\n    this.load({ identifier, controllerConstructor })\n  }\n\n  registerActionOption(name: string, filter: ActionDescriptorFilter) {\n    this.actionDescriptorFilters[name] = filter\n  }\n\n  load(...definitions: Definition[]): void\n  load(definitions: Definition[]): void\n  load(head: Definition | Definition[], ...rest: Definition[]) {\n    const definitions = Array.isArray(head) ? head : [head, ...rest]\n    definitions.forEach((definition) => {\n      if ((definition.controllerConstructor as any).shouldLoad) {\n        this.router.loadDefinition(definition)\n      }\n    })\n  }\n\n  unload(...identifiers: string[]): void\n  unload(identifiers: string[]): void\n  unload(head: string | string[], ...rest: string[]) {\n    const identifiers = Array.isArray(head) ? head : [head, ...rest]\n    identifiers.forEach((identifier) => this.router.unloadIdentifier(identifier))\n  }\n\n  // Controllers\n\n  get controllers(): Controller[] {\n    return this.router.contexts.map((context) => context.controller)\n  }\n\n  getControllerForElementAndIdentifier(element: Element, identifier: string): Controller | null {\n    const context = this.router.getContextForElementAndIdentifier(element, identifier)\n    return context ? context.controller : null\n  }\n\n  // Error handling\n\n  handleError(error: Error, message: string, detail: object) {\n    this.logger.error(`%s\\n\\n%o\\n\\n%o`, message, error, detail)\n\n    window.onerror?.(message, \"\", 0, 0, error)\n  }\n\n  // Debug logging\n\n  logDebugActivity = (identifier: string, functionName: string, detail: object = {}): void => {\n    if (this.debug) {\n      this.logFormattedMessage(identifier, functionName, detail)\n    }\n  }\n\n  private logFormattedMessage(identifier: string, functionName: string, detail: object = {}) {\n    detail = Object.assign({ application: this }, detail)\n\n    this.logger.groupCollapsed(`${identifier} #${functionName}`)\n    this.logger.log(\"details:\", { ...detail })\n    this.logger.groupEnd()\n  }\n}\n\nfunction domReady() {\n  return new Promise<void>((resolve) => {\n    if (document.readyState == \"loading\") {\n      document.addEventListener(\"DOMContentLoaded\", () => resolve())\n    } else {\n      resolve()\n    }\n  })\n}\n", "import { Constructor } from \"./constructor\"\nimport { Controller } from \"./controller\"\nimport { readInheritableStaticArrayValues } from \"./inheritable_statics\"\nimport { capitalize, namespaceCamelize } from \"./string_helpers\"\n\nexport function OutletPropertiesBlessing<T>(constructor: Constructor<T>) {\n  const outlets = readInheritableStaticArrayValues(constructor, \"outlets\")\n  return outlets.reduce((properties: any, outletDefinition: any) => {\n    return Object.assign(properties, propertiesForOutletDefinition(outletDefinition))\n  }, {} as PropertyDescriptorMap)\n}\n\nfunction getOutletController(controller: Controller, element: Element, identifier: string) {\n  return controller.application.getControllerForElementAndIdentifier(element, identifier)\n}\n\nfunction getControllerAndEnsureConnectedScope(controller: Controller, element: Element, outletName: string) {\n  let outletController = getOutletController(controller, element, outletName)\n  if (outletController) return outletController\n\n  controller.application.router.proposeToConnectScopeForElementAndIdentifier(element, outletName)\n\n  outletController = getOutletController(controller, element, outletName)\n  if (outletController) return outletController\n}\n\nfunction propertiesForOutletDefinition(name: string) {\n  const camelizedName = namespaceCamelize(name)\n\n  return {\n    [`${camelizedName}Outlet`]: {\n      get(this: Controller) {\n        const outletElement = this.outlets.find(name)\n        const selector = this.outlets.getSelectorForOutletName(name)\n\n        if (outletElement) {\n          const outletController = getControllerAndEnsureConnectedScope(this, outletElement, name)\n\n          if (outletController) return outletController\n\n          throw new Error(\n            `The provided outlet element is missing an outlet controller \"${name}\" instance for host controller \"${this.identifier}\"`\n          )\n        }\n\n        throw new Error(\n          `Missing outlet element \"${name}\" for host controller \"${this.identifier}\". Stimulus couldn't find a matching outlet element using selector \"${selector}\".`\n        )\n      },\n    },\n\n    [`${camelizedName}Outlets`]: {\n      get(this: Controller) {\n        const outlets = this.outlets.findAll(name)\n\n        if (outlets.length > 0) {\n          return outlets\n            .map((outletElement: Element) => {\n              const outletController = getControllerAndEnsureConnectedScope(this, outletElement, name)\n\n              if (outletController) return outletController\n\n              console.warn(\n                `The provided outlet element is missing an outlet controller \"${name}\" instance for host controller \"${this.identifier}\"`,\n                outletElement\n              )\n            })\n            .filter((controller) => controller) as Controller[]\n        }\n\n        return []\n      },\n    },\n\n    [`${camelizedName}OutletElement`]: {\n      get(this: Controller) {\n        const outletElement = this.outlets.find(name)\n        const selector = this.outlets.getSelectorForOutletName(name)\n\n        if (outletElement) {\n          return outletElement\n        } else {\n          throw new Error(\n            `Missing outlet element \"${name}\" for host controller \"${this.identifier}\". Stimulus couldn't find a matching outlet element using selector \"${selector}\".`\n          )\n        }\n      },\n    },\n\n    [`${camelizedName}OutletElements`]: {\n      get(this: Controller) {\n        return this.outlets.findAll(name)\n      },\n    },\n\n    [`has${capitalize(camelizedName)}Outlet`]: {\n      get(this: Controller) {\n        return this.outlets.has(name)\n      },\n    },\n  }\n}\n", "import { Constructor } from \"./constructor\"\nimport { Controller } from \"./controller\"\nimport { readInheritableStaticObjectPairs } from \"./inheritable_statics\"\nimport { camelize, capitalize, dasherize } from \"./string_helpers\"\nimport { isSomething, hasProperty } from \"./utils\"\n\nexport function ValuePropertiesBlessing<T>(constructor: Constructor<T>) {\n  const valueDefinitionPairs = readInheritableStaticObjectPairs<T, ValueTypeDefinition>(constructor, \"values\")\n  const propertyDescriptorMap: PropertyDescriptorMap = {\n    valueDescriptorMap: {\n      get(this: Controller) {\n        return valueDefinitionPairs.reduce((result, valueDefinitionPair) => {\n          const valueDescriptor = parseValueDefinitionPair(valueDefinitionPair, this.identifier)\n          const attributeName = this.data.getAttributeNameForKey(valueDescriptor.key)\n          return Object.assign(result, { [attributeName]: valueDescriptor })\n        }, {} as ValueDescriptorMap)\n      },\n    },\n  }\n\n  return valueDefinitionPairs.reduce((properties, valueDefinitionPair) => {\n    return Object.assign(properties, propertiesForValueDefinitionPair(valueDefinitionPair))\n  }, propertyDescriptorMap)\n}\n\nexport function propertiesForValueDefinitionPair<T>(\n  valueDefinitionPair: ValueDefinitionPair,\n  controller?: string\n): PropertyDescriptorMap {\n  const definition = parseValueDefinitionPair(valueDefinitionPair, controller)\n  const { key, name, reader: read, writer: write } = definition\n\n  return {\n    [name]: {\n      get(this: Controller) {\n        const value = this.data.get(key)\n        if (value !== null) {\n          return read(value)\n        } else {\n          return definition.defaultValue\n        }\n      },\n\n      set(this: Controller, value: T | undefined) {\n        if (value === undefined) {\n          this.data.delete(key)\n        } else {\n          this.data.set(key, write(value))\n        }\n      },\n    },\n\n    [`has${capitalize(name)}`]: {\n      get(this: Controller): boolean {\n        return this.data.has(key) || definition.hasCustomDefaultValue\n      },\n    },\n  }\n}\n\nexport type ValueDescriptor = {\n  type: ValueType\n  key: string\n  name: string\n  defaultValue: ValueTypeDefault\n  hasCustomDefaultValue: boolean\n  reader: Reader\n  writer: Writer\n}\n\nexport type ValueDescriptorMap = { [attributeName: string]: ValueDescriptor }\n\nexport type ValueDefinitionMap = { [token: string]: ValueTypeDefinition }\n\nexport type ValueDefinitionPair = [string, ValueTypeDefinition]\n\nexport type ValueTypeConstant = typeof Array | typeof Boolean | typeof Number | typeof Object | typeof String\n\nexport type ValueTypeDefault = Array<any> | boolean | number | Object | string\n\nexport type ValueTypeObject = Partial<{ type: ValueTypeConstant; default: ValueTypeDefault }>\n\nexport type ValueTypeDefinition = ValueTypeConstant | ValueTypeDefault | ValueTypeObject\n\nexport type ValueType = \"array\" | \"boolean\" | \"number\" | \"object\" | \"string\"\n\nfunction parseValueDefinitionPair([token, typeDefinition]: ValueDefinitionPair, controller?: string): ValueDescriptor {\n  return valueDescriptorForTokenAndTypeDefinition({\n    controller,\n    token,\n    typeDefinition,\n  })\n}\n\nexport function parseValueTypeConstant(constant?: ValueTypeConstant) {\n  switch (constant) {\n    case Array:\n      return \"array\"\n    case Boolean:\n      return \"boolean\"\n    case Number:\n      return \"number\"\n    case Object:\n      return \"object\"\n    case String:\n      return \"string\"\n  }\n}\n\nexport function parseValueTypeDefault(defaultValue?: ValueTypeDefault) {\n  switch (typeof defaultValue) {\n    case \"boolean\":\n      return \"boolean\"\n    case \"number\":\n      return \"number\"\n    case \"string\":\n      return \"string\"\n  }\n\n  if (Array.isArray(defaultValue)) return \"array\"\n  if (Object.prototype.toString.call(defaultValue) === \"[object Object]\") return \"object\"\n}\n\ntype ValueTypeObjectPayload = {\n  controller?: string\n  token: string\n  typeObject: ValueTypeObject\n}\n\nexport function parseValueTypeObject(payload: ValueTypeObjectPayload) {\n  const { controller, token, typeObject } = payload\n\n  const hasType = isSomething(typeObject.type)\n  const hasDefault = isSomething(typeObject.default)\n\n  const fullObject = hasType && hasDefault\n  const onlyType = hasType && !hasDefault\n  const onlyDefault = !hasType && hasDefault\n\n  const typeFromObject = parseValueTypeConstant(typeObject.type)\n  const typeFromDefaultValue = parseValueTypeDefault(payload.typeObject.default)\n\n  if (onlyType) return typeFromObject\n  if (onlyDefault) return typeFromDefaultValue\n\n  if (typeFromObject !== typeFromDefaultValue) {\n    const propertyPath = controller ? `${controller}.${token}` : token\n\n    throw new Error(\n      `The specified default value for the Stimulus Value \"${propertyPath}\" must match the defined type \"${typeFromObject}\". The provided default value of \"${typeObject.default}\" is of type \"${typeFromDefaultValue}\".`\n    )\n  }\n\n  if (fullObject) return typeFromObject\n}\n\ntype ValueTypeDefinitionPayload = {\n  controller?: string\n  token: string\n  typeDefinition: ValueTypeDefinition\n}\n\nexport function parseValueTypeDefinition(payload: ValueTypeDefinitionPayload): ValueType {\n  const { controller, token, typeDefinition } = payload\n\n  const typeObject = { controller, token, typeObject: typeDefinition as ValueTypeObject }\n\n  const typeFromObject = parseValueTypeObject(typeObject as ValueTypeObjectPayload)\n  const typeFromDefaultValue = parseValueTypeDefault(typeDefinition as ValueTypeDefault)\n  const typeFromConstant = parseValueTypeConstant(typeDefinition as ValueTypeConstant)\n\n  const type = typeFromObject || typeFromDefaultValue || typeFromConstant\n\n  if (type) return type\n\n  const propertyPath = controller ? `${controller}.${typeDefinition}` : token\n\n  throw new Error(`Unknown value type \"${propertyPath}\" for \"${token}\" value`)\n}\n\nexport function defaultValueForDefinition(typeDefinition: ValueTypeDefinition): ValueTypeDefault {\n  const constant = parseValueTypeConstant(typeDefinition as ValueTypeConstant)\n  if (constant) return defaultValuesByType[constant]\n\n  const hasDefault = hasProperty(typeDefinition, \"default\")\n  const hasType = hasProperty(typeDefinition, \"type\")\n  const typeObject = typeDefinition as ValueTypeObject\n\n  if (hasDefault) return typeObject.default!\n\n  if (hasType) {\n    const { type } = typeObject\n    const constantFromType = parseValueTypeConstant(type)\n\n    if (constantFromType) return defaultValuesByType[constantFromType]\n  }\n\n  return typeDefinition\n}\n\nfunction valueDescriptorForTokenAndTypeDefinition(payload: ValueTypeDefinitionPayload) {\n  const { token, typeDefinition } = payload\n\n  const key = `${dasherize(token)}-value`\n  const type = parseValueTypeDefinition(payload)\n  return {\n    type,\n    key,\n    name: camelize(key),\n    get defaultValue() {\n      return defaultValueForDefinition(typeDefinition)\n    },\n    get hasCustomDefaultValue() {\n      return parseValueTypeDefault(typeDefinition) !== undefined\n    },\n    reader: readers[type],\n    writer: writers[type] || writers.default,\n  }\n}\n\nconst defaultValuesByType = {\n  get array() {\n    return []\n  },\n  boolean: false,\n  number: 0,\n  get object() {\n    return {}\n  },\n  string: \"\",\n}\n\ntype Reader = (value: string) => any\n\nconst readers: { [type: string]: Reader } = {\n  array(value: string): any[] {\n    const array = JSON.parse(value)\n    if (!Array.isArray(array)) {\n      throw new TypeError(\n        `expected value of type \"array\" but instead got value \"${value}\" of type \"${parseValueTypeDefault(array)}\"`\n      )\n    }\n    return array\n  },\n\n  boolean(value: string): boolean {\n    return !(value == \"0\" || String(value).toLowerCase() == \"false\")\n  },\n\n  number(value: string): number {\n    return Number(value.replace(/_/g, \"\"))\n  },\n\n  object(value: string): object {\n    const object = JSON.parse(value)\n    if (object === null || typeof object != \"object\" || Array.isArray(object)) {\n      throw new TypeError(\n        `expected value of type \"object\" but instead got value \"${value}\" of type \"${parseValueTypeDefault(object)}\"`\n      )\n    }\n    return object\n  },\n\n  string(value: string): string {\n    return value\n  },\n}\n\ntype Writer = (value: any) => string\n\nconst writers: { [type: string]: Writer } = {\n  default: writeString,\n  array: writeJSON,\n  object: writeJSON,\n}\n\nfunction writeJSON(value: any) {\n  return JSON.stringify(value)\n}\n\nfunction writeString(value: any) {\n  return `${value}`\n}\n", "import { Application } from \"./application\"\nimport { ClassPropertiesBlessing } from \"./class_properties\"\nimport { Constructor } from \"./constructor\"\nimport { Context } from \"./context\"\nimport { OutletPropertiesBlessing } from \"./outlet_properties\"\nimport { TargetPropertiesBlessing } from \"./target_properties\"\nimport { ValuePropertiesBlessing, ValueDefinitionMap } from \"./value_properties\"\n\nexport type ControllerConstructor = Constructor<Controller>\n\ntype DispatchOptions = Partial<{\n  target: Element | Window | Document\n  detail: Object\n  prefix: string\n  bubbles: boolean\n  cancelable: boolean\n}>\n\nexport class Controller<ElementType extends Element = Element> {\n  static blessings = [\n    ClassPropertiesBlessing,\n    TargetPropertiesBlessing,\n    ValuePropertiesBlessing,\n    OutletPropertiesBlessing,\n  ]\n  static targets: string[] = []\n  static outlets: string[] = []\n  static values: ValueDefinitionMap = {}\n\n  static get shouldLoad() {\n    return true\n  }\n\n  static afterLoad(_identifier: string, _application: Application) {\n    return\n  }\n\n  readonly context: Context\n\n  constructor(context: Context) {\n    this.context = context\n  }\n\n  get application() {\n    return this.context.application\n  }\n\n  get scope() {\n    return this.context.scope\n  }\n\n  get element() {\n    return this.scope.element as ElementType\n  }\n\n  get identifier() {\n    return this.scope.identifier\n  }\n\n  get targets() {\n    return this.scope.targets\n  }\n\n  get outlets() {\n    return this.scope.outlets\n  }\n\n  get classes() {\n    return this.scope.classes\n  }\n\n  get data() {\n    return this.scope.data\n  }\n\n  initialize() {\n    // Override in your subclass to set up initial controller state\n  }\n\n  connect() {\n    // Override in your subclass to respond when the controller is connected to the DOM\n  }\n\n  disconnect() {\n    // Override in your subclass to respond when the controller is disconnected from the DOM\n  }\n\n  dispatch(\n    eventName: string,\n    {\n      target = this.element,\n      detail = {},\n      prefix = this.identifier,\n      bubbles = true,\n      cancelable = true,\n    }: DispatchOptions = {}\n  ) {\n    const type = prefix ? `${prefix}:${eventName}` : eventName\n    const event = new CustomEvent(type, { detail, bubbles, cancelable })\n    target.dispatchEvent(event)\n    return event\n  }\n}\n", "import { Constructor } from \"./constructor\"\nimport { Controller } from \"./controller\"\nimport { readInheritableStaticArrayValues } from \"./inheritable_statics\"\nimport { capitalize } from \"./string_helpers\"\n\nexport function ClassPropertiesBlessing<T>(constructor: Constructor<T>) {\n  const classes = readInheritableStaticArrayValues(constructor, \"classes\")\n  return classes.reduce((properties, classDefinition) => {\n    return Object.assign(properties, propertiesForClassDefinition(classDefinition))\n  }, {} as PropertyDescriptorMap)\n}\n\nfunction propertiesForClassDefinition(key: string) {\n  return {\n    [`${key}Class`]: {\n      get(this: Controller) {\n        const { classes } = this\n        if (classes.has(key)) {\n          return classes.get(key)\n        } else {\n          const attribute = classes.getAttributeName(key)\n          throw new Error(`Missing attribute \"${attribute}\"`)\n        }\n      },\n    },\n\n    [`${key}Classes`]: {\n      get(this: Controller) {\n        return this.classes.getAll(key)\n      },\n    },\n\n    [`has${capitalize(key)}Class`]: {\n      get(this: Controller) {\n        return this.classes.has(key)\n      },\n    },\n  }\n}\n", "import { Constructor } from \"./constructor\"\nimport { Controller } from \"./controller\"\nimport { readInheritableStaticArrayValues } from \"./inheritable_statics\"\nimport { capitalize } from \"./string_helpers\"\n\nexport function TargetPropertiesBlessing<T>(constructor: Constructor<T>) {\n  const targets = readInheritableStaticArrayValues(constructor, \"targets\")\n  return targets.reduce((properties, targetDefinition) => {\n    return Object.assign(properties, propertiesForTargetDefinition(targetDefinition))\n  }, {} as PropertyDescriptorMap)\n}\n\nfunction propertiesForTargetDefinition(name: string) {\n  return {\n    [`${name}Target`]: {\n      get(this: Controller) {\n        const target = this.targets.find(name)\n        if (target) {\n          return target\n        } else {\n          throw new Error(`Missing target element \"${name}\" for \"${this.identifier}\" controller`)\n        }\n      },\n    },\n\n    [`${name}Targets`]: {\n      get(this: Controller) {\n        return this.targets.findAll(name)\n      },\n    },\n\n    [`has${capitalize(name)}Target`]: {\n      get(this: Controller) {\n        return this.targets.has(name)\n      },\n    },\n  }\n}\n"], "names": ["EventListener", "constructor", "eventTarget", "eventName", "eventOptions", "this", "unorderedBindings", "Set", "connect", "addEventListener", "disconnect", "removeEventListener", "bindingConnected", "binding", "add", "bindingDisconnected", "delete", "handleEvent", "event", "extendedEvent", "stopImmediatePropagation", "Object", "assign", "immediatePropagationStopped", "call", "extendEvent", "bindings", "has<PERSON><PERSON><PERSON>", "size", "Array", "from", "sort", "left", "right", "leftIndex", "index", "rightIndex", "Di<PERSON>atcher", "application", "eventListenerMaps", "Map", "started", "start", "eventListeners", "for<PERSON>ach", "eventListener", "stop", "values", "reduce", "listeners", "map", "concat", "fetchEventListenerForBinding", "clearEventListeners", "clearEventListenersForBinding", "handleError", "error", "message", "detail", "removeMappedEventListenerFor", "eventListenerMap", "fetchEventListenerMapForEventTarget", "cache<PERSON>ey", "fetchEventListener", "get", "createEventListener", "set", "parts", "keys", "key", "push", "join", "defaultActionDescriptorFilters", "value", "stopPropagation", "prevent", "preventDefault", "self", "element", "target", "descriptorPattern", "parseEventTarget", "eventTargetName", "window", "document", "camelize", "replace", "_", "char", "toUpperCase", "namespaceCamelize", "capitalize", "char<PERSON>t", "slice", "dasherize", "toLowerCase", "isSomething", "object", "hasProperty", "property", "prototype", "hasOwnProperty", "allModifiers", "defaultEventNames", "a", "button", "form", "details", "input", "e", "getAttribute", "select", "textarea", "Error", "typecast", "JSON", "parse", "o_O", "Binding", "context", "action", "identifier", "actionEvent", "prepareActionEvent", "willBeInvokedByEvent", "applyEventModifiers", "invokeWithEvent", "method", "controller", "methodName", "actionDescriptorFilters", "passes", "name", "entries", "filter", "params", "currentTarget", "logDebugActivity", "KeyboardEvent", "shouldIgnoreKeyboardEvent", "MouseEvent", "shouldIgnoreMouseEvent", "Element", "contains", "scope", "containsElement", "ElementObserver", "delegate", "attributes", "childList", "subtree", "elements", "mutationObserver", "MutationObserver", "mutations", "processMutations", "observe", "mutationObserverInit", "refresh", "pause", "callback", "takeRecords", "matches", "matchElementsInTree", "has", "removeElement", "addElement", "mutation", "processMutation", "type", "processAttributeChange", "attributeName", "processRemovedNodes", "removedNodes", "processAddedNodes", "addedNodes", "elementAttributeChanged", "matchElement", "nodes", "node", "elementFromNode", "processTree", "elementIsActive", "tree", "processor", "nodeType", "Node", "ELEMENT_NODE", "isConnected", "elementMatched", "elementUnmatched", "AttributeObserver", "elementObserver", "selector", "hasAttribute", "match", "querySelectorAll", "elementMatchedAttribute", "elementUnmatchedAttribute", "elementAttributeValueChanged", "fetch", "del", "prune", "Multimap", "valuesByKey", "<PERSON><PERSON><PERSON>", "hasValue", "some", "getValuesForKey", "getKeysForValue", "_key", "_values", "IndexedMultimap", "super", "keysByValue", "SelectorObserver", "_selector", "matchesByElement", "selectorMatchElement", "selectorMatched", "selectors", "selectorUnmatched", "_attributeName", "matchedBefore", "StringMapObserver", "stringMap", "attributeOldValue", "knownAttributeNames", "refreshAttribute", "oldValue", "getStringMapKeyForAttribute", "stringMapKeyAdded", "stringMapValueChanged", "stringMapKeyRemoved", "currentAttributeNames", "recordedAttributeNames", "attribute", "TokenListObserver", "attributeObserver", "tokensByElement", "tokensMatched", "readTokensForElement", "unmatchedTokens", "matchedTokens", "refreshTokensForElement", "tokensUnmatched", "tokens", "token", "tokenMatched", "tokenUnmatched", "previousTokens", "currentTokens", "firstDifferingIndex", "length", "Math", "max", "zip", "findIndex", "previousToken", "currentToken", "content", "tokenString", "trim", "split", "parseTokenString", "ValueListObserver", "tokenListObserver", "parseResultsByToken", "WeakMap", "valuesByTokenByElement", "fetchParseResultForToken", "fetchValuesByTokenForElement", "elementMatchedValue", "elementUnmatchedValue", "parseResult", "parseToken", "valuesByToken", "parseValueForToken", "BindingObserver", "bindingsByAction", "valueListObserver", "actionAttribute", "disconnectAllActions", "schema", "connectAction", "disconnectAction", "clear", "descriptor", "tagName", "getDefaultEventNameForElement", "keyFilter", "forToken", "descriptorString", "includes", "options", "test", "parseActionDescriptorString", "toString", "eventFilter", "filters", "keyFilter<PERSON>issa<PERSON>", "standardFilter", "keyMappings", "pattern", "RegExp", "meta", "ctrl", "alt", "shift", "modifier", "metaKey", "ctrl<PERSON>ey", "altKey", "shift<PERSON>ey", "ValueObserver", "receiver", "stringMapObserver", "valueDescriptorMap", "invokeChangedCallbacksForDefaultValues", "invokeChangedCallback", "writer", "defaultValue", "valueDescriptorNameMap", "valueDescriptors", "undefined", "data", "rawValue", "rawOldValue", "changedMethodName", "changedMethod", "reader", "TypeError", "descriptors", "hasMethodName", "TargetObserver", "targetsByName", "disconnectAllTargets", "connectTarget", "disconnect<PERSON>arget", "targetConnected", "targetDisconnected", "readInheritableStaticArrayValues", "propertyName", "ancestors", "getAncestorsForConstructor", "definition", "isArray", "getOwnStaticArrayValues", "readInheritableStaticObjectPairs", "pairs", "getOwnStaticObjectPairs", "getPrototypeOf", "reverse", "OutletObserver", "outletsByName", "outletElementsByName", "selectorObserverMap", "attributeObserverMap", "outletDefinitions", "outletName", "setupSelectorObserverForOutlet", "setupAttributeObserverForOutlet", "dependentContexts", "observer", "disconnectAllOutlets", "stopSelectorObservers", "stopAttributeObservers", "outlet", "getOutlet", "connectOutlet", "getOutletFromMap", "disconnectOutlet", "hasOutlet", "hasOutletController", "controllerAttribute", "_element", "getOutletNameFromOutletAttributeName", "updateSelectorObserverForOutlet", "outletConnected", "outletDisconnected", "selectorObserver", "body", "attributeNameForOutletName", "outlets", "getSelectorForOutletName", "outletAttributeForScope", "find", "outletDependencies", "dependencies", "router", "modules", "module", "controllerConstructor", "dependentControllerIdentifiers", "identifiers", "contexts", "getControllerForElementAndIdentifier", "Context", "functionName", "bindingObserver", "dispatcher", "valueObserver", "targetObserver", "outletObserver", "initialize", "parentElement", "invokeControllerMethod", "args", "bless", "properties", "shadowConstructor", "extend", "shadowProperties", "getOwnKeys", "shadowingDescriptor", "getOwnPropertyDescriptor", "getShadowedDescriptor", "getShadowProperties", "defineProperties", "shadow", "blessedProperties", "blessing", "getBlessedProperties", "getOwnPropertySymbols", "getOwnPropertyNames", "extendWithReflect", "extended", "Reflect", "construct", "arguments", "create", "setPrototypeOf", "b", "testReflectExtension", "<PERSON><PERSON><PERSON>", "blessDefinition", "contextsByScope", "connectedContexts", "connectContextForScope", "fetchContextForScope", "disconnectContextForScope", "ClassMap", "getDataKey", "getAll", "getAttributeName", "getAttributeNameForKey", "DataMap", "setAttribute", "removeAttribute", "Guide", "logger", "warn", "<PERSON><PERSON><PERSON><PERSON>", "warnedKeysByObject", "attributeValueContainsToken", "TargetSet", "targetName", "targetNames", "<PERSON><PERSON><PERSON><PERSON>", "findLegacyTarget", "findAll", "targets", "findAllTargets", "findAllLegacyTargets", "getSelectorForTargetName", "findElement", "findAllElements", "targetAttributeForScope", "getLegacySelectorForTargetName", "deprecate", "targetDescriptor", "targetAttribute", "revisedAttributeName", "guide", "OutletSet", "controllerElement", "outletNames", "findOutlet", "findAllOutlets", "queryElements", "matchesElement", "<PERSON><PERSON>", "closest", "controllerSelector", "documentScope", "isDocumentScope", "documentElement", "ScopeObserver", "scopesByIdentifierByElement", "scopeReferenceCounts", "parseValueForElementAndIdentifier", "scopesByIdentifier", "fetchScopesByIdentifierForElement", "createScopeForElementAndIdentifier", "referenceCount", "scopeConnected", "scopeDisconnected", "Router", "scopeObserver", "modulesByIdentifier", "loadDefinition", "unloadIdentifier", "connectModule", "afterLoad", "disconnectModule", "getContextForElementAndIdentifier", "proposeToConnectScopeForElementAndIdentifier", "console", "defaultSchema", "enter", "tab", "esc", "space", "up", "down", "home", "end", "page_up", "page_down", "objectFromEntries", "c", "n", "array", "memo", "k", "v", "Application", "debug", "logFormattedMessage", "Promise", "resolve", "readyState", "register", "load", "registerActionOption", "head", "rest", "shouldLoad", "unload", "controllers", "onerror", "groupCollapsed", "log", "groupEnd", "getOutletController", "getControllerAndEnsureConnectedScope", "outletController", "parseValueDefinitionPair", "typeDefinition", "payload", "typeFromObject", "typeObject", "hasType", "<PERSON><PERSON><PERSON><PERSON>", "default", "fullObject", "onlyType", "<PERSON><PERSON><PERSON><PERSON>", "parseValueTypeConstant", "typeFromDefaultValue", "parseValueTypeDefault", "parseValueTypeObject", "typeFromConstant", "parseValueTypeDefinition", "constant", "defaultValuesByType", "constantFromType", "defaultValueForDefinition", "hasCustomDefaultValue", "readers", "writers", "valueDescriptorForTokenAndTypeDefinition", "Boolean", "Number", "String", "boolean", "number", "string", "writeJSON", "stringify", "Controller", "_identifier", "_application", "classes", "dispatch", "prefix", "bubbles", "cancelable", "CustomEvent", "dispatchEvent", "classDefinition", "targetDefinition", "valueDefinitionPairs", "propertyDescriptorMap", "result", "valueDefinitionPair", "valueDescriptor", "read", "write", "propertiesForValueDefinitionPair", "outletDefinition", "camelizedName", "outletElement", "propertiesForOutletDefinition"], "mappings": "MAEaA,EAMXC,YAAYC,EAA0BC,EAAmBC,GACvDC,KAAKH,YAAcA,EACnBG,KAAKF,UAAYA,EACjBE,KAAKD,aAAeA,EACpBC,KAAKC,kBAAoB,IAAIC,IAG/BC,UACEH,KAAKH,YAAYO,iBAAiBJ,KAAKF,UAAWE,KAAMA,KAAKD,cAG/DM,aACEL,KAAKH,YAAYS,oBAAoBN,KAAKF,UAAWE,KAAMA,KAAKD,cAKlEQ,iBAAiBC,GACfR,KAAKC,kBAAkBQ,IAAID,GAG7BE,oBAAoBF,GAClBR,KAAKC,kBAAkBU,OAAOH,GAGhCI,YAAYC,GAEV,MAAMC,EAuBV,SAAqBD,GACnB,GAAI,gCAAiCA,EACnC,OAAOA,EACF,CACL,MAAME,yBAAEA,GAA6BF,EACrC,OAAOG,OAAOC,OAAOJ,EAAO,CAC1BK,6BAA6B,EAC7BH,2BACEf,KAAKkB,6BAA8B,EACnCH,EAAyBI,KAAKnB,UAhCZoB,CAAYP,GAClC,IAAK,MAAML,KAAWR,KAAKqB,SAAU,CACnC,GAAIP,EAAcI,4BAChB,MAEAV,EAAQI,YAAYE,IAK1BQ,cACE,OAAOtB,KAAKC,kBAAkBsB,KAAO,EAGvCF,eACE,OAAOG,MAAMC,KAAKzB,KAAKC,mBAAmByB,MAAK,CAACC,EAAMC,KACpD,MAAMC,EAAYF,EAAKG,MACrBC,EAAaH,EAAME,MACrB,OAAOD,EAAYE,GAAc,EAAIF,EAAYE,EAAa,EAAI,YChD3DC,EAKXpC,YAAYqC,GACVjC,KAAKiC,YAAcA,EACnBjC,KAAKkC,kBAAoB,IAAIC,IAC7BnC,KAAKoC,SAAU,EAGjBC,QACOrC,KAAKoC,UACRpC,KAAKoC,SAAU,EACfpC,KAAKsC,eAAeC,SAASC,GAAkBA,EAAcrC,aAIjEsC,OACMzC,KAAKoC,UACPpC,KAAKoC,SAAU,EACfpC,KAAKsC,eAAeC,SAASC,GAAkBA,EAAcnC,gBAIjEiC,qBACE,OAAOd,MAAMC,KAAKzB,KAAKkC,kBAAkBQ,UAAUC,QACjD,CAACC,EAAWC,IAAQD,EAAUE,OAAOtB,MAAMC,KAAKoB,EAAIH,YACpD,IAMJnC,iBAAiBC,GACfR,KAAK+C,6BAA6BvC,GAASD,iBAAiBC,GAG9DE,oBAAoBF,EAAkBwC,GAAsB,GAC1DhD,KAAK+C,6BAA6BvC,GAASE,oBAAoBF,GAC3DwC,GAAqBhD,KAAKiD,8BAA8BzC,GAK9D0C,YAAYC,EAAcC,EAAiBC,EAAiB,IAC1DrD,KAAKiC,YAAYiB,YAAYC,EAAO,SAASC,IAAWC,GAGlDJ,8BAA8BzC,GACpC,MAAMgC,EAAgBxC,KAAK+C,6BAA6BvC,GACnDgC,EAAclB,gBACjBkB,EAAcnC,aACdL,KAAKsD,6BAA6B9C,IAI9B8C,6BAA6B9C,GACnC,MAAMX,YAAEA,EAAWC,UAAEA,EAASC,aAAEA,GAAiBS,EAC3C+C,EAAmBvD,KAAKwD,oCAAoC3D,GAC5D4D,EAAWzD,KAAKyD,SAAS3D,EAAWC,GAE1CwD,EAAiB5C,OAAO8C,GACK,GAAzBF,EAAiBhC,MAAWvB,KAAKkC,kBAAkBvB,OAAOd,GAGxDkD,6BAA6BvC,GACnC,MAAMX,YAAEA,EAAWC,UAAEA,EAASC,aAAEA,GAAiBS,EACjD,OAAOR,KAAK0D,mBAAmB7D,EAAaC,EAAWC,GAGjD2D,mBACN7D,EACAC,EACAC,GAEA,MAAMwD,EAAmBvD,KAAKwD,oCAAoC3D,GAC5D4D,EAAWzD,KAAKyD,SAAS3D,EAAWC,GAC1C,IAAIyC,EAAgBe,EAAiBI,IAAIF,GAKzC,OAJKjB,IACHA,EAAgBxC,KAAK4D,oBAAoB/D,EAAaC,EAAWC,GACjEwD,EAAiBM,IAAIJ,EAAUjB,IAE1BA,EAGDoB,oBACN/D,EACAC,EACAC,GAEA,MAAMyC,EAAgB,IAAI7C,EAAcE,EAAaC,EAAWC,GAIhE,OAHIC,KAAKoC,SACPI,EAAcrC,UAETqC,EAGDgB,oCAAoC3D,GAC1C,IAAI0D,EAAmBvD,KAAKkC,kBAAkByB,IAAI9D,GAKlD,OAJK0D,IACHA,EAAmB,IAAIpB,IACvBnC,KAAKkC,kBAAkB2B,IAAIhE,EAAa0D,IAEnCA,EAGDE,SAAS3D,EAAmBC,GAClC,MAAM+D,EAAQ,CAAChE,GAMf,OALAkB,OAAO+C,KAAKhE,GACT2B,OACAa,SAASyB,IACRF,EAAMG,KAAK,GAAGlE,EAAaiE,GAAO,GAAK,MAAMA,QAE1CF,EAAMI,KAAK,MC3Gf,MAAMC,EAA0D,CACrE1B,KAAI,EAAC5B,MAAEA,EAAKuD,MAAEA,MACRA,GAAOvD,EAAMwD,mBAEV,GAGTC,QAAO,EAACzD,MAAEA,EAAKuD,MAAEA,MACXA,GAAOvD,EAAM0D,kBAEV,GAGTC,KAAI,EAAC3D,MAAEA,EAAKuD,MAAEA,EAAKK,QAAEA,MACfL,GACKK,IAAY5D,EAAM6D,QAiBzBC,EAAoB,+FAuB1B,SAASC,EAAiBC,GACxB,MAAuB,UAAnBA,EACKC,OACqB,YAAnBD,EACFE,cADF,WCtEOC,EAASZ,GACvB,OAAOA,EAAMa,QAAQ,uBAAuB,CAACC,EAAGC,IAASA,EAAKC,yBAGhDC,EAAkBjB,GAChC,OAAOY,EAASZ,EAAMa,QAAQ,MAAO,KAAKA,QAAQ,MAAO,eAG3CK,EAAWlB,GACzB,OAAOA,EAAMmB,OAAO,GAAGH,cAAgBhB,EAAMoB,MAAM,YAGrCC,EAAUrB,GACxB,OAAOA,EAAMa,QAAQ,YAAY,CAACC,EAAGC,IAAS,IAAIA,EAAKO,2BCbzCC,EAAYC,GAC1B,OAAOA,MAAAA,WAGOC,EAAYD,EAAaE,GACvC,OAAO9E,OAAO+E,UAAUC,eAAe7E,KAAKyE,EAAQE,GCCtD,MAAMG,EAAe,CAAC,OAAQ,OAAQ,MAAO,SAoG7C,MAAMC,EAAyE,CAC7EC,EAAG,IAAM,QACTC,OAAQ,IAAM,QACdC,KAAM,IAAM,SACZC,QAAS,IAAM,SACfC,MAAQC,GAAiC,UAA1BA,EAAEC,aAAa,QAAsB,QAAU,QAC9DC,OAAQ,IAAM,SACdC,SAAU,IAAM,SAUlB,SAASxD,EAAMC,GACb,MAAM,IAAIwD,MAAMxD,GAGlB,SAASyD,EAASzC,GAChB,IACE,OAAO0C,KAAKC,MAAM3C,GAClB,MAAO4C,GACP,OAAO5C,SC9HE6C,EAIXrH,YAAYsH,EAAkBC,GAC5BnH,KAAKkH,QAAUA,EACflH,KAAKmH,OAASA,EAGhBrF,YACE,OAAO9B,KAAKmH,OAAOrF,MAGrBjC,kBACE,OAAOG,KAAKmH,OAAOtH,YAGrBE,mBACE,OAAOC,KAAKmH,OAAOpH,aAGrBqH,iBACE,OAAOpH,KAAKkH,QAAQE,WAGtBxG,YAAYC,GACV,MAAMwG,EAAcrH,KAAKsH,mBAAmBzG,GACxCb,KAAKuH,qBAAqB1G,IAAUb,KAAKwH,oBAAoBH,IAC/DrH,KAAKyH,gBAAgBJ,GAIzBvH,gBACE,OAAOE,KAAKmH,OAAOrH,UAGrB4H,aACE,MAAMA,EAAU1H,KAAK2H,WAAmB3H,KAAK4H,YAC7C,GAAqB,mBAAVF,EACT,OAAOA,EAET,MAAM,IAAId,MAAM,WAAW5G,KAAKmH,wCAAwCnH,KAAK4H,eAGvEJ,oBAAoB3G,GAC1B,MAAM4D,QAAEA,GAAYzE,KAAKmH,QACnBU,wBAAEA,GAA4B7H,KAAKkH,QAAQjF,aAC3C0F,WAAEA,GAAe3H,KAAKkH,QAE5B,IAAIY,GAAS,EAEb,IAAK,MAAOC,EAAM3D,KAAUpD,OAAOgH,QAAQhI,KAAKD,cAC9C,GAAIgI,KAAQF,EAAyB,CACnC,MAAMI,EAASJ,EAAwBE,GAEvCD,EAASA,GAAUG,EAAO,CAAEF,KAAAA,EAAM3D,MAAAA,EAAOvD,MAAAA,EAAO4D,QAAAA,EAASkD,WAAAA,IAM7D,OAAOG,EAGDR,mBAAmBzG,GACzB,OAAOG,OAAOC,OAAOJ,EAAO,CAAEqH,OAAQlI,KAAKmH,OAAOe,SAG5CT,gBAAgB5G,GACtB,MAAM6D,OAAEA,EAAMyD,cAAEA,GAAkBtH,EAClC,IACEb,KAAK0H,OAAOvG,KAAKnB,KAAK2H,WAAY9G,GAClCb,KAAKkH,QAAQkB,iBAAiBpI,KAAK4H,WAAY,CAAE/G,MAAAA,EAAO6D,OAAAA,EAAQyD,cAAAA,EAAehB,OAAQnH,KAAK4H,aAC5F,MAAOzE,GACP,MAAMiE,WAAEA,EAAUO,WAAEA,EAAUlD,QAAEA,EAAO3C,MAAEA,GAAU9B,KAC7CqD,EAAS,CAAE+D,WAAAA,EAAYO,WAAAA,EAAYlD,QAAAA,EAAS3C,MAAAA,EAAOjB,MAAAA,GACzDb,KAAKkH,QAAQhE,YAAYC,EAAO,oBAAoBnD,KAAKmH,UAAW9D,IAIhEkE,qBAAqB1G,GAC3B,MAAMhB,EAAcgB,EAAM6D,OAE1B,QAAI7D,aAAiBwH,eAAiBrI,KAAKmH,OAAOmB,0BAA0BzH,QAIxEA,aAAiB0H,YAAcvI,KAAKmH,OAAOqB,uBAAuB3H,MAIlEb,KAAKyE,UAAY5E,IAEVA,aAAuB4I,SAAWzI,KAAKyE,QAAQiE,SAAS7I,GAC1DG,KAAK2I,MAAMC,gBAAgB/I,GAE3BG,KAAK2I,MAAMC,gBAAgB5I,KAAKmH,OAAO1C,YAIlDkD,iBACE,OAAO3H,KAAKkH,QAAQS,WAGtBC,iBACE,OAAO5H,KAAKmH,OAAOS,WAGrBnD,cACE,OAAOzE,KAAK2I,MAAMlE,QAGpBkE,YACE,OAAO3I,KAAKkH,QAAQyB,aC7GXE,EASXjJ,YAAY6E,EAAkBqE,GAFtB9I,0BAA6C,CAAE+I,YAAY,EAAMC,WAAW,EAAMC,SAAS,GAGjGjJ,KAAKyE,QAAUA,EACfzE,KAAKoC,SAAU,EACfpC,KAAK8I,SAAWA,EAEhB9I,KAAKkJ,SAAW,IAAIhJ,IACpBF,KAAKmJ,iBAAmB,IAAIC,kBAAkBC,GAAcrJ,KAAKsJ,iBAAiBD,KAGpFhH,QACOrC,KAAKoC,UACRpC,KAAKoC,SAAU,EACfpC,KAAKmJ,iBAAiBI,QAAQvJ,KAAKyE,QAASzE,KAAKwJ,sBACjDxJ,KAAKyJ,WAITC,MAAMC,GACA3J,KAAKoC,UACPpC,KAAKmJ,iBAAiB9I,aACtBL,KAAKoC,SAAU,GAGjBuH,IAEK3J,KAAKoC,UACRpC,KAAKmJ,iBAAiBI,QAAQvJ,KAAKyE,QAASzE,KAAKwJ,sBACjDxJ,KAAKoC,SAAU,GAInBK,OACMzC,KAAKoC,UACPpC,KAAKmJ,iBAAiBS,cACtB5J,KAAKmJ,iBAAiB9I,aACtBL,KAAKoC,SAAU,GAInBqH,UACE,GAAIzJ,KAAKoC,QAAS,CAChB,MAAMyH,EAAU,IAAI3J,IAAIF,KAAK8J,uBAE7B,IAAK,MAAMrF,KAAWjD,MAAMC,KAAKzB,KAAKkJ,UAC/BW,EAAQE,IAAItF,IACfzE,KAAKgK,cAAcvF,GAIvB,IAAK,MAAMA,KAAWjD,MAAMC,KAAKoI,GAC/B7J,KAAKiK,WAAWxF,IAOd6E,iBAAiBD,GACvB,GAAIrJ,KAAKoC,QACP,IAAK,MAAM8H,KAAYb,EACrBrJ,KAAKmK,gBAAgBD,GAKnBC,gBAAgBD,GACD,cAAjBA,EAASE,KACXpK,KAAKqK,uBAAuBH,EAASxF,OAAmBwF,EAASI,eACvC,aAAjBJ,EAASE,OAClBpK,KAAKuK,oBAAoBL,EAASM,cAClCxK,KAAKyK,kBAAkBP,EAASQ,aAI5BL,uBAAuB5F,EAAkB6F,GAC3CtK,KAAKkJ,SAASa,IAAItF,GAChBzE,KAAK8I,SAAS6B,yBAA2B3K,KAAK4K,aAAanG,GAC7DzE,KAAK8I,SAAS6B,wBAAwBlG,EAAS6F,GAE/CtK,KAAKgK,cAAcvF,GAEZzE,KAAK4K,aAAanG,IAC3BzE,KAAKiK,WAAWxF,GAIZ8F,oBAAoBM,GAC1B,IAAK,MAAMC,KAAQtJ,MAAMC,KAAKoJ,GAAQ,CACpC,MAAMpG,EAAUzE,KAAK+K,gBAAgBD,GACjCrG,GACFzE,KAAKgL,YAAYvG,EAASzE,KAAKgK,gBAK7BS,kBAAkBI,GACxB,IAAK,MAAMC,KAAQtJ,MAAMC,KAAKoJ,GAAQ,CACpC,MAAMpG,EAAUzE,KAAK+K,gBAAgBD,GACjCrG,GAAWzE,KAAKiL,gBAAgBxG,IAClCzE,KAAKgL,YAAYvG,EAASzE,KAAKiK,aAO7BW,aAAanG,GACnB,OAAOzE,KAAK8I,SAAS8B,aAAanG,GAG5BqF,oBAAoBoB,EAAgBlL,KAAKyE,SAC/C,OAAOzE,KAAK8I,SAASgB,oBAAoBoB,GAGnCF,YAAYE,EAAeC,GACjC,IAAK,MAAM1G,KAAWzE,KAAK8J,oBAAoBoB,GAC7CC,EAAUhK,KAAKnB,KAAMyE,GAIjBsG,gBAAgBD,GACtB,GAAIA,EAAKM,UAAYC,KAAKC,aACxB,OAAOR,EAIHG,gBAAgBxG,GACtB,OAAIA,EAAQ8G,aAAevL,KAAKyE,QAAQ8G,aAG/BvL,KAAKyE,QAAQiE,SAASjE,GAMzBwF,WAAWxF,GACZzE,KAAKkJ,SAASa,IAAItF,IACjBzE,KAAKiL,gBAAgBxG,KACvBzE,KAAKkJ,SAASzI,IAAIgE,GACdzE,KAAK8I,SAAS0C,gBAChBxL,KAAK8I,SAAS0C,eAAe/G,IAM7BuF,cAAcvF,GAChBzE,KAAKkJ,SAASa,IAAItF,KACpBzE,KAAKkJ,SAASvI,OAAO8D,GACjBzE,KAAK8I,SAAS2C,kBAChBzL,KAAK8I,SAAS2C,iBAAiBhH,WCjK1BiH,EAMX9L,YAAY6E,EAAkB6F,EAAuBxB,GACnD9I,KAAKsK,cAAgBA,EACrBtK,KAAK8I,SAAWA,EAEhB9I,KAAK2L,gBAAkB,IAAI9C,EAAgBpE,EAASzE,MAGtDyE,cACE,OAAOzE,KAAK2L,gBAAgBlH,QAG9BmH,eACE,MAAO,IAAI5L,KAAKsK,iBAGlBjI,QACErC,KAAK2L,gBAAgBtJ,QAGvBqH,MAAMC,GACJ3J,KAAK2L,gBAAgBjC,MAAMC,GAG7BlH,OACEzC,KAAK2L,gBAAgBlJ,OAGvBgH,UACEzJ,KAAK2L,gBAAgBlC,UAGvBrH,cACE,OAAOpC,KAAK2L,gBAAgBvJ,QAK9BwI,aAAanG,GACX,OAAOA,EAAQoH,aAAa7L,KAAKsK,eAGnCR,oBAAoBoB,GAClB,MAAMY,EAAQ9L,KAAK4K,aAAaM,GAAQ,CAACA,GAAQ,GAC3CrB,EAAUrI,MAAMC,KAAKyJ,EAAKa,iBAAiB/L,KAAK4L,WACtD,OAAOE,EAAMhJ,OAAO+G,GAGtB2B,eAAe/G,GACTzE,KAAK8I,SAASkD,yBAChBhM,KAAK8I,SAASkD,wBAAwBvH,EAASzE,KAAKsK,eAIxDmB,iBAAiBhH,GACXzE,KAAK8I,SAASmD,2BAChBjM,KAAK8I,SAASmD,0BAA0BxH,EAASzE,KAAKsK,eAI1DK,wBAAwBlG,EAAkB6F,GACpCtK,KAAK8I,SAASoD,8BAAgClM,KAAKsK,eAAiBA,GACtEtK,KAAK8I,SAASoD,6BAA6BzH,EAAS6F,aC3E1C7J,EAAUoC,EAAqBmB,EAAQI,GACrD+H,EAAMtJ,EAAKmB,GAAKvD,IAAI2D,YAGNgI,EAAUvJ,EAAqBmB,EAAQI,GACrD+H,EAAMtJ,EAAKmB,GAAKrD,OAAOyD,GACvBiI,EAAMxJ,EAAKmB,YAGGmI,EAAYtJ,EAAqBmB,GAC/C,IAAItB,EAASG,EAAIc,IAAIK,GAKrB,OAJKtB,IACHA,EAAS,IAAIxC,IACb2C,EAAIgB,IAAIG,EAAKtB,IAERA,WAGO2J,EAAYxJ,EAAqBmB,GAC/C,MAAMtB,EAASG,EAAIc,IAAIK,GACT,MAAVtB,GAAiC,GAAfA,EAAOnB,MAC3BsB,EAAIlC,OAAOqD,SCnBFsI,EAGX1M,cACEI,KAAKuM,YAAc,IAAIpK,IAGzB4B,WACE,OAAOvC,MAAMC,KAAKzB,KAAKuM,YAAYxI,QAGrCrB,aAEE,OADalB,MAAMC,KAAKzB,KAAKuM,YAAY7J,UAC7BC,QAAO,CAACD,EAAQmB,IAAQnB,EAAOI,OAAOtB,MAAMC,KAAKoC,KAAY,IAG3EtC,WAEE,OADaC,MAAMC,KAAKzB,KAAKuM,YAAY7J,UAC7BC,QAAO,CAACpB,EAAMsC,IAAQtC,EAAOsC,EAAItC,MAAM,GAGrDd,IAAIuD,EAAQI,GACV3D,EAAIT,KAAKuM,YAAavI,EAAKI,GAG7BzD,OAAOqD,EAAQI,GACbgI,EAAIpM,KAAKuM,YAAavI,EAAKI,GAG7B2F,IAAI/F,EAAQI,GACV,MAAM1B,EAAS1C,KAAKuM,YAAY5I,IAAIK,GACpC,OAAiB,MAAVtB,GAAkBA,EAAOqH,IAAI3F,GAGtCoI,OAAOxI,GACL,OAAOhE,KAAKuM,YAAYxC,IAAI/F,GAG9ByI,SAASrI,GAEP,OADa5C,MAAMC,KAAKzB,KAAKuM,YAAY7J,UAC7BgK,MAAM7I,GAAQA,EAAIkG,IAAI3F,KAGpCuI,gBAAgB3I,GACd,MAAMtB,EAAS1C,KAAKuM,YAAY5I,IAAIK,GACpC,OAAOtB,EAASlB,MAAMC,KAAKiB,GAAU,GAGvCkK,gBAAgBxI,GACd,OAAO5C,MAAMC,KAAKzB,KAAKuM,aACpBtE,QAAO,EAAE4E,EAAMnK,KAAYA,EAAOqH,IAAI3F,KACtCvB,KAAI,EAAEmB,EAAK8I,KAAa9I,WClDlB+I,UAA8BT,EAGzC1M,cACEoN,QACAhN,KAAKiN,YAAc,IAAI9K,IAGzBO,aACE,OAAOlB,MAAMC,KAAKzB,KAAKiN,YAAYlJ,QAGrCtD,IAAIuD,EAAQI,GACV4I,MAAMvM,IAAIuD,EAAKI,GACf3D,EAAIT,KAAKiN,YAAa7I,EAAOJ,GAG/BrD,OAAOqD,EAAQI,GACb4I,MAAMrM,OAAOqD,EAAKI,GAClBgI,EAAIpM,KAAKiN,YAAa7I,EAAOJ,GAG/ByI,SAASrI,GACP,OAAOpE,KAAKiN,YAAYlD,IAAI3F,GAG9BwI,gBAAgBxI,GACd,MAAMP,EAAM7D,KAAKiN,YAAYtJ,IAAIS,GACjC,OAAOP,EAAMrC,MAAMC,KAAKoC,GAAO,UCtBtBqJ,EAOXtN,YAAY6E,EAAkBmH,EAAkB9C,EAAoCxC,GAClFtG,KAAKmN,UAAYvB,EACjB5L,KAAKsG,QAAUA,EACftG,KAAK2L,gBAAkB,IAAI9C,EAAgBpE,EAASzE,MACpDA,KAAK8I,SAAWA,EAChB9I,KAAKoN,iBAAmB,IAAId,EAG9BlK,cACE,OAAOpC,KAAK2L,gBAAgBvJ,QAG9BwJ,eACE,OAAO5L,KAAKmN,UAGdvB,aAAaA,GACX5L,KAAKmN,UAAYvB,EACjB5L,KAAKyJ,UAGPpH,QACErC,KAAK2L,gBAAgBtJ,QAGvBqH,MAAMC,GACJ3J,KAAK2L,gBAAgBjC,MAAMC,GAG7BlH,OACEzC,KAAK2L,gBAAgBlJ,OAGvBgH,UACEzJ,KAAK2L,gBAAgBlC,UAGvBhF,cACE,OAAOzE,KAAK2L,gBAAgBlH,QAK9BmG,aAAanG,GACX,MAAMmH,SAAEA,GAAa5L,KAErB,GAAI4L,EAAU,CACZ,MAAM/B,EAAUpF,EAAQoF,QAAQ+B,GAEhC,OAAI5L,KAAK8I,SAASuE,qBACTxD,GAAW7J,KAAK8I,SAASuE,qBAAqB5I,EAASzE,KAAKsG,SAG9DuD,EAEP,OAAO,EAIXC,oBAAoBoB,GAClB,MAAMU,SAAEA,GAAa5L,KAErB,GAAI4L,EAAU,CACZ,MAAME,EAAQ9L,KAAK4K,aAAaM,GAAQ,CAACA,GAAQ,GAC3CrB,EAAUrI,MAAMC,KAAKyJ,EAAKa,iBAAiBH,IAAW3D,QAAQ6D,GAAU9L,KAAK4K,aAAakB,KAChG,OAAOA,EAAMhJ,OAAO+G,GAEpB,MAAO,GAIX2B,eAAe/G,GACb,MAAMmH,SAAEA,GAAa5L,KAEjB4L,GACF5L,KAAKsN,gBAAgB7I,EAASmH,GAIlCH,iBAAiBhH,GACf,MAAM8I,EAAYvN,KAAKoN,iBAAiBR,gBAAgBnI,GAExD,IAAK,MAAMmH,KAAY2B,EACrBvN,KAAKwN,kBAAkB/I,EAASmH,GAIpCjB,wBAAwBlG,EAAkBgJ,GACxC,MAAM7B,SAAEA,GAAa5L,KAErB,GAAI4L,EAAU,CACZ,MAAM/B,EAAU7J,KAAK4K,aAAanG,GAC5BiJ,EAAgB1N,KAAKoN,iBAAiBrD,IAAI6B,EAAUnH,GAEtDoF,IAAY6D,EACd1N,KAAKsN,gBAAgB7I,EAASmH,IACpB/B,GAAW6D,GACrB1N,KAAKwN,kBAAkB/I,EAASmH,IAO9B0B,gBAAgB7I,EAAkBmH,GACxC5L,KAAK8I,SAASwE,gBAAgB7I,EAASmH,EAAU5L,KAAKsG,SACtDtG,KAAKoN,iBAAiB3M,IAAImL,EAAUnH,GAG9B+I,kBAAkB/I,EAAkBmH,GAC1C5L,KAAK8I,SAAS0E,kBAAkB/I,EAASmH,EAAU5L,KAAKsG,SACxDtG,KAAKoN,iBAAiBzM,OAAOiL,EAAUnH,UCxH9BkJ,EAOX/N,YAAY6E,EAAkBqE,GAC5B9I,KAAKyE,QAAUA,EACfzE,KAAK8I,SAAWA,EAChB9I,KAAKoC,SAAU,EACfpC,KAAK4N,UAAY,IAAIzL,IACrBnC,KAAKmJ,iBAAmB,IAAIC,kBAAkBC,GAAcrJ,KAAKsJ,iBAAiBD,KAGpFhH,QACOrC,KAAKoC,UACRpC,KAAKoC,SAAU,EACfpC,KAAKmJ,iBAAiBI,QAAQvJ,KAAKyE,QAAS,CAAEsE,YAAY,EAAM8E,mBAAmB,IACnF7N,KAAKyJ,WAIThH,OACMzC,KAAKoC,UACPpC,KAAKmJ,iBAAiBS,cACtB5J,KAAKmJ,iBAAiB9I,aACtBL,KAAKoC,SAAU,GAInBqH,UACE,GAAIzJ,KAAKoC,QACP,IAAK,MAAMkI,KAAiBtK,KAAK8N,oBAC/B9N,KAAK+N,iBAAiBzD,EAAe,MAOnChB,iBAAiBD,GACvB,GAAIrJ,KAAKoC,QACP,IAAK,MAAM8H,KAAYb,EACrBrJ,KAAKmK,gBAAgBD,GAKnBC,gBAAgBD,GACtB,MAAMI,EAAgBJ,EAASI,cAC3BA,GACFtK,KAAK+N,iBAAiBzD,EAAeJ,EAAS8D,UAM1CD,iBAAiBzD,EAAuB0D,GAC9C,MAAMhK,EAAMhE,KAAK8I,SAASmF,4BAA4B3D,GACtD,GAAW,MAAPtG,EAAa,CACVhE,KAAK4N,UAAU7D,IAAIO,IACtBtK,KAAKkO,kBAAkBlK,EAAKsG,GAG9B,MAAMlG,EAAQpE,KAAKyE,QAAQgC,aAAa6D,GAKxC,GAJItK,KAAK4N,UAAUjK,IAAI2G,IAAkBlG,GACvCpE,KAAKmO,sBAAsB/J,EAAOJ,EAAKgK,GAG5B,MAAT5J,EAAe,CACjB,MAAM4J,EAAWhO,KAAK4N,UAAUjK,IAAI2G,GACpCtK,KAAK4N,UAAUjN,OAAO2J,GAClB0D,GAAUhO,KAAKoO,oBAAoBpK,EAAKsG,EAAe0D,QAE3DhO,KAAK4N,UAAU/J,IAAIyG,EAAelG,IAKhC8J,kBAAkBlK,EAAasG,GACjCtK,KAAK8I,SAASoF,mBAChBlO,KAAK8I,SAASoF,kBAAkBlK,EAAKsG,GAIjC6D,sBAAsB/J,EAAsBJ,EAAagK,GAC3DhO,KAAK8I,SAASqF,uBAChBnO,KAAK8I,SAASqF,sBAAsB/J,EAAOJ,EAAKgK,GAI5CI,oBAAoBpK,EAAasG,EAAuB0D,GAC1DhO,KAAK8I,SAASsF,qBAChBpO,KAAK8I,SAASsF,oBAAoBpK,EAAKsG,EAAe0D,GAI1DF,0BACE,OAAOtM,MAAMC,KAAK,IAAIvB,IAAIF,KAAKqO,sBAAsBvL,OAAO9C,KAAKsO,0BAGnED,4BACE,OAAO7M,MAAMC,KAAKzB,KAAKyE,QAAQsE,YAAYlG,KAAK0L,GAAcA,EAAUxG,OAG1EuG,6BACE,OAAO9M,MAAMC,KAAKzB,KAAK4N,UAAU7J,eCnGxByK,EAKX5O,YAAY6E,EAAkB6F,EAAuBxB,GACnD9I,KAAKyO,kBAAoB,IAAI/C,EAAkBjH,EAAS6F,EAAetK,MACvEA,KAAK8I,SAAWA,EAChB9I,KAAK0O,gBAAkB,IAAIpC,EAG7BlK,cACE,OAAOpC,KAAKyO,kBAAkBrM,QAGhCC,QACErC,KAAKyO,kBAAkBpM,QAGzBqH,MAAMC,GACJ3J,KAAKyO,kBAAkB/E,MAAMC,GAG/BlH,OACEzC,KAAKyO,kBAAkBhM,OAGzBgH,UACEzJ,KAAKyO,kBAAkBhF,UAGzBhF,cACE,OAAOzE,KAAKyO,kBAAkBhK,QAGhC6F,oBACE,OAAOtK,KAAKyO,kBAAkBnE,cAKhC0B,wBAAwBvH,GACtBzE,KAAK2O,cAAc3O,KAAK4O,qBAAqBnK,IAG/CyH,6BAA6BzH,GAC3B,MAAOoK,EAAiBC,GAAiB9O,KAAK+O,wBAAwBtK,GACtEzE,KAAKgP,gBAAgBH,GACrB7O,KAAK2O,cAAcG,GAGrB7C,0BAA0BxH,GACxBzE,KAAKgP,gBAAgBhP,KAAK0O,gBAAgB/B,gBAAgBlI,IAGpDkK,cAAcM,GACpBA,EAAO1M,SAAS2M,GAAUlP,KAAKmP,aAAaD,KAGtCF,gBAAgBC,GACtBA,EAAO1M,SAAS2M,GAAUlP,KAAKoP,eAAeF,KAGxCC,aAAaD,GACnBlP,KAAK8I,SAASqG,aAAaD,GAC3BlP,KAAK0O,gBAAgBjO,IAAIyO,EAAMzK,QAASyK,GAGlCE,eAAeF,GACrBlP,KAAK8I,SAASsG,eAAeF,GAC7BlP,KAAK0O,gBAAgB/N,OAAOuO,EAAMzK,QAASyK,GAGrCH,wBAAwBtK,GAC9B,MAAM4K,EAAiBrP,KAAK0O,gBAAgB/B,gBAAgBlI,GACtD6K,EAAgBtP,KAAK4O,qBAAqBnK,GAC1C8K,EA0BV,SAAmB5N,EAAWC,GAC5B,MAAM4N,EAASC,KAAKC,IAAI/N,EAAK6N,OAAQ5N,EAAM4N,QAC3C,OAAOhO,MAAMC,KAAK,CAAE+N,OAAAA,IAAU,CAACtK,EAAGpD,IAAU,CAACH,EAAKG,GAAQF,EAAME,MA5BlC6N,CAAIN,EAAgBC,GAAeM,WAC7D,EAAEC,EAAeC,MAAkB,OA8BHlO,EA9BkCkO,KA8BhDnO,EA9BiCkO,IA+BxCjO,GAASD,EAAKG,OAASF,EAAME,OAASH,EAAKoO,SAAWnO,EAAMmO,SAD7E,IAAwBpO,EAAcC,KA3BlC,OAA4B,GAAxB2N,EACK,CAAC,GAAI,IAEL,CAACF,EAAe7J,MAAM+J,GAAsBD,EAAc9J,MAAM+J,IAInEX,qBAAqBnK,GAC3B,MAAM6F,EAAgBtK,KAAKsK,cAE3B,OAIJ,SAA0B0F,EAAqBvL,EAAkB6F,GAC/D,OAAO0F,EACJC,OACAC,MAAM,OACNjI,QAAQ8H,GAAYA,EAAQP,SAC5B3M,KAAI,CAACkN,EAASjO,MAAa2C,QAAAA,EAAS6F,cAAAA,EAAeyF,QAAAA,EAASjO,MAAAA,MATtDqO,CADa1L,EAAQgC,aAAa6D,IAAkB,GACtB7F,EAAS6F,UC5FrC8F,EAMXxQ,YAAY6E,EAAkB6F,EAAuBxB,GACnD9I,KAAKqQ,kBAAoB,IAAI7B,EAAkB/J,EAAS6F,EAAetK,MACvEA,KAAK8I,SAAWA,EAChB9I,KAAKsQ,oBAAsB,IAAIC,QAC/BvQ,KAAKwQ,uBAAyB,IAAID,QAGpCnO,cACE,OAAOpC,KAAKqQ,kBAAkBjO,QAGhCC,QACErC,KAAKqQ,kBAAkBhO,QAGzBI,OACEzC,KAAKqQ,kBAAkB5N,OAGzBgH,UACEzJ,KAAKqQ,kBAAkB5G,UAGzBhF,cACE,OAAOzE,KAAKqQ,kBAAkB5L,QAGhC6F,oBACE,OAAOtK,KAAKqQ,kBAAkB/F,cAGhC6E,aAAaD,GACX,MAAMzK,QAAEA,GAAYyK,GACd9K,MAAEA,GAAUpE,KAAKyQ,yBAAyBvB,GAC5C9K,IACFpE,KAAK0Q,6BAA6BjM,GAASZ,IAAIqL,EAAO9K,GACtDpE,KAAK8I,SAAS6H,oBAAoBlM,EAASL,IAI/CgL,eAAeF,GACb,MAAMzK,QAAEA,GAAYyK,GACd9K,MAAEA,GAAUpE,KAAKyQ,yBAAyBvB,GAC5C9K,IACFpE,KAAK0Q,6BAA6BjM,GAAS9D,OAAOuO,GAClDlP,KAAK8I,SAAS8H,sBAAsBnM,EAASL,IAIzCqM,yBAAyBvB,GAC/B,IAAI2B,EAAc7Q,KAAKsQ,oBAAoB3M,IAAIuL,GAK/C,OAJK2B,IACHA,EAAc7Q,KAAK8Q,WAAW5B,GAC9BlP,KAAKsQ,oBAAoBzM,IAAIqL,EAAO2B,IAE/BA,EAGDH,6BAA6BjM,GACnC,IAAIsM,EAAgB/Q,KAAKwQ,uBAAuB7M,IAAIc,GAKpD,OAJKsM,IACHA,EAAgB,IAAI5O,IACpBnC,KAAKwQ,uBAAuB3M,IAAIY,EAASsM,IAEpCA,EAGDD,WAAW5B,GACjB,IAEE,MAAO,CAAE9K,MADKpE,KAAK8I,SAASkI,mBAAmB9B,IAE/C,MAAO/L,GACP,MAAO,CAAEA,MAAAA,WC/EF8N,EAMXrR,YAAYsH,EAAkB4B,GAC5B9I,KAAKkH,QAAUA,EACflH,KAAK8I,SAAWA,EAChB9I,KAAKkR,iBAAmB,IAAI/O,IAG9BE,QACOrC,KAAKmR,oBACRnR,KAAKmR,kBAAoB,IAAIf,EAAkBpQ,KAAKyE,QAASzE,KAAKoR,gBAAiBpR,MACnFA,KAAKmR,kBAAkB9O,SAI3BI,OACMzC,KAAKmR,oBACPnR,KAAKmR,kBAAkB1O,cAChBzC,KAAKmR,kBACZnR,KAAKqR,wBAIT5M,cACE,OAAOzE,KAAKkH,QAAQzC,QAGtB2C,iBACE,OAAOpH,KAAKkH,QAAQE,WAGtBgK,sBACE,OAAOpR,KAAKsR,OAAOF,gBAGrBE,aACE,OAAOtR,KAAKkH,QAAQoK,OAGtBjQ,eACE,OAAOG,MAAMC,KAAKzB,KAAKkR,iBAAiBxO,UAGlC6O,cAAcpK,GACpB,MAAM3G,EAAU,IAAIyG,EAAQjH,KAAKkH,QAASC,GAC1CnH,KAAKkR,iBAAiBrN,IAAIsD,EAAQ3G,GAClCR,KAAK8I,SAASvI,iBAAiBC,GAGzBgR,iBAAiBrK,GACvB,MAAM3G,EAAUR,KAAKkR,iBAAiBvN,IAAIwD,GACtC3G,IACFR,KAAKkR,iBAAiBvQ,OAAOwG,GAC7BnH,KAAK8I,SAASpI,oBAAoBF,IAI9B6Q,uBACNrR,KAAKqB,SAASkB,SAAS/B,GAAYR,KAAK8I,SAASpI,oBAAoBF,GAAS,KAC9ER,KAAKkR,iBAAiBO,QAKxBT,mBAAmB9B,GACjB,MAAM/H,QX1DRvH,YAAY6E,EAAkB3C,EAAe4P,EAAuCJ,GAClFtR,KAAKyE,QAAUA,EACfzE,KAAK8B,MAAQA,EACb9B,KAAKH,YAAc6R,EAAW7R,aAAe4E,EAC7CzE,KAAKF,UAAY4R,EAAW5R,oBAyFc2E,GAC5C,MAAMkN,EAAUlN,EAAQkN,QAAQjM,cAChC,GAAIiM,KAAWzL,EACb,OAAOA,EAAkByL,GAASlN,GA5FOmN,CAA8BnN,IAAYtB,EAAM,sBACzFnD,KAAKD,aAAe2R,EAAW3R,cAAgB,GAC/CC,KAAKoH,WAAasK,EAAWtK,YAAcjE,EAAM,sBACjDnD,KAAK4H,WAAa8J,EAAW9J,YAAczE,EAAM,uBACjDnD,KAAK6R,UAAYH,EAAWG,WAAa,GACzC7R,KAAKsR,OAASA,EAbhBQ,gBAAgB5C,EAAcoC,GAC5B,OAAO,IAAItR,KAAKkP,EAAMzK,QAASyK,EAAMpN,eH0BGiQ,GAC1C,MACMlI,EADSkI,EAAiB9B,OACTnE,MAAMnH,IAAsB,GACnD,IAAI7E,EAAY+J,EAAQ,GACpBgI,EAAYhI,EAAQ,GAOxB,OALIgI,IAAc,CAAC,UAAW,QAAS,YAAYG,SAASlS,KAC1DA,GAAa,IAAI+R,IACjBA,EAAY,IAGP,CACLhS,YAAa+E,EAAiBiF,EAAQ,IACtC/J,UAAAA,EACAC,aAAc8J,EAAQ,IAeC9J,EAfsB8J,EAAQ,GAgBhD9J,EACJmQ,MAAM,KACNvN,QAAO,CAACsP,EAAS/C,IAAUlO,OAAOC,OAAOgR,EAAS,CAAE,CAAC/C,EAAMjK,QAAQ,KAAM,MAAO,KAAKiN,KAAKhD,MAAW,KAlB3C,GAC3D9H,WAAYyC,EAAQ,GACpBjC,WAAYiC,EAAQ,GACpBgI,UAAWhI,EAAQ,IAAMgI,GAY7B,IAA2B9R,EGvDqBoS,CAA4BjD,EAAMa,SAAUuB,GAe1Fc,WACE,MAAMC,EAAcrS,KAAK6R,UAAY,IAAI7R,KAAK6R,YAAc,GACtDhS,EAAcG,KAAK6E,gBAAkB,IAAI7E,KAAK6E,kBAAoB,GACxE,MAAO,GAAG7E,KAAKF,YAAYuS,IAAcxS,MAAgBG,KAAKoH,cAAcpH,KAAK4H,aAGnFU,0BAA0BzH,GACxB,IAAKb,KAAK6R,UACR,OAAO,EAGT,MAAMS,EAAUtS,KAAK6R,UAAU3B,MAAM,KACrC,GAAIlQ,KAAKuS,sBAAsB1R,EAAOyR,GACpC,OAAO,EAGT,MAAME,EAAiBF,EAAQrK,QAAQjE,IAASiC,EAAa+L,SAAShO,KAAM,GAC5E,QAAKwO,IAKA3M,EAAY7F,KAAKyS,YAAaD,IACjCrP,EAAM,gCAAgCnD,KAAK6R,aAGtC7R,KAAKyS,YAAYD,GAAgB9M,gBAAkB7E,EAAMmD,IAAI0B,eAGtE8C,uBAAuB3H,GACrB,IAAKb,KAAK6R,UACR,OAAO,EAGT,MAAMS,EAAU,CAACtS,KAAK6R,WACtB,QAAI7R,KAAKuS,sBAAsB1R,EAAOyR,GAOxCpK,aACE,MAAMA,EAAiC,GACjCwK,EAAU,IAAIC,OAAO,SAAS3S,KAAKoH,yBAA0B,KAEnE,IAAK,MAAMW,KAAEA,EAAI3D,MAAEA,KAAW5C,MAAMC,KAAKzB,KAAKyE,QAAQsE,YAAa,CACjE,MAAM+C,EAAQ/D,EAAK+D,MAAM4G,GACnB1O,EAAM8H,GAASA,EAAM,GACvB9H,IACFkE,EAAOlD,EAAShB,IAAQ6C,EAASzC,IAGrC,OAAO8D,EAGTrD,sBACE,OHXiChF,EGWLG,KAAKH,cHVhBiF,OACV,SACEjF,GAAekF,SACjB,gBADF,MAH4BlF,EGcnC4S,kBACE,OAAOzS,KAAKsR,OAAOmB,YAGbF,sBAAsB1R,EAAmCyR,GAC/D,MAAOM,EAAMC,EAAMC,EAAKC,GAAS9M,EAAapD,KAAKmQ,GAAaV,EAAQN,SAASgB,KAEjF,OAAOnS,EAAMoS,UAAYL,GAAQ/R,EAAMqS,UAAYL,GAAQhS,EAAMsS,SAAWL,GAAOjS,EAAMuS,WAAaL,IWrBhFjB,SAAS5C,EAAOlP,KAAKsR,QAC3C,GAAInK,EAAOC,YAAcpH,KAAKoH,WAC5B,OAAOD,EAIXwJ,oBAAoBlM,EAAkB0C,GACpCnH,KAAKuR,cAAcpK,GAGrByJ,sBAAsBnM,EAAkB0C,GACtCnH,KAAKwR,iBAAiBrK,UCvFbkM,EAMXzT,YAAYsH,EAAkBoM,GAC5BtT,KAAKkH,QAAUA,EACflH,KAAKsT,SAAWA,EAChBtT,KAAKuT,kBAAoB,IAAI5F,EAAkB3N,KAAKyE,QAASzE,MAC7DA,KAAKwT,mBAAsBxT,KAAK2H,WAAmB6L,mBAGrDnR,QACErC,KAAKuT,kBAAkBlR,QACvBrC,KAAKyT,yCAGPhR,OACEzC,KAAKuT,kBAAkB9Q,OAGzBgC,cACE,OAAOzE,KAAKkH,QAAQzC,QAGtBkD,iBACE,OAAO3H,KAAKkH,QAAQS,WAKtBsG,4BAA4B3D,GAC1B,GAAIA,KAAiBtK,KAAKwT,mBACxB,OAAOxT,KAAKwT,mBAAmBlJ,GAAevC,KAIlDmG,kBAAkBlK,EAAasG,GAC7B,MAAMoH,EAAa1R,KAAKwT,mBAAmBlJ,GAEtCtK,KAAKyM,SAASzI,IACjBhE,KAAK0T,sBAAsB1P,EAAK0N,EAAWiC,OAAO3T,KAAKsT,SAAStP,IAAO0N,EAAWiC,OAAOjC,EAAWkC,eAIxGzF,sBAAsB/J,EAAe2D,EAAciG,GACjD,MAAM0D,EAAa1R,KAAK6T,uBAAuB9L,GAEjC,OAAV3D,IAEa,OAAb4J,IACFA,EAAW0D,EAAWiC,OAAOjC,EAAWkC,eAG1C5T,KAAK0T,sBAAsB3L,EAAM3D,EAAO4J,IAG1CI,oBAAoBpK,EAAasG,EAAuB0D,GACtD,MAAM0D,EAAa1R,KAAK6T,uBAAuB7P,GAE3ChE,KAAKyM,SAASzI,GAChBhE,KAAK0T,sBAAsB1P,EAAK0N,EAAWiC,OAAO3T,KAAKsT,SAAStP,IAAOgK,GAEvEhO,KAAK0T,sBAAsB1P,EAAK0N,EAAWiC,OAAOjC,EAAWkC,cAAe5F,GAIxEyF,yCACN,IAAK,MAAMzP,IAAEA,EAAG+D,KAAEA,EAAI6L,aAAEA,EAAYD,OAAEA,KAAY3T,KAAK8T,iBACjCC,MAAhBH,GAA8B5T,KAAK2H,WAAWqM,KAAKjK,IAAI/F,IACzDhE,KAAK0T,sBAAsB3L,EAAM4L,EAAOC,QAAeG,GAKrDL,sBAAsB3L,EAAckM,EAAkBC,GAC5D,MAAMC,EAAoB,GAAGpM,WACvBqM,EAAgBpU,KAAKsT,SAASa,GAEpC,GAA4B,mBAAjBC,EAA6B,CACtC,MAAM1C,EAAa1R,KAAK6T,uBAAuB9L,GAE/C,IACE,MAAM3D,EAAQsN,EAAW2C,OAAOJ,GAChC,IAAIjG,EAAWkG,EAEXA,IACFlG,EAAW0D,EAAW2C,OAAOH,IAG/BE,EAAcjT,KAAKnB,KAAKsT,SAAUlP,EAAO4J,GACzC,MAAO7K,GAKP,MAJIA,aAAiBmR,YACnBnR,EAAMC,QAAU,mBAAmBpD,KAAKkH,QAAQE,cAAcsK,EAAW3J,WAAW5E,EAAMC,WAGtFD,IAKZ2Q,uBACE,MAAMN,mBAAEA,GAAuBxT,KAC/B,OAAOgB,OAAO+C,KAAKyP,GAAoB3Q,KAAKmB,GAAQwP,EAAmBxP,KAGzE6P,6BACE,MAAMU,EAAmD,GAOzD,OALAvT,OAAO+C,KAAK/D,KAAKwT,oBAAoBjR,SAASyB,IAC5C,MAAM0N,EAAa1R,KAAKwT,mBAAmBxP,GAC3CuQ,EAAY7C,EAAW3J,MAAQ2J,KAG1B6C,EAGD9H,SAASnC,GACf,MACMkK,EAAgB,MAAMlP,EADTtF,KAAK6T,uBAAuBvJ,GACGvC,QAElD,OAAO/H,KAAKsT,SAASkB,UCtHZC,EAMX7U,YAAYsH,EAAkB4B,GAC5B9I,KAAKkH,QAAUA,EACflH,KAAK8I,SAAWA,EAChB9I,KAAK0U,cAAgB,IAAIpI,EAG3BjK,QACOrC,KAAKqQ,oBACRrQ,KAAKqQ,kBAAoB,IAAI7B,EAAkBxO,KAAKyE,QAASzE,KAAKsK,cAAetK,MACjFA,KAAKqQ,kBAAkBhO,SAI3BI,OACMzC,KAAKqQ,oBACPrQ,KAAK2U,uBACL3U,KAAKqQ,kBAAkB5N,cAChBzC,KAAKqQ,mBAMhBlB,cAAa1K,QAAEA,EAASsL,QAAShI,IAC3B/H,KAAK2I,MAAMC,gBAAgBnE,IAC7BzE,KAAK4U,cAAcnQ,EAASsD,GAIhCqH,gBAAe3K,QAAEA,EAASsL,QAAShI,IACjC/H,KAAK6U,iBAAiBpQ,EAASsD,GAKjC6M,cAAcnQ,EAAkBsD,SACzB/H,KAAK0U,cAAc3K,IAAIhC,EAAMtD,KAChCzE,KAAK0U,cAAcjU,IAAIsH,EAAMtD,aAC7BzE,KAAKqQ,kCAAmB3G,OAAM,IAAM1J,KAAK8I,SAASgM,gBAAgBrQ,EAASsD,MAI/E8M,iBAAiBpQ,EAAkBsD,SAC7B/H,KAAK0U,cAAc3K,IAAIhC,EAAMtD,KAC/BzE,KAAK0U,cAAc/T,OAAOoH,EAAMtD,aAChCzE,KAAKqQ,kCAAmB3G,OAAM,IAAM1J,KAAK8I,SAASiM,mBAAmBtQ,EAASsD,MAIlF4M,uBACE,IAAK,MAAM5M,KAAQ/H,KAAK0U,cAAc3Q,KACpC,IAAK,MAAMU,KAAWzE,KAAK0U,cAAc/H,gBAAgB5E,GACvD/H,KAAK6U,iBAAiBpQ,EAASsD,GAOrCuC,oBACE,MAAO,QAAQtK,KAAKkH,QAAQE,oBAG9B3C,cACE,OAAOzE,KAAKkH,QAAQzC,QAGtBkE,YACE,OAAO3I,KAAKkH,QAAQyB,gBCjFRqM,EAAgDpV,EAA6BqV,GAC3F,MAAMC,EAAYC,EAA2BvV,GAC7C,OAAO4B,MAAMC,KACXyT,EAAUvS,QAAO,CAACD,EAAQ9C,KAwB9B,SAAoCA,EAA6BqV,GAC/D,MAAMG,EAAcxV,EAAoBqV,GACxC,OAAOzT,MAAM6T,QAAQD,GAAcA,EAAa,GAzB5CE,CAAwB1V,EAAaqV,GAAc1S,SAASwF,GAASrF,EAAOjC,IAAIsH,KACzErF,IACN,IAAIxC,eAIKqV,EAAuC3V,EAA6BqV,GAElF,OADkBE,EAA2BvV,GAC5B+C,QAAO,CAAC6S,EAAO5V,KAC9B4V,EAAMvR,QAmBV,SAAuCrE,EAA6BqV,GAClE,MAAMG,EAAcxV,EAAoBqV,GACxC,OAAOG,EAAapU,OAAO+C,KAAKqR,GAAYvS,KAAKmB,GAAQ,CAACA,EAAKoR,EAAWpR,MAAwB,GArBjFyR,CAAwB7V,EAAaqV,IAC7CO,IACN,IAGL,SAASL,EAA8BvV,GACrC,MAAMsV,EAAgC,GACtC,KAAOtV,GACLsV,EAAUjR,KAAKrE,GACfA,EAAcoB,OAAO0U,eAAe9V,GAEtC,OAAOsV,EAAUS,gBCXNC,EASXhW,YAAYsH,EAAkB4B,GAC5B9I,KAAKoC,SAAU,EACfpC,KAAKkH,QAAUA,EACflH,KAAK8I,SAAWA,EAChB9I,KAAK6V,cAAgB,IAAIvJ,EACzBtM,KAAK8V,qBAAuB,IAAIxJ,EAChCtM,KAAK+V,oBAAsB,IAAI5T,IAC/BnC,KAAKgW,qBAAuB,IAAI7T,IAGlCE,QACOrC,KAAKoC,UACRpC,KAAKiW,kBAAkB1T,SAAS2T,IAC9BlW,KAAKmW,+BAA+BD,GACpClW,KAAKoW,gCAAgCF,MAEvClW,KAAKoC,SAAU,EACfpC,KAAKqW,kBAAkB9T,SAAS2E,GAAYA,EAAQuC,aAIxDA,UACEzJ,KAAK+V,oBAAoBxT,SAAS+T,GAAaA,EAAS7M,YACxDzJ,KAAKgW,qBAAqBzT,SAAS+T,GAAaA,EAAS7M,YAG3DhH,OACMzC,KAAKoC,UACPpC,KAAKoC,SAAU,EACfpC,KAAKuW,uBACLvW,KAAKwW,wBACLxW,KAAKyW,0BAITD,wBACMxW,KAAK+V,oBAAoBxU,KAAO,IAClCvB,KAAK+V,oBAAoBxT,SAAS+T,GAAaA,EAAS7T,SACxDzC,KAAK+V,oBAAoBtE,SAI7BgF,yBACMzW,KAAKgW,qBAAqBzU,KAAO,IACnCvB,KAAKgW,qBAAqBzT,SAAS+T,GAAaA,EAAS7T,SACzDzC,KAAKgW,qBAAqBvE,SAM9BnE,gBAAgB7I,EAAkB0I,GAAmB+I,WAAEA,IACrD,MAAMQ,EAAS1W,KAAK2W,UAAUlS,EAASyR,GAEnCQ,GACF1W,KAAK4W,cAAcF,EAAQjS,EAASyR,GAIxC1I,kBAAkB/I,EAAkB0I,GAAmB+I,WAAEA,IACvD,MAAMQ,EAAS1W,KAAK6W,iBAAiBpS,EAASyR,GAE1CQ,GACF1W,KAAK8W,iBAAiBJ,EAAQjS,EAASyR,GAI3C7I,qBAAqB5I,GAAkByR,WAAEA,IACvC,MAAMtK,EAAW5L,KAAK4L,SAASsK,GACzBa,EAAY/W,KAAK+W,UAAUtS,EAASyR,GACpCc,EAAsBvS,EAAQoF,QAAQ,IAAI7J,KAAKsR,OAAO2F,wBAAwBf,MAEpF,QAAItK,IACKmL,GAAaC,GAAuBvS,EAAQoF,QAAQ+B,IAQ/DI,wBAAwBkL,EAAmB5M,GACzC,MAAM4L,EAAalW,KAAKmX,qCAAqC7M,GAEzD4L,GACFlW,KAAKoX,gCAAgClB,GAIzChK,6BAA6BgL,EAAmB5M,GAC9C,MAAM4L,EAAalW,KAAKmX,qCAAqC7M,GAEzD4L,GACFlW,KAAKoX,gCAAgClB,GAIzCjK,0BAA0BiL,EAAmB5M,GAC3C,MAAM4L,EAAalW,KAAKmX,qCAAqC7M,GAEzD4L,GACFlW,KAAKoX,gCAAgClB,GAMzCU,cAAcF,EAAoBjS,EAAkByR,SAC7ClW,KAAK8V,qBAAqB/L,IAAImM,EAAYzR,KAC7CzE,KAAK6V,cAAcpV,IAAIyV,EAAYQ,GACnC1W,KAAK8V,qBAAqBrV,IAAIyV,EAAYzR,aAC1CzE,KAAK+V,oBAAoBpS,IAAIuS,mBAAaxM,OAAM,IAAM1J,KAAK8I,SAASuO,gBAAgBX,EAAQjS,EAASyR,MAIzGY,iBAAiBJ,EAAoBjS,EAAkByR,SACjDlW,KAAK8V,qBAAqB/L,IAAImM,EAAYzR,KAC5CzE,KAAK6V,cAAclV,OAAOuV,EAAYQ,GACtC1W,KAAK8V,qBAAqBnV,OAAOuV,EAAYzR,aAC7CzE,KAAK+V,oBACFpS,IAAIuS,mBACHxM,OAAM,IAAM1J,KAAK8I,SAASwO,mBAAmBZ,EAAQjS,EAASyR,MAItEK,uBACE,IAAK,MAAML,KAAclW,KAAK8V,qBAAqB/R,KACjD,IAAK,MAAMU,KAAWzE,KAAK8V,qBAAqBnJ,gBAAgBuJ,GAC9D,IAAK,MAAMQ,KAAU1W,KAAK6V,cAAclJ,gBAAgBuJ,GACtDlW,KAAK8W,iBAAiBJ,EAAQjS,EAASyR,GAQvCkB,gCAAgClB,GACtC,MAAMI,EAAWtW,KAAK+V,oBAAoBpS,IAAIuS,GAE1CI,IACFA,EAAS1K,SAAW5L,KAAK4L,SAASsK,IAI9BC,+BAA+BD,GACrC,MAAMtK,EAAW5L,KAAK4L,SAASsK,GACzBqB,EAAmB,IAAIrK,EAAiBnI,SAASyS,KAAM5L,EAAW5L,KAAM,CAAEkW,WAAAA,IAEhFlW,KAAK+V,oBAAoBlS,IAAIqS,EAAYqB,GAEzCA,EAAiBlV,QAGX+T,gCAAgCF,GACtC,MAAM5L,EAAgBtK,KAAKyX,2BAA2BvB,GAChDzH,EAAoB,IAAI/C,EAAkB1L,KAAK2I,MAAMlE,QAAS6F,EAAetK,MAEnFA,KAAKgW,qBAAqBnS,IAAIqS,EAAYzH,GAE1CA,EAAkBpM,QAKZuJ,SAASsK,GACf,OAAOlW,KAAK2I,MAAM+O,QAAQC,yBAAyBzB,GAG7CuB,2BAA2BvB,GACjC,OAAOlW,KAAK2I,MAAM2I,OAAOsG,wBAAwB5X,KAAKoH,WAAY8O,GAG5DiB,qCAAqC7M,GAC3C,OAAOtK,KAAKiW,kBAAkB4B,MAAM3B,GAAelW,KAAKyX,2BAA2BvB,KAAgB5L,IAGrGwN,yBACE,MAAMC,EAAe,IAAIzL,EASzB,OAPAtM,KAAKgY,OAAOC,QAAQ1V,SAAS2V,IAEXlD,EADIkD,EAAO9C,WAAW+C,sBACwB,WAEtD5V,SAASmU,GAAWqB,EAAatX,IAAIiW,EAAQwB,EAAO9Q,iBAGvD2Q,EAGT9B,wBACE,OAAOjW,KAAK8X,mBAAmBlL,gBAAgB5M,KAAKoH,YAGtDgR,qCACE,OAAOpY,KAAK8X,mBAAmBnL,gBAAgB3M,KAAKoH,YAGtDiP,wBACE,MAAMgC,EAAcrY,KAAKoY,+BACzB,OAAOpY,KAAKgY,OAAOM,SAASrQ,QAAQf,GAAYmR,EAAYrG,SAAS9K,EAAQE,cAGvE2P,UAAUtS,EAAkByR,GAClC,QAASlW,KAAK2W,UAAUlS,EAASyR,MAAiBlW,KAAK6W,iBAAiBpS,EAASyR,GAG3ES,UAAUlS,EAAkByR,GAClC,OAAOlW,KAAKiC,YAAYsW,qCAAqC9T,EAASyR,GAGhEW,iBAAiBpS,EAAkByR,GACzC,OAAOlW,KAAK6V,cAAclJ,gBAAgBuJ,GAAY2B,MAAMnB,GAAWA,EAAOjS,UAAYA,IAG5FkE,YACE,OAAO3I,KAAKkH,QAAQyB,MAGtB2I,aACE,OAAOtR,KAAKkH,QAAQoK,OAGtBlK,iBACE,OAAOpH,KAAKkH,QAAQE,WAGtBnF,kBACE,OAAOjC,KAAKkH,QAAQjF,YAGtB+V,aACE,OAAOhY,KAAKiC,YAAY+V,cCnPfQ,EASX5Y,YAAYsY,EAAgBvP,GAmF5B3I,sBAAmB,CAACyY,EAAsBpV,EAAiB,MACzD,MAAM+D,WAAEA,EAAUO,WAAEA,EAAUlD,QAAEA,GAAYzE,KAC5CqD,EAASrC,OAAOC,OAAO,CAAEmG,WAAAA,EAAYO,WAAAA,EAAYlD,QAAAA,GAAWpB,GAC5DrD,KAAKiC,YAAYmG,iBAAiBpI,KAAKoH,WAAYqR,EAAcpV,IArFjErD,KAAKkY,OAASA,EACdlY,KAAK2I,MAAQA,EACb3I,KAAK2H,WAAa,IAAIuQ,EAAOC,sBAAsBnY,MACnDA,KAAK0Y,gBAAkB,IAAIzH,EAAgBjR,KAAMA,KAAK2Y,YACtD3Y,KAAK4Y,cAAgB,IAAIvF,EAAcrT,KAAMA,KAAK2H,YAClD3H,KAAK6Y,eAAiB,IAAIpE,EAAezU,KAAMA,MAC/CA,KAAK8Y,eAAiB,IAAIlD,EAAe5V,KAAMA,MAE/C,IACEA,KAAK2H,WAAWoR,aAChB/Y,KAAKoI,iBAAiB,cACtB,MAAOjF,GACPnD,KAAKkD,YAAYC,EAAO,4BAI5BhD,UACEH,KAAK0Y,gBAAgBrW,QACrBrC,KAAK4Y,cAAcvW,QACnBrC,KAAK6Y,eAAexW,QACpBrC,KAAK8Y,eAAezW,QAEpB,IACErC,KAAK2H,WAAWxH,UAChBH,KAAKoI,iBAAiB,WACtB,MAAOjF,GACPnD,KAAKkD,YAAYC,EAAO,0BAI5BsG,UACEzJ,KAAK8Y,eAAerP,UAGtBpJ,aACE,IACEL,KAAK2H,WAAWtH,aAChBL,KAAKoI,iBAAiB,cACtB,MAAOjF,GACPnD,KAAKkD,YAAYC,EAAO,4BAG1BnD,KAAK8Y,eAAerW,OACpBzC,KAAK6Y,eAAepW,OACpBzC,KAAK4Y,cAAcnW,OACnBzC,KAAK0Y,gBAAgBjW,OAGvBR,kBACE,OAAOjC,KAAKkY,OAAOjW,YAGrBmF,iBACE,OAAOpH,KAAKkY,OAAO9Q,WAGrBkK,aACE,OAAOtR,KAAKiC,YAAYqP,OAG1BqH,iBACE,OAAO3Y,KAAKiC,YAAY0W,WAG1BlU,cACE,OAAOzE,KAAK2I,MAAMlE,QAGpBuU,oBACE,OAAOhZ,KAAKyE,QAAQuU,cAKtB9V,YAAYC,EAAcC,EAAiBC,EAAiB,IAC1D,MAAM+D,WAAEA,EAAUO,WAAEA,EAAUlD,QAAEA,GAAYzE,KAC5CqD,EAASrC,OAAOC,OAAO,CAAEmG,WAAAA,EAAYO,WAAAA,EAAYlD,QAAAA,GAAWpB,GAC5DrD,KAAKiC,YAAYiB,YAAYC,EAAO,SAASC,IAAWC,GAa1DyR,gBAAgBrQ,EAAkBsD,GAChC/H,KAAKiZ,uBAAuB,GAAGlR,mBAAuBtD,GAGxDsQ,mBAAmBtQ,EAAkBsD,GACnC/H,KAAKiZ,uBAAuB,GAAGlR,sBAA0BtD,GAK3D4S,gBAAgBX,EAAoBjS,EAAkBsD,GACpD/H,KAAKiZ,uBAAuB,GAAG5T,EAAkB0C,oBAAwB2O,EAAQjS,GAGnF6S,mBAAmBZ,EAAoBjS,EAAkBsD,GACvD/H,KAAKiZ,uBAAuB,GAAG5T,EAAkB0C,uBAA2B2O,EAAQjS,GAKtFwU,uBAAuBrR,KAAuBsR,GAC5C,MAAMvR,EAAkB3H,KAAK2H,WACQ,mBAA1BA,EAAWC,IACpBD,EAAWC,MAAesR,aC/HhBC,EAASvZ,GACvB,OAGF,SAAmBA,EAA6BwZ,GAC9C,MAAMC,EAAoBC,EAAO1Z,GAC3B2Z,EAiBR,SAA6BxT,EAAgBqT,GAC3C,OAAOI,EAAWJ,GAAYzW,QAAO,CAAC4W,EAAkBvV,KACtD,MAAM0N,EAQV,SAA+B3L,EAAgBqT,EAAmCpV,GAChF,MAAMyV,EAAsBzY,OAAO0Y,yBAAyB3T,EAAW/B,GAEvE,IADwByV,KAAuB,UAAWA,GACpC,CACpB,MAAM/H,EAAa1Q,OAAO0Y,yBAAyBN,EAAYpV,GAAMI,MAKrE,OAJIqV,IACF/H,EAAW/N,IAAM8V,EAAoB9V,KAAO+N,EAAW/N,IACvD+N,EAAW7N,IAAM4V,EAAoB5V,KAAO6N,EAAW7N,KAElD6N,GAjBYiI,CAAsB5T,EAAWqT,EAAYpV,GAIhE,OAHI0N,GACF1Q,OAAOC,OAAOsY,EAAkB,CAAEvV,CAACA,GAAM0N,IAEpC6H,IACN,IAxBsBK,CAAoBha,EAAYmG,UAAWqT,GAEpE,OADApY,OAAO6Y,iBAAiBR,EAAkBtT,UAAWwT,GAC9CF,EAPAS,CAAOla,EAUhB,SAAiCA,GAE/B,OADkBoV,EAAiCpV,EAAa,aAC/C+C,QAAO,CAACoX,EAAmBC,KAC1C,MAAMZ,EAAaY,EAASpa,GAC5B,IAAK,MAAMoE,KAAOoV,EAAY,CAC5B,MAAM1H,EAAaqI,EAAkB/V,IAAS,GAC9C+V,EAAkB/V,GAAOhD,OAAOC,OAAOyQ,EAAY0H,EAAWpV,IAEhE,OAAO+V,IACN,IAnBwBE,CAAqBra,IA6ClD,MAAM4Z,EACuC,mBAAhCxY,OAAOkZ,sBACRtU,GAAgB,IAAI5E,OAAOmZ,oBAAoBvU,MAAY5E,OAAOkZ,sBAAsBtU,IAEzF5E,OAAOmZ,oBAIZb,EAAS,MACb,SAASc,EAA8Cxa,GACrD,SAASya,IACP,OAAOC,QAAQC,UAAU3a,EAAa4a,sBAQxC,OALAH,EAAStU,UAAY/E,OAAOyZ,OAAO7a,EAAYmG,UAAW,CACxDnG,YAAa,CAAEwE,MAAOiW,KAGxBC,QAAQI,eAAeL,EAAUza,GAC1Bya,EAYT,IAEE,OAXF,WACE,MAGMM,EAAIP,GAHA,WACRpa,KAAKmG,EAAEhF,KAAKnB,SAGd2a,EAAE5U,UAAUI,EAAI,aACT,IAAIwU,EAIXC,GACOR,EACP,MAAOjX,GACP,OAAoCvD,GAAmB,cAAuBA,MA3BnE,SCzDFib,EAMXjb,YAAYqC,EAA0BmT,GACpCpV,KAAKiC,YAAcA,EACnBjC,KAAKoV,oBCNuBA,GAC9B,MAAO,CACLhO,WAAYgO,EAAWhO,WACvB+Q,sBAAuBgB,EAAM/D,EAAW+C,wBDGtB2C,CAAgB1F,GAClCpV,KAAK+a,gBAAkB,IAAIxK,QAC3BvQ,KAAKgb,kBAAoB,IAAI9a,IAG/BkH,iBACE,OAAOpH,KAAKoV,WAAWhO,WAGzB+Q,4BACE,OAAOnY,KAAKoV,WAAW+C,sBAGzBG,eACE,OAAO9W,MAAMC,KAAKzB,KAAKgb,mBAGzBC,uBAAuBtS,GACrB,MAAMzB,EAAUlH,KAAKkb,qBAAqBvS,GAC1C3I,KAAKgb,kBAAkBva,IAAIyG,GAC3BA,EAAQ/G,UAGVgb,0BAA0BxS,GACxB,MAAMzB,EAAUlH,KAAK+a,gBAAgBpX,IAAIgF,GACrCzB,IACFlH,KAAKgb,kBAAkBra,OAAOuG,GAC9BA,EAAQ7G,cAIJ6a,qBAAqBvS,GAC3B,IAAIzB,EAAUlH,KAAK+a,gBAAgBpX,IAAIgF,GAKvC,OAJKzB,IACHA,EAAU,IAAIsR,EAAQxY,KAAM2I,GAC5B3I,KAAK+a,gBAAgBlX,IAAI8E,EAAOzB,IAE3BA,SEhDEkU,EAGXxb,YAAY+I,GACV3I,KAAK2I,MAAQA,EAGfoB,IAAIhC,GACF,OAAO/H,KAAKgU,KAAKjK,IAAI/J,KAAKqb,WAAWtT,IAGvCpE,IAAIoE,GACF,OAAO/H,KAAKsb,OAAOvT,GAAM,GAG3BuT,OAAOvT,GACL,MAAMiI,EAAchQ,KAAKgU,KAAKrQ,IAAI3D,KAAKqb,WAAWtT,KAAU,GAC5D,OAAgBiI,EtBHLlE,MAAM,YAAc,GsBMjCyP,iBAAiBxT,GACf,OAAO/H,KAAKgU,KAAKwH,uBAAuBxb,KAAKqb,WAAWtT,IAG1DsT,WAAWtT,GACT,MAAO,GAAGA,UAGZiM,WACE,OAAOhU,KAAK2I,MAAMqL,YC7BTyH,EAGX7b,YAAY+I,GACV3I,KAAK2I,MAAQA,EAGflE,cACE,OAAOzE,KAAK2I,MAAMlE,QAGpB2C,iBACE,OAAOpH,KAAK2I,MAAMvB,WAGpBzD,IAAIK,GACF,MAAM+D,EAAO/H,KAAKwb,uBAAuBxX,GACzC,OAAOhE,KAAKyE,QAAQgC,aAAasB,GAGnClE,IAAIG,EAAaI,GACf,MAAM2D,EAAO/H,KAAKwb,uBAAuBxX,GAEzC,OADAhE,KAAKyE,QAAQiX,aAAa3T,EAAM3D,GACzBpE,KAAK2D,IAAIK,GAGlB+F,IAAI/F,GACF,MAAM+D,EAAO/H,KAAKwb,uBAAuBxX,GACzC,OAAOhE,KAAKyE,QAAQoH,aAAa9D,GAGnCpH,OAAOqD,GACL,GAAIhE,KAAK+J,IAAI/F,GAAM,CACjB,MAAM+D,EAAO/H,KAAKwb,uBAAuBxX,GAEzC,OADAhE,KAAKyE,QAAQkX,gBAAgB5T,IACtB,EAEP,OAAO,EAIXyT,uBAAuBxX,GACrB,MAAO,QAAQhE,KAAKoH,cAAc3B,EAAUzB,YC3CnC4X,EAIXhc,YAAYic,GAFH7b,wBAAgD,IAAIuQ,QAG3DvQ,KAAK6b,OAASA,EAGhBC,KAAKlW,EAAa5B,EAAaZ,GAC7B,IAAI2Y,EAAsC/b,KAAKgc,mBAAmBrY,IAAIiC,GAEjEmW,IACHA,EAAa,IAAI7b,IACjBF,KAAKgc,mBAAmBnY,IAAI+B,EAAQmW,IAGjCA,EAAWhS,IAAI/F,KAClB+X,EAAWtb,IAAIuD,GACfhE,KAAK6b,OAAOC,KAAK1Y,EAASwC,cCpBhBqW,EAA4B3R,EAAuB4E,GACjE,MAAO,IAAI5E,OAAmB4E,YCEnBgN,EAGXtc,YAAY+I,GACV3I,KAAK2I,MAAQA,EAGflE,cACE,OAAOzE,KAAK2I,MAAMlE,QAGpB2C,iBACE,OAAOpH,KAAK2I,MAAMvB,WAGpBkK,aACE,OAAOtR,KAAK2I,MAAM2I,OAGpBvH,IAAIoS,GACF,OAAgC,MAAzBnc,KAAK6X,KAAKsE,GAGnBtE,QAAQuE,GACN,OAAOA,EAAYzZ,QACjB,CAAC+B,EAAQyX,IAAezX,GAAU1E,KAAKqc,WAAWF,IAAenc,KAAKsc,iBAAiBH,SACvFpI,GAIJwI,WAAWH,GACT,OAAOA,EAAYzZ,QACjB,CAAC6Z,EAASL,IAAe,IACpBK,KACAxc,KAAKyc,eAAeN,MACpBnc,KAAK0c,qBAAqBP,KAE/B,IAIIE,WAAWF,GACjB,MAAMvQ,EAAW5L,KAAK2c,yBAAyBR,GAC/C,OAAOnc,KAAK2I,MAAMiU,YAAYhR,GAGxB6Q,eAAeN,GACrB,MAAMvQ,EAAW5L,KAAK2c,yBAAyBR,GAC/C,OAAOnc,KAAK2I,MAAMkU,gBAAgBjR,GAG5B+Q,yBAAyBR,GAE/B,OAAOF,EADejc,KAAKsR,OAAOwL,wBAAwB9c,KAAKoH,YACb+U,GAG5CG,iBAAiBH,GACvB,MAAMvQ,EAAW5L,KAAK+c,+BAA+BZ,GACrD,OAAOnc,KAAKgd,UAAUhd,KAAK2I,MAAMiU,YAAYhR,GAAWuQ,GAGlDO,qBAAqBP,GAC3B,MAAMvQ,EAAW5L,KAAK+c,+BAA+BZ,GACrD,OAAOnc,KAAK2I,MAAMkU,gBAAgBjR,GAAU/I,KAAK4B,GAAYzE,KAAKgd,UAAUvY,EAAS0X,KAG/EY,+BAA+BZ,GACrC,MAAMc,EAAmB,GAAGjd,KAAKoH,cAAc+U,IAC/C,OAAOF,EAA4Bjc,KAAKsR,OAAO4L,gBAAiBD,GAG1DD,UAAavY,EAAY0X,GAC/B,GAAI1X,EAAS,CACX,MAAM2C,WAAEA,GAAepH,KACjBsK,EAAgBtK,KAAKsR,OAAO4L,gBAC5BC,EAAuBnd,KAAKsR,OAAOwL,wBAAwB1V,GACjEpH,KAAKod,MAAMtB,KACTrX,EACA,UAAU0X,IACV,kBAAkB7R,MAAkBlD,KAAc+U,WAAoBgB,MAAyBhB,WACtF7R,kFAGb,OAAO7F,EAGT2Y,YACE,OAAOpd,KAAK2I,MAAMyU,aCxFTC,EAIXzd,YAAY+I,EAAc2U,GACxBtd,KAAK2I,MAAQA,EACb3I,KAAKsd,kBAAoBA,EAG3B7Y,cACE,OAAOzE,KAAK2I,MAAMlE,QAGpB2C,iBACE,OAAOpH,KAAK2I,MAAMvB,WAGpBkK,aACE,OAAOtR,KAAK2I,MAAM2I,OAGpBvH,IAAImM,GACF,OAAgC,MAAzBlW,KAAK6X,KAAK3B,GAGnB2B,QAAQ0F,GACN,OAAOA,EAAY5a,QACjB,CAAC+T,EAAQR,IAAeQ,GAAU1W,KAAKwd,WAAWtH,SAClDnC,GAIJwI,WAAWgB,GACT,OAAOA,EAAY5a,QACjB,CAAC+U,EAASxB,IAAe,IAAIwB,KAAY1X,KAAKyd,eAAevH,KAC7D,IAIJyB,yBAAyBzB,GACvB,MAAM5L,EAAgBtK,KAAKsR,OAAOsG,wBAAwB5X,KAAKoH,WAAY8O,GAC3E,OAAOlW,KAAKsd,kBAAkB7W,aAAa6D,GAGrCkT,WAAWtH,GACjB,MAAMtK,EAAW5L,KAAK2X,yBAAyBzB,GAC/C,GAAItK,EAAU,OAAO5L,KAAK4c,YAAYhR,EAAUsK,GAG1CuH,eAAevH,GACrB,MAAMtK,EAAW5L,KAAK2X,yBAAyBzB,GAC/C,OAAOtK,EAAW5L,KAAK6c,gBAAgBjR,EAAUsK,GAAc,GAGzD0G,YAAYhR,EAAkBsK,GAEpC,OADiBlW,KAAK2I,MAAM+U,cAAc9R,GAC1B3D,QAAQxD,GAAYzE,KAAK2d,eAAelZ,EAASmH,EAAUsK,KAAa,GAGlF2G,gBAAgBjR,EAAkBsK,GAExC,OADiBlW,KAAK2I,MAAM+U,cAAc9R,GAC1B3D,QAAQxD,GAAYzE,KAAK2d,eAAelZ,EAASmH,EAAUsK,KAGrEyH,eAAelZ,EAAkBmH,EAAkBsK,GACzD,MAAMe,EAAsBxS,EAAQgC,aAAazG,KAAK2I,MAAM2I,OAAO2F,sBAAwB,GAC3F,OAAOxS,EAAQoF,QAAQ+B,IAAaqL,EAAoB/G,MAAM,KAAK8B,SAASkE,UC3DnE0H,EAUXhe,YAAY0R,EAAgB7M,EAAkB2C,EAAoByU,GAJzD7b,aAAU,IAAIkc,EAAUlc,MACxBA,aAAU,IAAIob,EAASpb,MACvBA,UAAO,IAAIyb,EAAQzb,MAqB5BA,qBAAmByE,GACVA,EAAQoZ,QAAQ7d,KAAK8d,sBAAwB9d,KAAKyE,QAnBzDzE,KAAKsR,OAASA,EACdtR,KAAKyE,QAAUA,EACfzE,KAAKoH,WAAaA,EAClBpH,KAAKod,MAAQ,IAAIxB,EAAMC,GACvB7b,KAAK0X,QAAU,IAAI2F,EAAUrd,KAAK+d,cAAetZ,GAGnDmY,YAAYhR,GACV,OAAO5L,KAAKyE,QAAQoF,QAAQ+B,GAAY5L,KAAKyE,QAAUzE,KAAK0d,cAAc9R,GAAUiM,KAAK7X,KAAK4I,iBAGhGiU,gBAAgBjR,GACd,MAAO,IACD5L,KAAKyE,QAAQoF,QAAQ+B,GAAY,CAAC5L,KAAKyE,SAAW,MACnDzE,KAAK0d,cAAc9R,GAAU3D,OAAOjI,KAAK4I,kBAQhD8U,cAAc9R,GACZ,OAAOpK,MAAMC,KAAKzB,KAAKyE,QAAQsH,iBAAiBH,IAGlDkS,yBACE,OAAO7B,EAA4Bjc,KAAKsR,OAAO2F,oBAAqBjX,KAAKoH,YAG3E4W,sBACE,OAAOhe,KAAKyE,UAAYM,SAASkZ,gBAGnCF,oBACE,OAAO/d,KAAKge,gBACRhe,KACA,IAAI4d,EAAM5d,KAAKsR,OAAQvM,SAASkZ,gBAAiBje,KAAKoH,WAAYpH,KAAKod,MAAMvB,eC9CxEqC,EAQXte,YAAY6E,EAAkB6M,EAAgBxI,GAC5C9I,KAAKyE,QAAUA,EACfzE,KAAKsR,OAASA,EACdtR,KAAK8I,SAAWA,EAChB9I,KAAKmR,kBAAoB,IAAIf,EAAkBpQ,KAAKyE,QAASzE,KAAKiX,oBAAqBjX,MACvFA,KAAKme,4BAA8B,IAAI5N,QACvCvQ,KAAKoe,qBAAuB,IAAI7N,QAGlClO,QACErC,KAAKmR,kBAAkB9O,QAGzBI,OACEzC,KAAKmR,kBAAkB1O,OAGzBwU,0BACE,OAAOjX,KAAKsR,OAAO2F,oBAKrBjG,mBAAmB9B,GACjB,MAAMzK,QAAEA,EAASsL,QAAS3I,GAAe8H,EACzC,OAAOlP,KAAKqe,kCAAkC5Z,EAAS2C,GAGzDiX,kCAAkC5Z,EAAkB2C,GAClD,MAAMkX,EAAqBte,KAAKue,kCAAkC9Z,GAElE,IAAIkE,EAAQ2V,EAAmB3a,IAAIyD,GAMnC,OALKuB,IACHA,EAAQ3I,KAAK8I,SAAS0V,mCAAmC/Z,EAAS2C,GAClEkX,EAAmBza,IAAIuD,EAAYuB,IAG9BA,EAGTgI,oBAAoBlM,EAAkBL,GACpC,MAAMqa,GAAkBze,KAAKoe,qBAAqBza,IAAIS,IAAU,GAAK,EACrEpE,KAAKoe,qBAAqBva,IAAIO,EAAOqa,GACf,GAAlBA,GACFze,KAAK8I,SAAS4V,eAAeta,GAIjCwM,sBAAsBnM,EAAkBL,GACtC,MAAMqa,EAAiBze,KAAKoe,qBAAqBza,IAAIS,GACjDqa,IACFze,KAAKoe,qBAAqBva,IAAIO,EAAOqa,EAAiB,GAChC,GAAlBA,GACFze,KAAK8I,SAAS6V,kBAAkBva,IAK9Bma,kCAAkC9Z,GACxC,IAAI6Z,EAAqBte,KAAKme,4BAA4Bxa,IAAIc,GAK9D,OAJK6Z,IACHA,EAAqB,IAAInc,IACzBnC,KAAKme,4BAA4Bta,IAAIY,EAAS6Z,IAEzCA,SC3EEM,EAMXhf,YAAYqC,GACVjC,KAAKiC,YAAcA,EACnBjC,KAAK6e,cAAgB,IAAIX,EAAcle,KAAKyE,QAASzE,KAAKsR,OAAQtR,MAClEA,KAAKse,mBAAqB,IAAIhS,EAC9BtM,KAAK8e,oBAAsB,IAAI3c,IAGjCsC,cACE,OAAOzE,KAAKiC,YAAYwC,QAG1B6M,aACE,OAAOtR,KAAKiC,YAAYqP,OAG1BuK,aACE,OAAO7b,KAAKiC,YAAY4Z,OAG1B5E,0BACE,OAAOjX,KAAKsR,OAAO2F,oBAGrBgB,cACE,OAAOzW,MAAMC,KAAKzB,KAAK8e,oBAAoBpc,UAG7C4V,eACE,OAAOtY,KAAKiY,QAAQtV,QAAO,CAAC2V,EAAUJ,IAAWI,EAASxV,OAAOoV,EAAOI,WAAW,IAGrFjW,QACErC,KAAK6e,cAAcxc,QAGrBI,OACEzC,KAAK6e,cAAcpc,OAGrBsc,eAAe3J,GACbpV,KAAKgf,iBAAiB5J,EAAWhO,YACjC,MAAM8Q,EAAS,IAAI2C,EAAO7a,KAAKiC,YAAamT,GAC5CpV,KAAKif,cAAc/G,GACnB,MAAMgH,EAAa9J,EAAW+C,sBAA8B+G,UACxDA,GACFA,EAAU/d,KAAKiU,EAAW+C,sBAAuB/C,EAAWhO,WAAYpH,KAAKiC,aAIjF+c,iBAAiB5X,GACf,MAAM8Q,EAASlY,KAAK8e,oBAAoBnb,IAAIyD,GACxC8Q,GACFlY,KAAKmf,iBAAiBjH,GAI1BkH,kCAAkC3a,EAAkB2C,GAClD,MAAM8Q,EAASlY,KAAK8e,oBAAoBnb,IAAIyD,GAC5C,GAAI8Q,EACF,OAAOA,EAAOI,SAAST,MAAM3Q,GAAYA,EAAQzC,SAAWA,IAIhE4a,6CAA6C5a,EAAkB2C,GAC7D,MAAMuB,EAAQ3I,KAAK6e,cAAcR,kCAAkC5Z,EAAS2C,GAExEuB,EACF3I,KAAK6e,cAAclO,oBAAoBhI,EAAMlE,QAASkE,GAEtD2W,QAAQnc,MAAM,kDAAkDiE,kBAA4B3C,GAMhGvB,YAAYC,EAAcC,EAAiBC,GACzCrD,KAAKiC,YAAYiB,YAAYC,EAAOC,EAASC,GAK/Cmb,mCAAmC/Z,EAAkB2C,GACnD,OAAO,IAAIwW,EAAM5d,KAAKsR,OAAQ7M,EAAS2C,EAAYpH,KAAK6b,QAG1D6C,eAAe/V,GACb3I,KAAKse,mBAAmB7d,IAAIkI,EAAMvB,WAAYuB,GAC9C,MAAMuP,EAASlY,KAAK8e,oBAAoBnb,IAAIgF,EAAMvB,YAC9C8Q,GACFA,EAAO+C,uBAAuBtS,GAIlCgW,kBAAkBhW,GAChB3I,KAAKse,mBAAmB3d,OAAOgI,EAAMvB,WAAYuB,GACjD,MAAMuP,EAASlY,KAAK8e,oBAAoBnb,IAAIgF,EAAMvB,YAC9C8Q,GACFA,EAAOiD,0BAA0BxS,GAM7BsW,cAAc/G,GACpBlY,KAAK8e,oBAAoBjb,IAAIqU,EAAO9Q,WAAY8Q,GACjClY,KAAKse,mBAAmB3R,gBAAgBuL,EAAO9Q,YACvD7E,SAASoG,GAAUuP,EAAO+C,uBAAuBtS,KAGlDwW,iBAAiBjH,GACvBlY,KAAK8e,oBAAoBne,OAAOuX,EAAO9Q,YACxBpH,KAAKse,mBAAmB3R,gBAAgBuL,EAAO9Q,YACvD7E,SAASoG,GAAUuP,EAAOiD,0BAA0BxS,YCrHlD4W,EAAwB,CACnCtI,oBAAqB,kBACrB7F,gBAAiB,cACjB8L,gBAAiB,cACjBJ,wBAA0B1V,GAAe,QAAQA,WACjDwQ,wBAAyB,CAACxQ,EAAYsP,IAAW,QAAQtP,KAAcsP,WACvEjE,yCACE+M,MAAO,QACPC,IAAK,MACLC,IAAK,SACLC,MAAO,IACPC,GAAI,UACJC,KAAM,YACNle,KAAM,YACNC,MAAO,aACPke,KAAM,OACNC,IAAK,MACLC,QAAS,SACTC,UAAW,YAERC,EAAkB,6BAA6BhQ,MAAM,IAAIrN,KAAKsd,GAAM,CAACA,EAAGA,OAExED,EAAkB,aAAahQ,MAAM,IAAIrN,KAAKud,GAAM,CAACA,EAAGA,QAI/D,SAASF,EAAkBG,GAEzB,OAAOA,EAAM1d,QAAO,CAAC2d,GAAOC,EAAGC,oCAAaF,IAAMC,CAACA,GAAIC,KAAM,UC5BlDC,EAeX7gB,YAAY6E,EAAmBM,SAASkZ,gBAAiB3M,EAAiBiO,GAT1Evf,YAAiBsf,QACjBtf,YAAQ,EA8ERA,sBAAmB,CAACoH,EAAoBqR,EAAsBpV,EAAiB,MACzErD,KAAK0gB,OACP1gB,KAAK2gB,oBAAoBvZ,EAAYqR,EAAcpV,IAvErDrD,KAAKyE,QAAUA,EACfzE,KAAKsR,OAASA,EACdtR,KAAK2Y,WAAa,IAAI3W,EAAWhC,MACjCA,KAAKgY,OAAS,IAAI4G,EAAO5e,MACzBA,KAAK6H,yCAA+B1D,GAXtC9B,aAAaoC,EAAmB6M,GAC9B,MAAMrP,EAAc,IAAIjC,KAAKyE,EAAS6M,GAEtC,OADArP,EAAYI,QACLJ,EAWTI,oBA8EO,IAAIue,SAAeC,IACG,WAAvB9b,SAAS+b,WACX/b,SAAS3E,iBAAiB,oBAAoB,IAAMygB,MAEpDA,OAhFF7gB,KAAKoI,iBAAiB,cAAe,YACrCpI,KAAK2Y,WAAWtW,QAChBrC,KAAKgY,OAAO3V,QACZrC,KAAKoI,iBAAiB,cAAe,SAGvC3F,OACEzC,KAAKoI,iBAAiB,cAAe,YACrCpI,KAAK2Y,WAAWlW,OAChBzC,KAAKgY,OAAOvV,OACZzC,KAAKoI,iBAAiB,cAAe,QAGvC2Y,SAAS3Z,EAAoB+Q,GAC3BnY,KAAKghB,KAAK,CAAE5Z,WAAAA,EAAY+Q,sBAAAA,IAG1B8I,qBAAqBlZ,EAAcE,GACjCjI,KAAK6H,wBAAwBE,GAAQE,EAKvC+Y,KAAKE,KAAoCC,IACnB3f,MAAM6T,QAAQ6L,GAAQA,EAAO,CAACA,KAASC,IAC/C5e,SAAS6S,IACdA,EAAW+C,sBAA8BiJ,YAC5CphB,KAAKgY,OAAO+G,eAAe3J,MAOjCiM,OAAOH,KAA4BC,IACb3f,MAAM6T,QAAQ6L,GAAQA,EAAO,CAACA,KAASC,IAC/C5e,SAAS6E,GAAepH,KAAKgY,OAAOgH,iBAAiB5X,KAKnEka,kBACE,OAAOthB,KAAKgY,OAAOM,SAASzV,KAAKqE,GAAYA,EAAQS,aAGvD4Q,qCAAqC9T,EAAkB2C,GACrD,MAAMF,EAAUlH,KAAKgY,OAAOoH,kCAAkC3a,EAAS2C,GACvE,OAAOF,EAAUA,EAAQS,WAAa,KAKxCzE,YAAYC,EAAcC,EAAiBC,SACzCrD,KAAK6b,OAAO1Y,MAAM,iBAAkBC,EAASD,EAAOE,aAEpDyB,OAAOyc,oCAAUne,EAAS,GAAI,EAAG,EAAGD,GAW9Bwd,oBAAoBvZ,EAAoBqR,EAAsBpV,EAAiB,IACrFA,EAASrC,OAAOC,OAAO,CAAEgB,YAAajC,MAAQqD,GAE9CrD,KAAK6b,OAAO2F,eAAe,GAAGpa,MAAeqR,KAC7CzY,KAAK6b,OAAO4F,IAAI,4BAAiBpe,IACjCrD,KAAK6b,OAAO6F,YC7FhB,SAASC,EAAoBha,EAAwBlD,EAAkB2C,GACrE,OAAOO,EAAW1F,YAAYsW,qCAAqC9T,EAAS2C,GAG9E,SAASwa,EAAqCja,EAAwBlD,EAAkByR,GACtF,IAAI2L,EAAmBF,EAAoBha,EAAYlD,EAASyR,GAChE,OAAI2L,IAEJla,EAAW1F,YAAY+V,OAAOqH,6CAA6C5a,EAASyR,GAEpF2L,EAAmBF,EAAoBha,EAAYlD,EAASyR,GACxD2L,QAAJ,GC+DF,SAASC,IAA0B5S,EAAO6S,GAAsCpa,GAC9E,OAiHF,SAAkDqa,GAChD,MAAM9S,MAAEA,EAAK6S,eAAEA,GAAmBC,EAE5Bhe,EAAM,GAAGyB,EAAUyJ,WACnB9E,WA1CiC4X,GACvC,MAAMra,WAAEA,EAAUuH,MAAEA,EAAK6S,eAAEA,GAAmBC,EAIxCC,WAtC6BD,GACnC,MAAMra,WAAEA,EAAUuH,MAAEA,EAAKgT,WAAEA,GAAeF,EAEpCG,EAAUxc,EAAYuc,EAAW9X,MACjCgY,EAAazc,EAAYuc,EAAWG,SAEpCC,EAAaH,GAAWC,EACxBG,EAAWJ,IAAYC,EACvBI,GAAeL,GAAWC,EAE1BH,EAAiBQ,GAAuBP,EAAW9X,MACnDsY,EAAuBC,GAAsBX,EAAQE,WAAWG,SAEtE,GAAIE,EAAU,OAAON,EACrB,GAAIO,EAAa,OAAOE,EAExB,GAAIT,IAAmBS,EAAsB,CAG3C,MAAM,IAAI9b,MACR,uDAHmBe,EAAa,GAAGA,KAAcuH,IAAUA,mCAG0C+S,sCAAmDC,EAAWG,wBAAwBK,OAI/L,GAAIJ,EAAY,OAAOL,EAcAW,CAFJ,CAAEjb,WAAAA,EAAYuH,MAAAA,EAAOgT,WAAYH,IAG9CW,EAAuBC,GAAsBZ,GAC7Cc,EAAmBJ,GAAuBV,GAE1C3X,EAAO6X,GAAkBS,GAAwBG,EAEvD,GAAIzY,EAAM,OAAOA,EAIjB,MAAM,IAAIxD,MAAM,uBAFKe,EAAa,GAAGA,KAAcoa,IAAmB7S,WAETA,YA2BhD4T,CAAyBd,GACtC,MAAO,CACL5X,KAAAA,EACApG,IAAAA,EACA+D,KAAM/C,EAAShB,GACf4P,mBACE,gBA9BoCmO,GACxC,MAAMgB,EAAWN,GAAuBV,GACxC,GAAIgB,EAAU,OAAOC,GAAoBD,GAEzC,MAAMX,EAAavc,EAAYkc,EAAgB,WACzCI,EAAUtc,EAAYkc,EAAgB,QACtCG,EAAaH,EAEnB,GAAIK,EAAY,OAAOF,EAAWG,QAElC,GAAIF,EAAS,CACX,MAAM/X,KAAEA,GAAS8X,EACXe,EAAmBR,GAAuBrY,GAEhD,GAAI6Y,EAAkB,OAAOD,GAAoBC,GAGnD,OAAOlB,EAaImB,CAA0BnB,IAEnCoB,4BACE,YAAiDpP,IAA1C4O,GAAsBZ,IAE/B1N,OAAQ+O,GAAQhZ,GAChBuJ,OAAQ0P,GAAQjZ,IAASiZ,GAAQhB,SAjI5BiB,CAAyC,CAC9C3b,WAAAA,EACAuH,MAAAA,EACA6S,eAAAA,aAIYU,GAAuBM,GACrC,OAAQA,GACN,KAAKvhB,MACH,MAAO,QACT,KAAK+hB,QACH,MAAO,UACT,KAAKC,OACH,MAAO,SACT,KAAKxiB,OACH,MAAO,SACT,KAAKyiB,OACH,MAAO,mBAIGd,GAAsB/O,GACpC,cAAeA,GACb,IAAK,UACH,MAAO,UACT,IAAK,SACH,MAAO,SACT,IAAK,SACH,MAAO,SAGX,OAAIpS,MAAM6T,QAAQzB,GAAsB,QACa,oBAAjD5S,OAAO+E,UAAUqM,SAASjR,KAAKyS,GAA4C,cAA/E,EAoGF,MAAMoP,GAAsB,CAC1B3C,YACE,MAAO,IAETqD,SAAS,EACTC,OAAQ,EACR/d,aACE,MAAO,IAETge,OAAQ,IAKJR,GAAsC,CAC1C/C,MAAMjc,GACJ,MAAMic,EAAQvZ,KAAKC,MAAM3C,GACzB,IAAK5C,MAAM6T,QAAQgL,GACjB,MAAM,IAAI/L,UACR,yDAAyDlQ,eAAmBue,GAAsBtC,OAGtG,OAAOA,GAGTqD,QAAQtf,KACY,KAATA,GAA+C,SAA/Bqf,OAAOrf,GAAOsB,eAGzCie,OAAOvf,GACEof,OAAOpf,EAAMa,QAAQ,KAAM,KAGpCW,OAAOxB,GACL,MAAMwB,EAASkB,KAAKC,MAAM3C,GAC1B,GAAe,OAAXwB,GAAoC,iBAAVA,GAAsBpE,MAAM6T,QAAQzP,GAChE,MAAM,IAAI0O,UACR,0DAA0DlQ,eAAmBue,GAAsB/c,OAGvG,OAAOA,GAGTge,OAAOxf,GACEA,GAMLif,GAAsC,CAC1ChB,QASF,SAAqBje,GACnB,MAAO,GAAGA,KATVic,MAAOwD,GACPje,OAAQie,IAGV,SAASA,GAAUzf,GACjB,OAAO0C,KAAKgd,UAAU1f,SCnQX2f,GAqBXnkB,YAAYsH,GACVlH,KAAKkH,QAAUA,EAXjBka,wBACE,OAAO,EAGTlC,iBAAiB8E,EAAqBC,IAUtChiB,kBACE,OAAOjC,KAAKkH,QAAQjF,YAGtB0G,YACE,OAAO3I,KAAKkH,QAAQyB,MAGtBlE,cACE,OAAOzE,KAAK2I,MAAMlE,QAGpB2C,iBACE,OAAOpH,KAAK2I,MAAMvB,WAGpBoV,cACE,OAAOxc,KAAK2I,MAAM6T,QAGpB9E,cACE,OAAO1X,KAAK2I,MAAM+O,QAGpBwM,cACE,OAAOlkB,KAAK2I,MAAMub,QAGpBlQ,WACE,OAAOhU,KAAK2I,MAAMqL,KAGpB+E,cAIA5Y,WAIAE,cAIA8jB,SACErkB,GACA4E,OACEA,EAAS1E,KAAKyE,QAAOpB,OACrBA,EAAS,GAAE+gB,OACXA,EAASpkB,KAAKoH,WAAUid,QACxBA,GAAU,EAAIC,WACdA,GAAa,GACM,IAErB,MACMzjB,EAAQ,IAAI0jB,YADLH,EAAS,GAAGA,KAAUtkB,IAAcA,EACb,CAAEuD,OAAAA,EAAQghB,QAAAA,EAASC,WAAAA,IAEvD,OADA5f,EAAO8f,cAAc3jB,GACdA,GAjFFkjB,aAAY,UCdsBnkB,GAEzC,OADgBoV,EAAiCpV,EAAa,WAC/C+C,QAAO,CAACyW,EAAYqL,KACjC,OAAOzjB,OAAOC,OAAOmY,EAKhB,CACL,CAAC,GAFiCpV,EAJ4BygB,UAM7C,CACf9gB,MACE,MAAMugB,QAAEA,GAAYlkB,KACpB,GAAIkkB,EAAQna,IAAI/F,GACd,OAAOkgB,EAAQvgB,IAAIK,GACd,CACL,MAAMuK,EAAY2V,EAAQ3I,iBAAiBvX,GAC3C,MAAM,IAAI4C,MAAM,sBAAsB2H,SAK5C,CAAC,GAAGvK,YAAe,CACjBL,MACE,OAAO3D,KAAKkkB,QAAQ5I,OAAOtX,KAI/B,CAAC,MAAMsB,EAAWtB,WAAc,CAC9BL,MACE,OAAO3D,KAAKkkB,QAAQna,IAAI/F,OAtBhC,IAAsCA,IAHjC,cCJuCpE,GAE1C,OADgBoV,EAAiCpV,EAAa,WAC/C+C,QAAO,CAACyW,EAAYsL,KACjC,OAAO1jB,OAAOC,OAAOmY,EAKhB,CACL,CAAC,GAFkCrR,EAJ4B2c,WAM5C,CACjB/gB,MACE,MAAMe,EAAS1E,KAAKwc,QAAQ3E,KAAK9P,GACjC,GAAIrD,EACF,OAAOA,EAEP,MAAM,IAAIkC,MAAM,2BAA2BmB,WAAc/H,KAAKoH,4BAKpE,CAAC,GAAGW,YAAgB,CAClBpE,MACE,OAAO3D,KAAKwc,QAAQD,QAAQxU,KAIhC,CAAC,MAAMzC,EAAWyC,YAAgB,CAChCpE,MACE,OAAO3D,KAAKwc,QAAQzS,IAAIhC,OArBhC,IAAuCA,IAHlC,cHHsCnI,GACzC,MAAM+kB,EAAuBpP,EAAyD3V,EAAa,UAC7FglB,EAA+C,CACnDpR,mBAAoB,CAClB7P,MACE,OAAOghB,EAAqBhiB,QAAO,CAACkiB,EAAQC,KAC1C,MAAMC,EAAkBjD,GAAyBgD,EAAqB9kB,KAAKoH,YACrEkD,EAAgBtK,KAAKgU,KAAKwH,uBAAuBuJ,EAAgB/gB,KACvE,OAAOhD,OAAOC,OAAO4jB,EAAQ,CAAEva,CAACA,GAAgBya,MAC/C,OAKT,OAAOJ,EAAqBhiB,QAAO,CAACyW,EAAY0L,IACvC9jB,OAAOC,OAAOmY,WAKvB0L,EACAnd,GAEA,MAAMyN,EAAa0M,GAAyBgD,EAAqBnd,IAC3D3D,IAAEA,EAAG+D,KAAEA,EAAMsM,OAAQ2Q,EAAMrR,OAAQsR,GAAU7P,EAEnD,MAAO,CACLrN,CAACA,GAAO,CACNpE,MACE,MAAMS,EAAQpE,KAAKgU,KAAKrQ,IAAIK,GAC5B,OAAc,OAAVI,EACK4gB,EAAK5gB,GAELgR,EAAWxB,cAItB/P,IAAsBO,QACN2P,IAAV3P,EACFpE,KAAKgU,KAAKrT,OAAOqD,GAEjBhE,KAAKgU,KAAKnQ,IAAIG,EAAKihB,EAAM7gB,MAK/B,CAAC,MAAMkB,EAAWyC,MAAU,CAC1BpE,MACE,OAAO3D,KAAKgU,KAAKjK,IAAI/F,IAAQoR,EAAW+N,yBAjCX+B,CAAiCJ,KACjEF,aDjBuChlB,GAE1C,OADgBoV,EAAiCpV,EAAa,WAC/C+C,QAAO,CAACyW,EAAiB+L,IAC/BnkB,OAAOC,OAAOmY,EAkBzB,SAAuCrR,GACrC,MAAMqd,EAAgB/f,EAAkB0C,GAExC,MAAO,CACL,CAAC,GAAGqd,WAAwB,CAC1BzhB,MACE,MAAM0hB,EAAgBrlB,KAAK0X,QAAQG,KAAK9P,GAClC6D,EAAW5L,KAAK0X,QAAQC,yBAAyB5P,GAEvD,GAAIsd,EAAe,CACjB,MAAMxD,EAAmBD,EAAqC5hB,KAAMqlB,EAAetd,GAEnF,GAAI8Z,EAAkB,OAAOA,EAE7B,MAAM,IAAIjb,MACR,gEAAgEmB,oCAAuC/H,KAAKoH,eAIhH,MAAM,IAAIR,MACR,2BAA2BmB,2BAA8B/H,KAAKoH,iFAAiFwE,SAKrJ,CAAC,GAAGwZ,YAAyB,CAC3BzhB,MACE,MAAM+T,EAAU1X,KAAK0X,QAAQ6E,QAAQxU,GAErC,OAAI2P,EAAQlI,OAAS,EACZkI,EACJ7U,KAAKwiB,IACJ,MAAMxD,EAAmBD,EAAqC5hB,KAAMqlB,EAAetd,GAEnF,GAAI8Z,EAAkB,OAAOA,EAE7BvC,QAAQxD,KACN,gEAAgE/T,oCAAuC/H,KAAKoH,cAC5Gie,MAGHpd,QAAQN,GAAeA,IAGrB,KAIX,CAAC,GAAGyd,kBAA+B,CACjCzhB,MACE,MAAM0hB,EAAgBrlB,KAAK0X,QAAQG,KAAK9P,GAClC6D,EAAW5L,KAAK0X,QAAQC,yBAAyB5P,GAEvD,GAAIsd,EACF,OAAOA,EAEP,MAAM,IAAIze,MACR,2BAA2BmB,2BAA8B/H,KAAKoH,iFAAiFwE,SAMvJ,CAAC,GAAGwZ,mBAAgC,CAClCzhB,MACE,OAAO3D,KAAK0X,QAAQ6E,QAAQxU,KAIhC,CAAC,MAAMzC,EAAW8f,YAAyB,CACzCzhB,MACE,OAAO3D,KAAK0X,QAAQ3N,IAAIhC,MAzFKud,CAA8BH,KAC9D,MEgBIpB,WAAoB,GACpBA,WAAoB,GACpBA,UAA6B"}