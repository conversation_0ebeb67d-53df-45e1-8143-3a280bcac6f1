/* DataFlow Pro Design System - Direct from enhanced-data-reflow-platform */

:root {
  /* Colors */
  --color-background: rgba(252, 252, 249, 1);
  --color-surface: rgba(255, 255, 253, 1);
  --color-text: rgba(19, 52, 59, 1);
  --color-text-secondary: rgba(98, 108, 113, 1);
  --color-primary: rgba(33, 128, 141, 1);
  --color-primary-hover: rgba(29, 116, 128, 1);
  --color-primary-active: rgba(26, 104, 115, 1);
  --color-secondary: rgba(94, 82, 64, 0.12);
  --color-secondary-hover: rgba(94, 82, 64, 0.2);
  --color-secondary-active: rgba(94, 82, 64, 0.25);
  --color-border: rgba(94, 82, 64, 0.2);
  --color-btn-primary-text: rgba(252, 252, 249, 1);
  --color-card-border: rgba(94, 82, 64, 0.12);
  --color-card-border-inner: rgba(94, 82, 64, 0.12);
  --color-error: rgba(192, 21, 47, 1);
  --color-success: rgba(33, 128, 141, 1);
  --color-warning: rgba(168, 75, 47, 1);
  --color-info: rgba(98, 108, 113, 1);
  --color-focus-ring: rgba(33, 128, 141, 0.4);
  --color-select-caret: rgba(19, 52, 59, 0.8);

  /* RGB versions for opacity control */
  --color-primary-rgb: 33, 128, 141;
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;

  /* Common style patterns */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* Typography */
  --font-family-base: "FKGroteskNeue", "Geist", "Inter", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: "Berkeley Mono", ui-monospace, SFMono-Regular, Menlo,
    Monaco, Consolas, monospace;
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
  --font-size-4xl: 30px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 550;
  --font-weight-bold: 600;
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --letter-spacing-tight: -0.01em;

  /* Spacing */
  --space-0: 0;
  --space-1: 1px;
  --space-2: 2px;
  --space-4: 4px;
  --space-6: 6px;
  --space-8: 8px;
  --space-10: 10px;
  --space-12: 12px;
  --space-16: 16px;
  --space-20: 20px;
  --space-24: 24px;
  --space-32: 32px;

  /* Border Radius */
  --radius-sm: 6px;
  --radius-base: 8px;
  --radius-md: 10px;
  --radius-lg: 12px;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.04),
    0 2px 4px -1px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.04),
    0 4px 6px -2px rgba(0, 0, 0, 0.02);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.03);

  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --ease-standard: cubic-bezier(0.16, 1, 0.3, 1);

  /* Layout */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
}

/* Dark mode colors */
@media (prefers-color-scheme: dark) {
  :root {
    --color-background: rgba(31, 33, 33, 1);
    --color-surface: rgba(38, 40, 40, 1);
    --color-text: rgba(245, 245, 245, 1);
    --color-text-secondary: rgba(167, 169, 169, 0.7);
    --color-primary: rgba(50, 184, 198, 1);
    --color-primary-hover: rgba(45, 166, 178, 1);
    --color-primary-active: rgba(41, 150, 161, 1);
    --color-secondary: rgba(119, 124, 124, 0.15);
    --color-secondary-hover: rgba(119, 124, 124, 0.25);
    --color-secondary-active: rgba(119, 124, 124, 0.3);
    --color-border: rgba(119, 124, 124, 0.3);
    --color-error: rgba(255, 84, 89, 1);
    --color-success: rgba(50, 184, 198, 1);
    --color-warning: rgba(230, 129, 97, 1);
    --color-info: rgba(167, 169, 169, 1);
    --color-focus-ring: rgba(50, 184, 198, 0.4);
    --color-btn-primary-text: rgba(19, 52, 59, 1);
    --color-card-border: rgba(119, 124, 124, 0.2);
    --color-card-border-inner: rgba(119, 124, 124, 0.15);
    --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    --button-border-secondary: rgba(119, 124, 124, 0.2);
    --color-border-secondary: rgba(119, 124, 124, 0.2);
    --color-select-caret: rgba(245, 245, 245, 0.8);

    /* RGB versions for dark mode */
    --color-success-rgb: 50, 184, 198;
    --color-error-rgb: 255, 84, 89;
    --color-warning-rgb: 230, 129, 97;
    --color-info-rgb: 167, 169, 169;
  }
}

/* Data attribute for manual theme switching */
[data-color-scheme="dark"] {
  --color-background: rgba(31, 33, 33, 1);
  --color-surface: rgba(38, 40, 40, 1);
  --color-text: rgba(245, 245, 245, 1);
  --color-text-secondary: rgba(167, 169, 169, 0.7);
  --color-primary: rgba(50, 184, 198, 1);
  --color-primary-hover: rgba(45, 166, 178, 1);
  --color-primary-active: rgba(41, 150, 161, 1);
  --color-secondary: rgba(119, 124, 124, 0.15);
  --color-secondary-hover: rgba(119, 124, 124, 0.25);
  --color-secondary-active: rgba(119, 124, 124, 0.3);
  --color-border: rgba(119, 124, 124, 0.3);
  --color-error: rgba(255, 84, 89, 1);
  --color-success: rgba(50, 184, 198, 1);
  --color-warning: rgba(230, 129, 97, 1);
  --color-info: rgba(167, 169, 169, 1);
  --color-focus-ring: rgba(50, 184, 198, 0.4);
  --color-btn-primary-text: rgba(19, 52, 59, 1);
  --color-card-border: rgba(119, 124, 124, 0.2);
  --color-card-border-inner: rgba(119, 124, 124, 0.15);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  --button-border-secondary: rgba(119, 124, 124, 0.2);
  --color-border-secondary: rgba(119, 124, 124, 0.2);
  --color-select-caret: rgba(245, 245, 245, 0.8);

  /* RGB versions for dark mode */
  --color-primary-rgb: 50, 184, 198;
  --color-success-rgb: 50, 184, 198;
  --color-error-rgb: 255, 84, 89;
  --color-warning-rgb: 230, 129, 97;
  --color-info-rgb: 167, 169, 169;
}

[data-color-scheme="light"] {
  --color-background: rgba(252, 252, 249, 1);
  --color-surface: rgba(255, 255, 253, 1);
  --color-text: rgba(19, 52, 59, 1);
  --color-text-secondary: rgba(98, 108, 113, 1);
  --color-primary: rgba(33, 128, 141, 1);
  --color-primary-hover: rgba(29, 116, 128, 1);
  --color-primary-active: rgba(26, 104, 115, 1);
  --color-secondary: rgba(94, 82, 64, 0.12);
  --color-secondary-hover: rgba(94, 82, 64, 0.2);
  --color-secondary-active: rgba(94, 82, 64, 0.25);
  --color-border: rgba(94, 82, 64, 0.2);
  --color-btn-primary-text: rgba(252, 252, 249, 1);
  --color-card-border: rgba(94, 82, 64, 0.12);
  --color-card-border-inner: rgba(94, 82, 64, 0.12);
  --color-error: rgba(192, 21, 47, 1);
  --color-success: rgba(33, 128, 141, 1);
  --color-warning: rgba(168, 75, 47, 1);
  --color-info: rgba(98, 108, 113, 1);
  --color-focus-ring: rgba(33, 128, 141, 0.4);

  /* RGB versions for light mode */
  --color-primary-rgb: 33, 128, 141;
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;
}

/* Base styles */
html {
  font-size: var(--font-size-base);
  font-family: var(--font-family-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

/* Theme transition for smooth switching */
.theme-transition,
.theme-transition *,
.theme-transition *:before,
.theme-transition *:after {
  transition: background var(--duration-normal) var(--ease-standard),
              color var(--duration-normal) var(--ease-standard),
              border-color var(--duration-normal) var(--ease-standard),
              box-shadow var(--duration-normal) var(--ease-standard) !important;
}

.dataflow-pro {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  min-height: 100vh;
  display: flex;
}

/* Sidebar Navigation */
.sidebar {
  width: 280px;
  background-color: var(--color-surface);
  border-right: 1px solid var(--color-border);
  transition: transform var(--duration-normal) var(--ease-standard);
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  overflow-y: auto;
  z-index: 1000;
}

.sidebar-header {
  padding: var(--space-24) var(--space-20);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-header h2 {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
}

.sidebar-toggle {
  display: none;
  background: none;
  border: none;
  padding: var(--space-8);
  cursor: pointer;
  color: var(--color-text);
}

.nav-menu {
  list-style: none;
  margin: 0;
  padding: var(--space-16) 0;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: var(--space-12) var(--space-20);
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
  text-decoration: none;
  border-left: 3px solid transparent;
}

.nav-item:hover {
  background-color: var(--color-secondary);
  color: var(--color-text);
}

.nav-item.active {
  background-color: var(--color-secondary);
  border-left: 3px solid var(--color-primary);
  color: var(--color-primary);
}

.nav-icon {
  font-size: var(--font-size-xl);
  width: 28px;
  margin-right: var(--space-12);
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-text {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
}

/* Main Content */
.main-content {
  flex: 1;
  margin-left: 280px;
  background-color: var(--color-background);
  min-height: 100vh;
}

/* Header */
.header {
  background-color: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  padding: var(--space-20) var(--space-32);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left h1 {
  margin: 0 0 var(--space-4) 0;
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
}

.header-left p {
  margin: 0;
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--space-12);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-10) var(--space-20);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-md);
  border: none;
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
  text-decoration: none;
}

.btn--primary {
  background-color: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.btn--primary:hover {
  background-color: var(--color-primary-hover);
}

.btn--secondary {
  background-color: var(--color-secondary);
  color: var(--color-text);
}

.btn--secondary:hover {
  background-color: var(--color-secondary-hover);
}

.btn--outline {
  background-color: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.btn--outline:hover {
  background-color: var(--color-secondary);
}

/* Content Sections */
.content-section {
  display: none;
  padding: var(--space-32);
}

.content-section.active {
  display: block;
}

/* Metric Cards */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-20);
  margin-bottom: var(--space-32);
}

.metric-card {
  background-color: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  transition: box-shadow var(--duration-normal) var(--ease-standard);
}

.metric-card:hover {
  box-shadow: var(--shadow-md);
}

.metric-icon {
  font-size: 32px;
  margin-bottom: var(--space-16);
}

.metric-content h3 {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin: 0 0 var(--space-8) 0;
  font-weight: var(--font-weight-normal);
}

.metric-value {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0 0 var(--space-4) 0;
}

.metric-change {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.metric-change.positive {
  color: var(--color-success);
}

.metric-change.negative {
  color: var(--color-error);
}

/* Insight Cards */
.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--space-20);
  margin-bottom: var(--space-32);
}

.insight-card {
  background-color: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-20);
  position: relative;
}

.insight-card.critical {
  border-left: 4px solid var(--color-error);
}

.insight-card.high {
  border-left: 4px solid var(--color-warning);
}

.insight-card.medium {
  border-left: 4px solid var(--color-info);
}

.insight-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-12);
}

.insight-type {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
}

.confidence-score {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
}

.insight-card p {
  margin: 0 0 var(--space-16) 0;
  line-height: 1.6;
}

/* Charts */
.charts-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--space-20);
}

.chart-container {
  background-color: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-20);
}

.chart-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.chart-controls {
  display: flex;
  gap: var(--space-8);
}

.chart-wrapper {
  position: relative;
  height: 250px;
}

/* User Profile */
.user-profile {
  position: relative;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
}

/* Modal */
.modal {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: none;
  align-items: center;
  justify-content: center;
}

.modal.show {
  display: flex;
}

.modal-content {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: var(--shadow-lg);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-24);
  border-bottom: 1px solid var(--color-border);
}

.modal-header h3 {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-2xl);
  cursor: pointer;
  color: var(--color-text-secondary);
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  flex: 1;
  overflow: auto;
  padding: var(--space-24);
}

/* Dropdown Menu */
.dropdown-menu {
  display: none;
  position: absolute;
  right: 0;
  top: 100%;
  margin-top: var(--space-8);
  min-width: 200px;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  z-index: 1000;
}

.dropdown-menu.show {
  display: block;
}

.dropdown-item {
  display: block;
  padding: var(--space-10) var(--space-16);
  color: var(--color-text);
  text-decoration: none;
  transition: background-color var(--duration-fast) var(--ease-standard);
}

.dropdown-item:hover {
  background-color: var(--color-secondary);
}

.dropdown-divider {
  height: 1px;
  margin: var(--space-4) 0;
  background-color: var(--color-border);
}

/* Form Controls */
.form-control {
  width: 100%;
  padding: var(--space-10) var(--space-16);
  font-size: var(--font-size-base);
  font-family: var(--font-family-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  transition: all var(--duration-fast) var(--ease-standard);
}

.form-control:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: var(--focus-ring);
}

/* Chat Components */
.chat-container {
  height: 400px;
  overflow-y: auto;
  margin-bottom: var(--space-16);
  padding: var(--space-16);
  background-color: var(--color-background);
  border-radius: var(--radius-md);
}

.chat-message {
  margin-bottom: var(--space-12);
  padding: var(--space-12);
  border-radius: var(--radius-md);
}

.chat-message.ai {
  background-color: var(--color-secondary);
}

.chat-message.user {
  background-color: var(--color-primary);
  color: var(--color-btn-primary-text);
  margin-left: auto;
  max-width: 80%;
}

.chat-input {
  display: flex;
  gap: var(--space-8);
}

.chat-input .form-control {
  flex: 1;
}

/* Typography Extensions */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text);
  letter-spacing: var(--letter-spacing-tight);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-md); }

p {
  margin: 0 0 var(--space-16) 0;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

a:hover {
  color: var(--color-primary-hover);
}

/* Form Label */
.form-label {
  display: block;
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  color: var(--color-text);
}

.form-group {
  margin-bottom: var(--space-16);
}

/* Select dropdown styling */
select.form-control {
  padding: var(--space-10) var(--space-16);
  padding-right: var(--space-32);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: var(--select-caret-light);
  background-repeat: no-repeat;
  background-position: right var(--space-12) center;
  background-size: 16px;
}

[data-color-scheme="dark"] select.form-control {
  background-image: var(--select-caret-dark);
}

textarea.form-control {
  font-family: var(--font-family-base);
  min-height: 100px;
  resize: vertical;
}

/* Card Component */
.card {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--duration-normal) var(--ease-standard);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card__body {
  padding: var(--space-16);
}

.card__header,
.card__footer {
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-card-border-inner);
}

.card__footer {
  border-bottom: none;
  border-top: 1px solid var(--color-card-border-inner);
}

/* Status indicators */
.status {
  display: inline-flex;
  align-items: center;
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.status--success {
  background-color: rgba(var(--color-success-rgb), var(--status-bg-opacity));
  color: var(--color-success);
  border: 1px solid rgba(var(--color-success-rgb), var(--status-border-opacity));
}

.status--error {
  background-color: rgba(var(--color-error-rgb), var(--status-bg-opacity));
  color: var(--color-error);
  border: 1px solid rgba(var(--color-error-rgb), var(--status-border-opacity));
}

.status--warning {
  background-color: rgba(var(--color-warning-rgb), var(--status-bg-opacity));
  color: var(--color-warning);
  border: 1px solid rgba(var(--color-warning-rgb), var(--status-border-opacity));
}

.status--info {
  background-color: rgba(var(--color-info-rgb), var(--status-bg-opacity));
  color: var(--color-info);
  border: 1px solid rgba(var(--color-info-rgb), var(--status-border-opacity));
}

/* Flash Messages / Alerts */
.alert {
  padding: var(--space-12) var(--space-16);
  margin-bottom: var(--space-16);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  display: flex;
  align-items: center;
  gap: var(--space-12);
}

.alert--success {
  background-color: rgba(var(--color-success-rgb), 0.1);
  color: var(--color-success);
  border: 1px solid rgba(var(--color-success-rgb), 0.2);
}

.alert--error {
  background-color: rgba(var(--color-error-rgb), 0.1);
  color: var(--color-error);
  border: 1px solid rgba(var(--color-error-rgb), 0.2);
}

.alert--warning {
  background-color: rgba(var(--color-warning-rgb), 0.1);
  color: var(--color-warning);
  border: 1px solid rgba(var(--color-warning-rgb), 0.2);
}

.alert--info {
  background-color: rgba(var(--color-info-rgb), 0.1);
  color: var(--color-info);
  border: 1px solid rgba(var(--color-info-rgb), 0.2);
}

.alert__icon {
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.alert__content {
  flex: 1;
}

.alert__close {
  background: none;
  border: none;
  color: inherit;
  opacity: 0.7;
  cursor: pointer;
  font-size: var(--font-size-lg);
  padding: 0;
  margin-left: auto;
}

.alert__close:hover {
  opacity: 1;
}

/* Container */
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--space-16);
  padding-left: var(--space-16);
}

@media (min-width: 640px) {
  .container {
    max-width: var(--container-sm);
  }
}

@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: var(--container-lg);
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: var(--container-xl);
  }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }

.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

.text-primary { color: var(--color-primary); }
.text-secondary { color: var(--color-text-secondary); }
.text-error { color: var(--color-error); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-info { color: var(--color-info); }

/* Spacing Utilities */
.m-0 { margin: 0; }
.mt-8 { margin-top: var(--space-8); }
.mb-8 { margin-bottom: var(--space-8); }
.mt-16 { margin-top: var(--space-16); }
.mb-16 { margin-bottom: var(--space-16); }
.mt-24 { margin-top: var(--space-24); }
.mb-24 { margin-bottom: var(--space-24); }
.mt-32 { margin-top: var(--space-32); }
.mb-32 { margin-bottom: var(--space-32); }
.my-16 { margin-top: var(--space-16); margin-bottom: var(--space-16); }
.my-24 { margin-top: var(--space-24); margin-bottom: var(--space-24); }
.my-32 { margin-top: var(--space-32); margin-bottom: var(--space-32); }

.p-0 { padding: 0; }
.p-8 { padding: var(--space-8); }
.p-16 { padding: var(--space-16); }
.p-24 { padding: var(--space-24); }
.p-32 { padding: var(--space-32); }

/* Display & Visibility */
.opacity-50 { opacity: 0.5; }

/* Analytics Dashboard Specific Styles */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-32);
}

.section-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0 0 var(--space-8) 0;
}

.section-subtitle {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--space-16);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-8);
  padding: var(--space-8) var(--space-16);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-full);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--color-text-secondary);
}

.status-dot.active {
  background-color: var(--color-success);
  animation: pulse 2s infinite;
}

.status-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

.date-range-form {
  display: flex;
  align-items: center;
}

.select-field {
  appearance: none;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  padding: var(--space-10) var(--space-32) var(--space-10) var(--space-16);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  cursor: pointer;
  background-image: var(--select-caret-light);
  background-repeat: no-repeat;
  background-position: right var(--space-12) center;
  transition: all var(--duration-fast) var(--ease-standard);
}

.select-field:hover {
  border-color: var(--color-primary);
}

.select-field:focus {
  outline: none;
  box-shadow: var(--focus-ring);
  border-color: var(--color-primary);
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-24);
  margin-bottom: var(--space-32);
}

.metric-card {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  display: flex;
  align-items: center;
  gap: var(--space-16);
  transition: all var(--duration-normal) var(--ease-standard);
}

.metric-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.metric-icon {
  font-size: 2.5rem;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.metric-content h3 {
  margin: 0 0 var(--space-8) 0;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metric-value {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin: 0;
  color: var(--color-text);
}

.metric-change {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-full);
  margin-top: var(--space-8);
  display: inline-block;
}

.metric-change.positive {
  background-color: rgba(var(--color-success-rgb), 0.1);
  color: var(--color-success);
}

.metric-change.negative {
  background-color: rgba(var(--color-error-rgb), 0.1);
  color: var(--color-error);
}

.change-indicator {
  font-size: var(--font-size-xs);
}

/* AI Insights Panel */
.ai-insights-panel {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-32);
  margin-bottom: var(--space-32);
}

.ai-insights-panel h2 {
  margin: 0 0 var(--space-24) 0;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-20);
}

.insight-card {
  background: var(--color-background);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-md);
  padding: var(--space-20);
  position: relative;
  overflow: hidden;
}

.insight-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--color-info);
}

.insight-card.critical::before {
  background: var(--color-error);
}

.insight-card.high::before {
  background: var(--color-warning);
}

.insight-card.medium::before {
  background: var(--color-primary);
}

.insight-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-12);
}

.insight-type {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  color: var(--color-text-secondary);
}

.confidence-score {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--color-primary);
  background: rgba(var(--color-success-rgb), 0.1);
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-full);
}

.insight-card p {
  margin: 0 0 var(--space-16) 0;
  line-height: 1.6;
  color: var(--color-text);
}

/* Charts Section */
.charts-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--space-32);
  margin-bottom: var(--space-32);
}

.chart-container {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-20);
}

.chart-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.chart-controls {
  display: flex;
  gap: var(--space-8);
}

.chart-wrapper {
  position: relative;
  height: 300px;
}

.chart-wrapper canvas {
  width: 100% !important;
  height: 100% !important;
}

.success-text {
  color: var(--color-success);
  font-weight: var(--font-weight-semibold);
}

.error-text {
  color: var(--color-error);
  font-weight: var(--font-weight-semibold);
}

/* Analytics Grid */
.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--space-24);
  margin-bottom: var(--space-32);
}

/* Data Cards */
.data-card {
  background-color: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.data-card--large {
  grid-column: span 2;
}

.data-card--full {
  grid-column: 1 / -1;
}

.card-header {
  padding: var(--space-20);
  border-bottom: 1px solid var(--color-card-border-inner);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--space-8);
}

.card-content {
  padding: var(--space-20);
}

/* Source Type List */
.source-type-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-12);
}

.source-type-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-12);
  background-color: var(--color-background);
  border-radius: var(--radius-md);
  transition: all var(--duration-fast) var(--ease-standard);
}

.source-type-item:hover {
  background-color: var(--color-secondary);
}

.type-info {
  display: flex;
  align-items: center;
  gap: var(--space-12);
}

.type-icon {
  font-size: var(--font-size-xl);
}

.type-name {
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

.type-stats {
  display: flex;
  align-items: center;
  gap: var(--space-16);
}

.type-count {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  min-width: 40px;
  text-align: right;
}

.type-bar {
  width: 80px;
  height: 6px;
  background-color: var(--color-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.type-bar-fill {
  height: 100%;
  background-color: var(--color-primary);
  border-radius: var(--radius-full);
  transition: width var(--duration-normal) var(--ease-standard);
}

/* Performance List */
.performance-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-12);
}

.performance-item {
  display: flex;
  align-items: center;
  gap: var(--space-16);
  padding: var(--space-12);
  background-color: var(--color-background);
  border-radius: var(--radius-md);
  transition: all var(--duration-fast) var(--ease-standard);
}

.performance-item:hover {
  background-color: var(--color-secondary);
}

.rank-badge {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-secondary);
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
  color: var(--color-text);
}

.rank-badge--top {
  background-color: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.source-info {
  flex: 1;
}

.source-name {
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  margin: 0 0 var(--space-4) 0;
}

.source-metric {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
}

.performance-indicator {
  width: 24px;
  height: 24px;
  border-radius: var(--radius-sm);
  background-color: var(--color-secondary);
}

.performance-indicator.trending-up {
  background-color: rgba(var(--color-success-rgb), 0.2);
  position: relative;
}

.performance-indicator.trending-up::after {
  content: "↗";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--color-success);
  font-size: var(--font-size-sm);
}

/* Activity Grid */
.activity-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--space-24);
  margin-bottom: var(--space-32);
}

/* Timeline Controls */
.timeline-controls {
  display: flex;
  gap: var(--space-4);
}

.timeline-btn {
  padding: var(--space-6) var(--space-12);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  background-color: transparent;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
}

.timeline-btn:hover {
  background-color: var(--color-secondary);
}

.timeline-btn.active {
  background-color: var(--color-primary);
  color: var(--color-btn-primary-text);
  border-color: var(--color-primary);
}

/* Activity Chart */
.activity-chart {
  height: 200px;
  position: relative;
}

.chart-bars {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  height: 100%;
  gap: var(--space-4);
}

.chart-bar-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  gap: var(--space-8);
}

.chart-bar {
  width: 100%;
  background-color: var(--color-primary);
  border-radius: var(--radius-sm) var(--radius-sm) 0 0;
  position: relative;
  transition: all var(--duration-fast) var(--ease-standard);
  cursor: pointer;
}

.chart-bar:hover {
  background-color: var(--color-primary-hover);
}

.bar-value {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  opacity: 0;
  transition: opacity var(--duration-fast) var(--ease-standard);
}

.chart-bar:hover .bar-value {
  opacity: 1;
}

.bar-label {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
}

/* Health Metrics */
.health-score {
  padding: var(--space-4) var(--space-12);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
}

.health-score--excellent {
  background-color: rgba(var(--color-success-rgb), 0.2);
  color: var(--color-success);
}

.health-score--good {
  background-color: rgba(var(--color-warning-rgb), 0.2);
  color: var(--color-warning);
}

.health-score--needs-attention {
  background-color: rgba(var(--color-error-rgb), 0.2);
  color: var(--color-error);
}

.health-metrics {
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
  margin-bottom: var(--space-24);
}

.health-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.health-label {
  display: flex;
  align-items: center;
  gap: var(--space-8);
  font-size: var(--font-size-sm);
  color: var(--color-text);
}

.health-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.health-dot--success {
  background-color: var(--color-success);
}

.health-dot--warning {
  background-color: var(--color-warning);
}

.health-dot--error {
  background-color: var(--color-error);
}

.health-value {
  display: flex;
  align-items: center;
  gap: var(--space-8);
}

.health-number {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
}

.health-percent {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* Health Chart */
.health-chart {
  display: flex;
  justify-content: center;
}

.donut-chart {
  width: 120px;
  height: 120px;
}

/* Alerts Section */
.alerts-section {
  margin-bottom: var(--space-32);
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-12);
}

.alert-item {
  display: flex;
  align-items: center;
  gap: var(--space-16);
  padding: var(--space-16);
  background-color: var(--color-background);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-border);
}

.alert-item--error {
  border-color: rgba(var(--color-error-rgb), 0.3);
  background-color: rgba(var(--color-error-rgb), 0.05);
}

.alert-icon {
  font-size: var(--font-size-xl);
  flex-shrink: 0;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0 0 var(--space-4) 0;
}

.alert-message {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0 0 var(--space-8) 0;
}

.alert-time {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
}

.alert-actions {
  display: flex;
  gap: var(--space-8);
  flex-shrink: 0;
}

/* Success State */
.success-state {
  text-align: center;
  padding: var(--space-32);
}

.success-icon {
  font-size: 48px;
  margin-bottom: var(--space-16);
}

.success-state h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0 0 var(--space-8) 0;
}

.success-state p {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin: 0;
}

/* ETL Pipeline Section */
.etl-header {
  margin-bottom: var(--space-32);
}

.etl-header h2 {
  margin: 0 0 var(--space-8) 0;
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
}

.etl-header p {
  margin: 0;
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
}

/* Pipeline Stats */
.pipeline-stats {
  display: flex;
  gap: var(--space-24);
  margin-bottom: var(--space-32);
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  margin-bottom: var(--space-4);
}

.stat-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Pipeline Canvas */
.pipeline-canvas {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-32);
  margin-bottom: var(--space-32);
}

.pipeline-flow {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-16);
  margin-bottom: var(--space-32);
}

.flow-node {
  background: var(--color-background);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  text-align: center;
  transition: all var(--duration-normal) var(--ease-standard);
  cursor: pointer;
  position: relative;
  min-width: 160px;
}

.flow-node:hover {
  border-color: var(--color-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.flow-node.selected {
  border-color: var(--color-primary);
  border-width: 3px;
  box-shadow: 0 0 0 4px rgba(var(--color-primary-rgb), 0.1);
}

.flow-node.source {
  border-color: var(--color-primary);
  background: linear-gradient(135deg, rgba(var(--color-primary-rgb), 0.05), rgba(var(--color-primary-rgb), 0.1));
}

.flow-node.transform {
  border-color: var(--color-warning);
  background: linear-gradient(135deg, rgba(var(--color-warning-rgb), 0.05), rgba(var(--color-warning-rgb), 0.1));
}

.flow-node.destination {
  border-color: var(--color-success);
  background: linear-gradient(135deg, rgba(var(--color-success-rgb), 0.05), rgba(var(--color-success-rgb), 0.1));
}

.node-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto var(--space-16);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-surface);
  border-radius: var(--radius-full);
  color: var(--color-primary);
}

.flow-node.transform .node-icon {
  color: var(--color-warning);
}

.flow-node.destination .node-icon {
  color: var(--color-success);
}

.node-label {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--space-4);
}

.node-detail {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.flow-arrow {
  display: flex;
  align-items: center;
}

/* Pipeline Actions */
.pipeline-actions {
  display: flex;
  gap: var(--space-16);
  justify-content: center;
}

.pipeline-actions .btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-8);
}

.pipeline-actions svg {
  width: 20px;
  height: 20px;
}

/* Active Pipelines */
.active-pipelines {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
}

.active-pipelines h3 {
  margin: 0 0 var(--space-20) 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
}

.pipeline-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
}

.pipeline-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-16);
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  transition: all var(--duration-fast) var(--ease-standard);
}

.pipeline-item:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-sm);
}

.pipeline-info h4 {
  margin: 0 0 var(--space-4) 0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
}

.pipeline-info p {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.pipeline-stats {
  display: flex;
  align-items: center;
  gap: var(--space-16);
}

.pipeline-rate {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

.pipeline-status {
  display: flex;
  align-items: center;
  gap: var(--space-6);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  padding: var(--space-4) var(--space-12);
  border-radius: var(--radius-full);
  background: var(--color-secondary);
}

.pipeline-status.running {
  background: rgba(var(--color-success-rgb), 0.1);
  color: var(--color-success);
}

.pipeline-status.paused {
  background: rgba(var(--color-warning-rgb), 0.1);
  color: var(--color-warning);
}

.pipeline-status.error {
  background: rgba(var(--color-error-rgb), 0.1);
  color: var(--color-error);
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

.pipeline-status.running .status-dot {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Business Intelligence Section */
.bi-section {
  margin-top: var(--space-32);
}

.bi-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-24);
  margin-bottom: var(--space-32);
}

.bi-metric-card {
  background-color: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  position: relative;
  overflow: hidden;
}

.bi-metric-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-16);
}

.bi-metric-icon {
  font-size: var(--font-size-2xl);
}

.confidence-badge {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  padding: var(--space-4) var(--space-8);
  background-color: rgba(var(--color-primary-rgb), 0.1);
  color: var(--color-primary);
  border-radius: var(--radius-full);
}

.bi-metric-content h3 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  margin: 0 0 var(--space-12) 0;
}

.bi-metric-value {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin: 0 0 var(--space-16) 0;
}

.bi-metric-value.positive {
  color: var(--color-success);
}

.bi-metric-value.negative {
  color: var(--color-error);
}

.bi-metric-value.warning {
  color: var(--color-warning);
}

.bi-metric-insights {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-12);
}

.insight-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.insight-label {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.insight-value {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
}

.insight-value.positive {
  color: var(--color-success);
}

.insight-value.negative {
  color: var(--color-error);
}

/* Insights Grid */
.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(480px, 1fr));
  gap: var(--space-24);
}

.insight-card {
  background-color: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.insight-card--highlight {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 1px var(--color-primary);
}

.insight-header {
  padding: var(--space-20);
  border-bottom: 1px solid var(--color-card-border-inner);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.insight-type {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
}

.insight-badge {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  padding: var(--space-4) var(--space-12);
  background-color: var(--color-secondary);
  color: var(--color-text);
  border-radius: var(--radius-full);
}

.insight-badge.pulse {
  background-color: var(--color-primary);
  color: var(--color-btn-primary-text);
  animation: pulse 2s infinite;
}

.insight-content {
  padding: var(--space-20);
}

/* Segments List */
.segments-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-12);
}

.segment-item {
  display: flex;
  align-items: center;
  gap: var(--space-16);
  padding: var(--space-16);
  background-color: var(--color-background);
  border-radius: var(--radius-md);
  transition: all var(--duration-fast) var(--ease-standard);
}

.segment-item:hover {
  background-color: var(--color-secondary);
}

.segment-rank {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-secondary);
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
}

.segment-rank.top-performer {
  background-color: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.segment-details {
  flex: 1;
}

.segment-name {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0 0 var(--space-4) 0;
}

.segment-stats {
  display: flex;
  gap: var(--space-16);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.stat-icon {
  font-size: var(--font-size-sm);
}

.segment-revenue {
  text-align: right;
}

.revenue-label {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: block;
  margin-bottom: var(--space-4);
}

.revenue-value {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
}

/* Opportunity Sections */
.opportunity-section {
  padding: var(--space-16) 0;
  border-bottom: 1px solid var(--color-card-border-inner);
}

.opportunity-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.opportunity-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0 0 var(--space-16) 0;
  display: flex;
  align-items: center;
  gap: var(--space-8);
}

.opportunity-icon {
  font-size: var(--font-size-lg);
}

/* Markets Grid */
.markets-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-8);
}

.market-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-12);
  background-color: var(--color-background);
  border-radius: var(--radius-md);
  transition: all var(--duration-fast) var(--ease-standard);
}

.market-card:hover {
  background-color: var(--color-secondary);
}

.market-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  margin-bottom: var(--space-4);
}

.market-value {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-primary);
}

/* Seasonal Info */
.seasonal-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
}

.seasonality-score {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.score-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
}

.score-bar {
  height: 8px;
  background-color: var(--color-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.score-fill {
  height: 100%;
  background-color: var(--color-primary);
  border-radius: var(--radius-full);
  transition: width var(--duration-normal) var(--ease-standard);
}

.score-value {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
}

.peak-months {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-8);
}

.month-badge {
  padding: var(--space-6) var(--space-12);
  background-color: rgba(var(--color-primary-rgb), 0.1);
  color: var(--color-primary);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

/* Cross-sell Summary */
.cross-sell-summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-16);
  background-color: var(--color-background);
  border-radius: var(--radius-md);
}

.opportunity-metric {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.metric-number {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
}

.metric-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* Animations */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: var(--space-32);
  color: var(--color-text-secondary);
}

.empty-state p {
  margin: 0 0 var(--space-16) 0;
}

/* Predictive Analytics Section */
.predictive-section {
  margin-top: var(--space-32);
  padding-top: var(--space-32);
  border-top: 1px solid var(--color-border);
}

.predictive-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--space-24);
  margin-bottom: var(--space-32);
}

.prediction-card {
  background-color: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  display: flex;
  flex-direction: column;
  gap: var(--space-20);
  transition: all var(--duration-normal) var(--ease-standard);
}

.prediction-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.prediction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.prediction-header h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0;
}

.model-badge {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  padding: var(--space-4) var(--space-12);
  background-color: var(--color-secondary);
  color: var(--color-text);
  border-radius: var(--radius-full);
}

.prediction-visual {
  display: flex;
  align-items: center;
  gap: var(--space-20);
}

.trend-indicator {
  width: 120px;
  height: 60px;
  position: relative;
}

.trend-chart {
  width: 100%;
  height: 100%;
}

.prediction-details {
  flex: 1;
}

.prediction-value {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin: 0 0 var(--space-8) 0;
}

.trend-up .prediction-value {
  color: var(--color-success);
}

.trend-down .prediction-value {
  color: var(--color-warning);
}

.trend-stable .prediction-value {
  color: var(--color-primary);
}

.prediction-text {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin: 0;
  line-height: var(--line-height-normal);
}

.prediction-metrics {
  display: flex;
  gap: var(--space-24);
  padding-top: var(--space-16);
  border-top: 1px solid var(--color-card-border-inner);
}

.prediction-metrics .metric-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.prediction-metrics .metric-label {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.prediction-metrics .metric-value {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
}

.confidence-high {
  color: var(--color-success);
}

.confidence-medium {
  color: var(--color-warning);
}

.confidence-low {
  color: var(--color-error);
}

/* Forecast Models Section */
.forecast-section {
  margin-bottom: var(--space-32);
}

.forecast-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0 0 var(--space-24) 0;
}

.models-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-24);
}

.model-card {
  background-color: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-20);
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
}

.model-header {
  display: flex;
  align-items: center;
  gap: var(--space-12);
}

.model-icon {
  font-size: var(--font-size-xl);
}

.model-header h4 {
  flex: 1;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0;
}

.model-status {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  padding: var(--space-4) var(--space-12);
  border-radius: var(--radius-full);
}

.model-status.active {
  background-color: rgba(var(--color-success-rgb), 0.2);
  color: var(--color-success);
}

.model-status.training {
  background-color: rgba(var(--color-warning-rgb), 0.2);
  color: var(--color-warning);
}

.model-status.inactive {
  background-color: var(--color-secondary);
  color: var(--color-text-secondary);
}

.model-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-12);
}

.model-stats .stat {
  text-align: center;
}

.model-stats .stat-label {
  display: block;
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-4);
}

.model-stats .stat-value {
  display: block;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
}

.model-actions {
  display: flex;
  gap: var(--space-8);
  margin-top: auto;
}

.model-progress {
  margin-top: var(--space-12);
}

.progress-bar {
  height: 8px;
  background-color: var(--color-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: var(--color-primary);
  border-radius: var(--radius-full);
  transition: width var(--duration-normal) var(--ease-standard);
}

/* Scenario Planning Section */
.scenario-section {
  margin-bottom: var(--space-32);
}

.scenario-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0 0 var(--space-24) 0;
}

.scenarios-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-24);
}

.scenario-card {
  background-color: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  position: relative;
  overflow: hidden;
}

.scenario-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.scenario-card.best-case::before {
  background-color: var(--color-success);
}

.scenario-card.expected::before {
  background-color: var(--color-primary);
}

.scenario-card.worst-case::before {
  background-color: var(--color-error);
}

.scenario-card h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0 0 var(--space-20) 0;
}

.scenario-metrics {
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
  margin-bottom: var(--space-20);
}

.scenario-metric {
  display: flex;
  align-items: center;
  gap: var(--space-12);
}

.scenario-metric .metric-icon {
  font-size: var(--font-size-lg);
  width: 24px;
  text-align: center;
}

.scenario-metric .metric-label {
  flex: 1;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.scenario-metric .metric-value {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
}

.best-case .scenario-metric .metric-value {
  color: var(--color-success);
}

.worst-case .scenario-metric .metric-value {
  color: var(--color-error);
}

.scenario-probability {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
  padding-top: var(--space-16);
  border-top: 1px solid var(--color-card-border-inner);
  text-align: center;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
  
  .sidebar-toggle {
    display: block;
  }
  
  .main-content {
    margin-left: 0;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .charts-section {
    grid-template-columns: 1fr;
  }
  
  .insights-grid {
    grid-template-columns: 1fr;
  }
  
  .analytics-grid {
    grid-template-columns: 1fr;
  }
  
  .activity-grid {
    grid-template-columns: 1fr;
  }
  
  .bi-metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .data-card--large {
    grid-column: span 1;
  }
  
  .predictive-grid {
    grid-template-columns: 1fr;
  }
  
  .models-grid {
    grid-template-columns: 1fr;
  }
  
  .scenarios-grid {
    grid-template-columns: 1fr;
  }
  
  .container {
    padding-left: var(--space-12);
    padding-right: var(--space-12);
  }
}

/* Predictive CTA Section */
.predictive-cta-section {
  margin-top: var(--space-32);
  margin-bottom: var(--space-32);
}

.cta-card {
  background-color: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: var(--space-32);
  padding: var(--space-32);
  box-shadow: var(--shadow-lg);
  background-image: linear-gradient(135deg, rgba(var(--color-primary-rgb), 0.05) 0%, rgba(var(--color-success-rgb), 0.05) 100%);
}

.cta-content {
  flex: 1;
}

.cta-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin: 0 0 var(--space-12) 0;
}

.cta-subtitle {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin: 0 0 var(--space-24) 0;
  line-height: var(--line-height-normal);
}

.cta-features {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-16);
  margin-bottom: var(--space-24);
}

.feature-item {
  display: flex;
  align-items: center;
  gap: var(--space-8);
  font-size: var(--font-size-sm);
  color: var(--color-text);
}

.feature-icon {
  font-size: var(--font-size-lg);
}

.cta-visual {
  width: 200px;
  height: 100px;
}

.prediction-illustration {
  width: 100%;
  height: 100%;
}

.btn--lg {
  padding: var(--space-12) var(--space-32);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .cta-card {
    flex-direction: column;
    text-align: center;
  }
  
  .cta-features {
    grid-template-columns: 1fr;
  }
  
  .feature-item {
    justify-content: center;
  }
  
  .cta-visual {
    width: 100%;
    max-width: 300px;
  }
}

/* Analytics Dashboard Ultra Premium Styles */
.analytics-dashboard {
  min-height: 100vh;
  background: var(--color-background);
  padding-bottom: 3rem;
}

.dashboard-header {
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  box-shadow: var(--shadow-sm);
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem 2rem;
}

.header-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1.5rem;
}

.header-title-group {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-icon-wrapper {
  position: relative;
}

.header-icon-gradient {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, var(--color-primary), rgba(var(--color-primary-rgb), 0.8));
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 6px -1px rgba(var(--color-primary-rgb), 0.1), 0 2px 4px -1px rgba(var(--color-primary-rgb), 0.06);
}

.header-icon {
  width: 1.5rem;
  height: 1.5rem;
  color: white;
}

.header-text {
  flex: 1;
}

.header-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--color-text);
  line-height: 1.2;
  letter-spacing: -0.025em;
}

.header-subtitle {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
  margin-top: 0.25rem;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.live-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.375rem 0.875rem;
  background: rgba(var(--color-success-rgb), 0.1);
  border: 1px solid rgba(var(--color-success-rgb), 0.2);
  border-radius: var(--radius-full);
}

.live-dot {
  width: 0.5rem;
  height: 0.5rem;
  background: var(--color-success);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.live-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-success);
}

.date-range-select {
  padding: 0.5rem 1rem;
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  color: var(--color-text);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.date-range-select:hover {
  border-color: var(--color-primary);
}

.date-range-select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: var(--focus-ring);
}

/* Metrics Grid */
.dashboard-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 3rem 2rem 0;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.metric-card {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.metric-card-inner {
  padding: 1.75rem;
  display: flex;
  gap: 1.25rem;
}

.metric-icon-wrapper {
  flex-shrink: 0;
}

.metric-icon-gradient {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.metric-icon-gradient::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, transparent 100%);
}

.gradient-primary {
  background: linear-gradient(135deg, var(--color-primary), rgba(var(--color-primary-rgb), 0.8));
  box-shadow: 0 4px 6px -1px rgba(var(--color-primary-rgb), 0.1);
}

.gradient-success {
  background: linear-gradient(135deg, var(--color-success), rgba(var(--color-success-rgb), 0.8));
  box-shadow: 0 4px 6px -1px rgba(var(--color-success-rgb), 0.1);
}

.gradient-purple {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  box-shadow: 0 4px 6px -1px rgba(139, 92, 246, 0.1);
}

.gradient-orange {
  background: linear-gradient(135deg, #f97316, #ea580c);
  box-shadow: 0 4px 6px -1px rgba(249, 115, 22, 0.1);
}

.gradient-teal {
  background: linear-gradient(135deg, #14b8a6, #0d9488);
  box-shadow: 0 4px 6px -1px rgba(20, 184, 166, 0.1);
}

.metric-icon {
  width: 1.5rem;
  height: 1.5rem;
  color: white;
}

.metric-content {
  flex: 1;
  min-width: 0;
}

.metric-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-text-secondary);
  margin-bottom: 0.25rem;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-text);
  line-height: 1.2;
  margin: 0;
}

.metric-footer {
  margin-top: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.metric-footer.split {
  justify-content: space-between;
}

.metric-status {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-text-secondary);
}

.metric-status.success {
  color: var(--color-success);
}

.metric-status.error {
  color: var(--color-error);
}

.metric-progress {
  margin-top: 0.75rem;
}

.progress-bar {
  width: 100%;
  height: 0.5rem;
  background: rgba(var(--color-primary-rgb), 0.1);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: var(--radius-full);
  transition: width 0.3s ease;
}

.progress-fill.success {
  background: var(--color-success);
}

.progress-fill.teal {
  background: #14b8a6;
}

/* Charts Section */
.charts-section {
  margin-top: 3rem;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.chart-card {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chart-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.chart-header {
  padding: 1.75rem;
  border-bottom: 1px solid var(--color-card-border);
  background: linear-gradient(to bottom, var(--color-surface), rgba(var(--color-primary-rgb), 0.02));
}

.chart-header-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.chart-icon-wrapper {
  flex-shrink: 0;
}

.chart-icon-gradient {
  width: 2rem;
  height: 2rem;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--color-primary), rgba(var(--color-primary-rgb), 0.8));
  box-shadow: 0 2px 4px -1px rgba(var(--color-primary-rgb), 0.1);
}

.chart-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: white;
}

.chart-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-text);
}

.chart-body {
  padding: 2rem;
}

/* Section Container */
.section-container {
  max-width: 1400px;
  margin: 0 auto var(--space-32) auto;
  padding: 0 2rem;
}

.section-container:last-child {
  margin-bottom: 0;
}

/* Analytics Page Specific Spacing */
.revenue-analytics .section-container,
.customer-analytics .section-container,
.product-analytics .section-container,
.risk-analytics .section-container {
  margin-bottom: var(--space-32);
}

.revenue-analytics .dashboard-content,
.customer-analytics .dashboard-content,
.product-analytics .dashboard-content,
.risk-analytics .dashboard-content {
  padding: var(--space-32) 0;
}

/* Chart Section Spacing */
.chart-section,
.fulfillment-section,
.insights-grid {
  margin-top: var(--space-24);
}

/* Section Headers */
.section-header {
  margin-bottom: var(--space-24);
}

.section-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin: 0 0 var(--space-8) 0;
}

.section-subtitle {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin: 0;
}

/* Activity Section */
.activity-section {
  margin-top: 3rem;
}

.activity-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.activity-chart-card {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.activity-bars {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.activity-bar-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.activity-date {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-text-secondary);
  width: 3rem;
}

.activity-bar-wrapper {
  flex: 1;
  position: relative;
  height: 0.75rem;
}

.activity-bar-background {
  position: absolute;
  inset: 0;
  background: rgba(var(--color-primary-rgb), 0.1);
  border-radius: var(--radius-full);
}

.activity-bar-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--color-primary);
  border-radius: var(--radius-full);
  transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.activity-count {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-text);
  width: 2rem;
  text-align: right;
}

/* System Health Card */
.system-health-card {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.health-metrics {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.health-metric-item {
  padding: 1.25rem;
  border-radius: var(--radius-md);
  border: 1px solid;
  transition: all 0.2s;
}

.health-metric-item.active {
  background: rgba(var(--color-success-rgb), 0.05);
  border-color: rgba(var(--color-success-rgb), 0.2);
}

.health-metric-item.pending {
  background: rgba(var(--color-warning-rgb), 0.05);
  border-color: rgba(var(--color-warning-rgb), 0.2);
}

.health-metric-item.issues {
  background: rgba(var(--color-error-rgb), 0.05);
  border-color: rgba(var(--color-error-rgb), 0.2);
}

.health-metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.health-metric-label {
  font-size: 0.875rem;
  font-weight: 500;
}

.health-metric-item.active .health-metric-label {
  color: var(--color-success);
}

.health-metric-item.pending .health-metric-label {
  color: var(--color-warning);
}

.health-metric-item.issues .health-metric-label {
  color: var(--color-error);
}

.health-metric-value {
  font-size: 1.25rem;
  font-weight: 700;
}

.health-metric-item.active .health-metric-value {
  color: var(--color-success);
}

.health-metric-item.pending .health-metric-value {
  color: var(--color-warning);
}

.health-metric-item.issues .health-metric-value {
  color: var(--color-error);
}

.health-progress {
  width: 100%;
}

.health-progress-bar {
  width: 100%;
  height: 0.5rem;
  background: rgba(0, 0, 0, 0.05);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.health-progress-fill {
  height: 100%;
  border-radius: var(--radius-full);
  transition: width 0.3s ease;
}

.health-progress-fill.active {
  background: var(--color-success);
}

.health-progress-fill.pending {
  background: var(--color-warning);
}

.health-progress-fill.issues {
  background: var(--color-error);
}

/* Alerts Section */
.alerts-section {
  margin-top: 3rem;
  margin-bottom: 3rem;
}

.alerts-card {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.alerts-card.error {
  border-color: rgba(var(--color-error-rgb), 0.2);
}

.alerts-card.success {
  border-color: rgba(var(--color-success-rgb), 0.2);
}

.alerts-header {
  padding: 1.75rem;
  border-bottom: 1px solid var(--color-card-border);
  background: linear-gradient(to bottom, var(--color-surface), rgba(var(--color-error-rgb), 0.02));
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.alerts-header-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.alerts-icon-wrapper {
  flex-shrink: 0;
}

.alerts-icon-gradient {
  width: 2rem;
  height: 2rem;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
}

.gradient-error {
  background: linear-gradient(135deg, var(--color-error), rgba(var(--color-error-rgb), 0.8));
  box-shadow: 0 2px 4px -1px rgba(var(--color-error-rgb), 0.1);
}

.alerts-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: white;
}

.alerts-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-text);
}

.alerts-badge {
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  border: 1px solid;
}

.alerts-badge.error {
  background: rgba(var(--color-error-rgb), 0.1);
  color: var(--color-error);
  border-color: rgba(var(--color-error-rgb), 0.2);
}

/* All Systems Operational */
.all-systems-operational {
  padding: 4rem 2rem;
  text-align: center;
}

.operational-icon-wrapper {
  margin: 0 auto 1.5rem;
  width: 5rem;
  height: 5rem;
}

.operational-icon-gradient {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--color-success), rgba(var(--color-success-rgb), 0.8));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 15px -3px rgba(var(--color-success-rgb), 0.1), 0 4px 6px -2px rgba(var(--color-success-rgb), 0.05);
}

.operational-icon {
  width: 2.5rem;
  height: 2.5rem;
  color: white;
}

.operational-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-text);
  margin-bottom: 0.5rem;
}

.operational-text {
  font-size: 1rem;
  color: var(--color-text-secondary);
}

/* Empty State */
.empty-state {
  padding: 3rem 2rem;
  text-align: center;
}

.empty-state-icon {
  margin: 0 auto 1rem;
  width: 4rem;
  height: 4rem;
  background: rgba(var(--color-primary-rgb), 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  width: 2rem;
  height: 2rem;
  color: var(--color-primary);
  opacity: 0.5;
}

.empty-text {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
}

/* Data Source List */
.data-source-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.data-source-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(var(--color-primary-rgb), 0.02);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-md);
  transition: all 0.2s;
}

.data-source-item:hover {
  background: rgba(var(--color-primary-rgb), 0.05);
  border-color: var(--color-primary);
}

.data-source-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.data-source-dot {
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
}

.data-source-dot.gradient-primary {
  background: var(--color-primary);
}

.data-source-dot.gradient-success {
  background: var(--color-success);
}

.data-source-dot.gradient-purple {
  background: #8b5cf6;
}

.data-source-dot.gradient-orange {
  background: #f97316;
}

.data-source-dot.gradient-teal {
  background: #14b8a6;
}

.data-source-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-text);
}

.data-source-count {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-text);
}

/* Business Intelligence Section */
.business-intelligence-section {
  margin-top: 3rem;
  padding-bottom: 3rem;
}

.section-header {
  margin-bottom: 3rem;
  text-align: center;
}

.section-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--color-text);
  margin-bottom: 0.5rem;
  letter-spacing: -0.025em;
}

.section-subtitle {
  font-size: 1rem;
  color: var(--color-text-secondary);
}

.bi-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.bi-metric-card {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.bi-metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary), rgba(var(--color-primary-rgb), 0.6));
  opacity: 0;
  transition: opacity 0.3s;
}

.bi-metric-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.bi-metric-card:hover::before {
  opacity: 1;
}

.bi-metric-inner {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.bi-metric-icon-wrapper {
  margin-bottom: 1.5rem;
}

.bi-metric-icon-gradient {
  width: 4rem;
  height: 4rem;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.bi-metric-icon-gradient::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.3) 0%, transparent 100%);
}

.bi-metric-emoji {
  font-size: 2rem;
  filter: grayscale(0);
}

.bi-metric-content {
  width: 100%;
}

.bi-metric-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-text-secondary);
  margin-bottom: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.bi-metric-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--color-text);
  line-height: 1.2;
  margin: 0 0 0.75rem;
}

.bi-metric-value.capitalize {
  text-transform: capitalize;
}

.bi-metric-detail {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
}

.bi-detailed-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 2rem;
}

/* Responsive padding adjustments */
@media (max-width: 768px) {
  .header-content {
    padding: 1.5rem 1rem;
  }
  
  .dashboard-content {
    padding: 2rem 1rem 0;
  }
  
  .section-container {
    padding: 0 1rem;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .activity-grid {
    grid-template-columns: 1fr;
  }
  
  .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-header,
  .alerts-header {
    padding: 1.25rem;
  }
  
  .chart-body,
  .health-metrics {
    padding: 1.25rem;
  }
  
  .metric-card-inner {
    padding: 1.25rem;
  }
  
  .bi-metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .bi-detailed-grid {
    grid-template-columns: 1fr;
  }
  
  .bi-metric-inner {
    padding: 1.5rem;
  }
  
  .section-title {
    font-size: 1.5rem;
  }
}

/* Premium CTA Section */
.cta-section-wrapper {
  margin-top: 4rem;
  padding-bottom: 2rem;
}

.cta-card {
  position: relative;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.cta-card.predictions {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
}

.cta-background {
  position: absolute;
  inset: 0;
  opacity: 0.1;
  background-image: 
    radial-gradient(circle at 20% 50%, rgba(255,255,255,0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255,255,255,0.2) 0%, transparent 50%),
    radial-gradient(circle at 40% 20%, rgba(255,255,255,0.2) 0%, transparent 50%);
}

.cta-content {
  position: relative;
  padding: 4rem 3rem;
  display: flex;
  align-items: center;
  gap: 3rem;
}

.cta-main {
  flex: 1;
}

.cta-icon-wrapper {
  display: inline-block;
  margin-bottom: 1.5rem;
}

.cta-icon {
  font-size: 3rem;
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
}

.cta-title {
  font-size: 2.25rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cta-description {
  font-size: 1.125rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.cta-features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2.5rem;
}

.cta-feature {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
}

.cta-feature-icon {
  font-size: 1.25rem;
}

.cta-feature-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: white;
}

.cta-button {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  background: white;
  color: #6366f1;
  border-radius: var(--radius-md);
  font-weight: 600;
  font-size: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  background: rgba(255, 255, 255, 0.95);
}

.cta-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.5);
}

.cta-button-icon {
  font-size: 1.25rem;
}

.cta-button-text {
  font-size: 1rem;
}

.cta-visual {
  display: none;
}

@media (min-width: 1024px) {
  .cta-visual {
    display: block;
    flex-shrink: 0;
  }
  
  .cta-graph {
    width: 280px;
    height: 140px;
    opacity: 0.8;
  }
}

@media (max-width: 768px) {
  .cta-content {
    padding: 3rem 2rem;
    flex-direction: column;
    text-align: center;
  }
  
  .cta-title {
    font-size: 1.875rem;
  }
  
  .cta-description {
    font-size: 1rem;
  }
  
  .cta-features-grid {
    grid-template-columns: 1fr;
  }
  
  .cta-button {
    width: 100%;
    justify-content: center;
  }
}

/* Revenue Analytics Styles */
.revenue-analytics {
  min-height: 100vh;
  background-color: var(--color-background);
}

/* Fulfillment Section */
.fulfillment-section {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  padding: 2rem;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-card-border);
}

.fulfillment-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.fulfillment-card {
  background-color: var(--color-background);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  border: 1px solid var(--color-card-border);
  transition: all var(--duration-normal) var(--ease-standard);
}

.fulfillment-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.fulfillment-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.fulfillment-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
}

.fulfillment-icon svg {
  width: 20px;
  height: 20px;
  stroke-width: 2.5;
}

.fulfillment-icon.success {
  background-color: rgba(var(--color-success-rgb), 0.15);
  color: var(--color-success);
}

.fulfillment-icon.warning {
  background-color: rgba(var(--color-warning-rgb), 0.15);
  color: var(--color-warning);
}

.fulfillment-icon.info {
  background-color: rgba(var(--color-info-rgb), 0.15);
  color: var(--color-info);
}

.fulfillment-icon.error {
  background-color: rgba(var(--color-error-rgb), 0.15);
  color: var(--color-error);
}

.fulfillment-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
}

.fulfillment-value {
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin-bottom: 0.5rem;
}

.fulfillment-details {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: 1rem;
}

/* Insights Grid */
.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.insight-card {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-card-border);
  transition: all var(--duration-normal) var(--ease-standard);
}

.insight-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.insight-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.insight-icon {
  width: 48px;
  height: 48px;
  background-color: var(--color-secondary);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-primary);
}

.insight-icon svg {
  width: 24px;
  height: 24px;
}

.insight-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
}

.insight-value {
  font-size: 1.75rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin-bottom: 0.25rem;
}

.insight-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* Customer Analytics Styles */
.customer-analytics {
  min-height: 100vh;
  background-color: var(--color-background);
}

/* Segments Section */
.segments-section {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  padding: 2rem;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-card-border);
}

.segments-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.segment-card {
  background-color: var(--color-background);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  border: 1px solid var(--color-card-border);
}

.segment-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: 1.5rem;
}

.segment-items {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.segment-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.segment-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.segment-name {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

.segment-count {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.segment-bar {
  height: 6px;
  background-color: var(--color-secondary);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.segment-fill {
  height: 100%;
  background-color: var(--color-primary);
  transition: width var(--duration-normal) var(--ease-standard);
}

/* Acquisition Section */
.acquisition-section {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  padding: 2rem;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-card-border);
}

.acquisition-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.acquisition-card {
  background-color: var(--color-background);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  border: 1px solid var(--color-card-border);
  text-align: center;
}

.acquisition-metric {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.acquisition-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.acquisition-value {
  font-size: 1.75rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
}

/* Product Analytics Styles */
.product-analytics {
  min-height: 100vh;
  background-color: var(--color-background);
}

/* Top Products Section */
.top-products-section {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  padding: 2rem;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-card-border);
}

.top-products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.products-list-card {
  background-color: var(--color-background);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  border: 1px solid var(--color-card-border);
}

.products-list-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: 1.5rem;
}

.products-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.product-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  border-radius: var(--radius-sm);
  transition: background-color var(--duration-fast) var(--ease-standard);
}

.product-item:hover {
  background-color: var(--color-secondary);
}

.product-rank {
  width: 32px;
  height: 32px;
  background-color: var(--color-primary);
  color: white;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-sm);
  flex-shrink: 0;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  margin-bottom: 0.25rem;
}

.product-stats {
  display: flex;
  gap: 1rem;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.product-revenue {
  font-weight: var(--font-weight-semibold);
  color: var(--color-success);
}

.product-quantity {
  color: var(--color-text-secondary);
}

/* Inventory Section */
.inventory-section {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  padding: 2rem;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-card-border);
}

.inventory-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.inventory-card {
  background-color: var(--color-background);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  border: 1px solid var(--color-card-border);
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.inventory-icon {
  width: 56px;
  height: 56px;
  background-color: var(--color-secondary);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.inventory-icon svg {
  width: 28px;
  height: 28px;
  color: var(--color-primary);
}

.inventory-icon.success {
  background-color: rgba(var(--color-success-rgb), 0.15);
}

.inventory-icon.success svg {
  color: var(--color-success);
}

.inventory-icon.warning {
  background-color: rgba(var(--color-warning-rgb), 0.15);
}

.inventory-icon.warning svg {
  color: var(--color-warning);
}

.inventory-icon.error {
  background-color: rgba(var(--color-error-rgb), 0.15);
}

.inventory-icon.error svg {
  color: var(--color-error);
}

.inventory-content {
  flex: 1;
}

.inventory-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  margin-bottom: 0.5rem;
}

.inventory-value {
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin-bottom: 0.5rem;
}

.inventory-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* Inventory Distribution */
.inventory-distribution {
  margin-top: 2rem;
  background-color: var(--color-background);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  border: 1px solid var(--color-card-border);
}

.distribution-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: 1rem;
}

.distribution-items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.distribution-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: var(--color-surface);
  border-radius: var(--radius-sm);
  border: 1px solid var(--color-card-border);
}

.distribution-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

.distribution-count {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* Risk Analytics Styles */
.risk-analytics {
  min-height: 100vh;
  background-color: var(--color-background);
}

/* Risk Score Section */
.risk-score-section {
  margin-bottom: 2rem;
}

.risk-score-card {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  padding: 2rem;
  text-align: center;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-card-border);
}

.risk-score-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  margin-bottom: 1rem;
}

.risk-score-display {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.risk-score-value {
  font-size: 4rem;
  font-weight: var(--font-weight-bold);
  line-height: 1;
}

.risk-score-value.low {
  color: var(--color-success);
}

.risk-score-value.medium {
  color: var(--color-warning);
}

.risk-score-value.high,
.risk-score-value.critical {
  color: var(--color-error);
}

.risk-score-max {
  font-size: 1.5rem;
  color: var(--color-text-secondary);
}

.risk-level {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: 1.5rem;
}

.risk-level.low {
  color: var(--color-success);
}

.risk-level.medium {
  color: var(--color-warning);
}

.risk-level.high,
.risk-level.critical {
  color: var(--color-error);
}

.risk-gauge {
  width: 100%;
  max-width: 400px;
  height: 8px;
  background-color: var(--color-secondary);
  border-radius: var(--radius-full);
  margin: 0 auto;
  overflow: hidden;
}

.risk-gauge-fill {
  height: 100%;
  background: linear-gradient(to right, var(--color-success), var(--color-warning), var(--color-error));
  transition: width var(--duration-normal) var(--ease-standard);
}

/* Risk Indicators Grid */
.risk-indicators-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.risk-indicator-card {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-card-border);
  transition: all var(--duration-normal) var(--ease-standard);
}

.risk-indicator-card.high-risk {
  border-color: var(--color-error);
  background-color: rgba(var(--color-error-rgb), 0.05);
}

.indicator-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.indicator-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.indicator-icon svg {
  width: 24px;
  height: 24px;
}

.indicator-icon.success {
  background-color: rgba(var(--color-success-rgb), 0.15);
  color: var(--color-success);
}

.indicator-icon.warning {
  background-color: rgba(var(--color-warning-rgb), 0.15);
  color: var(--color-warning);
}

.indicator-icon.error {
  background-color: rgba(var(--color-error-rgb), 0.15);
  color: var(--color-error);
}

.indicator-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
}

.indicator-value {
  font-size: 2.5rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin-bottom: 0.5rem;
}

.indicator-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: 0.5rem;
}

.indicator-alert {
  font-size: var(--font-size-sm);
  color: var(--color-error);
  font-weight: var(--font-weight-medium);
  padding: 0.5rem 0;
  border-top: 1px solid var(--color-border);
  margin-top: 0.5rem;
}

/* Recommendations Section */
.recommendations-section {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  padding: 2rem;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-card-border);
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1.5rem;
}

.recommendation-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: var(--color-background);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-card-border);
}

.recommendation-icon {
  width: 32px;
  height: 32px;
  background-color: rgba(var(--color-primary-rgb), 0.15);
  color: var(--color-primary);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.recommendation-icon svg {
  width: 16px;
  height: 16px;
}

.recommendation-text {
  font-size: var(--font-size-base);
  color: var(--color-text);
}

/* Opportunities Section */
.opportunities-section {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  padding: 2rem;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-card-border);
}

.opportunities-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
  padding: 1.5rem;
  background-color: var(--color-background);
  border-radius: var(--radius-md);
  margin: 1.5rem 0;
}

.opportunity-stat {
  text-align: center;
}

.stat-value {
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.opportunities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.opportunity-card {
  background-color: var(--color-background);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  border: 1px solid var(--color-card-border);
  transition: all var(--duration-normal) var(--ease-standard);
}

.opportunity-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.opportunity-card.priority-high {
  border-left: 4px solid var(--color-error);
}

.opportunity-card.priority-medium {
  border-left: 4px solid var(--color-warning);
}

.opportunity-card.priority-low {
  border-left: 4px solid var(--color-info);
}

.opportunity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.opportunity-type {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.opportunity-priority {
  font-size: var(--font-size-xs);
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);
}

.opportunity-priority.high {
  background-color: rgba(var(--color-error-rgb), 0.15);
  color: var(--color-error);
}

.opportunity-priority.medium {
  background-color: rgba(var(--color-warning-rgb), 0.15);
  color: var(--color-warning);
}

.opportunity-priority.low {
  background-color: rgba(var(--color-info-rgb), 0.15);
  color: var(--color-info);
}

.opportunity-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: 0.5rem;
}

.opportunity-description {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  margin-bottom: 1rem;
}

.opportunity-impact {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  padding: 0.75rem;
  background-color: var(--color-surface);
  border-radius: var(--radius-sm);
}

.impact-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.impact-value {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-success);
}

.opportunity-details {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-top: 0.5rem;
}
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* DataFlow Pro Design Tokens */
    --color-background: theme('colors.df-background');
    --color-surface: theme('colors.df-surface');
    --color-text: theme('colors.df-text');
    --color-text-secondary: theme('colors.df-text-secondary');
    --color-primary: theme('colors.df-primary');
    --color-primary-hover: theme('colors.df-primary-hover');
    --color-primary-active: theme('colors.df-primary-active');
    --color-secondary: theme('colors.df-secondary');
    --color-secondary-hover: theme('colors.df-secondary-hover');
    --color-secondary-active: theme('colors.df-secondary-active');
    --color-border: theme('colors.df-border');
    --color-card-border: theme('colors.df-card-border');
    --color-error: theme('colors.df-error');
    --color-success: theme('colors.df-success');
    --color-warning: theme('colors.df-warning');
    --color-info: theme('colors.df-info');
    --color-focus-ring: theme('colors.df-focus-ring');
  }

  .dark {
    --color-background: #1a1a1a;
    --color-surface: #242424;
    --color-text: #f5f5f5;
    --color-text-secondary: #a0a0a0;
    --color-primary: #3fb8cd;
    --color-primary-hover: #36a0b3;
    --color-primary-active: #2d899a;
    --color-secondary: rgba(255, 255, 255, 0.08);
    --color-secondary-hover: rgba(255, 255, 255, 0.12);
    --color-secondary-active: rgba(255, 255, 255, 0.16);
    --color-border: rgba(255, 255, 255, 0.1);
    --color-card-border: rgba(255, 255, 255, 0.08);
  }

  body {
    @apply bg-df-background text-df-text font-sans text-df-base;
  }
}

@layer components {
  /* Sidebar Navigation */
  .sidebar {
    @apply fixed left-0 top-0 h-full w-64 bg-df-surface border-r border-df-border
           flex flex-col transition-transform duration-300 ease-df-standard z-50;
  }

  .sidebar-header {
    @apply flex items-center justify-between px-df-20 py-df-16 border-b border-df-border;
  }

  .sidebar-header h2 {
    @apply text-df-xl font-semibold text-df-primary;
  }

  .sidebar-toggle {
    @apply lg:hidden text-df-text hover:bg-df-secondary p-df-8 rounded-df-sm;
  }

  .nav-menu {
    @apply flex-1 overflow-y-auto py-df-16;
  }

  .nav-item {
    @apply flex items-center gap-df-12 px-df-20 py-df-12 text-df-text-secondary
           hover:bg-df-secondary hover:text-df-text cursor-pointer
           transition-all duration-200 ease-df-standard;
  }

  .nav-item.active {
    @apply bg-df-primary text-white hover:bg-df-primary-hover;
  }

  .nav-icon {
    @apply text-df-lg flex-shrink-0;
  }

  .nav-text {
    @apply text-df-base font-medium;
  }

  /* Main Content Area */
  .main-content {
    @apply ml-64 min-h-screen bg-df-background;
  }

  /* Header */
  .header {
    @apply flex items-center justify-between px-df-32 py-df-20 bg-df-surface
           border-b border-df-border sticky top-0 z-40;
  }

  .header-left h1 {
    @apply text-df-3xl font-semibold text-df-text mb-df-4;
  }

  .header-left p {
    @apply text-df-base text-df-text-secondary;
  }

  .header-right {
    @apply flex items-center gap-df-16;
  }

  /* Buttons */
  .btn {
    @apply inline-flex items-center justify-center px-df-20 py-df-10
           text-df-base font-medium rounded-df-md
           transition-all duration-200 ease-df-standard
           focus:outline-none focus:ring-2 focus:ring-df-focus-ring;
  }

  .btn--primary {
    @apply bg-df-primary text-white hover:bg-df-primary-hover active:bg-df-primary-active;
  }

  .btn--secondary {
    @apply bg-df-secondary text-df-text hover:bg-df-secondary-hover active:bg-df-secondary-active;
  }

  .btn--outline {
    @apply border border-df-border text-df-text hover:bg-df-secondary;
  }

  .btn--sm {
    @apply px-df-12 py-df-6 text-df-sm;
  }

  .btn--full-width {
    @apply w-full;
  }

  /* Cards */
  .metric-card {
    @apply bg-df-surface rounded-df-lg p-df-24 border border-df-card-border
           hover:shadow-df-md transition-shadow duration-200;
  }

  .metric-icon {
    @apply text-3xl mb-df-16;
  }

  .metric-content h3 {
    @apply text-df-base text-df-text-secondary mb-df-8;
  }

  .metric-value {
    @apply text-df-3xl font-semibold text-df-text mb-df-4;
  }

  .metric-change {
    @apply text-df-sm;
  }

  .metric-change.positive {
    @apply text-df-success;
  }

  .metric-change.negative {
    @apply text-df-error;
  }

  /* Insights */
  .insight-card {
    @apply bg-df-surface rounded-df-lg p-df-20 border border-df-card-border;
  }

  .insight-card.critical {
    @apply border-l-4 border-l-df-error;
  }

  .insight-card.high {
    @apply border-l-4 border-l-df-warning;
  }

  .insight-card.medium {
    @apply border-l-4 border-l-df-info;
  }

  .insight-header {
    @apply flex items-center justify-between mb-df-12;
  }

  .insight-type {
    @apply text-df-sm font-medium text-df-text-secondary;
  }

  .confidence-score {
    @apply text-df-xs text-df-text-secondary;
  }

  /* Charts */
  .chart-container {
    @apply bg-df-surface rounded-df-lg p-df-24 border border-df-card-border;
  }

  .chart-header {
    @apply flex items-center justify-between mb-df-20;
  }

  .chart-header h3 {
    @apply text-df-lg font-semibold text-df-text;
  }

  .chart-wrapper {
    @apply relative h-64;
  }

  /* Form Elements */
  .form-control {
    @apply w-full px-df-12 py-df-8 bg-df-surface border border-df-border
           rounded-df-md text-df-base text-df-text
           focus:outline-none focus:ring-2 focus:ring-df-focus-ring
           transition-all duration-200;
  }

  /* Content Sections */
  .content-section {
    @apply hidden px-df-32 py-df-24;
  }

  .content-section.active {
    @apply block;
  }

  /* Grids */
  .metrics-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-df-20 mb-df-32;
  }

  .insights-grid {
    @apply grid grid-cols-1 lg:grid-cols-3 gap-df-20 mb-df-32;
  }

  .charts-section {
    @apply grid grid-cols-1 lg:grid-cols-2 gap-df-20;
  }

  /* Modal */
  .modal {
    @apply fixed inset-0 bg-black bg-opacity-50 z-50 hidden;
  }

  .modal.show {
    @apply flex items-center justify-center;
  }

  .modal-content {
    @apply bg-df-surface rounded-df-lg w-full max-w-2xl mx-df-20
           shadow-df-lg max-h-[90vh] flex flex-col;
  }

  .modal-header {
    @apply flex items-center justify-between px-df-24 py-df-16
           border-b border-df-border;
  }

  .modal-close {
    @apply text-df-text-secondary hover:text-df-text text-2xl
           leading-none p-df-4;
  }

  /* Mobile Responsive */
  @media (max-width: 768px) {
    .sidebar {
      @apply -translate-x-full;
    }

    .sidebar.open {
      @apply translate-x-0;
    }

    .main-content {
      @apply ml-0;
    }

    .metrics-grid {
      @apply grid-cols-1;
    }

    .charts-section {
      @apply grid-cols-1;
    }
  }

  /* Theme Transition */
  .theme-transition * {
    @apply transition-colors duration-200;
  }
}

@layer utilities {
  /* Utility classes for DataFlow Pro */
  .df-loading {
    @apply opacity-50 pointer-events-none;
  }

  .df-dragover {
    @apply bg-df-secondary border-2 border-dashed border-df-primary;
  }
}
/* Premium Navigation Styles */

/* Glassmorphism Base */
.nav-glassmorphic {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Premium Scrollbar */
#desktop-sidebar::-webkit-scrollbar {
  width: 6px;
}

#desktop-sidebar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

#desktop-sidebar::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #6366f1, #8b5cf6);
  border-radius: 3px;
}

#desktop-sidebar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #4f46e5, #7c3aed);
}

/* Navigation Item Animations */
.nav-item-premium {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-item-premium::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(to right, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
  border-radius: 0.75rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.nav-item-premium:hover::before {
  opacity: 1;
}

/* Submenu Animations */
.submenu-premium {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.submenu-premium.open {
  max-height: 500px;
}

/* Activity Feed Animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.activity-item {
  animation: slideIn 0.3s ease-out;
}

/* Status Indicators */
@keyframes statusPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

.status-indicator {
  animation: statusPulse 2s infinite;
}

/* Mobile Menu Animations */
.mobile-menu-enter {
  transform: translateX(-100%);
}

.mobile-menu-enter-active {
  transform: translateX(0);
  transition: transform 0.3s ease-out;
}

.mobile-menu-exit {
  transform: translateX(0);
}

.mobile-menu-exit-active {
  transform: translateX(-100%);
  transition: transform 0.3s ease-in;
}

/* Premium Hover Effects */
.premium-hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.premium-hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Gradient Text Animation */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-text-animated {
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

/* Icon Rotation Effects */
.icon-rotate {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.icon-rotate:hover {
  transform: rotate(360deg);
}

/* Glow Effects */
.glow-effect {
  position: relative;
}

.glow-effect::after {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(45deg, #6366f1, #8b5cf6, #ec4899, #6366f1);
  background-size: 400% 400%;
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
  animation: gradientShift 3s ease infinite;
  filter: blur(10px);
  z-index: -1;
}

.glow-effect:hover::after {
  opacity: 0.7;
}

/* Shimmer Loading Effect */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.shimmer-loading {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Premium Focus States */
.premium-focus:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1), 
              0 0 0 1px rgba(99, 102, 241, 0.3);
}

/* Smooth Height Transitions */
.smooth-height {
  transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Premium Badge Animations */
@keyframes badgePulse {
  0% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
  }
}

.badge-pulse {
  animation: badgePulse 2s infinite;
}

/* Navigation Dividers */
.nav-divider {
  position: relative;
  margin: 1rem 0;
}

.nav-divider::before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, 
    transparent, 
    rgba(99, 102, 241, 0.2) 20%, 
    rgba(139, 92, 246, 0.2) 80%, 
    transparent
  );
}

/* Premium Tooltips */
.premium-tooltip {
  position: relative;
}

.premium-tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-8px);
  padding: 0.5rem 1rem;
  background: rgba(17, 24, 39, 0.95);
  backdrop-filter: blur(10px);
  color: white;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 0.5rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 50;
}

.premium-tooltip:hover::after {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0);
}
/* Premium Authentication Styles with Glassmorphism */

/* Glassmorphic containers */
.auth-glassmorphic {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.auth-glassmorphic-dark {
  background: rgba(17, 24, 39, 0.85);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

/* Animated gradient backgrounds */
@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.auth-gradient-animated {
  background: linear-gradient(-45deg, #6366F1, #8B5CF6, #EC4899, #10B981);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

/* Floating elements animation */
@keyframes float {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(10deg); }
}

@keyframes floatReverse {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(-10deg); }
}

.auth-float {
  animation: float 6s ease-in-out infinite;
}

.auth-float-reverse {
  animation: floatReverse 8s ease-in-out infinite;
}

/* Pulse glow effect */
@keyframes pulseGlow {
  0%, 100% { 
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.5),
                0 0 40px rgba(99, 102, 241, 0.3),
                0 0 60px rgba(99, 102, 241, 0.1);
  }
  50% {
    box-shadow: 0 0 30px rgba(99, 102, 241, 0.8),
                0 0 60px rgba(99, 102, 241, 0.6),
                0 0 90px rgba(99, 102, 241, 0.4);
  }
}

.auth-glow-pulse {
  animation: pulseGlow 3s ease-in-out infinite;
}

/* Form input styles */
.auth-input {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(209, 213, 219, 0.5);
  transition: all 0.3s ease;
  font-size: 1rem;
  padding: 0.875rem 1rem;
}

.auth-input:focus {
  background: rgba(255, 255, 255, 1);
  border-color: rgba(99, 102, 241, 0.5);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1),
              0 4px 12px rgba(99, 102, 241, 0.1);
  transform: translateY(-1px);
}

@media (min-width: 1024px) {
  .auth-input {
    font-size: 1.125rem;
    padding: 1rem 1.25rem;
  }
}

/* Premium button styles */
.auth-button-premium {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%);
  transition: all 0.3s ease;
  transform-style: preserve-3d;
}

.auth-button-premium::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.auth-button-premium:hover::before {
  left: 100%;
}

.auth-button-premium:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(99, 102, 241, 0.3),
              0 5px 15px rgba(139, 92, 246, 0.3);
}

.auth-button-premium:active {
  transform: translateY(0);
}

/* Decorative orbs */
.auth-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  opacity: 0.7;
  animation: float 20s ease-in-out infinite;
}

.auth-orb-1 {
  width: 300px;
  height: 300px;
  background: linear-gradient(135deg, #6366F1, #8B5CF6);
  top: -150px;
  right: -150px;
  animation-delay: 0s;
}

.auth-orb-2 {
  width: 250px;
  height: 250px;
  background: linear-gradient(135deg, #EC4899, #F59E0B);
  bottom: -125px;
  left: -125px;
  animation-delay: 5s;
}

.auth-orb-3 {
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg, #10B981, #3B82F6);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: 10s;
}

/* Loading states */
.auth-loading {
  position: relative;
  color: transparent;
}

.auth-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Success animation */
@keyframes successPulse {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.auth-success-icon {
  animation: successPulse 0.6s ease-out;
}

/* Error shake animation */
@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
  20%, 40%, 60%, 80% { transform: translateX(10px); }
}

.auth-error-shake {
  animation: errorShake 0.6s ease-out;
}

/* Particle effects */
.auth-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.auth-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(99, 102, 241, 0.5);
  border-radius: 50%;
  animation: particleFloat 10s linear infinite;
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) rotate(720deg);
    opacity: 0;
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .auth-orb {
    filter: blur(60px);
  }
  
  .auth-orb-1 {
    width: 200px;
    height: 200px;
    top: -100px;
    right: -100px;
  }
  
  .auth-orb-2 {
    width: 150px;
    height: 150px;
    bottom: -75px;
    left: -75px;
  }
}

/* Link hover effects */
.auth-link-premium {
  position: relative;
  transition: color 0.3s ease;
}

.auth-link-premium::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #6366F1, #8B5CF6);
  transition: width 0.3s ease;
}

.auth-link-premium:hover::after {
  width: 100%;
}

/* Checkbox custom styling */
.auth-checkbox {
  appearance: none;
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(209, 213, 219, 0.5);
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
}

.auth-checkbox:checked {
  background: linear-gradient(135deg, #6366F1, #8B5CF6);
  border-color: transparent;
}

.auth-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.auth-checkbox:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}
/* AI Query Interface Styles */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Smooth transitions for AI query interface */
.ai-query-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Gradient background for AI branding */
.ai-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Pulse animation for loading states */
@keyframes ai-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.ai-pulse {
  animation: ai-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Custom scrollbar for suggestions dropdown */
.ai-suggestions::-webkit-scrollbar {
  width: 4px;
}

.ai-suggestions::-webkit-scrollbar-track {
  background: transparent;
}

.ai-suggestions::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 2px;
}

.ai-suggestions::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* Enhanced focus states */
.ai-input:focus {
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  border-color: rgb(99, 102, 241);
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .ai-suggestions::-webkit-scrollbar-thumb {
    background: rgba(75, 85, 99, 0.5);
  }
  
  .ai-suggestions::-webkit-scrollbar-thumb:hover {
    background: rgba(75, 85, 99, 0.7);
  }
}

/* Results animation */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.ai-results-enter {
  animation: slideInUp 0.4s ease-out;
}

/* Toast animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.toast-enter {
  animation: slideInRight 0.3s ease-out;
}

.toast-exit {
  animation: slideOutRight 0.3s ease-in;
}

/* Card hover effects */
.ai-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Loading skeleton */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@media (prefers-color-scheme: dark) {
  .skeleton {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200% 100%;
  }
}

/* Interactive elements */
.ai-interactive:hover {
  transform: scale(1.02);
}

.ai-interactive:active {
  transform: scale(0.98);
}

/* Responsive improvements */
@media (max-width: 640px) {
  .ai-query-input {
    font-size: 16px; /* Prevent zoom on iOS */
  }
}
/* Premium Dashboard Visual Effects and Animations */

/* Glass Morphism Base Styles */
.glass-card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.glass-card-dark {
  background: rgba(30, 41, 59, 0.3);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Premium Gradient Animations */
@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) skewX(-12deg);
  }
  100% {
    transform: translateX(200%) skewX(-12deg);
  }
}

@keyframes shimmer-fast {
  0% {
    transform: translateX(-100%) skewX(-12deg);
  }
  100% {
    transform: translateX(200%) skewX(-12deg);
  }
}

.animate-shimmer {
  animation: shimmer 3s ease-in-out infinite;
}

.animate-shimmer-fast {
  animation: shimmer-fast 1.5s ease-in-out infinite;
}

.animate-gradient {
  background-size: 400% 400%;
  animation: gradient-shift 4s ease infinite;
}

/* Premium Card Hover Effects */
.premium-card {
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.premium-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
  z-index: 1;
}

.premium-card:hover::before {
  left: 100%;
}

.premium-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Floating Animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float 3s ease-in-out infinite;
  animation-delay: 1s;
}

.animate-float-delayed-2 {
  animation: float 3s ease-in-out infinite;
  animation-delay: 2s;
}

/* Pulse Glow Effect */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
  }
  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.6), 0 0 60px rgba(59, 130, 246, 0.4);
  }
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Advanced Button Hover Effects */
.btn-premium {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-premium::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
  z-index: 1;
}

.btn-premium:hover::before {
  width: 300px;
  height: 300px;
}

.btn-premium:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Metric Counter Animation */
@keyframes counter-bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-4px);
  }
  60% {
    transform: translateY(-2px);
  }
}

.animate-counter-bounce {
  animation: counter-bounce 1s ease-in-out;
}

/* Status Indicator Animations */
.status-indicator {
  position: relative;
}

.status-indicator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.status-indicator.connected::before {
  background: rgba(34, 197, 94, 0.4);
}

.status-indicator.syncing::before {
  background: rgba(59, 130, 246, 0.4);
}

.status-indicator.error::before {
  background: rgba(239, 68, 68, 0.4);
}

/* Premium Scrollbar */
.premium-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.premium-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.premium-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.6);
  border-radius: 4px;
}

.premium-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.8);
}

/* Tooltip Styles */
.tooltip {
  position: relative;
}

.tooltip::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 14px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
  z-index: 1000;
}

.tooltip::after {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.9);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.tooltip:hover::before,
.tooltip:hover::after {
  opacity: 1;
  visibility: visible;
  bottom: calc(100% + 5px);
}

/* Loading States */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Progress Bar Animations */
.progress-bar {
  position: relative;
  overflow: hidden;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-image: linear-gradient(
    -45deg,
    rgba(255, 255, 255, 0.2) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    transparent 75%,
    transparent
  );
  background-size: 50px 50px;
  animation: move 2s linear infinite;
}

@keyframes move {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 50px 50px;
  }
}

/* Custom rounded corners */
.rounded-4xl {
  border-radius: 2rem;
}

.rounded-5xl {
  border-radius: 2.5rem;
}

/* Shadow variations */
.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

.shadow-4xl {
  box-shadow: 0 45px 80px -15px rgba(0, 0, 0, 0.3);
}

/* Responsive premium effects */
@media (max-width: 768px) {
  .premium-card:hover {
    transform: translateY(-4px) scale(1.01);
  }
  
  .glass-card {
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .glass-card {
    background: rgba(30, 41, 59, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .skeleton {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  }
}
/* DataReflow Premium Design System */

:root {
  /* Premium Brand Colors - Sophisticated Palette */
  --dataflow-primary-50: #faf7ff;
  --dataflow-primary-100: #f4edff;
  --dataflow-primary-200: #e9d8ff;
  --dataflow-primary-300: #d6b8ff;
  --dataflow-primary-400: #bc8cff;
  --dataflow-primary-500: #a855f7;
  --dataflow-primary-600: #9333ea;
  --dataflow-primary-700: #7c2d8a;
  --dataflow-primary-800: #6b1f6b;
  --dataflow-primary-900: #4a1545;

  --dataflow-secondary-50: #f0f9ff;
  --dataflow-secondary-100: #e0f2fe;
  --dataflow-secondary-200: #bae6fd;
  --dataflow-secondary-300: #7dd3fc;
  --dataflow-secondary-400: #38bdf8;
  --dataflow-secondary-500: #0ea5e9;
  --dataflow-secondary-600: #0284c7;
  --dataflow-secondary-700: #0369a1;
  --dataflow-secondary-800: #075985;
  --dataflow-secondary-900: #0c4a6e;

  /* Premium Neutral Palette */
  --dataflow-neutral-50: #fafafa;
  --dataflow-neutral-100: #f5f5f5;
  --dataflow-neutral-200: #e5e5e5;
  --dataflow-neutral-300: #d4d4d4;
  --dataflow-neutral-400: #a3a3a3;
  --dataflow-neutral-500: #737373;
  --dataflow-neutral-600: #525252;
  --dataflow-neutral-700: #404040;
  --dataflow-neutral-800: #262626;
  --dataflow-neutral-900: #171717;

  /* Premium Accent Colors */
  --dataflow-accent-emerald: #10b981;
  --dataflow-accent-amber: #f59e0b;
  --dataflow-accent-rose: #f43f5e;
  --dataflow-accent-violet: #8b5cf6;

  /* Accent Colors */
  --dataflow-success-500: #10b981;
  --dataflow-warning-500: #f59e0b;
  --dataflow-error-500: #ef4444;
  --dataflow-info-500: #06b6d4;

  /* Neutral Colors */
  --dataflow-gray-50: #f9fafb;
  --dataflow-gray-100: #f3f4f6;
  --dataflow-gray-200: #e5e7eb;
  --dataflow-gray-300: #d1d5db;
  --dataflow-gray-400: #9ca3af;
  --dataflow-gray-500: #6b7280;
  --dataflow-gray-600: #4b5563;
  --dataflow-gray-700: #374151;
  --dataflow-gray-800: #1f2937;
  --dataflow-gray-900: #111827;

  /* Typography Scale */
  --dataflow-text-xs: 0.75rem;
  --dataflow-text-sm: 0.875rem;
  --dataflow-text-base: 1rem;
  --dataflow-text-lg: 1.125rem;
  --dataflow-text-xl: 1.25rem;
  --dataflow-text-2xl: 1.5rem;
  --dataflow-text-3xl: 1.875rem;
  --dataflow-text-4xl: 2.25rem;
  --dataflow-text-5xl: 3rem;
  --dataflow-text-6xl: 3.75rem;

  /* Font Weights */
  --dataflow-font-light: 300;
  --dataflow-font-normal: 400;
  --dataflow-font-medium: 500;
  --dataflow-font-semibold: 600;
  --dataflow-font-bold: 700;

  /* Spacing Scale */
  --dataflow-space-xs: 0.25rem;
  --dataflow-space-sm: 0.5rem;
  --dataflow-space-md: 1rem;
  --dataflow-space-lg: 1.5rem;
  --dataflow-space-xl: 2rem;
  --dataflow-space-2xl: 3rem;
  --dataflow-space-3xl: 4rem;

  /* Border Radius */
  --dataflow-radius-sm: 0.25rem;
  --dataflow-radius-md: 0.375rem;
  --dataflow-radius-lg: 0.5rem;
  --dataflow-radius-xl: 0.75rem;
  --dataflow-radius-2xl: 1rem;
  --dataflow-radius-full: 9999px;

  /* Shadows */
  --dataflow-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --dataflow-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --dataflow-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --dataflow-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Transitions */
  --dataflow-transition-fast: 150ms ease-in-out;
  --dataflow-transition-normal: 300ms ease-in-out;
  --dataflow-transition-slow: 500ms ease-in-out;

  /* Z-Index Scale */
  --dataflow-z-dropdown: 1000;
  --dataflow-z-sticky: 1020;
  --dataflow-z-fixed: 1030;
  --dataflow-z-modal-backdrop: 1040;
  --dataflow-z-modal: 1050;
  --dataflow-z-popover: 1060;
  --dataflow-z-tooltip: 1070;
  --dataflow-z-toast: 1080;
}

/* Component Base Classes */
.dataflow-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--dataflow-radius-lg);
  font-weight: var(--dataflow-font-semibold);
  transition: all var(--dataflow-transition-fast);
  text-decoration: none;
  cursor: pointer;
  border: none;
  outline: none;
}

.dataflow-btn:focus {
  outline: 2px solid var(--dataflow-primary-500);
  outline-offset: 2px;
}

.dataflow-btn-primary {
  background: linear-gradient(135deg, var(--dataflow-primary-600), var(--dataflow-secondary-600));
  color: white;
  padding: 0.75rem 1.5rem;
  font-size: var(--dataflow-text-base);
}

.dataflow-btn-primary:hover {
  background: linear-gradient(135deg, var(--dataflow-primary-700), var(--dataflow-secondary-700));
  transform: translateY(-1px);
  box-shadow: var(--dataflow-shadow-lg);
}

.dataflow-btn-secondary {
  background: white;
  color: var(--dataflow-gray-700);
  border: 1px solid var(--dataflow-gray-300);
  padding: 0.75rem 1.5rem;
  font-size: var(--dataflow-text-base);
}

.dataflow-btn-secondary:hover {
  background: var(--dataflow-gray-50);
  border-color: var(--dataflow-gray-400);
}

.dataflow-btn-ghost {
  background: transparent;
  color: var(--dataflow-gray-600);
  padding: 0.5rem 1rem;
  font-size: var(--dataflow-text-sm);
}

.dataflow-btn-ghost:hover {
  background: var(--dataflow-gray-100);
  color: var(--dataflow-gray-800);
}

/* Card Components */
.dataflow-card {
  background: white;
  border-radius: var(--dataflow-radius-xl);
  box-shadow: var(--dataflow-shadow-md);
  overflow: hidden;
  transition: all var(--dataflow-transition-normal);
}

.dataflow-card:hover {
  box-shadow: var(--dataflow-shadow-xl);
  transform: translateY(-2px);
}

.dataflow-card-feature {
  padding: var(--dataflow-space-2xl);
  border-radius: var(--dataflow-radius-2xl);
  background: white;
  box-shadow: var(--dataflow-shadow-lg);
  transition: all var(--dataflow-transition-normal);
}

.dataflow-card-feature:hover {
  box-shadow: var(--dataflow-shadow-xl);
  transform: translateY(-4px);
}

/* Form Elements */
.dataflow-input {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: var(--dataflow-text-base);
  border: 1px solid var(--dataflow-gray-300);
  border-radius: var(--dataflow-radius-lg);
  background: white;
  transition: all var(--dataflow-transition-fast);
}

.dataflow-input:focus {
  outline: none;
  border-color: var(--dataflow-primary-500);
  box-shadow: 0 0 0 3px rgb(168 85 247 / 0.1);
}

.dataflow-label {
  display: block;
  font-size: var(--dataflow-text-sm);
  font-weight: var(--dataflow-font-medium);
  color: var(--dataflow-gray-700);
  margin-bottom: 0.5rem;
}

/* Navigation */
.dataflow-nav-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  border-radius: var(--dataflow-radius-md);
  font-size: var(--dataflow-text-sm);
  font-weight: var(--dataflow-font-semibold);
  color: var(--dataflow-gray-700);
  text-decoration: none;
  transition: all var(--dataflow-transition-fast);
}

.dataflow-nav-link:hover {
  background: var(--dataflow-gray-50);
  color: var(--dataflow-primary-600);
}

.dataflow-nav-link.active {
  background: var(--dataflow-gray-50);
  color: var(--dataflow-primary-600);
}

/* Utility Classes */
.dataflow-gradient-primary {
  background: linear-gradient(135deg, var(--dataflow-primary-600), var(--dataflow-secondary-600));
}

.dataflow-gradient-hero {
  background: linear-gradient(135deg, var(--dataflow-primary-900), var(--dataflow-secondary-900), var(--dataflow-primary-800));
}

.dataflow-text-gradient {
  background: linear-gradient(135deg, var(--dataflow-primary-600), var(--dataflow-secondary-600));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dataflow-shadow-glow {
  box-shadow: 0 0 20px rgb(168 85 247 / 0.3);
}

/* Animation Classes */
.dataflow-fade-in {
  animation: dataflowFadeIn var(--dataflow-transition-normal) ease-out;
}

.dataflow-slide-up {
  animation: dataflowSlideUp var(--dataflow-transition-normal) ease-out;
}

.dataflow-scale-in {
  animation: dataflowScaleIn var(--dataflow-transition-fast) ease-out;
}

@keyframes dataflowFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes dataflowSlideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes dataflowScaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Responsive Typography */
@media (max-width: 640px) {
  :root {
    --dataflow-text-4xl: 1.875rem;
    --dataflow-text-5xl: 2.25rem;
    --dataflow-text-6xl: 2.5rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --dataflow-gray-50: #1f2937;
    --dataflow-gray-100: #374151;
    --dataflow-gray-200: #4b5563;
    --dataflow-gray-300: #6b7280;
    --dataflow-gray-400: #9ca3af;
    --dataflow-gray-500: #d1d5db;
    --dataflow-gray-600: #e5e7eb;
    --dataflow-gray-700: #f3f4f6;
    --dataflow-gray-800: #f9fafb;
    --dataflow-gray-900: #ffffff;
  }
}

/* Print Styles */
@media print {
  .dataflow-no-print {
    display: none !important;
  }
  
  .dataflow-card {
    box-shadow: none;
    border: 1px solid var(--dataflow-gray-300);
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .dataflow-btn-primary {
    background: var(--dataflow-primary-800);
    border: 2px solid var(--dataflow-primary-900);
  }
  
  .dataflow-input {
    border-width: 2px;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
/* Enhanced Flash Messages Styles */
/* Provides animations, transitions, and visual enhancements for the flash message system */

/* Flash Messages Container */
#flash-messages-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 9999;
  max-width: 28rem;
  width: 100%;
  pointer-events: none;
}

@media (max-width: 640px) {
  #flash-messages-container {
    top: 0.5rem;
    right: 0.5rem;
    left: 0.5rem;
    max-width: none;
  }
}

/* Flash Message Base Styles */
.flash-message {
  pointer-events: auto;
  margin-bottom: 0.75rem;
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  backdrop-filter: blur(8px);
  border-left: 4px solid;
  position: relative;
  overflow: hidden;
}

/* Entrance Animation */
.flash-message.animate-in {
  transform: translateX(0);
  opacity: 1;
}

/* Exit Animation */
.flash-message.dismissing {
  transform: translateX(100%);
  opacity: 0;
  max-height: 0;
  margin-bottom: 0;
  padding-top: 0;
  padding-bottom: 0;
}

/* Progress Bar Styles */
.flash-progress-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0.8) 0%, 
    rgba(255, 255, 255, 0.6) 50%, 
    rgba(255, 255, 255, 0.8) 100%);
  transition: width linear;
  border-radius: 0 0 0.375rem 0;
}

/* Flash Message Type Specific Styles */

/* Success Messages */
.flash-message.flash-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-left-color: #047857;
  color: white;
}

.flash-message.flash-success .flash-icon {
  color: #d1fae5;
}

.flash-message.flash-success .flash-dismiss:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Error Messages */
.flash-message.flash-error {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border-left-color: #b91c1c;
  color: white;
}

.flash-message.flash-error .flash-icon {
  color: #fecaca;
}

.flash-message.flash-error .flash-dismiss:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Warning Messages */
.flash-message.flash-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border-left-color: #b45309;
  color: white;
}

.flash-message.flash-warning .flash-icon {
  color: #fed7aa;
}

.flash-message.flash-warning .flash-dismiss:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Info Messages */
.flash-message.flash-info {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  border-left-color: #1d4ed8;
  color: white;
}

.flash-message.flash-info .flash-icon {
  color: #dbeafe;
}

.flash-message.flash-info .flash-dismiss:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Notice Messages */
.flash-message.flash-notice {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  border-left-color: #6d28d9;
  color: white;
}

.flash-message.flash-notice .flash-icon {
  color: #e9d5ff;
}

.flash-message.flash-notice .flash-dismiss:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Alert Messages */
.flash-message.flash-alert {
  background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
  border-left-color: #c2410c;
  color: white;
}

.flash-message.flash-alert .flash-icon {
  color: #fed7aa;
}

.flash-message.flash-alert .flash-dismiss:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Default Messages */
.flash-message.flash-default {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  border-left-color: #374151;
  color: white;
}

.flash-message.flash-default .flash-icon {
  color: #e5e7eb;
}

.flash-message.flash-default .flash-dismiss:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Flash Message Content */
.flash-title {
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
  opacity: 0.95;
}

.flash-content {
  font-size: 0.875rem;
  line-height: 1.4;
  opacity: 0.9;
}

/* Action Button Styles */
.flash-action {
  display: inline-flex;
  align-items: center;
  margin-top: 0.75rem;
  padding: 0.375rem 0.75rem;
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 0.375rem;
  color: white;
  text-decoration: none;
  font-size: 0.75rem;
  font-weight: 500;
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
}

.flash-action:hover {
  background-color: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.flash-action:active {
  transform: translateY(0);
}

/* Dismiss Button */
.flash-dismiss {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s ease;
  cursor: pointer;
  opacity: 0.8;
}

.flash-dismiss:hover {
  opacity: 1;
}

.flash-dismiss:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

/* Icon Styles */
.flash-icon {
  flex-shrink: 0;
  margin-right: 0.75rem;
  opacity: 0.9;
}

/* Persistent Message Indicator */
.flash-message.persistent::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 1rem 1rem 0;
  border-color: transparent rgba(255, 255, 255, 0.3) transparent transparent;
}

/* Hover Effects */
.flash-message:hover {
  transform: translateX(-4px);
  box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.15), 0 10px 20px -5px rgba(0, 0, 0, 0.08);
}

.flash-message.dismissing:hover {
  transform: translateX(100%);
}

/* Focus Styles for Accessibility */
.flash-message:focus {
  outline: 3px solid rgba(59, 130, 246, 0.5);
  outline-offset: 2px;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .flash-message {
    transition: opacity 0.2s ease;
    transform: none;
  }
  
  .flash-message.animate-in {
    opacity: 1;
  }
  
  .flash-message.dismissing {
    opacity: 0;
    transform: none;
  }
  
  .flash-message:hover {
    transform: none;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .flash-message {
    border: 2px solid;
    backdrop-filter: none;
  }
  
  .flash-action {
    border: 2px solid;
    backdrop-filter: none;
  }
}

/* Dark Mode Adjustments */
@media (prefers-color-scheme: dark) {
  .flash-message {
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
  }
}

/* Print Styles */
@media print {
  #flash-messages-container {
    display: none;
  }
}

/* Animation Keyframes */
@keyframes flash-slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes flash-slide-out {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes flash-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes flash-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

/* Utility Classes */
.flash-stacked {
  margin-bottom: 0.5rem;
}

.flash-compact .flash-title {
  font-size: 0.75rem;
}

.flash-compact .flash-content {
  font-size: 0.75rem;
}

.flash-large {
  padding: 1.5rem;
}

.flash-large .flash-title {
  font-size: 1rem;
}

.flash-large .flash-content {
  font-size: 0.875rem;
}
/* Performance Optimizations for Premium Landing Page */

/* Critical CSS - Above the fold optimizations */
.critical-content {
  contain: layout style paint;
}

/* GPU acceleration for animations */
.premium-animate-float,
.premium-animate-pulse,
.premium-btn,
.premium-card {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Optimize backdrop-filter performance */
.premium-card-glass {
  contain: layout style paint;
  isolation: isolate;
}

/* Optimize gradient performance */
.premium-gradient-text {
  contain: layout style paint;
}

/* Lazy loading optimizations */
.lazy-load {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.lazy-load.loaded {
  opacity: 1;
}

/* Image optimization */
img {
  content-visibility: auto;
  contain-intrinsic-size: 300px 200px;
}

/* Reduce layout thrashing */
.premium-grid {
  contain: layout;
}

/* Optimize scroll performance */
.premium-section {
  contain: layout style paint;
}

/* Font loading optimizations */
@font-face {
  font-family: 'Inter';
  font-display: swap;
  src: url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
}

/* Reduce paint complexity */
.premium-card::before,
.premium-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  pointer-events: none;
  will-change: opacity;
}

/* Optimize animations for 60fps */
@keyframes premium-float-optimized {
  0%, 100% { 
    transform: translate3d(0, 0, 0); 
  }
  50% { 
    transform: translate3d(0, -10px, 0); 
  }
}

@keyframes premium-pulse-optimized {
  0%, 100% { 
    opacity: 1; 
    transform: scale3d(1, 1, 1); 
  }
  50% { 
    opacity: 0.7; 
    transform: scale3d(1.05, 1.05, 1); 
  }
}

/* Replace existing animations with optimized versions */
.premium-animate-float {
  animation: premium-float-optimized 3s ease-in-out infinite;
}

.premium-animate-pulse {
  animation: premium-pulse-optimized 2s ease-in-out infinite;
}

/* Accessibility optimizations */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .premium-card-glass {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid #000;
  }
  
  .premium-btn-primary {
    background: #000;
    color: #fff;
    border: 2px solid #fff;
  }
  
  .premium-btn-secondary {
    background: #fff;
    color: #000;
    border: 2px solid #000;
  }
}

/* Focus management for accessibility */
.premium-btn:focus,
.premium-card:focus {
  outline: 3px solid #4f46e5;
  outline-offset: 2px;
}

/* Skip link for screen readers */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 9999;
}

.skip-link:focus {
  top: 6px;
}

/* Optimize for print */
@media print {
  .premium-animate-float,
  .premium-animate-pulse {
    animation: none;
  }
  
  .premium-card-glass {
    background: #fff;
    border: 1px solid #000;
  }
  
  .premium-gradient-text {
    color: #000 !important;
    background: none !important;
    -webkit-text-fill-color: #000 !important;
  }
}

/* Content visibility optimizations */
.premium-section:not(.in-viewport) {
  content-visibility: auto;
  contain-intrinsic-size: 0 500px;
}

/* Intersection observer optimizations */
.fade-in-observer {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in-observer.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Memory usage optimizations */
.premium-card-large {
  contain: layout style paint size;
}

/* Optimize for mobile performance */
@media (max-width: 768px) {
  .premium-card-glass {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
  
  .premium-animate-float,
  .premium-animate-pulse {
    animation-duration: 4s;
  }
}

/* Critical resource hints */
.preload-font {
  font-display: swap;
}

/* Optimize SVG performance */
svg {
  contain: layout style paint;
}

/* Reduce composite layers */
.premium-btn:not(:hover):not(:focus) {
  will-change: auto;
}

.premium-card:not(:hover):not(:focus) {
  will-change: auto;
}
/* Premium Effects Stylesheet */

/* Ripple Effect Animation */
@keyframes ripple-animation {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* Skeleton Loader Animations */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.skeleton-line {
  height: 1rem;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 0.5rem;
  margin-bottom: 0.5rem;
}

.skeleton-circle {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 50%;
}

/* Command Palette Styles */
.command-palette {
  @apply fixed inset-0 z-50 flex items-start justify-center pt-20 px-4;
}

.command-palette-backdrop {
  @apply fixed inset-0 bg-black/50 backdrop-blur-sm;
  animation: fadeIn 0.2s ease-out;
}

.command-palette-modal {
  @apply relative w-full max-w-2xl bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 overflow-hidden;
  transform: translateY(-20px) scale(0.95);
  opacity: 0;
  transition: all 0.2s ease-out;
}

.command-palette-open .command-palette-modal {
  transform: translateY(0) scale(1);
  opacity: 1;
}

.command-palette-header {
  @apply p-4 border-b border-gray-200/50;
}

.command-palette-input {
  @apply w-full px-4 py-3 text-lg bg-transparent border-0 outline-none placeholder-gray-400;
}

.command-palette-results {
  @apply max-h-96 overflow-y-auto p-2;
}

.command-result {
  @apply flex items-center space-x-3 px-4 py-3 rounded-xl cursor-pointer transition-all duration-200;
}

.command-result:hover {
  @apply bg-gradient-to-r from-indigo-50 to-purple-50;
}

.command-result.selected {
  @apply bg-gradient-to-r from-indigo-100 to-purple-100;
}

.command-icon {
  @apply text-2xl;
}

.command-name {
  @apply flex-1 font-medium text-gray-900;
}

.command-keywords {
  @apply text-xs text-gray-500;
}

/* Page Transition Effects */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.page-transition-out {
  animation: fadeIn 0.3s ease-out reverse;
}

.page-transition-in {
  animation: slideUp 0.5s ease-out;
}

/* Advanced Glassmorphism */
.glass-morphism {
  @apply bg-white/80 backdrop-blur-xl border border-white/20 shadow-xl;
}

.glass-morphism-dark {
  @apply bg-gray-900/80 backdrop-blur-xl border border-gray-700/20 shadow-xl;
}

.glass-morphism-colored {
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255,255,255,0.18);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
}

/* Glow Effects */
.glow-blue {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
}

.glow-purple {
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.5);
}

.glow-green {
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.5);
}

/* Hover Lift Effect */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Smooth Scroll */
html {
  scroll-behavior: smooth;
}

/* Premium Scrollbar */
::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #6366f1 0%, #8b5cf6 100%);
  border-radius: 10px;
  border: 2px solid transparent;
  background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #4f46e5 0%, #7c3aed 100%);
  background-clip: content-box;
}

/* Focus Styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500;
}

/* Premium Loading Spinner */
.premium-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(139, 92, 246, 0.2);
  border-top-color: #8b5cf6;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Gradient Text */
.gradient-text {
  @apply bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent;
}

/* Premium Badge */
.premium-badge {
  @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px 0 rgba(102, 126, 234, 0.4);
}

/* Floating Labels */
.floating-label {
  @apply absolute left-3 top-3 text-gray-500 transition-all duration-200 pointer-events-none;
}

.floating-label-input:focus ~ .floating-label,
.floating-label-input:not(:placeholder-shown) ~ .floating-label {
  @apply text-xs -top-2 left-2 bg-white px-1;
}

/* Neumorphism */
.neumorphism {
  background: #e0e5ec;
  box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
  border-radius: 1rem;
}

.neumorphism-inset {
  background: #e0e5ec;
  box-shadow: inset 5px 5px 10px #a3b1c6, inset -5px -5px 10px #ffffff;
  border-radius: 1rem;
}

/* Tooltip */
.tooltip {
  @apply absolute z-50 px-2 py-1 text-xs text-white bg-gray-900 rounded-lg opacity-0 pointer-events-none transition-opacity duration-200;
}

.tooltip-trigger:hover .tooltip {
  @apply opacity-100;
}

/* Premium Checkbox */
.premium-checkbox {
  @apply w-5 h-5 text-indigo-600 bg-white border-gray-300 rounded focus:ring-2 focus:ring-indigo-500 cursor-pointer;
  transition: all 0.2s ease;
}

.premium-checkbox:checked {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: transparent;
  animation: checkmark 0.3s ease;
}

@keyframes checkmark {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}
/* Premium UI Enhancements for Data Refinery Platform */

/* ===========================
   1. CSS Variables & Theming
   =========================== */
:root {
  /* Premium Color Palette */
  --color-primary: #3b82f6;
  --color-primary-dark: #2563eb;
  --color-primary-light: #60a5fa;
  
  --color-secondary: #8b5cf6;
  --color-secondary-dark: #7c3aed;
  --color-secondary-light: #a78bfa;
  
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-danger: #ef4444;
  --color-info: #06b6d4;
  
  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  --gradient-dark: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  --gradient-light: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  
  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark mode variables */
[data-theme="dark"] {
  --color-primary: #60a5fa;
  --color-primary-dark: #3b82f6;
  --color-primary-light: #93bbfc;
  
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

/* ===========================
   2. Base Typography
   =========================== */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap');

body {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  font-feature-settings: 'cv11', 'ss01', 'ss03';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Fluid typography */
h1 { font-size: clamp(2rem, 5vw, 3rem); }
h2 { font-size: clamp(1.5rem, 4vw, 2.25rem); }
h3 { font-size: clamp(1.25rem, 3vw, 1.875rem); }
h4 { font-size: clamp(1.125rem, 2.5vw, 1.5rem); }

/* Tabular numbers for data */
.tabular-nums {
  font-variant-numeric: tabular-nums;
}

/* ===========================
   3. Premium Components
   =========================== */

/* Glass morphism cards */
.glass-card {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.1);
}

.dark .glass-card {
  background: rgba(30, 41, 59, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 3D card effect */
.card-3d {
  transform-style: preserve-3d;
  transition: transform var(--transition-normal);
}

.card-3d:hover {
  transform: perspective(1000px) rotateY(-5deg) rotateX(5deg) scale(1.02);
}

/* Gradient borders */
.gradient-border {
  position: relative;
  background: white;
  border-radius: 1rem;
}

.gradient-border::before {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: 1rem;
  padding: 2px;
  background: var(--gradient-primary);
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
}

/* ===========================
   4. Animations
   =========================== */

/* Skeleton loading */
@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.skeleton {
  background: linear-gradient(
    90deg,
    #f0f0f0 25%,
    #e0e0e0 50%,
    #f0f0f0 75%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite ease-in-out;
}

/* Pulse animation for live indicators */
@keyframes pulse-dot {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.5;
  }
}

.pulse-dot {
  animation: pulse-dot 2s infinite;
}

/* Shimmer effect */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.shimmer {
  position: relative;
  overflow: hidden;
}

.shimmer::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transform: translateX(-100%);
  animation: shimmer 2s infinite;
}

/* Bounce animation for loading dots */
@keyframes bounce-delay {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.bounce-delay-1 { animation-delay: -0.32s; }
.bounce-delay-2 { animation-delay: -0.16s; }
.bounce-delay-3 { animation-delay: 0; }

/* ===========================
   5. Micro-interactions
   =========================== */

/* Button hover effects */
.btn-premium {
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
}

.btn-premium::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  transform: translate(-50%, -50%);
  transition: width var(--transition-normal), height var(--transition-normal);
}

.btn-premium:hover::before {
  width: 300px;
  height: 300px;
}

/* Card hover lift */
.card-hover-lift {
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md);
}

.card-hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

/* Input focus effects */
.input-premium {
  transition: all var(--transition-normal);
  border: 2px solid #e5e7eb;
}

.input-premium:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* ===========================
   6. Custom Scrollbars
   =========================== */

/* Webkit browsers */
::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 6px;
  border: 2px solid #f1f5f9;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

/* ===========================
   7. Progress Indicators
   =========================== */

/* Circular progress */
.progress-ring {
  transform: rotate(-90deg);
  transform-origin: 50% 50%;
}

.progress-ring-circle {
  transition: stroke-dashoffset 1s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Linear progress with stripes */
.progress-striped {
  background-image: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 10px,
    rgba(255, 255, 255, 0.1) 10px,
    rgba(255, 255, 255, 0.1) 20px
  );
  background-size: 28px 28px;
  animation: progress-stripes 1s linear infinite;
}

@keyframes progress-stripes {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 28px 0;
  }
}

/* ===========================
   8. Status Indicators
   =========================== */

/* Status dots with glow */
.status-dot {
  position: relative;
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-dot.success {
  background: var(--color-success);
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.status-dot.warning {
  background: var(--color-warning);
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
}

.status-dot.danger {
  background: var(--color-danger);
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.status-dot.info {
  background: var(--color-info);
  box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.2);
}

/* ===========================
   9. Timeline Styles
   =========================== */

.timeline-item {
  position: relative;
  padding-left: 2rem;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.5rem;
  bottom: -2rem;
  width: 2px;
  background: #e5e7eb;
}

.timeline-item:last-child::before {
  bottom: 0.5rem;
}

.timeline-dot {
  position: absolute;
  left: -6px;
  top: 0.5rem;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: white;
  border: 2px solid var(--color-primary);
  z-index: 1;
}

/* ===========================
   10. Data Tables
   =========================== */

.table-premium {
  border-collapse: separate;
  border-spacing: 0;
}

.table-premium thead th {
  background: #f8fafc;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
  color: #64748b;
  padding: 1rem;
  position: sticky;
  top: 0;
  z-index: 10;
}

.table-premium tbody tr {
  transition: all var(--transition-fast);
}

.table-premium tbody tr:hover {
  background: #f8fafc;
  transform: scale(1.01);
  box-shadow: var(--shadow-md);
}

/* ===========================
   11. Charts & Visualizations
   =========================== */

.chart-container {
  position: relative;
  height: 300px;
}

.chart-tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  pointer-events: none;
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.chart-tooltip.show {
  opacity: 1;
}

/* ===========================
   12. Mobile Optimizations
   =========================== */

@media (max-width: 768px) {
  /* Touch-friendly tap targets */
  button, a, [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Swipeable cards */
  .swipe-container {
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    -webkit-overflow-scrolling: touch;
  }
  
  .swipe-card {
    scroll-snap-align: start;
    flex-shrink: 0;
  }
  
  /* Bottom sheet style modals */
  .modal-mobile {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    border-radius: 1rem 1rem 0 0;
    transform: translateY(100%);
    transition: transform var(--transition-normal);
  }
  
  .modal-mobile.show {
    transform: translateY(0);
  }
}

/* ===========================
   13. Dark Mode Enhancements
   =========================== */

.dark {
  color-scheme: dark;
}

.dark .glass-card {
  background: rgba(30, 41, 59, 0.8);
  border-color: rgba(148, 163, 184, 0.1);
}

.dark .skeleton {
  background: linear-gradient(
    90deg,
    #1e293b 25%,
    #334155 50%,
    #1e293b 75%
  );
}

/* ===========================
   14. Print Styles
   =========================== */

@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break {
    page-break-before: always;
  }
  
  body {
    font-size: 12pt;
    line-height: 1.5;
  }
  
  .card-3d, .card-hover-lift {
    transform: none !important;
    box-shadow: none !important;
  }
}

/* ===========================
   15. Accessibility
   =========================== */

/* Focus visible for keyboard navigation */
:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Skip to content link */
.skip-to-content {
  position: absolute;
  left: -9999px;
  z-index: 999;
  padding: 1rem;
  background: var(--color-primary);
  color: white;
  text-decoration: none;
}

.skip-to-content:focus {
  left: 50%;
  transform: translateX(-50%);
  top: 1rem;
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
/* Premium Landing Page Design System */

/* Import Google Fonts for Premium Typography */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

:root {
  /* Premium Color Palette */
  --premium-primary: #6366f1;
  --premium-primary-light: #818cf8;
  --premium-primary-dark: #4f46e5;
  --premium-secondary: #06b6d4;
  --premium-accent: #f59e0b;
  --premium-success: #10b981;
  --premium-warning: #f59e0b;
  --premium-error: #ef4444;
  
  /* Premium Neutrals */
  --premium-white: #ffffff;
  --premium-gray-50: #fafafa;
  --premium-gray-100: #f4f4f5;
  --premium-gray-200: #e4e4e7;
  --premium-gray-300: #d4d4d8;
  --premium-gray-400: #a1a1aa;
  --premium-gray-500: #71717a;
  --premium-gray-600: #52525b;
  --premium-gray-700: #3f3f46;
  --premium-gray-800: #27272a;
  --premium-gray-900: #18181b;
  
  /* Premium Gradients */
  --premium-gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --premium-gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --premium-gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --premium-gradient-warm: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  --premium-gradient-cool: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  
  /* Premium Typography */
  --premium-font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --premium-font-display: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  
  /* Premium Spacing */
  --premium-space-1: 0.25rem;
  --premium-space-2: 0.5rem;
  --premium-space-3: 0.75rem;
  --premium-space-4: 1rem;
  --premium-space-5: 1.25rem;
  --premium-space-6: 1.5rem;
  --premium-space-8: 2rem;
  --premium-space-10: 2.5rem;
  --premium-space-12: 3rem;
  --premium-space-16: 4rem;
  --premium-space-20: 5rem;
  --premium-space-24: 6rem;
  --premium-space-32: 8rem;
  
  /* Premium Shadows */
  --premium-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --premium-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --premium-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --premium-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --premium-shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --premium-shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);
  
  /* Premium Border Radius */
  --premium-radius-sm: 0.375rem;
  --premium-radius-md: 0.5rem;
  --premium-radius-lg: 0.75rem;
  --premium-radius-xl: 1rem;
  --premium-radius-2xl: 1.5rem;
  --premium-radius-3xl: 2rem;
  --premium-radius-full: 9999px;
  
  /* Premium Transitions */
  --premium-transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --premium-transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --premium-transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Premium Base Styles */
.premium-landing {
  font-family: var(--premium-font-family);
  line-height: 1.6;
  color: var(--premium-gray-800);
  background: var(--premium-white);
}

/* Premium Typography Classes */
.premium-heading-1 {
  font-size: clamp(2.5rem, 5vw, 4.5rem);
  font-weight: 800;
  line-height: 1.1;
  letter-spacing: -0.025em;
  font-family: var(--premium-font-display);
}

.premium-heading-2 {
  font-size: clamp(2rem, 4vw, 3.5rem);
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.02em;
  font-family: var(--premium-font-display);
}

.premium-heading-3 {
  font-size: clamp(1.5rem, 3vw, 2.25rem);
  font-weight: 600;
  line-height: 1.3;
  letter-spacing: -0.015em;
}

.premium-body-large {
  font-size: 1.25rem;
  font-weight: 400;
  line-height: 1.7;
}

.premium-body {
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.6;
}

.premium-body-small {
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
}

/* Premium Button Styles */
.premium-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--premium-space-3) var(--premium-space-6);
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: var(--premium-radius-lg);
  transition: all var(--premium-transition-normal);
  cursor: pointer;
  border: none;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.premium-btn-primary {
  background: var(--premium-gradient-primary);
  color: var(--premium-white);
  box-shadow: var(--premium-shadow-md);
}

.premium-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--premium-shadow-xl);
}

.premium-btn-secondary {
  background: var(--premium-white);
  color: var(--premium-primary);
  border: 2px solid var(--premium-primary);
  box-shadow: var(--premium-shadow-sm);
}

.premium-btn-secondary:hover {
  background: var(--premium-primary);
  color: var(--premium-white);
  transform: translateY(-1px);
  box-shadow: var(--premium-shadow-lg);
}

.premium-btn-large {
  padding: var(--premium-space-4) var(--premium-space-8);
  font-size: 1.125rem;
}

/* Premium Card Styles */
.premium-card {
  background: var(--premium-white);
  border-radius: var(--premium-radius-2xl);
  box-shadow: var(--premium-shadow-lg);
  padding: var(--premium-space-8);
  transition: all var(--premium-transition-normal);
  border: 1px solid var(--premium-gray-200);
}

.premium-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--premium-shadow-2xl);
}

.premium-card-glass {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Premium Gradient Text */
.premium-gradient-text {
  background: var(--premium-gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Premium Animations */
@keyframes premium-float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes premium-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes premium-shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.premium-animate-float {
  animation: premium-float 3s ease-in-out infinite;
}

.premium-animate-pulse {
  animation: premium-pulse 2s ease-in-out infinite;
}

/* Premium Utility Classes */
.premium-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--premium-space-6);
}

.premium-section {
  padding: var(--premium-space-24) 0;
}

.premium-grid {
  display: grid;
  gap: var(--premium-space-8);
}

.premium-flex {
  display: flex;
  align-items: center;
  gap: var(--premium-space-4);
}

.premium-text-center {
  text-align: center;
}

.premium-mb-4 { margin-bottom: var(--premium-space-4); }
.premium-mb-6 { margin-bottom: var(--premium-space-6); }
.premium-mb-8 { margin-bottom: var(--premium-space-8); }
.premium-mb-12 { margin-bottom: var(--premium-space-12); }
.premium-mb-16 { margin-bottom: var(--premium-space-16); }

/* Enhanced Responsive Design */
@media (min-width: 640px) {
  .premium-grid-sm-2 { grid-template-columns: repeat(2, 1fr); }
  .premium-grid-sm-3 { grid-template-columns: repeat(3, 1fr); }
}

@media (min-width: 768px) {
  .premium-grid-md-2 { grid-template-columns: repeat(2, 1fr); }
  .premium-grid-md-3 { grid-template-columns: repeat(3, 1fr); }
  .premium-grid-md-4 { grid-template-columns: repeat(4, 1fr); }
}

@media (min-width: 1024px) {
  .premium-grid-lg-2 { grid-template-columns: repeat(2, 1fr); }
  .premium-grid-lg-3 { grid-template-columns: repeat(3, 1fr); }
  .premium-grid-lg-4 { grid-template-columns: repeat(4, 1fr); }
  .premium-grid-lg-5 { grid-template-columns: repeat(5, 1fr); }
  .premium-grid-lg-6 { grid-template-columns: repeat(6, 1fr); }
}

/* Mobile Optimizations */
@media (max-width: 767px) {
  .premium-heading-1 {
    font-size: clamp(2rem, 8vw, 3rem);
  }

  .premium-heading-2 {
    font-size: clamp(1.5rem, 6vw, 2.25rem);
  }

  .premium-heading-3 {
    font-size: clamp(1.25rem, 5vw, 1.75rem);
  }

  .premium-card {
    padding: var(--premium-space-6);
  }

  .premium-btn-large {
    padding: var(--premium-space-3) var(--premium-space-6);
    font-size: 1rem;
  }

  .premium-section {
    padding: var(--premium-space-16) 0;
  }
}

/* Enhanced Card Layouts */
.premium-card-fixed-height {
  min-height: 400px;
  display: flex;
  flex-direction: column;
}

.premium-card-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.premium-card-footer {
  margin-top: auto;
}

/* Text Overflow Protection */
.premium-text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.premium-text-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.premium-text-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Additional Responsive Utilities */
.premium-hide-mobile {
  display: block;
}

.premium-show-mobile {
  display: none;
}

@media (max-width: 767px) {
  .premium-hide-mobile {
    display: none;
  }

  .premium-show-mobile {
    display: block;
  }

  .premium-stack-mobile {
    flex-direction: column;
  }

  .premium-center-mobile {
    text-align: center;
  }

  .premium-full-width-mobile {
    width: 100%;
  }
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  .premium-btn:hover {
    transform: none;
  }

  .premium-card:hover {
    transform: none;
  }

  .premium-btn:active {
    transform: scale(0.98);
  }

  .premium-card:active {
    transform: scale(0.99);
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .premium-card-glass {
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .premium-animate-float,
  .premium-animate-pulse {
    animation: none;
  }

  .premium-btn,
  .premium-card {
    transition: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .premium-landing {
    color-scheme: dark;
  }
}

/* Footer Integration Cards */
.footer-integration-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.footer-integration-card:hover {
  transform: translateY(-4px) scale(1.02);
}

/* Integration icon animations */
.integration-icon {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.footer-integration-card:hover .integration-icon {
  transform: scale(1.1) rotate(3deg);
}

/* Responsive text sizing for footer */
@media (max-width: 640px) {
  .footer-integration-card {
    padding: 1rem;
  }

  .footer-integration-card .integration-icon {
    width: 2.5rem;
    height: 2.5rem;
  }
}
/* Visualization Builder Styles */
.visualization-builder {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.visualization-builder__header {
  background: rgba(255, 255, 255, 0.95);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
}

.visualization-builder__controls {
  background: rgba(255, 255, 255, 0.98);
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.visualization-builder__chart-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.visualization-builder__chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
  border-radius: 12px 12px 0 0;
}

.visualization-builder__data-table {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  max-height: 400px;
  overflow-y: auto;
}

.visualization-builder__data-table::-webkit-scrollbar {
  width: 6px;
}

.visualization-builder__data-table::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.visualization-builder__data-table::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.visualization-builder__data-table::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.visualization-builder__insight-card {
  transition: all 0.3s ease;
  border-radius: 12px;
  border: 1px solid transparent;
}

.visualization-builder__insight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 255, 255, 0.2);
}

.visualization-builder__control-group {
  position: relative;
}

.visualization-builder__control-group label {
  display: block;
  font-weight: 600;
  font-size: 0.875rem;
  color: #374151;
  margin-bottom: 6px;
  transition: color 0.2s ease;
}

.visualization-builder__control-group:focus-within label {
  color: #6366f1;
}

.visualization-builder__select,
.visualization-builder__input {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  background: white;
}

.visualization-builder__select:focus,
.visualization-builder__input:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  transform: translateY(-1px);
}

.visualization-builder__button {
  display: inline-flex;
  align-items: center;
  padding: 10px 16px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  text-decoration: none;
  border: none;
  cursor: pointer;
}

.visualization-builder__button--primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
}

.visualization-builder__button--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
}

.visualization-builder__button--secondary {
  background: white;
  color: #6b7280;
  border: 2px solid #e5e7eb;
}

.visualization-builder__button--secondary:hover {
  background: #f9fafb;
  border-color: #d1d5db;
  transform: translateY(-1px);
}

.visualization-builder__loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  flex-direction: column;
  color: #6b7280;
}

.visualization-builder__loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #6366f1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.visualization-builder__notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 16px 20px;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  transform: translateX(400px);
  transition: transform 0.3s ease;
  z-index: 1000;
}

.visualization-builder__notification--success {
  background: linear-gradient(135deg, #10b981, #059669);
}

.visualization-builder__notification--error {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.visualization-builder__notification--show {
  transform: translateX(0);
}

/* Chart container enhancements */
.visualization-builder__chart-wrapper {
  position: relative;
  height: 400px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
}

.visualization-builder__chart-wrapper canvas {
  border-radius: 8px;
}

/* Table styling improvements */
.visualization-builder__table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.visualization-builder__table th {
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  color: #374151;
  font-weight: 600;
  font-size: 0.875rem;
  padding: 12px 16px;
  text-align: left;
  border-bottom: 2px solid #e5e7eb;
}

.visualization-builder__table th:first-child {
  border-top-left-radius: 8px;
}

.visualization-builder__table th:last-child {
  border-top-right-radius: 8px;
}

.visualization-builder__table td {
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  color: #6b7280;
  font-size: 0.875rem;
}

.visualization-builder__table tr:hover td {
  background: #f9fafb;
}

.visualization-builder__table tr:last-child td {
  border-bottom: none;
}

/* Responsive design */
@media (max-width: 1024px) {
  .visualization-builder__controls {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .visualization-builder__controls {
    grid-template-columns: 1fr;
  }
  
  .visualization-builder__chart-container {
    margin-bottom: 20px;
  }
  
  .visualization-builder__button {
    width: 100%;
    justify-content: center;
    margin-bottom: 8px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .visualization-builder__controls {
    background: rgba(31, 41, 55, 0.98);
  }
  
  .visualization-builder__control-group label {
    color: #d1d5db;
  }
  
  .visualization-builder__select,
  .visualization-builder__input {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }
  
  .visualization-builder__chart-container {
    background: #1f2937;
  }
  
  .visualization-builder__data-table {
    background: #1f2937;
  }
  
  .visualization-builder__table th {
    background: linear-gradient(135deg, #374151, #4b5563);
    color: #f9fafb;
    border-bottom-color: #6b7280;
  }
  
  .visualization-builder__table td {
    color: #d1d5db;
    border-bottom-color: #4b5563;
  }
  
  .visualization-builder__table tr:hover td {
    background: #374151;
  }
}
/*
 * This is a manifest file that'll be compiled into application.css, which will include all the files
 * listed below.
 *
 * Any CSS (and SCSS, if configured) file within this directory, lib/assets/stylesheets, or any plugin's
 * vendor/assets/stylesheets directory can be referenced here using a relative path.
 *
 * You're free to add application-wide styles to this file and they'll appear at the bottom of the
 * compiled file so the styles you add here take precedence over styles defined in any other CSS
 * files in this directory. Styles in this file should be added after the last require_* statement.
 * It is generally better to create a new file per style scope.
 *






 */
